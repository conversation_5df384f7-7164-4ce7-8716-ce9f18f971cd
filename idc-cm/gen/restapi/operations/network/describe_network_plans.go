// Code generated by go-swagger; DO NOT EDIT.

package network

// This file was generated by the swagger tool.
// Editing this file might prove futile when you re-run the generate command

import (
	"context"
	"net/http"

	"github.com/go-openapi/errors"
	"github.com/go-openapi/runtime/middleware"
	"github.com/go-openapi/strfmt"
	"github.com/go-openapi/swag"
	"github.com/go-openapi/validate"

	"coding.jd.com/fabric/zeusV2/idc-cm/gen/models"
)

// DescribeNetworkPlansHandlerFunc turns a function with the right signature into a describe network plans handler
type DescribeNetworkPlansHandlerFunc func(DescribeNetworkPlansParams) middleware.Responder

// Handle executing the request and returning a response
func (fn DescribeNetworkPlansHandlerFunc) Handle(params DescribeNetworkPlansParams) middleware.Responder {
	return fn(params)
}

// DescribeNetworkPlansHandler interface for that can handle valid describe network plans params
type DescribeNetworkPlansHandler interface {
	Handle(DescribeNetworkPlansParams) middleware.Responder
}

// NewDescribeNetworkPlans creates a new http.Handler for the describe network plans operation
func NewDescribeNetworkPlans(ctx *middleware.Context, handler DescribeNetworkPlansHandler) *DescribeNetworkPlans {
	return &DescribeNetworkPlans{Context: ctx, Handler: handler}
}

/*
	DescribeNetworkPlans swagger:route GET /v1/network_plans network describeNetworkPlans

describeNetworkPlans

获取网络规划列表
*/
type DescribeNetworkPlans struct {
	Context *middleware.Context
	Handler DescribeNetworkPlansHandler
}

func (o *DescribeNetworkPlans) ServeHTTP(rw http.ResponseWriter, r *http.Request) {
	route, rCtx, _ := o.Context.RouteInfo(r)
	if rCtx != nil {
		*r = *rCtx
	}
	var Params = NewDescribeNetworkPlansParams()
	if err := o.Context.BindValidRequest(r, route, &Params); err != nil { // bind params
		o.Context.Respond(rw, r, route.Produces, route, err)
		return
	}

	res := o.Handler.Handle(Params) // actually handle the request
	o.Context.Respond(rw, r, route.Produces, route, res)

}

// DescribeNetworkPlansBody describe network plans body
//
// swagger:model DescribeNetworkPlansBody
type DescribeNetworkPlansBody struct {

	// as number
	// Max Length: 64
	// Min Length: 0
	AsNumber *string `json:"asNumber,omitempty"`

	// batch target
	BatchTarget []string `json:"batch_target"`

	// description
	// Max Length: 64
	// Min Length: 0
	Description *string `json:"description,omitempty"`

	// region Uuid
	// Max Length: 64
	// Min Length: 0
	RegionUUID *string `json:"regionUuid,omitempty"`

	// res Id
	// Max Length: 64
	// Min Length: 0
	ResID *string `json:"resId,omitempty"`

	// res s n
	// Max Length: 64
	// Min Length: 0
	ResSN *string `json:"resSN,omitempty"`

	// res type
	// Max Length: 64
	// Min Length: 0
	ResType *string `json:"resType,omitempty"`

	// status
	// Max Length: 32
	// Min Length: 0
	Status *string `json:"status,omitempty"`

	// subnet
	// Max Length: 64
	// Min Length: 0
	Subnet *string `json:"subnet,omitempty"`

	// 目标类型 | dns  - dns lbs - lbs ispBGPs - ispBGPs jdIPv6s - jdIPv6s interPriOverlaps - interPriOverlaps inats - inats local - local ark - ark container - container sdnDRVR - sdnDRVR sdnVRDR - sdnVRDR sdnVRVM - sdnVRVM sdnDRSW - sdnDRSW
	// Max Length: 64
	// Min Length: 0
	Target *string `json:"target,omitempty"`
}

// Validate validates this describe network plans body
func (o *DescribeNetworkPlansBody) Validate(formats strfmt.Registry) error {
	var res []error

	if err := o.validateAsNumber(formats); err != nil {
		res = append(res, err)
	}

	if err := o.validateDescription(formats); err != nil {
		res = append(res, err)
	}

	if err := o.validateRegionUUID(formats); err != nil {
		res = append(res, err)
	}

	if err := o.validateResID(formats); err != nil {
		res = append(res, err)
	}

	if err := o.validateResSN(formats); err != nil {
		res = append(res, err)
	}

	if err := o.validateResType(formats); err != nil {
		res = append(res, err)
	}

	if err := o.validateStatus(formats); err != nil {
		res = append(res, err)
	}

	if err := o.validateSubnet(formats); err != nil {
		res = append(res, err)
	}

	if err := o.validateTarget(formats); err != nil {
		res = append(res, err)
	}

	if len(res) > 0 {
		return errors.CompositeValidationError(res...)
	}
	return nil
}

func (o *DescribeNetworkPlansBody) validateAsNumber(formats strfmt.Registry) error {
	if swag.IsZero(o.AsNumber) { // not required
		return nil
	}

	if err := validate.MinLength("condition"+"."+"asNumber", "body", *o.AsNumber, 0); err != nil {
		return err
	}

	if err := validate.MaxLength("condition"+"."+"asNumber", "body", *o.AsNumber, 64); err != nil {
		return err
	}

	return nil
}

func (o *DescribeNetworkPlansBody) validateDescription(formats strfmt.Registry) error {
	if swag.IsZero(o.Description) { // not required
		return nil
	}

	if err := validate.MinLength("condition"+"."+"description", "body", *o.Description, 0); err != nil {
		return err
	}

	if err := validate.MaxLength("condition"+"."+"description", "body", *o.Description, 64); err != nil {
		return err
	}

	return nil
}

func (o *DescribeNetworkPlansBody) validateRegionUUID(formats strfmt.Registry) error {
	if swag.IsZero(o.RegionUUID) { // not required
		return nil
	}

	if err := validate.MinLength("condition"+"."+"regionUuid", "body", *o.RegionUUID, 0); err != nil {
		return err
	}

	if err := validate.MaxLength("condition"+"."+"regionUuid", "body", *o.RegionUUID, 64); err != nil {
		return err
	}

	return nil
}

func (o *DescribeNetworkPlansBody) validateResID(formats strfmt.Registry) error {
	if swag.IsZero(o.ResID) { // not required
		return nil
	}

	if err := validate.MinLength("condition"+"."+"resId", "body", *o.ResID, 0); err != nil {
		return err
	}

	if err := validate.MaxLength("condition"+"."+"resId", "body", *o.ResID, 64); err != nil {
		return err
	}

	return nil
}

func (o *DescribeNetworkPlansBody) validateResSN(formats strfmt.Registry) error {
	if swag.IsZero(o.ResSN) { // not required
		return nil
	}

	if err := validate.MinLength("condition"+"."+"resSN", "body", *o.ResSN, 0); err != nil {
		return err
	}

	if err := validate.MaxLength("condition"+"."+"resSN", "body", *o.ResSN, 64); err != nil {
		return err
	}

	return nil
}

func (o *DescribeNetworkPlansBody) validateResType(formats strfmt.Registry) error {
	if swag.IsZero(o.ResType) { // not required
		return nil
	}

	if err := validate.MinLength("condition"+"."+"resType", "body", *o.ResType, 0); err != nil {
		return err
	}

	if err := validate.MaxLength("condition"+"."+"resType", "body", *o.ResType, 64); err != nil {
		return err
	}

	return nil
}

func (o *DescribeNetworkPlansBody) validateStatus(formats strfmt.Registry) error {
	if swag.IsZero(o.Status) { // not required
		return nil
	}

	if err := validate.MinLength("condition"+"."+"status", "body", *o.Status, 0); err != nil {
		return err
	}

	if err := validate.MaxLength("condition"+"."+"status", "body", *o.Status, 32); err != nil {
		return err
	}

	return nil
}

func (o *DescribeNetworkPlansBody) validateSubnet(formats strfmt.Registry) error {
	if swag.IsZero(o.Subnet) { // not required
		return nil
	}

	if err := validate.MinLength("condition"+"."+"subnet", "body", *o.Subnet, 0); err != nil {
		return err
	}

	if err := validate.MaxLength("condition"+"."+"subnet", "body", *o.Subnet, 64); err != nil {
		return err
	}

	return nil
}

func (o *DescribeNetworkPlansBody) validateTarget(formats strfmt.Registry) error {
	if swag.IsZero(o.Target) { // not required
		return nil
	}

	if err := validate.MinLength("condition"+"."+"target", "body", *o.Target, 0); err != nil {
		return err
	}

	if err := validate.MaxLength("condition"+"."+"target", "body", *o.Target, 64); err != nil {
		return err
	}

	return nil
}

// ContextValidate validates this describe network plans body based on context it is used
func (o *DescribeNetworkPlansBody) ContextValidate(ctx context.Context, formats strfmt.Registry) error {
	return nil
}

// MarshalBinary interface implementation
func (o *DescribeNetworkPlansBody) MarshalBinary() ([]byte, error) {
	if o == nil {
		return nil, nil
	}
	return swag.WriteJSON(o)
}

// UnmarshalBinary interface implementation
func (o *DescribeNetworkPlansBody) UnmarshalBinary(b []byte) error {
	var res DescribeNetworkPlansBody
	if err := swag.ReadJSON(b, &res); err != nil {
		return err
	}
	*o = res
	return nil
}

// DescribeNetworkPlansDefaultBody describe network plans default body
//
// swagger:model DescribeNetworkPlansDefaultBody
type DescribeNetworkPlansDefaultBody struct {

	// error
	// Required: true
	Error *models.Error `json:"error"`
}

// Validate validates this describe network plans default body
func (o *DescribeNetworkPlansDefaultBody) Validate(formats strfmt.Registry) error {
	var res []error

	if err := o.validateError(formats); err != nil {
		res = append(res, err)
	}

	if len(res) > 0 {
		return errors.CompositeValidationError(res...)
	}
	return nil
}

func (o *DescribeNetworkPlansDefaultBody) validateError(formats strfmt.Registry) error {

	if err := validate.Required("describeNetworkPlans default"+"."+"error", "body", o.Error); err != nil {
		return err
	}

	if o.Error != nil {
		if err := o.Error.Validate(formats); err != nil {
			if ve, ok := err.(*errors.Validation); ok {
				return ve.ValidateName("describeNetworkPlans default" + "." + "error")
			}
			return err
		}
	}

	return nil
}

// ContextValidate validate this describe network plans default body based on the context it is used
func (o *DescribeNetworkPlansDefaultBody) ContextValidate(ctx context.Context, formats strfmt.Registry) error {
	var res []error

	if err := o.contextValidateError(ctx, formats); err != nil {
		res = append(res, err)
	}

	if len(res) > 0 {
		return errors.CompositeValidationError(res...)
	}
	return nil
}

func (o *DescribeNetworkPlansDefaultBody) contextValidateError(ctx context.Context, formats strfmt.Registry) error {

	if o.Error != nil {
		if err := o.Error.ContextValidate(ctx, formats); err != nil {
			if ve, ok := err.(*errors.Validation); ok {
				return ve.ValidateName("describeNetworkPlans default" + "." + "error")
			}
			return err
		}
	}

	return nil
}

// MarshalBinary interface implementation
func (o *DescribeNetworkPlansDefaultBody) MarshalBinary() ([]byte, error) {
	if o == nil {
		return nil, nil
	}
	return swag.WriteJSON(o)
}

// UnmarshalBinary interface implementation
func (o *DescribeNetworkPlansDefaultBody) UnmarshalBinary(b []byte) error {
	var res DescribeNetworkPlansDefaultBody
	if err := swag.ReadJSON(b, &res); err != nil {
		return err
	}
	*o = res
	return nil
}

// DescribeNetworkPlansOKBody describe network plans o k body
//
// swagger:model DescribeNetworkPlansOKBody
type DescribeNetworkPlansOKBody struct {

	// result
	// Required: true
	Result *models.DescribeNetworkPlansResponse `json:"result"`
}

// Validate validates this describe network plans o k body
func (o *DescribeNetworkPlansOKBody) Validate(formats strfmt.Registry) error {
	var res []error

	if err := o.validateResult(formats); err != nil {
		res = append(res, err)
	}

	if len(res) > 0 {
		return errors.CompositeValidationError(res...)
	}
	return nil
}

func (o *DescribeNetworkPlansOKBody) validateResult(formats strfmt.Registry) error {

	if err := validate.Required("describeNetworkPlansOK"+"."+"result", "body", o.Result); err != nil {
		return err
	}

	if o.Result != nil {
		if err := o.Result.Validate(formats); err != nil {
			if ve, ok := err.(*errors.Validation); ok {
				return ve.ValidateName("describeNetworkPlansOK" + "." + "result")
			}
			return err
		}
	}

	return nil
}

// ContextValidate validate this describe network plans o k body based on the context it is used
func (o *DescribeNetworkPlansOKBody) ContextValidate(ctx context.Context, formats strfmt.Registry) error {
	var res []error

	if err := o.contextValidateResult(ctx, formats); err != nil {
		res = append(res, err)
	}

	if len(res) > 0 {
		return errors.CompositeValidationError(res...)
	}
	return nil
}

func (o *DescribeNetworkPlansOKBody) contextValidateResult(ctx context.Context, formats strfmt.Registry) error {

	if o.Result != nil {
		if err := o.Result.ContextValidate(ctx, formats); err != nil {
			if ve, ok := err.(*errors.Validation); ok {
				return ve.ValidateName("describeNetworkPlansOK" + "." + "result")
			}
			return err
		}
	}

	return nil
}

// MarshalBinary interface implementation
func (o *DescribeNetworkPlansOKBody) MarshalBinary() ([]byte, error) {
	if o == nil {
		return nil, nil
	}
	return swag.WriteJSON(o)
}

// UnmarshalBinary interface implementation
func (o *DescribeNetworkPlansOKBody) UnmarshalBinary(b []byte) error {
	var res DescribeNetworkPlansOKBody
	if err := swag.ReadJSON(b, &res); err != nil {
		return err
	}
	*o = res
	return nil
}
