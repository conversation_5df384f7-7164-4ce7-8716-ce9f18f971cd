// Code generated by go-swagger; DO NOT EDIT.

package server

// This file was generated by the swagger tool.
// Editing this file might prove futile when you re-run the generate command

import (
	"context"
	"net/http"

	"github.com/go-openapi/errors"
	"github.com/go-openapi/runtime/middleware"
	"github.com/go-openapi/strfmt"
	"github.com/go-openapi/swag"
	"github.com/go-openapi/validate"

	"coding.jd.com/fabric/zeusV2/idc-cm/gen/models"
)

// DeleteServerHandlerFunc turns a function with the right signature into a delete server handler
type DeleteServerHandlerFunc func(DeleteServerParams) middleware.Responder

// <PERSON>le executing the request and returning a response
func (fn DeleteServerHandlerFunc) Handle(params DeleteServerParams) middleware.Responder {
	return fn(params)
}

// DeleteServerHandler interface for that can handle valid delete server params
type DeleteServerHandler interface {
	Handle(DeleteServerParams) middleware.Responder
}

// NewDeleteServer creates a new http.Handler for the delete server operation
func NewDeleteServer(ctx *middleware.Context, handler DeleteServerHandler) *DeleteServer {
	return &DeleteServer{Context: ctx, Handler: handler}
}

/*
	DeleteServer swagger:route DELETE /v1/server server deleteServer

deleteServer

删除服务器信息
*/
type DeleteServer struct {
	Context *middleware.Context
	Handler DeleteServerHandler
}

func (o *DeleteServer) ServeHTTP(rw http.ResponseWriter, r *http.Request) {
	route, rCtx, _ := o.Context.RouteInfo(r)
	if rCtx != nil {
		*r = *rCtx
	}
	var Params = NewDeleteServerParams()
	if err := o.Context.BindValidRequest(r, route, &Params); err != nil { // bind params
		o.Context.Respond(rw, r, route.Produces, route, err)
		return
	}

	res := o.Handler.Handle(Params) // actually handle the request
	o.Context.Respond(rw, r, route.Produces, route, res)

}

// DeleteServerDefaultBody delete server default body
//
// swagger:model DeleteServerDefaultBody
type DeleteServerDefaultBody struct {

	// error
	// Required: true
	Error *models.Error `json:"error"`
}

// Validate validates this delete server default body
func (o *DeleteServerDefaultBody) Validate(formats strfmt.Registry) error {
	var res []error

	if err := o.validateError(formats); err != nil {
		res = append(res, err)
	}

	if len(res) > 0 {
		return errors.CompositeValidationError(res...)
	}
	return nil
}

func (o *DeleteServerDefaultBody) validateError(formats strfmt.Registry) error {

	if err := validate.Required("deleteServer default"+"."+"error", "body", o.Error); err != nil {
		return err
	}

	if o.Error != nil {
		if err := o.Error.Validate(formats); err != nil {
			if ve, ok := err.(*errors.Validation); ok {
				return ve.ValidateName("deleteServer default" + "." + "error")
			}
			return err
		}
	}

	return nil
}

// ContextValidate validate this delete server default body based on the context it is used
func (o *DeleteServerDefaultBody) ContextValidate(ctx context.Context, formats strfmt.Registry) error {
	var res []error

	if err := o.contextValidateError(ctx, formats); err != nil {
		res = append(res, err)
	}

	if len(res) > 0 {
		return errors.CompositeValidationError(res...)
	}
	return nil
}

func (o *DeleteServerDefaultBody) contextValidateError(ctx context.Context, formats strfmt.Registry) error {

	if o.Error != nil {
		if err := o.Error.ContextValidate(ctx, formats); err != nil {
			if ve, ok := err.(*errors.Validation); ok {
				return ve.ValidateName("deleteServer default" + "." + "error")
			}
			return err
		}
	}

	return nil
}

// MarshalBinary interface implementation
func (o *DeleteServerDefaultBody) MarshalBinary() ([]byte, error) {
	if o == nil {
		return nil, nil
	}
	return swag.WriteJSON(o)
}

// UnmarshalBinary interface implementation
func (o *DeleteServerDefaultBody) UnmarshalBinary(b []byte) error {
	var res DeleteServerDefaultBody
	if err := swag.ReadJSON(b, &res); err != nil {
		return err
	}
	*o = res
	return nil
}

// DeleteServerOKBody delete server o k body
//
// swagger:model DeleteServerOKBody
type DeleteServerOKBody struct {

	// result
	// Required: true
	Result interface{} `json:"result"`
}

// Validate validates this delete server o k body
func (o *DeleteServerOKBody) Validate(formats strfmt.Registry) error {
	var res []error

	if err := o.validateResult(formats); err != nil {
		res = append(res, err)
	}

	if len(res) > 0 {
		return errors.CompositeValidationError(res...)
	}
	return nil
}

func (o *DeleteServerOKBody) validateResult(formats strfmt.Registry) error {

	if o.Result == nil {
		return errors.Required("deleteServerOK"+"."+"result", "body", nil)
	}

	return nil
}

// ContextValidate validates this delete server o k body based on context it is used
func (o *DeleteServerOKBody) ContextValidate(ctx context.Context, formats strfmt.Registry) error {
	return nil
}

// MarshalBinary interface implementation
func (o *DeleteServerOKBody) MarshalBinary() ([]byte, error) {
	if o == nil {
		return nil, nil
	}
	return swag.WriteJSON(o)
}

// UnmarshalBinary interface implementation
func (o *DeleteServerOKBody) UnmarshalBinary(b []byte) error {
	var res DeleteServerOKBody
	if err := swag.ReadJSON(b, &res); err != nil {
		return err
	}
	*o = res
	return nil
}
