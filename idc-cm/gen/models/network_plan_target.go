// Code generated by go-swagger; DO NOT EDIT.

package models

// This file was generated by the swagger tool.
// Editing this file might prove futile when you re-run the swagger generate command

import (
	"context"

	"github.com/go-openapi/strfmt"
	"github.com/go-openapi/swag"
)

// NetworkPlanTarget network plan target
//
// swagger:model network_plan_target
type NetworkPlanTarget struct {

	// 是否必须有至少一条，否则影响交付执行
	IsMust bool `json:"isMust,omitempty"`

	// 是否只能有一个
	IsOnly bool `json:"isOnly,omitempty"`

	// name
	Name string `json:"name,omitempty"`

	// 所属父级target
	ParentTarget string `json:"parent_target,omitempty"`

	// rule
	Rule string `json:"rule,omitempty"`

	// target
	Target string `json:"target,omitempty"`
}

// Validate validates this network plan target
func (m *NetworkPlanTarget) Validate(formats strfmt.Registry) error {
	return nil
}

// ContextValidate validates this network plan target based on context it is used
func (m *NetworkPlanTarget) ContextValidate(ctx context.Context, formats strfmt.Registry) error {
	return nil
}

// MarshalBinary interface implementation
func (m *NetworkPlanTarget) MarshalBinary() ([]byte, error) {
	if m == nil {
		return nil, nil
	}
	return swag.WriteJSON(m)
}

// UnmarshalBinary interface implementation
func (m *NetworkPlanTarget) UnmarshalBinary(b []byte) error {
	var res NetworkPlanTarget
	if err := swag.ReadJSON(b, &res); err != nil {
		return err
	}
	*m = res
	return nil
}
