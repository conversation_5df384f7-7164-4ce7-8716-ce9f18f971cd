// Code generated by go-swagger; DO NOT EDIT.

package eip

// This file was generated by the swagger tool.
// Editing this file might prove futile when you re-run the swagger generate command

import (
	"context"
	"net/http"
	"time"

	"github.com/go-openapi/errors"
	"github.com/go-openapi/runtime"
	cr "github.com/go-openapi/runtime/client"
	"github.com/go-openapi/strfmt"
)

// NewCreateEipParams creates a new CreateEipParams object,
// with the default timeout for this client.
//
// Default values are not hydrated, since defaults are normally applied by the API server side.
//
// To enforce default values in parameter, use SetDefaults or WithDefaults.
func NewCreateEipParams() *CreateEipParams {
	return &CreateEipParams{
		timeout: cr.DefaultTimeout,
	}
}

// NewCreateEipParamsWithTimeout creates a new CreateEipParams object
// with the ability to set a timeout on a request.
func NewCreateEipParamsWithTimeout(timeout time.Duration) *CreateEipParams {
	return &CreateEipParams{
		timeout: timeout,
	}
}

// NewCreateEipParamsWithContext creates a new CreateEipParams object
// with the ability to set a context for a request.
func NewCreateEipParamsWithContext(ctx context.Context) *CreateEipParams {
	return &CreateEipParams{
		Context: ctx,
	}
}

// NewCreateEipParamsWithHTTPClient creates a new CreateEipParams object
// with the ability to set a custom HTTPClient for a request.
func NewCreateEipParamsWithHTTPClient(client *http.Client) *CreateEipParams {
	return &CreateEipParams{
		HTTPClient: client,
	}
}

/*
CreateEipParams contains all the parameters to send to the API endpoint

	for the create eip operation.

	Typically these are written to a http.Request.
*/
type CreateEipParams struct {

	// Eip.
	Eip []*CreateEipParamsBodyItems0

	timeout    time.Duration
	Context    context.Context
	HTTPClient *http.Client
}

// WithDefaults hydrates default values in the create eip params (not the query body).
//
// All values with no default are reset to their zero value.
func (o *CreateEipParams) WithDefaults() *CreateEipParams {
	o.SetDefaults()
	return o
}

// SetDefaults hydrates default values in the create eip params (not the query body).
//
// All values with no default are reset to their zero value.
func (o *CreateEipParams) SetDefaults() {
	// no default values defined for this parameter
}

// WithTimeout adds the timeout to the create eip params
func (o *CreateEipParams) WithTimeout(timeout time.Duration) *CreateEipParams {
	o.SetTimeout(timeout)
	return o
}

// SetTimeout adds the timeout to the create eip params
func (o *CreateEipParams) SetTimeout(timeout time.Duration) {
	o.timeout = timeout
}

// WithContext adds the context to the create eip params
func (o *CreateEipParams) WithContext(ctx context.Context) *CreateEipParams {
	o.SetContext(ctx)
	return o
}

// SetContext adds the context to the create eip params
func (o *CreateEipParams) SetContext(ctx context.Context) {
	o.Context = ctx
}

// WithHTTPClient adds the HTTPClient to the create eip params
func (o *CreateEipParams) WithHTTPClient(client *http.Client) *CreateEipParams {
	o.SetHTTPClient(client)
	return o
}

// SetHTTPClient adds the HTTPClient to the create eip params
func (o *CreateEipParams) SetHTTPClient(client *http.Client) {
	o.HTTPClient = client
}

// WithEip adds the eip to the create eip params
func (o *CreateEipParams) WithEip(eip []*CreateEipParamsBodyItems0) *CreateEipParams {
	o.SetEip(eip)
	return o
}

// SetEip adds the eip to the create eip params
func (o *CreateEipParams) SetEip(eip []*CreateEipParamsBodyItems0) {
	o.Eip = eip
}

// WriteToRequest writes these params to a swagger request
func (o *CreateEipParams) WriteToRequest(r runtime.ClientRequest, reg strfmt.Registry) error {

	if err := r.SetTimeout(o.timeout); err != nil {
		return err
	}
	var res []error
	if o.Eip != nil {
		if err := r.SetBodyParam(o.Eip); err != nil {
			return err
		}
	}

	if len(res) > 0 {
		return errors.CompositeValidationError(res...)
	}
	return nil
}
