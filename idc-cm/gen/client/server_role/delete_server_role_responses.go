// Code generated by go-swagger; DO NOT EDIT.

package server_role

// This file was generated by the swagger tool.
// Editing this file might prove futile when you re-run the swagger generate command

import (
	"context"
	"fmt"
	"io"

	"github.com/go-openapi/errors"
	"github.com/go-openapi/runtime"
	"github.com/go-openapi/strfmt"
	"github.com/go-openapi/swag"
	"github.com/go-openapi/validate"

	"coding.jd.com/fabric/zeusV2/idc-cm/gen/models"
)

// DeleteServerRoleReader is a Reader for the DeleteServerRole structure.
type DeleteServerRoleReader struct {
	formats strfmt.Registry
}

// ReadResponse reads a server response into the received o.
func (o *DeleteServerRoleReader) ReadResponse(response runtime.ClientResponse, consumer runtime.Consumer) (interface{}, error) {
	switch response.Code() {
	case 200:
		result := NewDeleteServerRoleOK()
		if err := result.readResponse(response, consumer, o.formats); err != nil {
			return nil, err
		}
		return result, nil
	default:
		result := NewDeleteServerRoleDefault(response.Code())
		if err := result.readResponse(response, consumer, o.formats); err != nil {
			return nil, err
		}
		if response.Code()/100 == 2 {
			return result, nil
		}
		return nil, result
	}
}

// NewDeleteServerRoleOK creates a DeleteServerRoleOK with default headers values
func NewDeleteServerRoleOK() *DeleteServerRoleOK {
	return &DeleteServerRoleOK{}
}

/*
DeleteServerRoleOK describes a response with status code 200, with default header values.

delete of server_roles
*/
type DeleteServerRoleOK struct {
	Payload *DeleteServerRoleOKBody
}

func (o *DeleteServerRoleOK) Error() string {
	return fmt.Sprintf("[DELETE /v1/serverRole/{serverId}][%d] deleteServerRoleOK  %+v", 200, o.Payload)
}
func (o *DeleteServerRoleOK) GetPayload() *DeleteServerRoleOKBody {
	return o.Payload
}

func (o *DeleteServerRoleOK) readResponse(response runtime.ClientResponse, consumer runtime.Consumer, formats strfmt.Registry) error {

	o.Payload = new(DeleteServerRoleOKBody)

	// response payload
	if err := consumer.Consume(response.Body(), o.Payload); err != nil && err != io.EOF {
		return err
	}

	return nil
}

// NewDeleteServerRoleDefault creates a DeleteServerRoleDefault with default headers values
func NewDeleteServerRoleDefault(code int) *DeleteServerRoleDefault {
	return &DeleteServerRoleDefault{
		_statusCode: code,
	}
}

/*
DeleteServerRoleDefault describes a response with status code -1, with default header values.

delete server_packages  error response
*/
type DeleteServerRoleDefault struct {
	_statusCode int

	Payload *DeleteServerRoleDefaultBody
}

// Code gets the status code for the delete server role default response
func (o *DeleteServerRoleDefault) Code() int {
	return o._statusCode
}

func (o *DeleteServerRoleDefault) Error() string {
	return fmt.Sprintf("[DELETE /v1/serverRole/{serverId}][%d] deleteServerRole default  %+v", o._statusCode, o.Payload)
}
func (o *DeleteServerRoleDefault) GetPayload() *DeleteServerRoleDefaultBody {
	return o.Payload
}

func (o *DeleteServerRoleDefault) readResponse(response runtime.ClientResponse, consumer runtime.Consumer, formats strfmt.Registry) error {

	o.Payload = new(DeleteServerRoleDefaultBody)

	// response payload
	if err := consumer.Consume(response.Body(), o.Payload); err != nil && err != io.EOF {
		return err
	}

	return nil
}

/*
DeleteServerRoleDefaultBody delete server role default body
swagger:model DeleteServerRoleDefaultBody
*/
type DeleteServerRoleDefaultBody struct {

	// error
	// Required: true
	Error *models.Error `json:"error"`
}

// Validate validates this delete server role default body
func (o *DeleteServerRoleDefaultBody) Validate(formats strfmt.Registry) error {
	var res []error

	if err := o.validateError(formats); err != nil {
		res = append(res, err)
	}

	if len(res) > 0 {
		return errors.CompositeValidationError(res...)
	}
	return nil
}

func (o *DeleteServerRoleDefaultBody) validateError(formats strfmt.Registry) error {

	if err := validate.Required("deleteServerRole default"+"."+"error", "body", o.Error); err != nil {
		return err
	}

	if o.Error != nil {
		if err := o.Error.Validate(formats); err != nil {
			if ve, ok := err.(*errors.Validation); ok {
				return ve.ValidateName("deleteServerRole default" + "." + "error")
			}
			return err
		}
	}

	return nil
}

// ContextValidate validate this delete server role default body based on the context it is used
func (o *DeleteServerRoleDefaultBody) ContextValidate(ctx context.Context, formats strfmt.Registry) error {
	var res []error

	if err := o.contextValidateError(ctx, formats); err != nil {
		res = append(res, err)
	}

	if len(res) > 0 {
		return errors.CompositeValidationError(res...)
	}
	return nil
}

func (o *DeleteServerRoleDefaultBody) contextValidateError(ctx context.Context, formats strfmt.Registry) error {

	if o.Error != nil {
		if err := o.Error.ContextValidate(ctx, formats); err != nil {
			if ve, ok := err.(*errors.Validation); ok {
				return ve.ValidateName("deleteServerRole default" + "." + "error")
			}
			return err
		}
	}

	return nil
}

// MarshalBinary interface implementation
func (o *DeleteServerRoleDefaultBody) MarshalBinary() ([]byte, error) {
	if o == nil {
		return nil, nil
	}
	return swag.WriteJSON(o)
}

// UnmarshalBinary interface implementation
func (o *DeleteServerRoleDefaultBody) UnmarshalBinary(b []byte) error {
	var res DeleteServerRoleDefaultBody
	if err := swag.ReadJSON(b, &res); err != nil {
		return err
	}
	*o = res
	return nil
}

/*
DeleteServerRoleOKBody delete server role o k body
swagger:model DeleteServerRoleOKBody
*/
type DeleteServerRoleOKBody struct {

	// result
	// Required: true
	Result interface{} `json:"result"`
}

// Validate validates this delete server role o k body
func (o *DeleteServerRoleOKBody) Validate(formats strfmt.Registry) error {
	var res []error

	if err := o.validateResult(formats); err != nil {
		res = append(res, err)
	}

	if len(res) > 0 {
		return errors.CompositeValidationError(res...)
	}
	return nil
}

func (o *DeleteServerRoleOKBody) validateResult(formats strfmt.Registry) error {

	if o.Result == nil {
		return errors.Required("deleteServerRoleOK"+"."+"result", "body", nil)
	}

	return nil
}

// ContextValidate validates this delete server role o k body based on context it is used
func (o *DeleteServerRoleOKBody) ContextValidate(ctx context.Context, formats strfmt.Registry) error {
	return nil
}

// MarshalBinary interface implementation
func (o *DeleteServerRoleOKBody) MarshalBinary() ([]byte, error) {
	if o == nil {
		return nil, nil
	}
	return swag.WriteJSON(o)
}

// UnmarshalBinary interface implementation
func (o *DeleteServerRoleOKBody) UnmarshalBinary(b []byte) error {
	var res DeleteServerRoleOKBody
	if err := swag.ReadJSON(b, &res); err != nil {
		return err
	}
	*o = res
	return nil
}
