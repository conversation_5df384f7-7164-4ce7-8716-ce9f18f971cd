// =================================================================================
// This is auto-generated by GoFrame CLI tool only once. Fill this file as you wish.
// =================================================================================

package dao

import (
	"coding.jd.com/fabric/zeusV2/idc-cm/gen/goframe/dao/internal"
)

// internalPrimaryTaskCenterParamsDao is internal type for wrapping internal DAO implements.
type internalPrimaryTaskCenterParamsDao = *internal.PrimaryTaskCenterParamsDao

// primaryTaskCenterParamsDao is the data access object for table task_center_params.
// You can define custom methods on it to extend its functionality as you wish.
type primaryTaskCenterParamsDao struct {
	internalPrimaryTaskCenterParamsDao
}

var (
	// PrimaryTaskCenterParams is globally public accessible object for table task_center_params operations.
	PrimaryTaskCenterParams = primaryTaskCenterParamsDao{
		internal.NewPrimaryTaskCenterParamsDao(),
	}
)

// Fill with you ideas below.
