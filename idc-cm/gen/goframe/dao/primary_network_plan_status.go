// =================================================================================
// This is auto-generated by GoFrame CLI tool only once. Fill this file as you wish.
// =================================================================================

package dao

import (
	"coding.jd.com/fabric/zeusV2/idc-cm/gen/goframe/dao/internal"
)

// internalPrimaryNetworkPlanStatusDao is internal type for wrapping internal DAO implements.
type internalPrimaryNetworkPlanStatusDao = *internal.PrimaryNetworkPlanStatusDao

// primaryNetworkPlanStatusDao is the data access object for table network_plan_status.
// You can define custom methods on it to extend its functionality as you wish.
type primaryNetworkPlanStatusDao struct {
	internalPrimaryNetworkPlanStatusDao
}

var (
	// PrimaryNetworkPlanStatus is globally public accessible object for table network_plan_status operations.
	PrimaryNetworkPlanStatus = primaryNetworkPlanStatusDao{
		internal.NewPrimaryNetworkPlanStatusDao(),
	}
)

// Fill with you ideas below.
