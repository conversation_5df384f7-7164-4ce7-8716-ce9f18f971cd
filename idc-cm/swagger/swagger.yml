swagger: "2.0"
produces:
  - application/json
consumes:
  - application/json
schemes:
  - http
info:
  description: IDC API Server
  title: idc api
  version: 0.0.1
host: localhost:8080

paths:
  /v1/available_zone:
    get:
      description: 获取可用区信息
      summary: describeAvailableZone
      operationId: describeAvailableZone
      tags:
        - available_zone
      parameters:
        - name: condition
          in: body
          schema:
            properties:
              region_uuid:
                type: string
                minLength: 0
                maxLength: 64
              region_name:
                type: string
                minLength: 0
                maxLength: 64
              az_uuid:
                type: string
                minLength: 0
                maxLength: 64
              az_name:
                type: string
                minLength: 0
                maxLength: 64
              cn_name:
                type: string
                minLength: 0
                maxLength: 64
              is_default:
                description: 是否默认
                type: string
                minLength: 0
                maxLength: 32
              status:
                description: 状态
                type: string
                minLength: 0
                maxLength: 32
              arbitrationAz:
                description: 逻辑仲裁az的展示.默认为false,不展示
                type: boolean
                default: false
        - name: page_size
          in: query
          required: true
          type: integer
          format: int
          minimum: 1
          maximum: 16777216
        - name: page_no
          in: query
          required: true
          type: integer
          format: int
          minimum: -1
          maximum: 16777216
      responses:
        200:
          description: describe of az list
          schema:
            type: object
            required:
              - result
            properties:
              result:
                $ref: "#/definitions/available_zones_response"
        default:
          description: describe  of az error response
          schema:
            type: object
            required:
              - error
            properties:
              error:
                $ref: "#/definitions/error"
    post:
      description: 添加可用区信息
      summary: createAvailableZone
      operationId: createAvailableZone
      tags:
        - available_zone
      parameters:
        - name: available_zone
          in: body
          schema:
            type: array
            items:
              required:
                - name
                - region_name
                - cn_name
                - is_default
              properties:
                uuid:
                  type: string
                  minLength: 0
                  maxLength: 64
                  pattern: ^([A-Za-z0-9-_]{1,64})$
                  x-go-custom-tag: db:"uuid"
                  x-nullable: true
                name:
                  type: string
                  minLength: 0
                  maxLength: 64
                  pattern: ^([a-z][a-z0-9-]{1,62}[a-z])$
                  x-go-custom-tag: db:"name"
                region_name:
                  type: string
                  minLength: 0
                  maxLength: 64
                  pattern: ^([a-z][a-z0-9-]{1,62}[0-9])$
                  x-go-custom-tag: db:"region_name"
                use_way:
                  type: string
                  minLength: 0
                  maxLength: 64
                  x-go-custom-tag: db:"use_way"
                cn_name:
                  description: az中文名称
                  type: string
                  minLength: 0
                  maxLength: 64
                  x-go-custom-tag: db:"cn_name"
                  x-nullable: false
                is_default:
                  description: 是否默认
                  type: string
                  minLength: 0
                  maxLength: 32
                  pattern: ^[0-1]{1}$
                  x-go-custom-tag: db:"is_default"
                  x-nullable: false
                status:
                  description: 状态
                  type: string
                  minLength: 0
                  maxLength: 32
                  enum:
                    - "building"
                    - "available"
                  x-go-custom-tag: db:"status"
                  x-nullable: false
                description:
                  description: 描述
                  type: string
                  minLength: 0
                  maxLength: 128
                  x-go-custom-tag: db:"description"
                  x-nullable: true
      responses:
        200:
          description: create of az
          schema:
            type: object
            required:
              - result
            properties:
              result:
                type: object
        default:
          description: create of az error response
          schema:
            type: object
            required:
              - error
            properties:
              error:
                $ref: "#/definitions/error"
    delete:
      description: 批量删除可用区信息
      summary: deleteAvailableZone
      operationId: deleteAvailableZone
      tags:
        - available_zone
      parameters:
        - name: uuids
          in: body
          schema:
            type: array
            items:
              type: string
      responses:
        200:
          description: delete of az
          schema:
            type: object
            required:
              - result
            properties:
              result:
                type: object
        default:
          description: delete of az  error response
          schema:
            type: object
            required:
              - error
            properties:
              error:
                $ref: "#/definitions/error"
  /v1/available_zone/{uuid}:
    put:
      description: 修改可用区信息
      summary: updateAvailableZone
      operationId: updateAvailableZone
      tags:
        - available_zone
      parameters:
        - name: uuid
          in: path
          required: true
          type: string
          minLength: 0
          maxLength: 32
        - name: available_zone
          in: body
          schema:
            properties:
              region_name:
                type: string
                minLength: 0
                maxLength: 64
                pattern: ^([a-z][a-z0-9-]{1,62}[0-9])$
                x-go-custom-tag: db:"region_name"
              use_way:
                type: string
                minLength: 0
                maxLength: 64
                x-go-custom-tag: db:"use_way"
              cn_name:
                description: az中文名称
                type: string
                minLength: 0
                maxLength: 64
                x-go-custom-tag: db:"cn_name"
              is_default:
                description: 是否默认
                type: string
                minLength: 0
                maxLength: 32
                pattern: ^[0-1]{1}$
                x-go-custom-tag: db:"is_default"
              status:
                description: 状态
                type: string
                minLength: 0
                maxLength: 32
                enum:
                  - "building"
                  - "available"
                x-go-custom-tag: db:"status"
              description:
                description: 描述
                type: string
                minLength: 0
                maxLength: 128
                x-go-custom-tag: db:"description"
                x-nullable: true
              clear_fields:
                type: array
                items:
                  type: string
                  enum:
                    - "useWay"
                    - "description"
      responses:
        200:
          description: describe of server
          schema:
            type: object
            required:
              - result
            properties:
              result:
                type: object
        default:
          description: describe server  error response
          schema:
            type: object
            required:
              - error
            properties:
              error:
                $ref: "#/definitions/error"
  /v1/region:
    get:
      description: 获取region列表
      summary: describeRegion
      operationId: describeRegion
      tags:
        - region
      parameters:
        - name: page_size
          in: query
          required: true
          type: integer
          format: int
          minimum: 1
          maximum: 16777216
        - name: page_no
          in: query
          required: true
          type: integer
          format: int
          minimum: -1
          maximum: 16777216
        - name: condition
          in: body
          schema:
            properties:
              region_uuid:
                type: string
                minLength: 0
                maxLength: 64
              region_name:
                type: string
                minLength: 0
                maxLength: 64
              az_uuid:
                type: string
                minLength: 0
                maxLength: 64
              az_name:
                type: string
                minLength: 0
                maxLength: 64
              cn_name:
                type: string
                minLength: 0
                maxLength: 64
              is_default:
                description: 是否默认
                type: string
                minLength: 0
                maxLength: 32
              status:
                description: 状态
                type: string
                minLength: 0
                maxLength: 32
      responses:
        200:
          description: describe of region
          schema:
            type: object
            required:
              - result
            properties:
              result:
                $ref: "#/definitions/regions_response"
        default:
          description: describe of region  error response
          schema:
            type: object
            required:
              - error
            properties:
              error:
                $ref: "#/definitions/error"
    post:
      description: 新增 region
      summary: createRegion
      operationId: createRegion
      tags:
        - region
      parameters:
        - name: region
          in: body
          schema:
            type: array
            items:
              required:
                - name
                - cn_name
                - status
                - is_default
              properties:
                uuid:
                  type: string
                  minLength: 0
                  maxLength: 64
                  pattern: ^([A-Za-z0-9-_]{1,64})$
                  x-go-custom-tag: db:"uuid"
                  x-nullable: true
                name:
                  type: string
                  minLength: 0
                  maxLength: 64
                  pattern: ^([a-z][a-z0-9-]{1,62}[0-9])$
                  x-go-custom-tag: db:"name"
                cn_name:
                  type: string
                  minLength: 0
                  maxLength: 64
                  x-go-custom-tag: db:"cn_name"
                use_way:
                  type: string
                  minLength: 0
                  maxLength: 64
                  x-go-custom-tag: db:"use_way"
                description:
                  type: string
                  minLength: 0
                  maxLength: 128
                  x-go-custom-tag: db:"description"
                status:
                  type: string
                  minLength: 0
                  maxLength: 32
                  enum:
                    - "building"
                    - "available"
                  x-go-custom-tag: db:"status"
                is_default:
                  type: string
                  minLength: 0
                  maxLength: 32
                  pattern: ^[0-1]{1}$
                  x-go-custom-tag: db:"is_default"
      responses:
        200:
          description: create of region
          schema:
            type: object
            required:
              - result
            properties:
              result:
                type: object
        default:
          description: create of region  error response
          schema:
            type: object
            required:
              - error
            properties:
              error:
                $ref: "#/definitions/error"
    delete:
      description: 批量删除某一条 region
      summary: deleteRegion
      operationId: deleteRegion
      tags:
        - region
      parameters:
        - name: uuids
          in: body
          schema:
            type: array
            items:
              type: string
      responses:
        200:
          description: delete of regions
          schema:
            type: object
            required:
              - result
            properties:
              result:
                type: object
        default:
          description: delete of regions  error response
          schema:
            type: object
            required:
              - error
            properties:
              error:
                $ref: "#/definitions/error"
  /v1/region/{uuid}:
    put:
      description: 修改region信息
      summary: updateRegion
      operationId: updateRegion
      tags:
        - region
      parameters:
        - name: uuid
          in: path
          required: true
          type: string
          minLength: 0
          maxLength: 32
        - name: region
          in: body
          schema:
            properties:
              cn_name:
                type: string
                minLength: 0
                maxLength: 64
                x-go-custom-tag: db:"cn_name"
              use_way:
                description: use_way
                type: string
                minLength: 0
                maxLength: 64
                x-go-custom-tag: db:"use_way"
              description:
                type: string
                minLength: 0
                maxLength: 128
                x-go-custom-tag: db:"description"
              status:
                type: string
                minLength: 0
                maxLength: 32
                enum:
                  - "building"
                  - "available"
                x-go-custom-tag: db:"status"
              clear_fields:
                type: array
                items:
                  type: string
                  enum:
                    - "useWay"
                    - "description"
#              is_default:
#                type: string
#                minLength: 0
#                maxLength: 32
#                pattern: ^[0-1]{1}$
#                x-go-custom-tag: db:"is_default"
      responses:
        200:
          description: describe of server
          schema:
            type: object
            required:
              - result
            properties:
              result:
                type: object
        default:
          description: describe server  error response
          schema:
            type: object
            required:
              - error
            properties:
              error:
                $ref: "#/definitions/error"
  /v1/datacenter:
    get:
      description: 获取机房列表信息, 查询成功，只返回result, 若失败, 只返回 error
      summary: describeDatacenter
      operationId: describeDatacenter
      tags:
        - datacenter
      parameters:
        - name: condition
          in: body
          schema:
            properties:
              region_uuid:
                type: string
                minLength: 0
                maxLength: 64
              az_uuid:
                type: string
                minLength: 0
                maxLength: 64
              region_name:
                type: string
                minLength: 0
                maxLength: 64
              az_name:
                type: string
                minLength: 0
                maxLength: 64
              datacenter_name:
                type: array
                items:
                  type: string
                  minLength: 0
                  maxLength: 64
              provider:
                type: array
                items:
                  type: string
                  minLength: 0
                  maxLength: 64
              level:
                type: array
                description: 机房等级
                items:
                  type: string
                  minLength: 0
                  maxLength: 64
              province:
                type: array
                items:
                  type: string
                  minLength: 0
                  maxLength: 64
              city:
                type: array
                items:
                  type: string
                  minLength: 0
                  maxLength: 64
              electrical_spec:
                description: 电力情况
                type: array
                items:
                  type: string
                  minLength: 0
                  maxLength: 64
        - name: page_size
          in: query
          required: true
          type: integer
          format: int
          minimum: 1
          maximum: 16777216
        - name: page_no
          in: query
          required: true
          type: integer
          format: int
          minimum: -1
          maximum: 16777216
      responses:
        200:
          description: list of datacenter
          schema:
            type: object
            required:
              - result
            properties:
              result:
                type: object
                properties:
                  dataceneters:
                    type: array
                    items:
                      $ref: "#/definitions/describe_datacenter"
                  total_count:
                    description: total count
                    type: integer
                    format: int32
                    minimum: 0
                    maximum: 16777216
                    x-go-custom-tag: db:"total_count"
                    x-nullable: false
        default:
          description: generic error response
          schema:
            type: object
            required:
              - error
            properties:
              error:
                $ref: "#/definitions/error"
    post:
      description: 添加机房信息, 若成功只返回result=nil, 失败只返回error
      summary: createDatacenter
      operationId: createDatacenter
      tags:
        - datacenter
      parameters:
        - name: datacenter
          in: body
          schema:
            type: array
            items:
              required:
                - name
              properties:
                uuid:
                  type: string
                  minLength: 0
                  maxLength: 64
                  pattern: ^([A-Za-z0-9-_]{0,64})$
                  x-go-custom-tag: db:"uuid"
                name:
                  type: string
                  minLength: 1
                  maxLength: 64
                  x-go-custom-tag: db:"name"
                az_uuid:
                  type: string
                  minLength: 0
                  maxLength: 64
                  pattern: ^([A-Za-z0-9-]{0,64})$
                  x-go-custom-tag: db:"az_uuid"
                level:
                  type: string
                  minLength: 0
                  maxLength: 64
                  x-go-custom-tag: db:"level"
                provider:
                  description: 运营商
                  type: string
                  minLength: 1
                  maxLength: 64
                  x-go-custom-tag: db:"provider"
                room_count:
                  type: integer
                  format: int32
                  minimum: 0
                  maximum: 16777216
                  x-go-custom-tag: db:"room_count"
                province:
                  type: string
                  minLength: 1
                  maxLength: 64
                  x-go-custom-tag: db:"province"
                city:
                  type: string
                  minLength: 1
                  maxLength: 64
                  x-go-custom-tag: db:"city"
                address:
                  type: string
                  minLength: 0
                  maxLength: 128
                  x-go-custom-tag: db:"address"
                electrical_spec:
                  type: string
                  minLength: 1
                  maxLength: 64
                  x-go-custom-tag: db:"electrical_spec"
      responses:
        200:
          description: create datacenter
          schema:
            type: object
            required:
              - result
            properties:
              ids:
                type: array
                items:
                  type: string
        default:
          description: generic error response
          schema:
            type: object
            required:
              - error
            properties:
              error:
                $ref: "#/definitions/error"
    delete:
      description: 批量删除机房信息
      summary: deleteDatacenters
      operationId: deleteDatacenters
      tags:
        - datacenter
      parameters:
        - name: uuids
          in: body
          schema:
            type: array
            items:
              type: string
      responses:
        200:
          description: datacenters detele
          schema:
            type: object
            required:
              - result
            properties:
              result:
                type: object
        default:
          description: delete error response
          schema:
            type: object
            required:
              - error
            properties:
              error:
                $ref: "#/definitions/error"
  /v1/datacenter/{uuid}:
    get:
      description: 获取机房详情
      summary: describeDetailDatacenter
      operationId: describeDetailDatacenter
      tags:
        - datacenter
      parameters:
        - name: uuid
          in: path
          required: true
          type: string
          minLength: 0
          maxLength: 32
      responses:
        200:
          description: datacenter detail
          schema:
            type: object
            required:
              - result
            properties:
              result:
                $ref: "#/definitions/describe_detail_datacenter_response"
        default:
          description: generic error response
          schema:
            type: object
            required:
              - error
            properties:
              error:
                $ref: "#/definitions/error"
    put:
      description: 修改机房信息
      summary: updateDatacenter
      operationId: updateDatacenter
      tags:
        - datacenter
      parameters:
        - name: uuid
          in: path
          required: true
          type: string
          minLength: 0
          maxLength: 32
        - name: datacenter
          in: body
          schema:
            required:
              - name
              - level
              - provider
              - province
              - city
              - electrical_spec
            properties:
              name:
                type: string
                minLength: 0
                maxLength: 64
                x-go-custom-tag: db:"name"
              level:
                type: string
                minLength: 0
                maxLength: 64
                x-go-custom-tag: db:"level"
              provider:
                description: 运营商
                type: string
                minLength: 0
                maxLength: 64
                x-go-custom-tag: db:"provider"
              room_count:
                type: integer
                format: int32
                minimum: 0
                maximum: 16777216
                x-go-custom-tag: db:"room_count"
              province:
                type: string
                minLength: 0
                maxLength: 64
                x-go-custom-tag: db:"province"
              city:
                type: string
                minLength: 0
                maxLength: 64
                x-go-custom-tag: db:"city"
              address:
                type: string
                minLength: 0
                maxLength: 128
                x-go-custom-tag: db:"address"
              electrical_spec:
                type: string
                minLength: 0
                maxLength: 64
                x-go-custom-tag: db:"electrical_spec"
      responses:
        200:
          description: datacenter update
          schema:
            type: object
            required:
              - result
            properties:
              result:
                type: object
        default:
          description: update error response
          schema:
            type: object
            required:
              - error
            properties:
              error:
                $ref: "#/definitions/error"
  /v1/datacenter/provider_list:
    get:
      description: 获取运营商list
      summary: describeProvider
      operationId: describeProvider
      tags:
        - datacenter
      responses:
        200:
          description: provider detail
          schema:
            type: object
            required:
              - result
            properties:
              result:
                $ref: "#/definitions/provider_list"
        default:
          description: generic error response
          schema:
            type: object
            required:
              - error
            properties:
              error:
                $ref: "#/definitions/error"
  /v1/datacenter/electrical_spec_list:
    get:
      description: 获取电力规格list
      summary: describeElectricalSpec
      operationId: describeElectricalSpec
      tags:
        - datacenter
      responses:
        200:
          description: electrical_spec detail
          schema:
            type: object
            required:
              - result
            properties:
              result:
                $ref: "#/definitions/electrical_spec_list"
        default:
          description: generic error response
          schema:
            type: object
            required:
              - error
            properties:
              error:
                $ref: "#/definitions/error"
  /v1/datacenter/level_list:
    get:
      description: 获取 机房等级 list
      summary: describeLevel
      operationId: describeLevel
      tags:
        - datacenter
      responses:
        200:
          description: level detail
          schema:
            type: object
            required:
              - result
            properties:
              result:
                $ref: "#/definitions/level_list"
        default:
          description: generic error response
          schema:
            type: object
            required:
              - error
            properties:
              error:
                $ref: "#/definitions/error"
  /v1/eip:
    get:
      description: 获取eip列表
      summary: describeEip
      operationId: describeEip
      tags:
        - eip
      parameters:
        - name: condition
          in: body
          schema:
            properties:
              region_uuid:
                type: string
                minLength: 0
                maxLength: 64
              az_uuid:
                type: string
                minLength: 0
                maxLength: 64
              region_name:
                type: string
                minLength: 0
                maxLength: 64
              az_name:
                type: string
                minLength: 0
                maxLength: 64
              network_segments:
                type: string
                minLength: 0
                maxLength: 1024
              tags_name:
                type: string
                minLength: 0
                maxLength: 64
              tags_value:
                type: string
                minLength: 0
                maxLength: 64
        - name: page_size
          in: query
          required: true
          type: integer
          format: int
          minimum: 1
          maximum: 16777216
        - name: page_no
          in: query
          required: true
          type: integer
          format: int
          minimum: -1
          maximum: 16777216
      responses:
        200:
          description: describe of eips
          schema:
            type: object
            required:
              - result
            properties:
              result:
                $ref: "#/definitions/describe_eip_response"
        default:
          description: describe server  error response
          schema:
            type: object
            required:
              - error
            properties:
              error:
                $ref: "#/definitions/error"
    post:
      description: 新增eip网段
      summary: createEip
      operationId: createEip
      tags:
        - eip
      parameters:
        - name: eip
          in: body
          schema:
            type: array
            items:
              required:
                - network_segment
                - start_ip_address
                - total_ip_count
              properties:
                network_segment:
                  type: string
                  pattern: ^(?:(?:[0-9]|[1-9][0-9]|1[0-9]{2}|2[0-4][0-9]|25[0-5])\.){3}(?:[0-9]|[1-9][0-9]|1[0-9]{2}|2[0-4][0-9]|25[0-5])\/([1-9]|[1-2]\d|3[0-2])$
                  minLength: 0
                  maxLength: 64
                  x-go-custom-tag: db:"network_segment"
                description:
                  type: string
                  minLength: 0
                  maxLength: 64
                  x-go-custom-tag: db:"description"
                start_ip_address:
                  type: string
                  pattern: ^(?:(?:[0-9]|[1-9][0-9]|1[0-9]{2}|2[0-4][0-9]|25[0-5])\.){3}(?:[0-9]|[1-9][0-9]|1[0-9]{2}|2[0-4][0-9]|25[0-5])$
                  minLength: 0
                  maxLength: 64
                  x-go-custom-tag: db:"start_ip_address"
                total_ip_count:
                  description: 总ip数量, 最小值为0, 但不能为空
                  type: integer
                  format: int32
                  minimum: 0
                  maximum: 16777216
                  x-go-custom-tag: db:"total_ip_count"
      responses:
        200:
          description: describe of eips
          schema:
            type: object
            required:
              - result
            properties:
              result:
                type: object
        default:
          description: describe eips  error response
          schema:
            type: object
            required:
              - error
            properties:
              error:
                $ref: "#/definitions/error"
    delete:
      description: 批量删除某一条 eip
      summary: deleteEip
      operationId: deleteEip
      tags:
        - eip
      parameters:
        - name: uuids
          in: body
          schema:
            type: array
            items:
              type: string
      responses:
        200:
          description: describe of server
          schema:
            type: object
            required:
              - result
            properties:
              result:
                type: object
        default:
          description: describe server  error response
          schema:
            type: object
            required:
              - error
            properties:
              error:
                $ref: "#/definitions/error"
  /v1/eip/tag:
    post:
      description: 添加eip tag
      summary: createEipTag
      operationId: createEipTag
      tags:
        - eip_tag
      parameters:
        - name: eip_tag
          in: body
          schema:
            type: array
            items:
              required:
                - eip_uuid
                - name
                - value
              properties:
                eip_uuid:
                  description: eip uuid
                  type: string
                  minLength: 0
                  maxLength: 64
                  x-go-custom-tag: db:"eip_uuid"
                  x-nullable: false
                name:
                  description: name
                  type: string
                  minLength: 0
                  maxLength: 64
                  x-go-custom-tag: db:"name"
                  x-nullable: false
                value:
                  description: value
                  type: string
                  minLength: 0
                  maxLength: 64
                  x-go-custom-tag: db:"value"
                  x-nullable: false
      responses:
        200:
          description: create eip tag ok
          schema:
            type: object
            required:
              - result
            properties:
              result:
                type: object
        default:
          description: create eip tag error response
          schema:
            type: object
            required:
              - error
            properties:
              error:
                $ref: "#/definitions/error"
    get:
      description: 获取eip tag
      summary: describeEipTag
      operationId: describeEipTag
      tags:
        - eip_tag
      parameters:
        - name: eip_uuid
          in: body
          schema:
            type: array
            items:
              type: string
              minLength: 0
              maxLength: 64
      responses:
        200:
          description: describe of eip tag
          schema:
            type: object
            required:
              - result
            properties:
              result:
                $ref: "#/definitions/eip_tags"
        default:
          description: describe eip tag  error response
          schema:
            type: object
            required:
              - error
            properties:
              error:
                $ref: "#/definitions/error"
    delete:
      description: 删除 eip tag
      summary: deleteEipTag
      operationId: deleteEipTag
      tags:
        - eip_tag
      parameters:
        - name: eip_uuid
          in: query
          required: true
          type: string
          minLength: 0
          maxLength: 64
        - name: names
          in: body
          schema:
            type: array
            items:
              type: string
      responses:
        200:
          description: delete eip tag
          schema:
            type: object
            required:
              - result
            properties:
              result:
                type: object
        default:
          description: delete eip tag error response
          schema:
            type: object
            required:
              - error
            properties:
              error:
                $ref: "#/definitions/error"
  /v1/eip/{uuid}:
    put:
      description: 修改eip信息
      summary: updateEip
      operationId: updateEip
      tags:
        - eip
      parameters:
        - name: uuid
          in: path
          required: true
          type: string
          minLength: 0
          maxLength: 32
        - name: eip
          in: body
          schema:
            required:
              - network_segment
              - start_ip_address
              - total_ip_count
            properties:
              descriptions:
                description: 描述
                type: string
                minLength: 0
                maxLength: 64
                x-go-custom-tag: db:"description"
              network_segment:
                description: 网段
                type: string
                pattern: ^(?:(?:[0-9]|[1-9][0-9]|1[0-9]{2}|2[0-4][0-9]|25[0-5])\.){3}(?:[0-9]|[1-9][0-9]|1[0-9]{2}|2[0-4][0-9]|25[0-5])\/([1-9]|[1-2]\d|3[0-2])$
                minLength: 0
                maxLength: 64
                x-go-custom-tag: db:"network_segment"
              start_ip_address:
                description: 网段起始地址
                type: string
                pattern: ^(?:(?:[0-9]|[1-9][0-9]|1[0-9]{2}|2[0-4][0-9]|25[0-5])\.){3}(?:[0-9]|[1-9][0-9]|1[0-9]{2}|2[0-4][0-9]|25[0-5])$
                minLength: 0
                maxLength: 64
                x-go-custom-tag: db:"start_ip_address"
              total_ip_count:
                description: 总ip数量, 最小值为0
                type: integer
                format: int32
                minimum: 0
                maximum: 16777216
                x-go-custom-tag: db:"total_ip_count"
      responses:
        200:
          description: update of eips
          schema:
            type: object
            required:
              - result
            properties:
              result:
                type: object
        default:
          description: update eip  error response
          schema:
            type: object
            required:
              - error
            properties:
              error:
                $ref: "#/definitions/error"
  /v1/eip_used_record:
    delete:
      description: 批量删除 eip_used_record
      summary: deleteEipUsedRecord
      operationId: deleteEipUsedRecord
      tags:
        - eip_used_record
      parameters:
        - name: uuids
          in: body
          schema:
            type: array
            items:
              type: string
      responses:
        200:
          description: deleteEipUsedRecord success
          schema:
            type: object
            required:
              - result
            properties:
              result:
                type: object
        default:
          description: deleteEipUsedRecord error response
          schema:
            type: object
            required:
              - error
            properties:
              error:
                $ref: "#/definitions/error"
    post:
      description: 批量新增eip_used_record网段
      summary: createEipUsedRecord
      operationId: createEipUsedRecord
      tags:
        - eip_used_record
      parameters:
        - name: eip_used_record
          in: body
          schema:
            type: array
            items:
              required:
                - eip_uuid
                - start_ip_address
                - count
              properties:
                eip_uuid:
                  description: eip 的 uuid
                  type: string
                  minLength: 0
                  maxLength: 64
                  x-go-custom-tag: db:"eip_uuid"
                start_ip_address:
                  type: string
                  pattern: ^(?:(?:[0-9]|[1-9][0-9]|1[0-9]{2}|2[0-4][0-9]|25[0-5])\.){3}(?:[0-9]|[1-9][0-9]|1[0-9]{2}|2[0-4][0-9]|25[0-5])$
                  minLength: 0
                  maxLength: 64
                  x-go-custom-tag: db:"start_ip_address"
                count:
                  description: 占用ip数量，最小值为0
                  type: integer
                  format: int32
                  minimum: 0
                  maximum: 16777216
                  x-go-custom-tag: db:"count"
                reason:
                  description: 占用原因
                  type: string
                  minLength: 0
                  maxLength: 64
                  x-go-custom-tag: db:"reason"
                  x-nullable: true
                start_time:
                  description: 占用时间
                  type: string
                  format: dateTime
                  x-go-custom-tag: db:"start_time"
                  x-nullable: true
      responses:
        200:
          description: create of eip_used_records
          schema:
            type: object
            required:
              - result
            properties:
              result:
                type: object
        default:
          description: create eip_used_records  error response
          schema:
            type: object
            required:
              - error
            properties:
              error:
                $ref: "#/definitions/error"
    get:
      description: 获取eip对应的eip_used_record列表
      summary: describeEipUsedRecord
      operationId: describeEipUsedRecord
      tags:
        - eip_used_record
      parameters:
        - name: eip_uuid
          in: query
          required: true
          type: string
          minLength: 0
          maxLength: 32
        - name: page_size
          in: query
          required: true
          type: integer
          format: int
          minimum: 1
          maximum: 16777216
        - name: page_no
          in: query
          required: true
          type: integer
          format: int
          minimum: -1
          maximum: 16777216
      responses:
        200:
          description: describe eip_used_record by eip_uuid
          schema:
            type: object
            required:
              - result
            properties:
              result:
                $ref: "#/definitions/describe_eip_used_record_response"
        default:
          description: describe eip_used_record by eip_uuid error response
          schema:
            type: object
            required:
              - error
            properties:
              error:
                $ref: "#/definitions/error"
    put:
      description: 退还 eip_used
      summary: sendBackEip
      operationId: sendBackEip
      tags:
        - eip_used_record
      parameters:
        - name: uuid
          in: query
          required: true
          type: string
          minLength: 0
          maxLength: 32
      responses:
        200:
          description: sendback of eip_used_record
          schema:
            type: object
            required:
              - result
            properties:
              result:
                type: object
        default:
          description: sendback of eip_used_record error response
          schema:
            type: object
            required:
              - error
            properties:
              error:
                $ref: "#/definitions/error"
  /v1/maintainer:
    delete:
      description: 删除机房人员信息
      summary: deleteMaintainer
      operationId: deleteMaintainer
      tags:
        - maintainer
      parameters:
        - name: uuids
          in: body
          schema:
            type: array
            items:
              required:
                - datacenter_uuid
                - maintainer_uuid
              properties:
                datacenter_uuid:
                  type: string
                  minLength: 0
                  maxLength: 32
                  x-go-custom-tag: db:"datacenter_uuid"
                maintainer_uuid:
                  type: string
                  minLength: 0
                  maxLength: 32
                  x-go-custom-tag: db:"maintainer_uuid"
      responses:
        200:
          description: list of maintainer
          schema:
            type: object
            required:
              - result
            properties:
              result:
                type: object
        default:
          description: create error response
          schema:
            type: object
            required:
              - error
            properties:
              error:
                $ref: "#/definitions/error"
    get:
      description: 获取机房人员信息, 可获取全部 pageno=-1, 也可以根据 datacenter_uuid 查询 该机房下的 运维人信息
      summary: describeMaintainers
      operationId: describeMaintainers
      tags:
        - maintainer
      parameters:
        - name: datacenter_uuid
          in: query
          required: false
          type: string
          minLength: 0
          maxLength: 32
        - name: page_size
          in: query
          required: true
          type: integer
          format: int
          minimum: 1
          maximum: 16777216
        - name: page_no
          in: query
          required: true
          type: integer
          format: int
          minimum: -1
          maximum: 16777216
      responses:
        200:
          description: list of maintainers
          schema:
            type: object
            required:
              - result
            properties:
              result:
                type: object
                properties:
                  maintainers:
                    type: array
                    items:
                      $ref: "#/definitions/maintainer"
                  total_count:
                    description: total count
                    type: integer
                    format: int32
                    minimum: 0
                    maximum: 16777216
                    x-go-custom-tag: db:"total_count"
                    x-nullable: false
        default:
          description: create error response
          schema:
            type: object
            required:
              - error
            properties:
              error:
                $ref: "#/definitions/error"
    post:
      description: 新建机房人员信息
      summary: createMaintainers
      operationId: createMaintainers
      tags:
        - maintainer
      parameters:
        - name: maintainer
          in: body
          schema:
            type: array
            items:
              required:
                - datacenter_uuid
                - name
                - phone
                - position
              properties:
                datacenter_uuid:
                  type: string
                  minLength: 0
                  maxLength: 64
                  x-go-custom-tag: db:"datacenter_uuid"
                name:
                  type: string
                  minLength: 0
                  maxLength: 64
                  x-go-custom-tag: db:"name"
                position:
                  type: string
                  minLength: 0
                  maxLength: 64
                  x-go-custom-tag: db:"position"
                phone:
                  type: string
                  pattern: ^1[3-9]\d{9}$
                  minLength: 0
                  maxLength: 64
                  x-go-custom-tag: db:"phone"
                notes:
                  type: string
                  minLength: 0
                  maxLength: 64
                  x-go-custom-tag: db:"notes"
      responses:
        200:
          description: create of maintainer
          schema:
            type: object
            required:
              - result
            properties:
              result:
                type: object
        default:
          description: create error response
          schema:
            type: object
            required:
              - error
            properties:
              error:
                $ref: "#/definitions/error"
  /v1/maintainer/{uuid}:
    post:
      description: 新建机房人员信息
      summary: createMaintainerByDatacenterId
      operationId: createMaintainerByDatacenterId
      tags:
        - maintainer
      parameters:
        - name: uuid
          description: 机房id
          in: path
          required: true
          type: string
          minLength: 0
          maxLength: 32
        - name: maintainer
          in: body
          schema:
            type: array
            items:
              required:
                - name
                - phone
                - position
              properties:
                name:
                  type: string
                  minLength: 0
                  maxLength: 64
                  x-go-custom-tag: db:"name"
                position:
                  type: string
                  minLength: 0
                  maxLength: 64
                  x-go-custom-tag: db:"position"
                phone:
                  type: string
                  pattern: ^1[3-9]\d{9}$
                  minLength: 0
                  maxLength: 64
                  x-go-custom-tag: db:"phone"
                notes:
                  type: string
                  minLength: 0
                  maxLength: 64
                  x-go-custom-tag: db:"notes"
      responses:
        200:
          description: create of maintainer
          schema:
            type: object
            required:
              - result
            properties:
              result:
                type: object
        default:
          description: create error response
          schema:
            type: object
            required:
              - error
            properties:
              error:
                $ref: "#/definitions/error"
    put:
      description: 编辑机房人员信息
      summary: updateMaintainer
      operationId: updateMaintainer
      tags:
        - maintainer
      parameters:
        - name: uuid
          in: path
          required: true
          type: string
          minLength: 0
          maxLength: 32
        - name: maintainer
          in: body
          schema:
            required:
              - name
              - phone
              - position
            properties:
              name:
                type: string
                minLength: 0
                maxLength: 64
                x-go-custom-tag: db:"name"
              position:
                type: string
                minLength: 0
                maxLength: 64
                x-go-custom-tag: db:"position"
              phone:
                type: string
                pattern: ^1[3-9]\d{9}$
                minLength: 0
                maxLength: 64
                x-go-custom-tag: db:"phone"
              notes:
                type: string
                minLength: 0
                maxLength: 64
                x-go-custom-tag: db:"notes"
      responses:
        200:
          description: list of maintainer
          schema:
            type: object
            required:
              - result
            properties:
              result:
                type: object
        default:
          description: create error response
          schema:
            type: object
            required:
              - error
            properties:
              error:
                $ref: "#/definitions/error"
  /v1/network_equipment:
    get:
      description: 获取网络设备信息列表
      summary: describeNetworkEquipment
      operationId: describeNetworkEquipment
      tags:
        - network-equipment
      parameters:
        - name: condition
          in: body
          schema:
            properties:
              region_uuid:
                type: string
                minLength: 0
                maxLength: 64
              az_uuid:
                type: string
                minLength: 0
                maxLength: 64
              rack_uuid:
                type: string
                minLength: 0
                maxLength: 64
              region_name:
                type: string
                minLength: 0
                maxLength: 64
              az_name:
                type: string
                minLength: 0
                maxLength: 64
              asset_number:
                type: array
                items:
                  type: string
                  minLength: 0
                  maxLength: 64
              serial_number:
                type: array
                items:
                  type: string
                  minLength: 0
                  maxLength: 64
              serial_number_eq:
                type: array
                items:
                  type: string
                  minLength: 0
                  maxLength: 64
              manage_ip_eq:
                type: string
                minLength: 0
                maxLength: 64
              manage_ip:
                type: array
                items:
                  type: string
                  minLength: 0
                  maxLength: 64
              outofband_ip:
                type: array
                items:
                  type: string
                  minLength: 0
                  maxLength: 64
              way_to_use:
                type: array
                items:
                  type: string
                  minLength: 0
                  maxLength: 64
              asset_state:
                description: 状态id 故障1、维修中2、离线3、在线4
                type: array
                items:
                  type: integer
                  format: int32
                  minimum: 1
                  maximum: 4
              warranty_state:
                description: 维保状态， 0在保，1不在保
                type: integer
                format: int32
                minimum: 0
                maximum: 1
              brand:
                type: array
                items:
                  type: string
                  minLength: 0
                  maxLength: 64
              model:
                type: array
                items:
                  type: string
                  minLength: 0
                  maxLength: 64
              datacenter_name:
                type: array
                items:
                  type: string
                  minLength: 0
                  maxLength: 64
              rack_number:
                type: array
                items:
                  type: string
                  minLength: 0
                  maxLength: 64
              datacenter_name_eq:
                type: array
                items:
                  type: string
                  minLength: 0
                  maxLength: 64
              rack_number_eq:
                type: array
                items:
                  type: string
                  minLength: 0
                  maxLength: 64
        - name: page_size
          in: query
          required: true
          type: integer
          format: int
          minimum: 1
          maximum: 16777216
        - name: page_no
          in: query
          required: true
          type: integer
          format: int
          minimum: -1
          maximum: 16777216
      responses:
        200:
          description: describe of network_equipments
          schema:
            type: object
            required:
              - result
            properties:
              result:
                type: object
                properties:
                  network_equipments:
                    type: array
                    items:
                      $ref: "#/definitions/describe_network_equipment"
                  total_count:
                    description: total count
                    type: integer
                    format: int32
                    minimum: 0
                    maximum: 16777216
                    x-go-custom-tag: db:"total_count"
                    x-nullable: false
        default:
          description: describe network_equipments  error response
          schema:
            type: object
            required:
              - error
            properties:
              error:
                $ref: "#/definitions/error"
    post:
      description: 添加网络设备信息
      summary: createNetworkEquipment
      operationId: createNetworkEquipment
      tags:
        - network-equipment
      parameters:
        - name: create_network_equipment
          in: body
          schema:
            type: array
            items:
              required:
                - serial_number
              properties:
                uuid:
                  description: uuid
                  type: string
                  minLength: 0
                  maxLength: 64
                  x-go-custom-tag: db:"uuid"
                datacenter:
                  description: 机房 name
                  type: string
                  minLength: 0
                  maxLength: 64
                  x-go-custom-tag: db:"datacenter"
                rack_uuid:
                  description: 机架id
                  type: string
                  minLength: 0
                  maxLength: 64
                  x-go-custom-tag: db:"rack_uuid"
                rack_number:
                  description: 机架编号
                  type: string
                  minLength: 0
                  maxLength: 64
                  x-go-custom-tag: db:"rack_number"
                asset_number:
                  type: string
                  minLength: 0
                  maxLength: 64
                  x-go-custom-tag: db:"asset_number"
                serial_number:
                  type: string
                  minLength: 0
                  maxLength: 64
                  x-go-custom-tag: db:"serial_number"
                u_count:
                  type: integer
                  format: int32
                  minimum: 0
                  maximum: 16777216
                  x-go-custom-tag: db:"u_count"
                brand:
                  type: string
                  minLength: 0
                  maxLength: 64
                  x-go-custom-tag: db:"brand"
                model:
                  type: string
                  minLength: 0
                  maxLength: 64
                  x-go-custom-tag: db:"model"
                outofband_ip:
                  type: string
                  pattern: ^(?:(?:[0-9]|[1-9][0-9]|1[0-9]{2}|2[0-4][0-9]|25[0-5])\.){3}(?:[0-9]|[1-9][0-9]|1[0-9]{2}|2[0-4][0-9]|25[0-5])$
                  minLength: 0
                  maxLength: 64
                  x-go-custom-tag: db:"outofband_ip"
                manage_ip:
                  type: string
                  pattern: ^(?:(?:[0-9]|[1-9][0-9]|1[0-9]{2}|2[0-4][0-9]|25[0-5])\.){3}(?:[0-9]|[1-9][0-9]|1[0-9]{2}|2[0-4][0-9]|25[0-5])$
                  minLength: 0
                  maxLength: 64
                  x-go-custom-tag: db:"manage_ip"
                procurement_time:
                  description: 采购时间
                  type: string
                  format: dateTime
                  x-go-custom-tag: db:"procurement_time"
                warranty_start:
                  type: string
                  format: dateTime
                  x-go-custom-tag: db:"warranty_start"
                warranty_end:
                  type: string
                  format: dateTime
                  x-go-custom-tag: db:"warranty_end"
                mc_name:
                  type: string
                  minLength: 0
                  maxLength: 64
                  x-go-custom-tag: db:"mc_name"
                mc_phone:
                  type: string
                  pattern: ^1[3-9]\d{9}$
                  minLength: 0
                  maxLength: 64
                  x-go-custom-tag: db:"mc_phone"
                mc_email:
                  type: string
                  pattern: ^[a-zA-Z0-9_-]+@[a-zA-Z0-9_-]+(\.[a-zA-Z0-9_-]+)+$
                  minLength: 0
                  maxLength: 64
                  x-go-custom-tag: db:"mc_email"
                conn_type:
                  description: 连接方式
                  type: string
                  x-nullable: false
                conn_port:
                  description: 连接端口
                  type: integer
                  x-nullable: false
                conn_user:
                  description: 连接使用的用户
                  type: string
                  x-nullable: false
                conn_pwd:
                  description: 连接使用的密码
                  type: string
                  x-nullable: false
      responses:
        200:
          description: describe of network_equipment
          schema:
            type: object
            properties:
              ids:
                type: array
                items:
                  type: string
        default:
          description: describe network_equipment error response
          schema:
            type: object
            required:
              - error
            properties:
              error:
                $ref: "#/definitions/error"
    delete:
      description: 删除网络设备
      summary: deleteNetworkEquipment
      operationId: deleteNetworkEquipment
      tags:
        - network-equipment
      parameters:
        - name: uuids
          in: body
          schema:
            type: array
            items:
              type: string
      responses:
        200:
          description: deleteNetworkEquipment
          schema:
            type: object
            required:
              - result
            properties:
              result:
                type: object
        default:
          description: deleteNetworkEquipment  error response
          schema:
            type: object
            required:
              - error
            properties:
              error:
                $ref: "#/definitions/error"
  /v1/network_equipment/{uuid}:
    get:
      description: 获取网络设备信息详情
      summary: describeDetailNetworkEquipment
      operationId: describeDetailNetworkEquipment
      tags:
        - network-equipment
      parameters:
        - name: uuid
          in: path
          required: true
          type: string
          minLength: 0
          maxLength: 32
      responses:
        200:
          description: describe detail of NetworkEquipment
          schema:
            type: object
            required:
              - result
            properties:
              result:
                $ref: "#/definitions/describe_detail_network_equipment_response"
        default:
          description: describe detail NetworkEquipment  error response
          schema:
            type: object
            required:
              - error
            properties:
              error:
                $ref: "#/definitions/error"
    put:
      description: 更新网络设备信息
      summary: updateNetworkEquipment
      operationId: updateNetworkEquipment
      tags:
        - network-equipment
      parameters:
        - name: uuid
          in: path
          required: true
          type: string
          minLength: 0
          maxLength: 32
        - name: update_network_equipment
          in: body
          schema:
            required:
              - asset_number
              - serial_number
              - datacenter_uuid
              - rack_uuid
              - u_count
              - warranty_start
              - warranty_end
              - procurement_time
            properties:
              datacenter_uuid:
                type: string
                minLength: 0
                maxLength: 64
                x-go-custom-tag: db:"datacenter_uuid"
              rack_uuid:
                type: string
                minLength: 0
                maxLength: 64
                x-go-custom-tag: db:"rack_uuid"
              asset_number:
                type: string
                minLength: 0
                maxLength: 64
                x-go-custom-tag: db:"asset_number"
              serial_number:
                type: string
                minLength: 0
                maxLength: 64
                x-go-custom-tag: db:"serial_number"
              u_count:
                type: integer
                format: int32
                minimum: 0
                maximum: 16777216
                x-go-custom-tag: db:"u_count"
              brand:
                type: string
                minLength: 0
                maxLength: 64
                x-go-custom-tag: db:"brand"
              model:
                type: string
                minLength: 0
                maxLength: 64
                x-go-custom-tag: db:"model"
              outofband_ip:
                type: string
                pattern: ^(?:(?:[0-9]|[1-9][0-9]|1[0-9]{2}|2[0-4][0-9]|25[0-5])\.){3}(?:[0-9]|[1-9][0-9]|1[0-9]{2}|2[0-4][0-9]|25[0-5])$
                minLength: 0
                maxLength: 64
                x-go-custom-tag: db:"outofband_ip"
              manage_ip:
                type: string
                pattern: ^(?:(?:[0-9]|[1-9][0-9]|1[0-9]{2}|2[0-4][0-9]|25[0-5])\.){3}(?:[0-9]|[1-9][0-9]|1[0-9]{2}|2[0-4][0-9]|25[0-5])$
                minLength: 0
                maxLength: 64
                x-go-custom-tag: db:"manage_ip"
              warranty_start:
                type: string
                format: dateTime
                x-go-custom-tag: db:"warranty_start"
              warranty_end:
                type: string
                format: dateTime
                x-go-custom-tag: db:"warranty_end"
              procurement_time:
                description: 采购时间
                type: string
                format: dateTime
                x-go-custom-tag: db:"procurement_time"
              mc_name:
                type: string
                minLength: 0
                maxLength: 64
                x-go-custom-tag: db:"mc_name"
              mc_phone:
                type: string
                pattern: ^1[3-9]\d{9}$
                minLength: 0
                maxLength: 64
                x-go-custom-tag: db:"mc_phone"
              mc_email:
                type: string
                pattern: ^[a-zA-Z0-9_-]+@[a-zA-Z0-9_-]+(\.[a-zA-Z0-9_-]+)+$
                minLength: 0
                maxLength: 64
                x-go-custom-tag: db:"mc_email"
              conn_type:
                description: 连接方式
                type: string
                x-nullable: false
              conn_port:
                description: 连接端口
                type: integer
                x-nullable: false
              conn_user:
                description: 连接使用的用户
                type: string
                x-nullable: false
              conn_pwd:
                description: 连接使用的密码
                type: string
                x-nullable: false
      responses:
        200:
          description: updateNetworkEquipment
          schema:
            type: object
            required:
              - result
            properties:
              result:
                type: object
        default:
          description: updateNetworkEquipment error response
          schema:
            type: object
            required:
              - error
            properties:
              error:
                $ref: "#/definitions/error"
  /v1/network_equipment/brand_model:
    get:
      description: 获取全部现存网络设备的品牌brand与型号modellist
      summary: describeNetworkEquipmentBrandAndModel
      operationId: describeNetworkEquipmentBrandAndModel
      tags:
        - network-equipment
      responses:
        200:
          description: describe network equipment brand model
          schema:
            type: object
            required:
              - result
            properties:
              result:
                $ref: "#/definitions/brand_model"
        default:
          description: describe network equipment brand model
          schema:
            type: object
            required:
              - error
            properties:
              error:
                $ref: "#/definitions/error"
  /v1/os_package:
    get:
      description: 获取os_package列表
      summary: describeOsPackage
      operationId: describeOsPackage
      tags:
        - os_package
      parameters:
        - name: page_size
          in: query
          required: true
          type: integer
          format: int
          minimum: 1
          maximum: 16777216
        - name: page_no
          in: query
          required: true
          type: integer
          format: int
          minimum: -1
          maximum: 16777216
      responses:
        200:
          description: describe of os_package
          schema:
            type: object
            required:
              - result
            properties:
              result:
                $ref: "#/definitions/os_packages_response"
        default:
          description: describe os_package  error response
          schema:
            type: object
            required:
              - error
            properties:
              error:
                $ref: "#/definitions/error"
    post:
      description: 新增 os package
      summary: createOsPackage
      operationId: createOsPackage
      tags:
        - os_package
      parameters:
        - name: os_package
          in: body
          schema:
            type: array
            items:
              required:
                - package_name
              properties:
                package_name:
                  description: name
                  type: string
                  minLength: 0
                  maxLength: 64
                  x-go-custom-tag: db:"package_name"
                package_desc:
                  description: 版本
                  type: string
                  minLength: 0
                  maxLength: 64
                  x-go-custom-tag: db:"package_desc"
      responses:
        200:
          description: createOsPackage
          schema:
            type: object
            required:
              - result
            properties:
              result:
                type: object
        default:
          description: createOsPackage error response
          schema:
            type: object
            required:
              - error
            properties:
              error:
                $ref: "#/definitions/error"
    delete:
      description: 批量删除某一条 os_package
      summary: deleteOsPackages
      operationId: deleteOsPackages
      tags:
        - os_package
      parameters:
        - name: uuids
          in: body
          schema:
            type: array
            items:
              type: string
      responses:
        200:
          description: delete of os_package
          schema:
            type: object
            required:
              - result
            properties:
              result:
                type: object
        default:
          description: delete of os_package error response
          schema:
            type: object
            required:
              - error
            properties:
              error:
                $ref: "#/definitions/error"
  /v1/os_package/{uuid}:
    put:
      description: 修改os_package信息
      summary: updateOsPackage
      operationId: updateOsPackage
      tags:
        - os_package
      parameters:
        - name: uuid
          in: path
          required: true
          type: string
          minLength: 0
          maxLength: 32
        - name: os_package
          in: body
          schema:
            required:
              - package_name
            properties:
              package_name:
                description: name
                type: string
                minLength: 0
                maxLength: 64
                x-go-custom-tag: db:"package_name"
              package_desc:
                description: 版本
                type: string
                minLength: 0
                maxLength: 64
                x-go-custom-tag: db:"package_desc"
      responses:
        200:
          description: updateOsPackage
          schema:
            type: object
            required:
              - result
            properties:
              result:
                type: object
        default:
          description: updateOsPackage error response
          schema:
            type: object
            required:
              - error
            properties:
              error:
                $ref: "#/definitions/error"
    get:
      description: 获取 套餐 详细信息, 通过 uuis
      summary: describeDetailOsPackage
      operationId: describeDetailOsPackage
      tags:
        - os_package
      parameters:
        - name: uuid
          in: path
          required: true
          type: string
          minLength: 0
          maxLength: 32
      responses:
        200:
          description: describeDetailOsPackage
          schema:
            type: object
            required:
              - result
            properties:
              result:
                $ref: "#/definitions/os_package_response"
        default:
          description: describeDetailOsPackage error response
          schema:
            type: object
            required:
              - error
            properties:
              error:
                $ref: "#/definitions/error"
  /v1/rack:
    get:
      description: 获取机架信息列表
      summary: describeRack
      operationId: describeRack
      tags:
        - rack
      parameters:
        - name: condition
          in: body
          schema:
            properties:
              region_uuid:
                type: string
                minLength: 0
                maxLength: 64
              az_uuid:
                type: string
                minLength: 0
                maxLength: 64
              region_name:
                type: string
                minLength: 0
                maxLength: 64
              az_name:
                type: string
                minLength: 0
                maxLength: 64
              rack_number:
                type: array
                items:
                  type: string
                  minLength: 0
                  maxLength: 64
              rack_number_eq:
                type: string
                minLength: 0
                maxLength: 64
              datacenter:
                type: array
                items:
                  type: string
                  minLength: 0
                  maxLength: 64
              electrical_spec:
                type: array
                items:
                  type: string
                  minLength: 0
                  maxLength: 64
              u_count:
                type: array
                items:
                  type: integer
                  format: int32
                  minimum: 0
                  maximum: 16777216
              room_number:
                type: array
                items:
                  type: string
                  minLength: 0
                  maxLength: 64
              network:
                type: array
                items:
                  type: string
                  minLength: 0
                  maxLength: 64
              network_mask:
                type: array
                items:
                  type: string
                  minLength: 0
                  maxLength: 64
              tor_ip:
                type: array
                items:
                  type: string
                  minLength: 0
                  maxLength: 64
              manage_ip:
                description: 管理ip
                type: string
                minLength: 0
                maxLength: 64
              system_ip:
                description: 系统ip
                type: string
                minLength: 0
                maxLength: 64
        - name: page_size
          in: query
          required: true
          type: integer
          format: int
          minimum: 1
          maximum: 16777216
        - name: page_no
          in: query
          required: true
          type: integer
          format: int
          minimum: -1
          maximum: 16777216
      responses:
        200:
          description: list of racks
          schema:
            type: object
            required:
              - result
            properties:
              result:
                $ref: "#/definitions/describe_rack_response"
        default:
          description: error response
          schema:
            type: object
            required:
              - error
            properties:
              error:
                $ref: "#/definitions/error"
    post:
      description: 添加机架信息
      summary: createRack
      operationId: createRack
      tags:
        - rack
      parameters:
        - name: rack
          in: body
          schema:
            type: array
            items:
              properties:
                uuid:
                  type: string
                  minLength: 0
                  maxLength: 64
                  pattern: ^([A-Za-z0-9-_]{0,64})$
                  x-go-custom-tag: db:"uuid"
                datacenter_uuid:
                  type: string
                  minLength: 0
                  maxLength: 64
                  pattern: ^([A-Za-z0-9-_]{0,64})$
                  x-go-custom-tag: db:"datacenter_uuid"
                datacenter_name:
                  type: string
                  minLength: 0
                  maxLength: 32
                  x-go-custom-tag: db:"datacenter_name"
                rack_number:
                  type: string
                  minLength: 0
                  maxLength: 64
                  x-go-custom-tag: db:"rack_number"
                electrical_spec:
                  type: string
                  minLength: 0
                  maxLength: 64
                  x-go-custom-tag: db:"electrical_spec"
                u_count:
                  type: integer
                  format: int32
                  minimum: 0
                  maximum: 16777216
                  x-go-custom-tag: db:"u_count"
                room_number:
                  type: string
                  minLength: 0
                  maxLength: 64
                  x-go-custom-tag: db:"room_number"
                network:
                  type: string
                  pattern: ^(?:(?:[0-9]|[1-9][0-9]|1[0-9]{2}|2[0-4][0-9]|25[0-5])\.){3}(?:[0-9]|[1-9][0-9]|1[0-9]{2}|2[0-4][0-9]|25[0-5])$
                  minLength: 0
                  maxLength: 64
                  x-go-custom-tag: db:"network"
                network_mask:
                  type: string
                  pattern: ^(?:(?:[0-9]|[1-9][0-9]|1[0-9]{2}|2[0-4][0-9]|25[0-5])\.){3}(?:[0-9]|[1-9][0-9]|1[0-9]{2}|2[0-4][0-9]|25[0-5])$
                  minLength: 0
                  maxLength: 64
                  x-go-custom-tag: db:"network_mask"
                tor_ip:
                  type: string
                  pattern: ^(?:(?:[0-9]|[1-9][0-9]|1[0-9]{2}|2[0-4][0-9]|25[0-5])\.){3}(?:[0-9]|[1-9][0-9]|1[0-9]{2}|2[0-4][0-9]|25[0-5])$
                  minLength: 0
                  maxLength: 64
                  x-go-custom-tag: db:"tor_ip"
                az_uuid:
                  description: 所属az_uuid
                  type: string
                  minLength: 0
                  maxLength: 64
                  pattern: ^([A-Za-z0-9-_]{1,64})$
                  x-go-custom-tag: db:"az_uuid"
      responses:
        200:
          description: create racks
          schema:
            type: object
            properties:
              ids:
                type: array
                items:
                  type: string
        default:
          description: create error response
          schema:
            type: object
            required:
              - error
            properties:
              error:
                $ref: "#/definitions/error"
    delete:
      description: 批量删除机架信息
      summary: deleteRack
      operationId: deleteRack
      tags:
        - rack
      parameters:
        - name: uuids
          in: body
          schema:
            type: array
            items:
              type: string
      responses:
        200:
          description: delete racks
          schema:
            type: object
            required:
              - result
            properties:
              result:
                type: object
        default:
          description: delete error response
          schema:
            type: object
            required:
              - error
            properties:
              error:
                $ref: "#/definitions/error"
  /v1/rack/{uuid}:
    get:
      description: 获取机架详细信息
      summary: describeDetailRack
      operationId: describeDetailRack
      tags:
        - rack
      parameters:
        - name: uuid
          in: path
          required: true
          type: string
          minLength: 0
          maxLength: 32
      responses:
        200:
          description: detail of rack
          schema:
            type: object
            required:
              - result
            properties:
              result:
                $ref: "#/definitions/describe_detail_rack_response"
        default:
          description: detail error response
          schema:
            type: object
            required:
              - error
            properties:
              error:
                $ref: "#/definitions/error"
    put:
      description: 修改机架信息
      summary: updateRack
      operationId: updateRack
      tags:
        - rack
      parameters:
        - name: uuid
          in: path
          required: true
          type: string
          minLength: 0
          maxLength: 32
        - name: rack
          in: body
          schema:
            required:
              - datacenter_uuid
              - rack_number
              - u_count
              - network
              - network_mask
            properties:
              datacenter_uuid:
                type: string
                minLength: 0
                maxLength: 32
                pattern: ^([A-Za-z0-9-_]{1,64})$
                x-go-custom-tag: db:"datacenter_uuid"
              rack_number:
                type: string
                minLength: 0
                maxLength: 64
                x-go-custom-tag: db:"rack_number"
              electrical_spec:
                type: string
                minLength: 0
                maxLength: 64
                x-go-custom-tag: db:"electrical_spec"
              u_count:
                type: integer
                format: int32
                minimum: 0
                maximum: 16777216
                x-go-custom-tag: db:"u_count"
              room_number:
                type: string
                minLength: 0
                maxLength: 64
                x-go-custom-tag: db:"room_number"
              network:
                type: string
                pattern: ^(?:(?:[0-9]|[1-9][0-9]|1[0-9]{2}|2[0-4][0-9]|25[0-5])\.){3}(?:[0-9]|[1-9][0-9]|1[0-9]{2}|2[0-4][0-9]|25[0-5])$
                minLength: 0
                maxLength: 64
                x-go-custom-tag: db:"network"
              network_mask:
                type: string
                pattern: ^(?:(?:[0-9]|[1-9][0-9]|1[0-9]{2}|2[0-4][0-9]|25[0-5])\.){3}(?:[0-9]|[1-9][0-9]|1[0-9]{2}|2[0-4][0-9]|25[0-5])$
                minLength: 0
                maxLength: 64
                x-go-custom-tag: db:"network_mask"
              tor_ip:
                type: string
                pattern: ^(?:(?:[0-9]|[1-9][0-9]|1[0-9]{2}|2[0-4][0-9]|25[0-5])\.){3}(?:[0-9]|[1-9][0-9]|1[0-9]{2}|2[0-4][0-9]|25[0-5])$
                minLength: 0
                maxLength: 64
                x-go-custom-tag: db:"tor_ip"
      responses:
        200:
          description: update of rack
          schema:
            type: object
            required:
              - result
            properties:
              result:
                type: object
        default:
          description: update error response
          schema:
            type: object
            required:
              - error
            properties:
              error:
                $ref: "#/definitions/error"
  /v1/rack/electrical_spec_list:
    get:
      description: 获取电力规格list
      summary: describeRackElectricalSpec
      operationId: describeRackElectricalSpec
      tags:
        - rack
      responses:
        200:
          description: electrical_spec detail
          schema:
            type: object
            required:
              - result
            properties:
              result:
                $ref: "#/definitions/electrical_spec_list"
        default:
          description: generic error response
          schema:
            type: object
            required:
              - error
            properties:
              error:
                $ref: "#/definitions/error"
  /v1/server:
    get:
      description: 获取服务器信息列表
      summary: describeServer
      operationId: describeServer
      tags:
        - server
      parameters:
        - name: condition
          in: body
          schema:
            properties:
              asset_number:
                type: array
                items:
                  type: string
                  minLength: 0
                  maxLength: 64
              serial_number:
                type: array
                items:
                  type: string
                  minLength: 0
                  maxLength: 64
              serial_number_eq:
                type: array
                items:
                  type: string
                  minLength: 0
                  maxLength: 64
              system_ip:
                type: array
                items:
                  type: string
                  minLength: 0
                  maxLength: 64
              system_ip_eq:
                type: array
                items:
                  type: string
                  minLength: 0
                  maxLength: 64
              manage_ip:
                type: array
                items:
                  type: string
                  minLength: 0
                  maxLength: 64
              data_ip:
                type: array
                items:
                  type: string
                  minLength: 0
                  maxLength: 64
              server_id:
                type: array
                items:
                  type: string
                  minLength: 0
                  maxLength: 64
              smartnic_man_ip:
                type: array
                items:
                  type: string
                  minLength: 0
                  maxLength: 64
              smartnic_data_ip:
                type: array
                items:
                  type: string
                  minLength: 0
                  maxLength: 64
              datacenter_name:
                type: array
                items:
                  type: string
                  minLength: 0
                  maxLength: 64
              rack_number:
                type: array
                items:
                  type: string
                  minLength: 0
                  maxLength: 64
              datacenter_name_eq:
                type: array
                items:
                  type: string
                  minLength: 0
                  maxLength: 64
              rack_number_eq:
                type: array
                items:
                  type: string
                  minLength: 0
                  maxLength: 64
              region_uuid:
                type: string
                minLength: 0
                maxLength: 64
              az_uuid:
                type: string
                minLength: 0
                maxLength: 64
              region_name:
                type: string
                minLength: 0
                maxLength: 64
              az_name:
                type: string
                minLength: 0
                maxLength: 64
              brand:
                type: array
                items:
                  type: string
                  minLength: 0
                  maxLength: 64
              model:
                type: array
                items:
                  type: string
                  minLength: 0
                  maxLength: 64
              asset_state:
                description: 服务器状态 故障1、维修中2、离线3、在线4
                type: integer
                minimum: 0
                maximum: 10
              warranty_state:
                description: 维保状态,0在保，1不在保
                type: integer
                minimum: 0
                maximum: 1
              tags_name:
                type: string
                minLength: 0
                maxLength: 64
              tags_value:
                type: string
                minLength: 0
                maxLength: 64
              server_role:
                type: object
                properties:
                  role:
                    type: array
                    items:
                      type: string
                      minLength: 0
                      maxLength: 64
                  metadata_key:
                    type: array
                    items:
                      type: string
                      minLength: 0
                      maxLength: 64
                  metadata_value:
                    type: array
                    items:
                      type: string
                      minLength: 0
                      maxLength: 64
              arbitration_az:
                description: |- 
                  过滤逻辑仲裁az的服务器.false-过滤非仲裁Az的服务器.true-过滤属于仲裁az的服务器
                  默认为空,都展示
                type: boolean
                default: false
              server_category:
                description: |- 
                  服务器分类.1-内部机器,2-外部机器(公有云),3-查询全部.
                  默认值:
                  (1)当查询中,没有其他过滤条件时,默认查询内部机器<兼容之前接口>,为1
                  (2)当查询中,存在其他过滤条件时,默认查询全部,为3
                type: integer
                format: int32
                minimum: 1
                maximum: 3
                x-nullable: true
        - name: page_size
          in: query
          required: true
          type: integer
          format: int
          minimum: 1
          maximum: 16777216
        - name: page_no
          in: query
          required: true
          type: integer
          format: int
          minimum: -1
          maximum: 16777216
      responses:
        200:
          description: describe of server
          schema:
            type: object
            required:
              - result
            properties:
              result:
                $ref: "#/definitions/describe_server_response"
        default:
          description: describe server  error response
          schema:
            type: object
            required:
              - error
            properties:
              error:
                $ref: "#/definitions/error"
    post:
      description: 添加服务器信息
      summary: createServer
      operationId: createServer
      tags:
        - server
      parameters:
        - name: server
          in: body
          schema:
            type: array
            items:
              properties:
                uuid:
                  type: string
                  minLength: 0
                  maxLength: 64
                  x-go-custom-tag: db:"uuid"
                host_name:
                  description: 机器name
                  type: string
                  minLength: 0
                  maxLength: 64
                  x-go-custom-tag: db:"host_name"
                datacenter_name:
                  description: 机房name
                  type: string
                  minLength: 0
                  maxLength: 64
                  x-go-custom-tag: db:"datacenter_name"
                rack_uuid:
                  description: 机架id
                  type: string
                  minLength: 0
                  maxLength: 64
                  x-go-custom-tag: db:"rack_uuid"
                rack_number:
                  description: 机架编号
                  type: string
                  minLength: 0
                  maxLength: 64
                  x-go-custom-tag: db:"rack_number"
                server_package_uuid:
                  description: 服务器配置uuid
                  type: string
                  minLength: 0
                  maxLength: 32
                  x-go-custom-tag: db:"server_package_uuid"
                os_package_name:
                  description: osPackageName
                  type: string
                  minLength: 0
                  maxLength: 64
                  x-go-custom-tag: db:"os_package_name"
                asset_number:
                  description: 资产编号
                  type: string
                  minLength: 0
                  maxLength: 64
                  x-go-custom-tag: db:"asset_number"
                serial_number:
                  description: 序列号
                  type: string
                  minLength: 0
                  maxLength: 64
                  x-go-custom-tag: db:"serial_number"
                brand:
                  description: 品牌
                  type: string
                  minLength: 0
                  maxLength: 64
                  x-go-custom-tag: db:"brand"
                model:
                  description: 型号
                  type: string
                  minLength: 0
                  maxLength: 64
                  x-go-custom-tag: db:"brand"
                mc_name:
                  description: 厂商联系人姓名
                  type: string
                  minLength: 0
                  maxLength: 64
                  x-go-custom-tag: db:"mc_name"
                mc_phone:
                  description: 厂商联系人电话
                  type: string
                  pattern: ^1[3-9]\d{9}$
                  minLength: 0
                  maxLength: 64
                  x-go-custom-tag: db:"mc_phone"
                mc_email:
                  description: 厂商联系人邮件
                  type: string
                  pattern: ^[a-zA-Z0-9_-]+@[a-zA-Z0-9_-]+(\.[a-zA-Z0-9_-]+)+$
                  minLength: 0
                  maxLength: 64
                  x-go-custom-tag: db:"mc_email"
                system_ip:
                  description: 系统ip
                  type: string
                  pattern: ^(?:(?:[0-9]|[1-9][0-9]|1[0-9]{2}|2[0-4][0-9]|25[0-5])\.){3}(?:[0-9]|[1-9][0-9]|1[0-9]{2}|2[0-4][0-9]|25[0-5])$
                  minLength: 0
                  maxLength: 64
                  x-go-custom-tag: db:"system_ip"
                system_ip_mask:
                  description: 系统ip MASK
                  type: string
                  pattern: ^(?:(?:[0-9]|[1-9][0-9]|1[0-9]{2}|2[0-4][0-9]|25[0-5])\.){3}(?:[0-9]|[1-9][0-9]|1[0-9]{2}|2[0-4][0-9]|25[0-5])$
                  minLength: 0
                  maxLength: 64
                  x-go-custom-tag: db:"system_ip_mask"
                system_ip_mac:
                  description: 系统IP MAC
                  type: string
                  pattern: ^[A-Fa-f0-9]{2}(:[A-Fa-f0-9]{2}){5}$
                  minLength: 0
                  maxLength: 64
                  x-go-custom-tag: db:"system_ip_mac"
                  x-nullable: true
                manage_ip:
                  description: 管理ip
                  type: string
                  pattern: ^(?:(?:[0-9]|[1-9][0-9]|1[0-9]{2}|2[0-4][0-9]|25[0-5])\.){3}(?:[0-9]|[1-9][0-9]|1[0-9]{2}|2[0-4][0-9]|25[0-5])$
                  minLength: 0
                  maxLength: 64
                  x-go-custom-tag: db:"manage_ip"
                data_ip:
                  description: 数据ip
                  type: string
                  pattern: ^(?:(?:[0-9]|[1-9][0-9]|1[0-9]{2}|2[0-4][0-9]|25[0-5])\.){3}(?:[0-9]|[1-9][0-9]|1[0-9]{2}|2[0-4][0-9]|25[0-5])$
                  minLength: 0
                  maxLength: 64
                  x-go-custom-tag: db:"data_ip"
                data_ip_mask:
                  description: 数据IP MASK
                  type: string
                  pattern: ^(?:(?:[0-9]|[1-9][0-9]|1[0-9]{2}|2[0-4][0-9]|25[0-5])\.){3}(?:[0-9]|[1-9][0-9]|1[0-9]{2}|2[0-4][0-9]|25[0-5])$
                  minLength: 0
                  maxLength: 64
                  x-go-custom-tag: db:"data_ip_mask"
                  x-nullable: true
                smartnic_man_ip:
                  description: SmartNic 管理ip
                  type: string
                  pattern: ^(?:(?:[0-9]|[1-9][0-9]|1[0-9]{2}|2[0-4][0-9]|25[0-5])\.){3}(?:[0-9]|[1-9][0-9]|1[0-9]{2}|2[0-4][0-9]|25[0-5])$
                  minLength: 0
                  maxLength: 64
                  x-go-custom-tag: db:"smartnic_man_ip"
                smartnic_man_ip_gateway:
                  description: SmartNic 管理ip网关
                  type: string
                  minLength: 0
                  maxLength: 64
                  x-go-custom-tag: db:"smartnic_man_ip_gateway"
                smartnic_data_ip:
                  description: SmartNic 数据ip
                  type: string
                  pattern: ^(?:(?:[0-9]|[1-9][0-9]|1[0-9]{2}|2[0-4][0-9]|25[0-5])\.){3}(?:[0-9]|[1-9][0-9]|1[0-9]{2}|2[0-4][0-9]|25[0-5])$
                  minLength: 0
                  maxLength: 64
                  x-go-custom-tag: db:"smartnic_data_ip"
                u_count:
                  description: u位
                  type: integer
                  format: int32
                  minimum: 0
                  maximum: 16777216
                  x-go-custom-tag: db:"u_count"
                warranty_start:
                  description: 维保开始时间
                  type: string
                  format: dateTime
                  x-go-custom-tag: db:"warranty_start"
                warranty_end:
                  description: 维保结束时间
                  type: string
                  format: dateTime
                  x-go-custom-tag: db:"warranty_end"
                procurement_time:
                  description: 采购时间
                  type: string
                  format: dateTime
                  x-go-custom-tag: db:"procurement_time"
                arbitration_az:
                  description: 标识本服务器是否属于逻辑仲裁az. 默认为false-不属于
                  type: boolean
                  default: false
                  x-go-custom-tag: db:"arbitration_az"
                server_category:
                  description: 服务器分类.1-内部机器，2-外部机器(公有云).默认值1.若为外部机器，则一定是逻辑仲裁az的节点
                  type: integer
                  format: int32
                  minimum: 1
                  maximum: 2
                  x-go-custom-tag: db:"server_category"
                  x-nullable: true
                region_uuid:
                  description: regionUUID.内部服务器可不传,根据rackID自动关联.外部服务器为必传项
                  type: string
                  minLength: 0
                  maxLength: 64
                  x-go-custom-tag: db:"region_uuid"
                  x-nullable: true
                az_uuid:
                  description: azUUID.内部服务器可不传,根据rackID自动关联.外部服务器为必传项
                  type: string
                  minLength: 0
                  maxLength: 64
                  x-go-custom-tag: db:"az_uuid"
                  x-nullable: true
                cluster_id:
                  description: 集群id
                  type: string
                  minLength: 0
                  maxLength: 64
                  x-go-custom-tag: db:"cluster_id"
                  x-nullable: false
                  x-omitempty: false
      responses:
        200:
          description: create  server
          schema:
            type: object
            properties:
              ids:
                type: array
                items:
                  type: string
        default:
          description: create server  error response
          schema:
            type: object
            required:
              - error
            properties:
              error:
                $ref: "#/definitions/error"
    patch:
      description: 更新服务器信息
      summary: updateServers
      operationId: updateServers
      tags:
        - server
      parameters:
        - name: servers
          in: body
          schema:
            type: array
            items:
              required:
                - serial_number
              properties:
                serial_number:
                  description: 序列号
                  type: string
                  minLength: 1
                  maxLength: 64
                  x-go-custom-tag: db:"serial_number"
                warranty_start:
                  description: 维保开始时间
                  type: string
                  format: dateTime
                  x-go-custom-tag: db:"warranty_start"
                warranty_end:
                  description: 维保结束时间
                  type: string
                  format: dateTime
                  x-go-custom-tag: db:"warranty_end"
                asset_state:
                  description: 资产状态id 故障1、维修中2、离线3、在线4、装机中5
                  type: integer
                  format: int32
                  minimum: 1
                  maximum: 5
                  x-go-custom-tag: db:"asset_state"
                  x-nullable: true
                arbitration_az:
                  description: 逻辑仲裁az的服务器标识.false-不属于仲裁az; true-属于仲裁az
                  type: boolean
                  default: false
                ipmi_user:
                  description: 管理口ipmi用户名
                  type: string
                  minLength: 0
                  maxLength: 64
                  x-go-custom-tag: db:"ipmi_user"
                ipmi_password:
                  description: 管理口ipmi密码,使用base64传输
                  type: string
                  minLength: 0
                  maxLength: 64
                  x-go-custom-tag: db:"ipmi_password"
                root_password:
                  description: 服务器root密码,使用base64传输
                  type: string
                  minLength: 0
                  maxLength: 64
                  x-go-custom-tag: db:"root_password"
      responses:
        200:
          description: create  server
          schema:
            type: object
            properties:
              ids:
                type: array
                items:
                  type: string
        default:
          description: create server  error response
          schema:
            type: object
            required:
              - error
            properties:
              error:
                $ref: "#/definitions/error"
    delete:
      description: 删除服务器信息
      summary: deleteServer
      operationId: deleteServer
      tags:
        - server
      parameters:
        - name: uuids
          in: body
          schema:
            type: array
            items:
              type: string
      responses:
        200:
          description: delete servers
          schema:
            type: object
            required:
              - result
            properties:
              result:
                type: object
        default:
          description: delete servers error response
          schema:
            type: object
            required:
              - error
            properties:
              error:
                $ref: "#/definitions/error"
  /v1/server/tag:
    post:
      description: 添加服务器tag
      summary: createServerTag
      operationId: createServerTag
      tags:
        - server_tag
      parameters:
        - name: server_tag
          in: body
          schema:
            type: array
            items:
              required:
                - server_uuid
                - name
                - value
              properties:
                server_uuid:
                  description: server uuid
                  type: string
                  minLength: 0
                  maxLength: 64
                  x-go-custom-tag: db:"server_uuid"
                  x-nullable: false
                name:
                  description: name
                  type: string
                  minLength: 0
                  maxLength: 64
                  x-go-custom-tag: db:"name"
                  x-nullable: false
                value:
                  description: value
                  type: string
                  minLength: 0
                  maxLength: 64
                  x-go-custom-tag: db:"value"
                  x-nullable: false
      responses:
        200:
          description: create server tag ok
          schema:
            type: object
            required:
              - result
            properties:
              result:
                type: object
        default:
          description: create server tag error response
          schema:
            type: object
            required:
              - error
            properties:
              error:
                $ref: "#/definitions/error"
    get:
      description: 获取服务器tag
      summary: describeServerTag
      operationId: describeServerTag
      tags:
        - server_tag
      parameters:
        - name: server_uuid
          in: body
          schema:
            type: array
            items:
              type: string
              minLength: 0
              maxLength: 64
      responses:
        200:
          description: describe of server tag
          schema:
            type: object
            required:
              - result
            properties:
              result:
                $ref: "#/definitions/server_tags"
        default:
          description: describe server  error response
          schema:
            type: object
            required:
              - error
            properties:
              error:
                $ref: "#/definitions/error"
    delete:
      description: 删除服务器tag
      summary: deleteServerTag
      operationId: deleteServerTag
      tags:
        - server_tag
      parameters:
        - name: server_uuid
          in: query
          required: true
          type: string
          minLength: 0
          maxLength: 64
        - name: names
          in: body
          schema:
            type: array
            items:
              type: string
      responses:
        200:
          description: delete server tag
          schema:
            type: object
            required:
              - result
            properties:
              result:
                type: object
        default:
          description: delete server tag error response
          schema:
            type: object
            required:
              - error
            properties:
              error:
                $ref: "#/definitions/error"
  /v1/server/{uuid}:
    get:
      description: 获取服务器信息详情
      summary: describeDetailServer
      operationId: describeDetailServer
      tags:
        - server
      parameters:
        - name: uuid
          in: path
          required: true
          type: string
          minLength: 0
          maxLength: 32
      responses:
        200:
          description: describe detail of server
          schema:
            type: object
            required:
              - result
            properties:
              result:
                $ref: "#/definitions/describe_detail_server_response"
        default:
          description: describe detail of  server  error response
          schema:
            type: object
            required:
              - error
            properties:
              error:
                $ref: "#/definitions/error"
    put:
      description: 修改服务器信息
      summary: updateServer
      operationId: updateServer
      tags:
        - server
      parameters:
        - name: uuid
          in: path
          required: true
          type: string
          minLength: 0
          maxLength: 32
        - name: server
          in: body
          schema:
            required:
              - asset_number
              - serial_number
              - datacenter_name
              - rack_number
              - u_count
              - warranty_start
              - warranty_end
              - procurement_time
            properties:
              datacenter_uuid:
                description: 机房id(弃用,以机房名称为准)
                type: string
                minLength: 0
                maxLength: 32
                x-go-custom-tag: db:"datacenter_uuid"
              datacenter_name:
                description: 机房名称
                type: string
                minLength: 0
                maxLength: 32
                x-go-custom-tag: db:"datacenter_name"
              rack_uuid:
                description: 机架id(弃用,以机架编号为准)
                type: string
                minLength: 0
                maxLength: 64
                x-go-custom-tag: db:"rack_uuid"
              rack_number:
                description: 机架编号
                type: string
                minLength: 0
                maxLength: 64
                x-go-custom-tag: db:"rack_number"
              server_package_uuid:
                description: 服务器配置uuid
                type: string
                minLength: 0
                maxLength: 32
                x-go-custom-tag: db:"server_package_uuid"
              os_package_uuid:
                description: 服务器uuid
                type: string
                minLength: 0
                maxLength: 32
                x-go-custom-tag: db:"os_package_uuid"
              asset_number:
                description: 资产编号
                type: string
                minLength: 0
                maxLength: 64
                x-go-custom-tag: db:"asset_number"
              serial_number:
                description: 序列号
                type: string
                minLength: 0
                maxLength: 64
                x-go-custom-tag: db:"serial_number"
              brand:
                description: 品牌
                type: string
                minLength: 0
                maxLength: 64
                x-go-custom-tag: db:"brand"
              model:
                description: 型号
                type: string
                minLength: 0
                maxLength: 64
                x-go-custom-tag: db:"model"
              mc_name:
                description: 厂商联系人姓名
                type: string
                minLength: 0
                maxLength: 64
                x-go-custom-tag: db:"mc_name"
              mc_phone:
                description: 厂商联系人电话
                type: string
                pattern: ^1[3-9]\d{9}$
                minLength: 0
                maxLength: 64
                x-go-custom-tag: db:"mc_phone"
              mc_email:
                description: 厂商联系人邮件
                type: string
                pattern: ^[a-zA-Z0-9_-]+@[a-zA-Z0-9_-]+(\.[a-zA-Z0-9_-]+)+$
                minLength: 0
                maxLength: 64
                x-go-custom-tag: db:"mc_email"
              system_ip:
                description: 系统ip
                type: string
                pattern: ^(?:(?:[0-9]|[1-9][0-9]|1[0-9]{2}|2[0-4][0-9]|25[0-5])\.){3}(?:[0-9]|[1-9][0-9]|1[0-9]{2}|2[0-4][0-9]|25[0-5])$
                minLength: 0
                maxLength: 64
                x-go-custom-tag: db:"system_ip"
              system_ip_mask:
                description: 系统ip MASK
                type: string
                pattern: ^(?:(?:[0-9]|[1-9][0-9]|1[0-9]{2}|2[0-4][0-9]|25[0-5])\.){3}(?:[0-9]|[1-9][0-9]|1[0-9]{2}|2[0-4][0-9]|25[0-5])$
                minLength: 0
                maxLength: 64
                x-go-custom-tag: db:"system_ip_mask"
              system_ip_mac:
                description: 系统IP MAC
                type: string
                pattern: ^[A-Fa-f0-9]{2}(:[A-Fa-f0-9]{2}){5}$
                minLength: 0
                maxLength: 64
                x-go-custom-tag: db:"system_ip_mac"
                x-nullable: true
              manage_ip:
                description: 管理ip
                type: string
                pattern: ^(?:(?:[0-9]|[1-9][0-9]|1[0-9]{2}|2[0-4][0-9]|25[0-5])\.){3}(?:[0-9]|[1-9][0-9]|1[0-9]{2}|2[0-4][0-9]|25[0-5])$
                minLength: 1
                maxLength: 64
                x-go-custom-tag: db:"manage_ip"
              data_ip:
                description: 数据ip
                type: string
                pattern: ^(?:(?:[0-9]|[1-9][0-9]|1[0-9]{2}|2[0-4][0-9]|25[0-5])\.){3}(?:[0-9]|[1-9][0-9]|1[0-9]{2}|2[0-4][0-9]|25[0-5])$
                minLength: 0
                maxLength: 64
                x-go-custom-tag: db:"data_ip"
              data_ip_mask:
                description: 数据IP MASK
                type: string
                pattern: ^(?:(?:[0-9]|[1-9][0-9]|1[0-9]{2}|2[0-4][0-9]|25[0-5])\.){3}(?:[0-9]|[1-9][0-9]|1[0-9]{2}|2[0-4][0-9]|25[0-5])$
                minLength: 0
                maxLength: 64
                x-go-custom-tag: db:"data_ip_mask"
                x-nullable: true
              smartnic_man_ip:
                description: SmartNic 管理ip
                type: string
                pattern: ^(?:(?:[0-9]|[1-9][0-9]|1[0-9]{2}|2[0-4][0-9]|25[0-5])\.){3}(?:[0-9]|[1-9][0-9]|1[0-9]{2}|2[0-4][0-9]|25[0-5])$
                minLength: 0
                maxLength: 64
                x-go-custom-tag: db:"smartnic_man_ip"
              smartnic_man_ip_gateway:
                description: SmartNic 管理ip网关
                type: string
                minLength: 0
                maxLength: 64
                x-go-custom-tag: db:"smartnic_man_ip_gateway"
              smartnic_data_ip:
                description: SmartNic 数据ip
                type: string
                pattern: ^(?:(?:[0-9]|[1-9][0-9]|1[0-9]{2}|2[0-4][0-9]|25[0-5])\.){3}(?:[0-9]|[1-9][0-9]|1[0-9]{2}|2[0-4][0-9]|25[0-5])$
                minLength: 0
                maxLength: 64
                x-go-custom-tag: db:"smartnic_data_ip"
              u_count:
                description: u位
                type: integer
                format: int32
                minimum: 0
                maximum: 16777216
                x-go-custom-tag: db:"u_count"
              warranty_start:
                description: 维保开始时间
                type: string
                format: dateTime
                x-go-custom-tag: db:"warranty_start"
              warranty_end:
                description: 维保结束时间
                type: string
                format: dateTime
                x-go-custom-tag: db:"warranty_end"
              procurement_time:
                description: 采购时间
                type: string
                format: dateTime
                x-go-custom-tag: db:"procurement_time"
              arbitration_az:
                description: 逻辑仲裁az的服务器标识.false-不属于仲裁az; true-属于仲裁az
                type: boolean
                default: false
              ipmi_user:
                description: 管理口ipmi用户名
                type: string
                minLength: 0
                maxLength: 64
                x-go-custom-tag: db:"ipmi_user"
              ipmi_password:
                description: 管理口ipmi密码,使用base64传输
                type: string
                minLength: 0
                maxLength: 64
                x-go-custom-tag: db:"ipmi_password"
              root_password:
                description: 服务器root密码,使用base64传输
                type: string
                minLength: 0
                maxLength: 64
                x-go-custom-tag: db:"root_password"
      responses:
        200:
          description: describe of server
          schema:
            type: object
            required:
              - result
            properties:
              result:
                type: object
        default:
          description: describe server  error response
          schema:
            type: object
            required:
              - error
            properties:
              error:
                $ref: "#/definitions/error"

  /v1/server/brand_model:
    get:
      description: 获取全部现存服务器的品牌brand与型号modellist
      summary: describeServerBrandAndModel
      operationId: describeServerBrandAndModel
      tags:
        - server
      responses:
        200:
          description: describe server brand model
          schema:
            type: object
            required:
              - result
            properties:
              result:
                $ref: "#/definitions/brand_model"
        default:
          description: describe server brand model
          schema:
            type: object
            required:
              - error
            properties:
              error:
                $ref: "#/definitions/error"
  /v1/server_package:
    get:
      description: 获取server_package列表
      summary: describeServerPackage
      operationId: describeServerPackage
      tags:
        - server_package
      parameters:
        - name: page_size
          in: query
          required: true
          type: integer
          format: int
          minimum: 1
          maximum: 16777216
        - name: page_no
          in: query
          required: true
          type: integer
          format: int
          minimum: -1
          maximum: 16777216
      responses:
        200:
          description: describe of server_package
          schema:
            type: object
            required:
              - result
            properties:
              result:
                $ref: "#/definitions/server_packages_response"
        default:
          description: describe server_package  error response
          schema:
            type: object
            required:
              - error
            properties:
              error:
                $ref: "#/definitions/error"
    post:
      description: 新增 server package
      summary: createServerPackage
      operationId: createServerPackage
      tags:
        - server_package
      parameters:
        - name: server_package
          in: body
          schema:
            type: array
            items:
              required:
                - package_name
              properties:
                package_name:
                  description: 名称
                  type: string
                  minLength: 0
                  maxLength: 25
                  x-go-custom-tag: db:"package_name"
                min_cpu_core_num:
                  type: integer
                  minimum: 0
                  maximum: 16777216
                  x-go-custom-tag: db:"min_cpu_core_num"
                cpu_model:
                  type: string
                  minLength: 0
                  maxLength: 255
                  x-go-custom-tag: db:"cpu_model"
                min_memory_size:
                  type: integer
                  minimum: 0
                  maximum: 16777216
                  x-go-custom-tag: db:"min_memory_size"
                disk_num:
                  type: integer
                  minimum: 0
                  maximum: 16777216
                  x-go-custom-tag: db:"disk_num"
                disk_size:
                  type: integer
                  minimum: 0
                  maximum: 16777216
                  x-go-custom-tag: db:"disk_size"
                nic_num:
                  type: integer
                  minimum: 0
                  maximum: 16777216
                  x-go-custom-tag: db:"nic_num"
                nic_model:
                  type: string
                  minLength: 0
                  maxLength: 255
                  x-go-custom-tag: db:"nic_model"
                package_desc:
                  type: string
                  minLength: 0
                  maxLength: 255
                  x-go-custom-tag: db:"package_desc"
                server_module_name:
                  type: string
                  minLength: 0
                  maxLength: 64
                  x-go-custom-tag: db:"server_module_name"
                server_module_params:
                  type: string
                  minLength: 0
                  maxLength: 2048
                  x-go-custom-tag: db:"server_module_params"
                sys_disk_hdd_num:
                  type: integer
                  minimum: 0
                  maximum: 16777216
                  x-go-custom-tag: db:"sys_disk_hdd_num"
                sys_disk_hdd_size:
                  type: integer
                  minimum: 0
                  maximum: 16777216
                  x-go-custom-tag: db:"sys_disk_hdd_size"
                sys_disk_ssd_num:
                  type: integer
                  minimum: 0
                  maximum: 16777216
                  x-go-custom-tag: db:"sys_disk_ssd_num"
                sys_disk_ssd_size:
                  type: integer
                  minimum: 0
                  maximum: 16777216
                  x-go-custom-tag: db:"sys_disk_ssd_size"
                sys_disk_raid_type:
                  type: string
                  minLength: 0
                  maxLength: 45
                  x-go-custom-tag: db:"sys_disk_raid_type"
                data_disk_hdd_num:
                  type: integer
                  minimum: 0
                  maximum: 16777216
                  x-go-custom-tag: db:"data_disk_hdd_num"
                data_disk_hdd_size:
                  type: integer
                  minimum: 0
                  maximum: 16777216
                  x-go-custom-tag: db:"data_disk_hdd_size"
                data_disk_ssd_num:
                  type: integer
                  minimum: 0
                  maximum: 16777216
                  x-go-custom-tag: db:"data_disk_ssd_num"
                data_disk_ssd_size:
                  type: integer
                  minimum: 0
                  maximum: 16777216
                  x-go-custom-tag: db:"data_disk_ssd_size"
                data_disk_raid_type:
                  type: string
                  minLength: 0
                  maxLength: 45
                  x-go-custom-tag: db:"data_disk_raid_type"
                data_disk_nvme_num:
                  type: integer
                  minimum: 0
                  maximum: 16777216
                  x-go-custom-tag: db:"data_disk_nvme_num"
                data_disk_nvme_size:
                  type: integer
                  minimum: 0
                  maximum: 16777216
                  x-go-custom-tag: db:"data_disk_nvme_size"
                gpu:
                  type: string
                  minLength: 0
                  maxLength: 64
                  x-go-custom-tag: db:"gpu"
      responses:
        200:
          description: create server_packages
          schema:
            type: object
            required:
              - result
            properties:
              result:
                type: object
        default:
          description: create server  error response
          schema:
            type: object
            required:
              - error
            properties:
              error:
                $ref: "#/definitions/error"
    delete:
      description: 删除多条 server_packages
      summary: deleteServerPackages
      operationId: deleteServerPackages
      tags:
        - server_package
      parameters:
        - name: uuids
          in: body
          schema:
            type: array
            items:
              type: string
      responses:
        200:
          description: delete of server_packages
          schema:
            type: object
            required:
              - result
            properties:
              result:
                type: object
        default:
          description: delete server_packages  error response
          schema:
            type: object
            required:
              - error
            properties:
              error:
                $ref: "#/definitions/error"
  /v1/server_package/{uuid}:
    get:
      description: 获取server_package 根据 uuid
      summary: describeDetailServerPackage
      operationId: describeDetailServerPackage
      tags:
        - server_package
      parameters:
        - name: uuid
          in: path
          required: true
          type: string
          minLength: 0
          maxLength: 32
      responses:
        200:
          description: describe detail of server_package
          schema:
            type: object
            required:
              - result
            properties:
              result:
                $ref: "#/definitions/server_package_response"
        default:
          description: describe detail of server_package error response
          schema:
            type: object
            required:
              - error
            properties:
              error:
                $ref: "#/definitions/error"
    put:
      description: 修改server_package信息
      summary: updateServerPackage
      operationId: updateServerPackage
      tags:
        - server_package
      parameters:
        - name: uuid
          in: path
          required: true
          type: string
          minLength: 0
          maxLength: 32
        - name: server_package
          in: body
          schema:
            required:
              - package_name
            properties:
              package_name:
                description: 名称
                type: string
                minLength: 0
                maxLength: 25
                x-go-custom-tag: db:"package_name"
              min_cpu_core_num:
                type: integer
                minimum: 0
                maximum: 16777216
                x-go-custom-tag: db:"min_cpu_core_num"
              cpu_model:
                type: string
                minLength: 0
                maxLength: 255
                x-go-custom-tag: db:"cpu_model"
              min_memory_size:
                type: integer
                minimum: 0
                maximum: 16777216
                x-go-custom-tag: db:"min_memory_size"
              disk_num:
                type: integer
                minimum: 0
                maximum: 16777216
                x-go-custom-tag: db:"disk_num"
              disk_size:
                type: integer
                minimum: 0
                maximum: 16777216
                x-go-custom-tag: db:"disk_size"
              nic_num:
                type: integer
                minimum: 0
                maximum: 16777216
                x-go-custom-tag: db:"nic_num"
              nic_model:
                type: string
                minLength: 0
                maxLength: 255
                x-go-custom-tag: db:"nic_model"
              package_desc:
                type: string
                minLength: 0
                maxLength: 255
                x-go-custom-tag: db:"package_desc"
              server_module_name:
                type: string
                minLength: 0
                maxLength: 64
                x-go-custom-tag: db:"server_module_name"
              server_module_params:
                type: string
                minLength: 0
                maxLength: 2048
                x-go-custom-tag: db:"server_module_params"
              sys_disk_hdd_num:
                type: integer
                minimum: 0
                maximum: 16777216
                x-go-custom-tag: db:"sys_disk_hdd_num"
              sys_disk_hdd_size:
                type: integer
                minimum: 0
                maximum: 16777216
                x-go-custom-tag: db:"sys_disk_hdd_size"
              sys_disk_ssd_num:
                type: integer
                minimum: 0
                maximum: 16777216
                x-go-custom-tag: db:"sys_disk_ssd_num"
              sys_disk_ssd_size:
                type: integer
                minimum: 0
                maximum: 16777216
                x-go-custom-tag: db:"sys_disk_ssd_size"
              sys_disk_raid_type:
                type: string
                minLength: 0
                maxLength: 45
                x-go-custom-tag: db:"sys_disk_raid_type"
              data_disk_hdd_num:
                type: integer
                minimum: 0
                maximum: 16777216
                x-go-custom-tag: db:"data_disk_hdd_num"
              data_disk_hdd_size:
                type: integer
                minimum: 0
                maximum: 16777216
                x-go-custom-tag: db:"data_disk_hdd_size"
              data_disk_ssd_num:
                type: integer
                minimum: 0
                maximum: 16777216
                x-go-custom-tag: db:"data_disk_ssd_num"
              data_disk_ssd_size:
                type: integer
                minimum: 0
                maximum: 16777216
                x-go-custom-tag: db:"data_disk_ssd_size"
              data_disk_raid_type:
                type: string
                minLength: 0
                maxLength: 45
                x-go-custom-tag: db:"data_disk_raid_type"
              data_disk_nvme_num:
                type: integer
                minimum: 0
                maximum: 16777216
                x-go-custom-tag: db:"data_disk_nvme_num"
              data_disk_nvme_size:
                type: integer
                minimum: 0
                maximum: 16777216
                x-go-custom-tag: db:"data_disk_nvme_size"
              gpu:
                type: string
                minLength: 0
                maxLength: 64
                x-go-custom-tag: db:"gpu"
      responses:
        200:
          description: update server_package
          schema:
            type: object
            required:
              - result
            properties:
              result:
                type: object
        default:
          description: update server_package error response
          schema:
            type: object
            required:
              - error
            properties:
              error:
                $ref: "#/definitions/error"
  /v1/overview/asset_state:
    get:
      description: 概览页 设备状态信息
      summary: selectAssetState
      operationId: selectAssetState
      tags:
        - overview
      parameters:
        - name: region_uuid
          in: query
          required: false
          type: string
          minLength: 0
          maxLength: 64
        - name: az_uuid
          in: query
          required: false
          type: string
          minLength: 0
          maxLength: 64
        - name: region_name
          in: query
          required: false
          type: string
          minLength: 0
          maxLength: 64
        - name: az_name
          in: query
          required: false
          type: string
          minLength: 0
          maxLength: 64
      responses:
        200:
          description: selectAssetState
          schema:
            type: object
            required:
              - result
            properties:
              result:
                $ref: "#/definitions/asset_state"
        default:
          description: selectAssetState  error response
          schema:
            type: object
            required:
              - error
            properties:
              error:
                $ref: "#/definitions/error"
  /v1/overview/asset_years:
    get:
      description: 概览页 设备 年限
      summary: selectAssetYears
      operationId: selectAssetYears
      tags:
        - overview
      parameters:
        - name: region_uuid
          in: query
          required: false
          type: string
          minLength: 0
          maxLength: 64
        - name: az_uuid
          in: query
          required: false
          type: string
          minLength: 0
          maxLength: 64
        - name: region_name
          in: query
          required: false
          type: string
          minLength: 0
          maxLength: 64
        - name: az_name
          in: query
          required: false
          type: string
          minLength: 0
          maxLength: 64
      responses:
        200:
          description: AssetYears
          schema:
            type: object
            required:
              - result
            properties:
              result:
                $ref: "#/definitions/asset_years"
        default:
          description: AssetYears  error response
          schema:
            type: object
            required:
              - error
            properties:
              error:
                $ref: "#/definitions/error"
  /v1/overview/asset_warranty_state:
    get:
      description: 概览页 维保状态
      summary: selectAssetWarrantyState
      operationId: selectAssetWarrantyState
      tags:
        - overview
      parameters:
        - name: region_uuid
          in: query
          required: false
          type: string
          minLength: 0
          maxLength: 64
        - name: az_uuid
          in: query
          required: false
          type: string
          minLength: 0
          maxLength: 64
        - name: region_name
          in: query
          required: false
          type: string
          minLength: 0
          maxLength: 64
        - name: az_name
          in: query
          required: false
          type: string
          minLength: 0
          maxLength: 64
      responses:
        200:
          description: selectAssetWarrantyState
          schema:
            type: object
            required:
              - result
            properties:
              result:
                $ref: "#/definitions/asset_warranty_state"
        default:
          description: selectAssetWarrantyState error response
          schema:
            type: object
            required:
              - error
            properties:
              error:
                $ref: "#/definitions/error"
  /v1/overview/asset_way_to_use:
    get:
      description: 概览页设备用途分类, 网络设备用途：1网络设备、2未分配(默认), 服务器用途：计算1、存储2、管理3、未分配4(默认)
      summary: selectAssetWayToUse
      operationId: selectAssetWayToUse
      tags:
        - overview
      parameters:
        - name: region_uuid
          in: query
          required: false
          type: string
          minLength: 0
          maxLength: 64
        - name: az_uuid
          in: query
          required: false
          type: string
          minLength: 0
          maxLength: 64
        - name: region_name
          in: query
          required: false
          type: string
          minLength: 0
          maxLength: 64
        - name: az_name
          in: query
          required: false
          type: string
          minLength: 0
          maxLength: 64
      responses:
        200:
          description: selectAssetWayToUse
          schema:
            type: object
            required:
              - result
            properties:
              result:
                $ref: "#/definitions/asset_way_to_use"
        default:
          description: selectAssetWayToUse  error response
          schema:
            type: object
            required:
              - error
            properties:
              error:
                $ref: "#/definitions/error"
  /v1/overview/datacenters_values:
    get:
      description: 获取 全部机房下，机架数量、服务器数量、网络设备数量、总u位、已用u位
      summary: selectDatacenterValues
      operationId: selectDatacenterValues
      tags:
        - overview
      parameters:
        - name: region_uuid
          in: query
          required: false
          type: string
          minLength: 0
          maxLength: 64
        - name: az_uuid
          in: query
          required: false
          type: string
          minLength: 0
          maxLength: 64
        - name: region_name
          in: query
          required: false
          type: string
          minLength: 0
          maxLength: 64
        - name: az_name
          in: query
          required: false
          type: string
          minLength: 0
          maxLength: 64
      responses:
        200:
          description: select datacenter values
          schema:
            type: object
            required:
              - result
            properties:
              result:
                type: object
                properties:
                  datacenter_values:
                    type: array
                    items:
                      $ref: "#/definitions/datacenter_values"
        default:
          description: generic error response
          schema:
            type: object
            required:
              - error
            properties:
              error:
                $ref: "#/definitions/error"
  /v1/overview/region_count:
    get:
      description: 获取 region count
      summary: selectRegionCount
      operationId: selectRegionCount
      tags:
        - overview
      parameters:
        - name: region_uuid
          in: query
          required: false
          type: string
          minLength: 0
          maxLength: 64
        - name: az_uuid
          in: query
          required: false
          type: string
          minLength: 0
          maxLength: 64
        - name: region_name
          in: query
          required: false
          type: string
          minLength: 0
          maxLength: 64
        - name: az_name
          in: query
          required: false
          type: string
          minLength: 0
          maxLength: 64
      responses:
        200:
          description: select region count
          schema:
            type: object
            required:
              - result
            properties:
              result:
                $ref: "#/definitions/kind_count"
        default:
          description: error response
          schema:
            type: object
            required:
              - error
            properties:
              error:
                $ref: "#/definitions/error"
  /v1/overview/az_count:
    get:
      description: 获取 az count
      summary: selectAZCount
      operationId: selectAZCount
      tags:
        - overview
      parameters:
        - name: region_uuid
          in: query
          required: false
          type: string
          minLength: 0
          maxLength: 64
        - name: az_uuid
          in: query
          required: false
          type: string
          minLength: 0
          maxLength: 64
        - name: region_name
          in: query
          required: false
          type: string
          minLength: 0
          maxLength: 64
        - name: az_name
          in: query
          required: false
          type: string
          minLength: 0
          maxLength: 64
      responses:
        200:
          description: select AZ count
          schema:
            type: object
            required:
              - result
            properties:
              result:
                $ref: "#/definitions/kind_count"
        default:
          description: error response
          schema:
            type: object
            required:
              - error
            properties:
              error:
                $ref: "#/definitions/error"
  /v1/overview/datacenter_count:
    get:
      description: 获取 region count
      summary: selectDatacenterCount
      operationId: selectDatacenterCount
      tags:
        - overview
      parameters:
        - name: region_uuid
          in: query
          required: false
          type: string
          minLength: 0
          maxLength: 64
        - name: az_uuid
          in: query
          required: false
          type: string
          minLength: 0
          maxLength: 64
        - name: region_name
          in: query
          required: false
          type: string
          minLength: 0
          maxLength: 64
        - name: az_name
          in: query
          required: false
          type: string
          minLength: 0
          maxLength: 64
      responses:
        200:
          description: select datacenter count
          schema:
            type: object
            required:
              - result
            properties:
              result:
                $ref: "#/definitions/kind_count"
        default:
          description: error response
          schema:
            type: object
            required:
              - error
            properties:
              error:
                $ref: "#/definitions/error"
  /v1/overview/rack_count:
    get:
      description: 获取 rack count
      summary: selectRackCount
      operationId: selectRackCount
      tags:
        - overview
      parameters:
        - name: region_uuid
          in: query
          required: false
          type: string
          minLength: 0
          maxLength: 64
        - name: az_uuid
          in: query
          required: false
          type: string
          minLength: 0
          maxLength: 64
        - name: region_name
          in: query
          required: false
          type: string
          minLength: 0
          maxLength: 64
        - name: az_name
          in: query
          required: false
          type: string
          minLength: 0
          maxLength: 64
      responses:
        200:
          description: select rack count
          schema:
            type: object
            required:
              - result
            properties:
              result:
                $ref: "#/definitions/kind_count"
        default:
          description: error response
          schema:
            type: object
            required:
              - error
            properties:
              error:
                $ref: "#/definitions/error"
  /v1/overview/server_count:
    get:
      description: 获取 server count
      summary: selectServerCount
      operationId: selectServerCount
      tags:
        - overview
      parameters:
        - name: region_uuid
          in: query
          required: false
          type: string
          minLength: 0
          maxLength: 64
        - name: az_uuid
          in: query
          required: false
          type: string
          minLength: 0
          maxLength: 64
        - name: region_name
          in: query
          required: false
          type: string
          minLength: 0
          maxLength: 64
        - name: az_name
          in: query
          required: false
          type: string
          minLength: 0
          maxLength: 64
      responses:
        200:
          description: select server count
          schema:
            type: object
            required:
              - result
            properties:
              result:
                $ref: "#/definitions/kind_count"
        default:
          description: error response
          schema:
            type: object
            required:
              - error
            properties:
              error:
                $ref: "#/definitions/error"
  /v1/overview/network_equipment_count:
    get:
      description: 获取 网络设备 count
      summary: selectNetworkEquipmentCount
      operationId: selectNetworkEquipmentCount
      tags:
        - overview
      parameters:
        - name: region_uuid
          in: query
          required: false
          type: string
          minLength: 0
          maxLength: 64
        - name: az_uuid
          in: query
          required: false
          type: string
          minLength: 0
          maxLength: 64
        - name: region_name
          in: query
          required: false
          type: string
          minLength: 0
          maxLength: 64
        - name: az_name
          in: query
          required: false
          type: string
          minLength: 0
          maxLength: 64
      responses:
        200:
          description: select net equ count
          schema:
            type: object
            required:
              - result
            properties:
              result:
                $ref: "#/definitions/kind_count"
        default:
          description: error response
          schema:
            type: object
            required:
              - error
            properties:
              error:
                $ref: "#/definitions/error"
  /v1/overview/eip_count:
    get:
      description: 获取 eip count
      summary: selectEipCount
      operationId: selectEipCount
      tags:
        - overview
      parameters:
        - name: region_uuid
          in: query
          required: false
          type: string
          minLength: 0
          maxLength: 64
        - name: az_uuid
          in: query
          required: false
          type: string
          minLength: 0
          maxLength: 64
        - name: region_name
          in: query
          required: false
          type: string
          minLength: 0
          maxLength: 64
        - name: az_name
          in: query
          required: false
          type: string
          minLength: 0
          maxLength: 64
      responses:
        200:
          description: select eip count
          schema:
            type: object
            required:
              - result
            properties:
              result:
                $ref: "#/definitions/kind_count"
        default:
          description: error response
          schema:
            type: object
            required:
              - error
            properties:
              error:
                $ref: "#/definitions/error"
  /v1/tag/server:
    get:
      description: 获取服务器上的所有tag
      summary: describeServerTagAll
      operationId: describeServerTagAll
      tags:
        - server_tag
      responses:
        200:
          description: describe of tag
          schema:
            type: object
            required:
              - result
            properties:
              result:
                $ref: "#/definitions/tags_values"
        default:
          description: describe server  error response
          schema:
            type: object
            required:
              - error
            properties:
              error:
                $ref: "#/definitions/error"
  /v1/tag/eip:
    get:
      description: 获取eip上的所有tag
      summary: describeEipTagAll
      operationId: describeEipTagAll
      tags:
        - eip_tag
      responses:
        200:
          description: describe of tag
          schema:
            type: object
            required:
              - result
            properties:
              result:
                $ref: "#/definitions/tags_values"
        default:
          description: describe server  error response
          schema:
            type: object
            required:
              - error
            properties:
              error:
                $ref: "#/definitions/error"

  /v1/serverRole:
    get:
      description: 查询服务器角色
      summary: describeServerRole
      operationId: describeServerRole
      tags:
        - server_role
      parameters:
        - name: role
          in: query
          required: false
          type: string
          minLength: 0
          maxLength: 64
        - name: serverId
          in: query
          required: false
          type: string
          minLength: 0
          maxLength: 64
        - name: metadata_key
          in: query
          required: false
          type: string
          minLength: 0
          maxLength: 64
        - name: metadata_value
          in: query
          required: false
          type: string
          minLength: 0
          maxLength: 64
      responses:
        200:
          description: describe of server_package
          schema:
            type: object
            required:
              - result
            properties:
              result:
                $ref: "#/definitions/server_roles_response"
        default:
          description: describe server  error response
          schema:
            type: object
            required:
              - error
            properties:
              error:
                $ref: "#/definitions/error"
    post:
      description: 添加服务器角色
      summary: createServerRole
      operationId: createServerRole
      tags:
        - server_role
      parameters:
        - name: ServerRole
          in: body
          schema:
            type: array
            items:
              properties:
                serverId:
                  type: string
                  x-go-custom-tag: db:"uuid"
                role:
                  type: string
                  x-go-custom-tag: db:"role"
                metadata_key:
                  type: string
                  x-go-custom-tag: db:"metadata_key"
                metadata_value:
                  type: string
                  x-go-custom-tag: db:"metadata_value"
      responses:
        200:
          description: create server role
          schema:
            type: object
            required:
              - result
            properties:
              result:
                type: object
        default:
          description: create server role error response
          schema:
            type: object
            required:
              - error
            properties:
              error:
                $ref: "#/definitions/error"

  /v1/serverRole:serialNumberList:
    get:
      description: 获取服务器SN列表
      summary: describeServerRoleSerialNumberList
      operationId: describeServerRoleSerialNumberList
      tags:
        - server_role
      parameters:
        - name: condition
          in: body
          required: false
          schema:
            type: array
            items:
              properties:
                metadataKey:
                  type: string
                  description: 高级属性Key
                metadataValue:
                  type: string
                  description: 高级属性Value
                role:
                  type: string
                  description: 服务器角色
      responses:
        200:
          description: A successful response.
          schema:
            type: object
            required:
              - result
            properties:
              result:
                $ref: '#/definitions/server_role_serial_number_list_response'
        default:
          description: describe server  error response
          schema:
            type: object
            required:
              - error
            properties:
              error:
                $ref: "#/definitions/error"

  /v1/serverAllRoles:
    get:
      description: 查询服务器角色常量表
      summary: describeServerAllRole
      operationId: describeServerAllRole
      tags:
        - server_role
      responses:
        200:
          description: describe of server
          schema:
            type: object
            required:
              - result
            properties:
              result:
                $ref: "#/definitions/describe_server_allroles_response"
        default:
          description: describe server  error response
          schema:
            type: object
            required:
              - error
            properties:
              error:
                $ref: "#/definitions/error"

  /v1/serverRole/{serverId}:
    delete:
      summary: deleteServerRole
      operationId: deleteServerRole
      tags:
        - server_role
      description: 删除服务器角色
      parameters:
        - name: serverId
          in: path
          type: string
          required: true
      responses:
        200:
          description: delete of server_roles
          schema:
            type: object
            required:
              - result
            properties:
              result:
                type: object
        default:
          description: delete server_packages  error response
          schema:
            type: object
            required:
              - error
            properties:
              error:
                $ref: "#/definitions/error"

  /v1/delivery_plan:
    get:
      description: 获取交付计划列表
      summary: describesDeliveryPlan
      operationId: describesDeliveryPlan
      tags:
        - delivery_plan
      parameters:
        - name: condition
          in: body
          schema:
            properties:
              region_name:
                type: string
                minLength: 0
                maxLength: 32
              az_name:
                type: string
                minLength: 0
                maxLength: 32
              release_id:
                type: string
                minLength: 0
                maxLength: 32
              state:
                type: array
                items:
                  type: integer
                  format: int32
        - name: page_size
          in: query
          required: true
          type: integer
          format: int
          minimum: 1
          maximum: 16777216
        - name: page_no
          in: query
          required: true
          type: integer
          format: int
          minimum: -1
          maximum: 16777216
      responses:
        200:
          description: list of delivery plan
          schema:
            type: object
            required:
              - result
            properties:
              result:
                $ref: "#/definitions/describes_delivery_plan_response"
        default:
          description: detail error response
          schema:
            type: object
            required:
              - error
            properties:
              error:
                $ref: "#/definitions/error"
    post:
      description: 添加交付计划信息
      summary: createDeliveryPlan
      operationId: createDeliveryPlan
      tags:
        - delivery_plan
      parameters:
        - name: body
          in: body
          schema:
            properties:
              uuid:
                type: string
                minLength: 0
                maxLength: 32
                x-nullable: false
              region_name:
                type: string
                minLength: 0
                maxLength: 32
                x-nullable: false
              az_name:
                type: string
                minLength: 0
                maxLength: 32
                x-nullable: false
              release_id:
                type: string
                minLength: 0
                maxLength: 32
                x-nullable: false
              product:
                type: array
                description: 交付计划平台产品ID，首数据长度为0表示清空
                items:
                  type: string
                  minLength: 0
                  maxLength: 32
                  x-nullable: false
              arch:
                type: string
                description: 交付CPU架构:x86_64、arm
                maxLength: 10
                x-nullable: false
              quota:
                type: string
                description: 交付配额：light、small、medium、large
                maxLength: 10
                x-nullable: false
      responses:
        200:
          description: create delivery plan
          schema:
            type: object
            required:
              - result
            properties:
              result:
                type: object
        default:
          description: create error response
          schema:
            type: object
            required:
              - error
            properties:
              error:
                $ref: "#/definitions/error"
  /v1/delivery_plan/{uuid}:
    get:
      description: 获取交付计划
      summary: describeDeliveryPlan
      operationId: describeDeliveryPlan
      tags:
        - delivery_plan
      parameters:
        - name: uuid
          in: path
          required: true
          type: string
          minLength: 0
          maxLength: 32
      responses:
        200:
          description: detail of delivery plan
          schema:
            type: object
            required:
              - result
            properties:
              result:
                $ref: "#/definitions/delivery_plan_response"
        default:
          description: detail error response
          schema:
            type: object
            required:
              - error
            properties:
              error:
                $ref: "#/definitions/error"
    put:
      description: 修改交付计划信息
      summary: updateDeliveryPlan
      operationId: updateDeliveryPlan
      tags:
        - delivery_plan
      parameters:
        - name: uuid
          in: path
          required: true
          type: string
          minLength: 0
          maxLength: 32
        - name: body
          in: body
          schema:
            properties:
              region_name:
                type: string
                minLength: 0
                maxLength: 32
              az_name:
                type: string
                minLength: 0
                maxLength: 32
              release_id:
                type: string
                minLength: 0
                maxLength: 32
              product:
                type: array
                description: 交付计划平台产品ID，首数据长度为0表示清空
                items:
                  type: string
                  minLength: 0
                  maxLength: 32
                  x-nullable: false
              state:
                type: integer
                format: int32
              arch:
                type: string
                description: 交付CPU架构:x86_64、arm
                minLength: 0
                maxLength: 10
              quota:
                type: string
                description: 交付配额：light、small、medium、large
                minLength: 0
                maxLength: 10
      responses:
        200:
          description: update of delivery plan
          schema:
            type: object
            required:
              - result
            properties:
              result:
                type: object
        default:
          description: update error response
          schema:
            type: object
            required:
              - error
            properties:
              error:
                $ref: "#/definitions/error"

    delete:
      description: 删除交付计划信息
      summary: deleteDeliveryPlan
      operationId: deleteDeliveryPlan
      tags:
        - delivery_plan
      parameters:
        - name: uuid
          in: path
          required: true
          type: string
          minLength: 0
          maxLength: 32
      responses:
        200:
          description: delete of delivery plan
          schema:
            type: object
            required:
              - result
            properties:
              result:
                type: object
        default:
          description: delete error response
          schema:
            type: object
            required:
              - error
            properties:
              error:
                $ref: "#/definitions/error"

  /v1/network_plan_targets:
    get:
      description: 获取支持的网络规划列表
      summary: describeNetworkPlanTargets
      operationId: describeNetworkPlanTargets
      tags:
        - network
      parameters:
        - name: condition
          in: body
          schema:
            properties:
              target:
                type: string
              isMust:
                type: integer
        - name: page_no
          in: query
          type: integer
          minimum: -1
          maximum: 16777216
          required: true
          description: "页码；默认为1"
        - name: page_size
          in: query
          type: integer
          minimum: 1
          maximum: 16777216
          required: true
          description: "分页大小；默认为20；取值范围[10, 100]"
      responses:
        200:
          description: list of global network plan target
          schema:
            type: object
            required:
              - result
            properties:
              result:
                $ref: "#/definitions/describe_network_plan_targets_response"
        default:
          description: detail error response
          schema:
            type: object
            required:
              - error
            properties:
              error:
                $ref: "#/definitions/error"

    post:
      description: 创建网络规划列表
      summary: createNetworkPlanTarget
      operationId: createNetworkPlanTarget
      tags:
        - network
      parameters:
        - name: plans
          in: body
          schema:
            $ref: "#/definitions/network_plan_target"
      responses:
        200:
          description: create network plan target success
          schema:
            type: object
            properties:
              target:
                type: string
        default:
          description: create error response
          schema:
            type: object
            required:
              - error
            properties:
              error:
                $ref: "#/definitions/error"

  /v1/network_plan_targets/{target}:
    delete:
      description: 删除网络规划类型
      summary: deleteNetworkPlanTarget
      operationId: deleteNetworkPlanTarget
      tags:
        - network
      parameters:
        - name: target
          in: path
          required: true
          type: string
          minLength: 0
          maxLength: 64
      responses:
        200:
          description: delete of network plan target success
          schema:
            type: object
            required:
              - result
            properties:
              result:
                type: object
        default:
          description: delete error response
          schema:
            type: object
            required:
              - error
            properties:
              error:
                $ref: "#/definitions/error"

    put:
      description: 修改网络规划类型
      summary: updateNetworkPlanTarget
      operationId: updateNetworkPlanTarget
      tags:
        - network
      parameters:
        - name: target
          in: path
          required: true
          type: string
          minLength: 0
          maxLength: 64
        - name: plan
          in: body
          schema:
            $ref: "#/definitions/network_plan_target"
      responses:
        200:
          description: update network plan target success
          schema:
            type: object
            required:
              - result
            properties:
              result:
                type: object
        default:
          description: update error response
          schema:
            type: object
            required:
              - error
            properties:
              error:
                $ref: "#/definitions/error"
  /v1/network_plans:check:
    post:
      description: 校验交换机的网段规划信息
      summary:  checkNetworkPlans
      operationId: checkNetworkPlans
      tags:
        - network
      responses:
        200:
          description: check network plans success
          schema:
            type: object
            required:
              - result
            properties:
              result:
                type: object
        default:
          description: check network plans error response
          schema:
            type: object
            required:
              - error
            properties:
              error:
                $ref: "#/definitions/error"

  /v1/network_plans:
    get:
      description: 获取网络规划列表
      summary: describeNetworkPlans
      operationId: describeNetworkPlans
      tags:
        - network
      parameters:
        - name: condition
          in: body
          schema:
            properties:
              regionUuid:
                type: string
                minLength: 0
                maxLength: 64
              subnet:
                type: string
                minLength: 0
                maxLength: 64
              resId:
                type: string
                minLength: 0
                maxLength: 64
              resType:
                type: string
                minLength: 0
                maxLength: 64
              resSN:
                type: string
                minLength: 0
                maxLength: 64
              asNumber:
                type: string
                minLength: 0
                maxLength: 64
              description:
                type: string
                minLength: 0
                maxLength: 64
              batch_target:
                type: array
                items:
                  type: string
              status:
                type: string
                minLength: 0
                maxLength: 32
              target:
                type: string
                minLength: 0
                maxLength: 64
                description: 目标类型 |
                  dns  - dns
                  lbs - lbs
                  ispBGPs - ispBGPs
                  jdIPv6s - jdIPv6s
                  interPriOverlaps - interPriOverlaps
                  inats - inats
                  local - local
                  ark - ark
                  container - container
                  sdnDRVR - sdnDRVR
                  sdnVRDR - sdnVRDR
                  sdnVRVM - sdnVRVM
                  sdnDRSW - sdnDRSW
        - name: page_no
          in: query
          type: integer
          minimum: -1
          maximum: 16777216
          required: true
          description: "页码；默认为1"
        - name: page_size
          in: query
          type: integer
          minimum: 1
          maximum: 16777216
          required: true
          description: "分页大小；默认为20；取值范围[10, 100]"
      responses:
        200:
          description: list of global network plan
          schema:
            type: object
            required:
              - result
            properties:
              result:
                $ref: "#/definitions/describe_network_plans_response"

        default:
          description: detail error response
          schema:
            type: object
            required:
              - error
            properties:
              error:
                $ref: "#/definitions/error"

    post:
      description: 添加网络规划信息
      summary: createNetworkPlans
      operationId: createNetworkPlans
      tags:
        - network
      parameters:
        - name: plans
          in: body
          schema:
            type: array
            items:
              required:
                - target
              $ref: "#/definitions/network_plan"
      responses:
        200:
          description: create network plan success
          schema:
            type: object
            properties:
              ids:
                type: array
                items:
                  type: string
        default:
          description: create error response
          schema:
            type: object
            required:
              - error
            properties:
              error:
                $ref: "#/definitions/error"

    put:
      description: 校验网络规划信息
      summary: validateNetworkPlans
      operationId: validateNetworkPlans
      tags:
        - network
      parameters:
        - name: plans
          in: body
          schema:
            type: array
            items:
              required:
                - target
              $ref: "#/definitions/network_plan"
      responses:
        200:
          description: validate network plan success
          schema:
            type: object
            required:
              - result
            properties:
              result:
                $ref: "#/definitions/validate_network_plans_response"
        default:
          description: create error response
          schema:
            type: object
            required:
              - error
            properties:
              error:
                $ref: "#/definitions/error"

  /v1/network_plans/{id}:
    get:
      description: 获取网络规划
      summary: describeNetworkPlanDetail
      operationId: describeNetworkPlanDetail
      tags:
        - network
      parameters:
        - name: id
          in: path
          required: true
          type: string
          minLength: 0
          maxLength: 32
      responses:
        200:
          description: detail of network plan
          schema:
            type: object
            required:
              - result
            properties:
              result:
                $ref: "#/definitions/describe_network_plan_detail_response"
        default:
          description: detail error response
          schema:
            type: object
            required:
              - error
            properties:
              error:
                $ref: "#/definitions/error"

    put:
      description: 修改网络规划信息
      summary: updateNetworkPlan
      operationId: updateNetworkPlan
      tags:
        - network
      parameters:
        - name: id
          in: path
          required: true
          type: string
          minLength: 0
          maxLength: 32
        - name: plan
          in: body
          schema:
            required:
              - target
            $ref: "#/definitions/network_plan"
      responses:
        200:
          description: update of network plan
          schema:
            type: object
            required:
              - result
            properties:
              result:
                type: object
        default:
          description: update error response
          schema:
            type: object
            required:
              - error
            properties:
              error:
                $ref: "#/definitions/error"

    delete:
      description: 删除网络规划信息
      summary: deleteNetworkPlan
      operationId: deleteNetworkPlan
      tags:
        - network
      parameters:
        - name: id
          in: path
          required: true
          type: string
          minLength: 0
          maxLength: 32
      responses:
        200:
          description: delete of network plan success
          schema:
            type: object
            required:
              - result
            properties:
              result:
                type: object
        default:
          description: delete error response
          schema:
            type: object
            required:
              - error
            properties:
              error:
                $ref: "#/definitions/error"

  /v1/bgpAsNumberDescs:
    get:
      description: 获取BGPAsNumber网络规划的描述列表
      summary: describeBgpAsNumberDescs
      operationId: describeBgpAsNumberDescs
      tags:
        - network
      responses:
        200:
          description: list of bgp as number desc
          schema:
            type: object
            required:
              - result
            properties:
              result:
                $ref: "#/definitions/describe_bgp_as_number_descs_response"
        default:
          description: detail error response
          schema:
            type: object
            required:
              - error
            properties:
              error:
                $ref: "#/definitions/error"

  /v1/device/{id}/conn:
    get:
      description: 获取设备的连接信息
      summary: describeDeviceConn
      operationId: describeDeviceConn
      tags:
        - device
      parameters:
        - name: id
          in: path
          type: string
          required: true
          description: "设备ID"
      responses:
        200:
          description: device connect info
          schema:
            type: object
            required:
              - result
            properties:
              result:
                $ref: "#/definitions/describe_device_conn_response"
        default:
          description: detail error response
          schema:
            type: object
            required:
              - error
            properties:
              error:
                $ref: "#/definitions/error"

    post:
      description: 创建设备的连接信息
      summary: createDeviceConn
      operationId: createDeviceConn
      tags:
        - device
      parameters:
        - name: id
          in: path
          type: string
          required: true
          description: "设备ID"
        - name: conn
          in: body
          schema:
            $ref: "#/definitions/device_conn"
      responses:
        200:
          description: create device conn success
          schema:
            type: object
            required:
              - result
            properties:
              result:
                type: object
        default:
          description: create error response
          schema:
            type: object
            required:
              - error
            properties:
              error:
                $ref: "#/definitions/error"

    put:
      description: 修改设备连接信息
      summary: updateDeviceConn
      operationId: updateDeviceConn
      tags:
        - device
      parameters:
        - name: id
          in: path
          required: true
          type: string
          minLength: 0
          maxLength: 32
          description: "设备ID"
        - name: conn
          in: body
          schema:
            $ref: "#/definitions/device_conn"
      responses:
        200:
          description: update of device conn
          schema:
            type: object
            required:
              - result
            properties:
              result:
                type: object
        default:
          description: update error response
          schema:
            type: object
            required:
              - error
            properties:
              error:
                $ref: "#/definitions/error"

  /v1/network/topology:
    get:
      description: 获取网络拓扑信息
      summary: describeNetworkTopology
      operationId: describeNetworkTopology
      tags:
        - network_topology
      parameters:
        - name: network_id
          in: query
          type: string
          required: false
          description: "设备ID"
        - name: region
          in: query
          type: string
          required: false
          description: "region"
        - name: az
          in: query
          type: string
          required: false
          description: "az"
      responses:
        200:
          description: device network topology info
          schema:
            type: object
            required:
              - result
            properties:
              result:
                $ref: "#/definitions/describe_network_topo_response"
        default:
          description: detail error response
          schema:
            type: object
            required:
              - error
            properties:
              error:
                $ref: "#/definitions/error"

  /v1/network/topology:sync:
    post:
      description: 异步的同步网络拓扑信息
      summary: syncNetworkTopology
      operationId: syncNetworkTopology
      tags:
        - network_topology
      responses:
        200:
          description: sync network topology success
          schema:
            type: object
            required:
              - result
            properties:
              result:
                type: object
        default:
          description: sync network topology error response
          schema:
            type: object
            required:
              - error
            properties:
              error:
                $ref: "#/definitions/error"


  /v1/network/topology:async:
    post:
      description: 同步的同步网络拓扑信息
      summary: asyncNetworkTopology
      operationId: asyncNetworkTopology
      tags:
        - network_topology
      parameters:
        - name: network_ips
          description: "设备IP"
          in: body
          schema:
            type: array
            items:
              type: string
      responses:
        200:
          description: async network topology success
          schema:
            type: object
            required:
              - result
            properties:
              result:
                type: object
                properties:
                  code:
                    type: integer
                    format: int32
                  message:
                    type: string
        default:
          description: async network topology error response
          schema:
            type: object
            required:
              - error
            properties:
              error:
                $ref: "#/definitions/error"

  /v1/cmdb/{resId}/logs:
    get:
      description: 获取CMDB资源操作列表
      summary: describeCmdbLogs
      operationId: describeCmdbLogs
      tags:
        - cmdb
      parameters:
        - name: resId
          in: path
          required: true
          type: string
        - name: page_no
          in: query
          type: integer
          minimum: -1
          maximum: 16777216
          required: true
          description: "页码；默认为1"
        - name: page_size
          in: query
          type: integer
          minimum: 1
          maximum: 16777216
          required: true
          description: "分页大小；默认为20；取值范围[10, 100]"
      responses:
        200:
          description: list of cmdn op log
          schema:
            type: object
            required:
              - result
            properties:
              result:
                $ref: "#/definitions/describe_cmdb_op_log_response"
        default:
          description: detail error response
          schema:
            type: object
            required:
              - error
            properties:
              error:
                $ref: "#/definitions/error"

  /v1/serverRoleSummary:
    get:
      description: 获取按照角色统计的资源信息
      summary: describeServerRoleSummary
      operationId: describeServerRoleSummary
      tags:
        - server_role
      parameters:
        - name: region
          in: query
          required: false
          type: string
        - name: az
          in: query
          required: false
          type: string
        - name: category
          in: query
          required: false
          type: string
        - name: device_type
          in: query
          required: false
          type: string
      responses:
        200:
          description: list of role summary
          schema:
            type: object
            required:
              - result
            properties:
              result:
                $ref: "#/definitions/describe_role_summary_response"
        default:
          description: detail error response
          schema:
            type: object
            required:
              - error
            properties:
              error:
                $ref: "#/definitions/error"

  /v1/resource_role_metadata:
    get:
      description: 查询产品服务与角色映射列表
      summary: describeResourceRoleMetadata
      operationId: describeResourceRoleMetadata
      tags:
        - resource_role
      parameters:
        - name: serviceCode
          in: query
          required: false
          type: string
          minLength: 0
          maxLength: 64
        - name: resourceCode
          in: query
          required: false
          type: string
          minLength: 0
          maxLength: 64
        - name: role
          in: query
          required: false
          type: string
          minLength: 0
          maxLength: 64
        - name: metadata_key
          in: query
          required: false
          type: string
          minLength: 0
          maxLength: 64
        - name: metadata_value
          in: query
          required: false
          type: string
          minLength: 0
          maxLength: 64
      responses:
        200:
          description: describe of resource_role_metadata
          schema:
            type: object
            required:
              - result
            properties:
              result:
                $ref: "#/definitions/resource_role_metadata_response"
        default:
          description: describe server  error response
          schema:
            type: object
            required:
              - error
            properties:
              error:
                $ref: "#/definitions/error"

  /v1/device_group:
    get:
      description: 获取设备组列表
      summary: describeDeviceGroups
      operationId: describeDeviceGroups
      tags:
        - device_group
      parameters:
        - name: condition
          in: body
          schema:
            properties:
              region:
                type: string
                minLength: 0
                maxLength: 32
              group_id_like:
                type: string
                minLength: 0
                maxLength: 32
              group_name_like:
                type: string
                minLength: 0
                maxLength: 32
              group_type:
                type: string
                minLength: 0
                maxLength: 32
              device_type:
                type: string
                minLength: 0
                maxLength: 32
              sorts:
                type: array
                items:
                  $ref: "#/definitions/Sort"
        - name: page_size
          in: query
          required: true
          type: integer
          format: int
          minimum: 1
          maximum: 16777216
        - name: page_no
          in: query
          required: true
          type: integer
          format: int
          minimum: -1
          maximum: 16777216

      responses:
        200:
          description: list of device group
          schema:
            type: object
            required:
              - result
            properties:
              result:
                $ref: "#/definitions/describe_device_groups_response"
        default:
          description: detail error response
          schema:
            type: object
            required:
              - error
            properties:
              error:
                $ref: "#/definitions/error"
    post:
      description: 创建设备组
      summary: createDeviceGroups
      operationId: createDeviceGroups
      tags:
        - device_group
      parameters:
        - name: groups
          in: body
          schema:
            type: array
            items:
              $ref: "#/definitions/device_group"
      responses:
        200:
          description: create device_group
          schema:
            type: object
            properties:
              ids:
                type: array
                items:
                  type: string
        default:
          description: create error response
          schema:
            type: object
            required:
              - error
            properties:
              error:
                $ref: "#/definitions/error"

  /v1/device_group/{group_id}:
    get:
      description: 获取设备组详情
      summary: describeDeviceGroup
      operationId: describeDeviceGroup
      tags:
        - device_group
      parameters:
        - name: group_id
          in: path
          required: true
          type: string
          minLength: 0
          maxLength: 32
      responses:
        200:
          description: detail of device_group
          schema:
            type: object
            required:
              - result
            properties:
              result:
                $ref: "#/definitions/device_group"
        default:
          description: detail error response
          schema:
            type: object
            required:
              - error
            properties:
              error:
                $ref: "#/definitions/error"
    put:
      description: 修改设备组信息
      summary: updateDeviceGroup
      operationId: updateDeviceGroup
      tags:
        - device_group
      parameters:
        - name: group_id
          in: path
          required: true
          type: string
          minLength: 0
          maxLength: 32
        - name: body
          in: body
          schema:
            properties:
              region:
                type: string
                minLength: 0
                maxLength: 32
                x-nullable: false
              group_name:
                type: string
                minLength: 0
                maxLength: 32
                x-nullable: false
              group_type:
                type: string
                minLength: 0
                maxLength: 32
                x-nullable: false
              device_type:
                type: string
                minLength: 0
                maxLength: 32
                x-nullable: false
              devices:
                type: array
                x-nullable: false
                items:
                  $ref: "#/definitions/device_group_rel_res"
              description:
                type: string
                minLength: 0
                maxLength: 32
      responses:
        200:
          description: update of device_group
          schema:
            type: object
            required:
              - result
            properties:
              result:
                type: object
        default:
          description: update error response
          schema:
            type: object
            required:
              - error
            properties:
              error:
                $ref: "#/definitions/error"

    delete:
      description: 删除设备组
      summary: deleteDeviceGroup
      operationId: deleteDeviceGroup
      tags:
        - device_group
      parameters:
        - name: group_id
          in: path
          required: true
          type: string
          minLength: 0
          maxLength: 32
      responses:
        200:
          description: delete of device_group
          schema:
            type: object
            required:
              - result
            properties:
              result:
                type: object
        default:
          description: delete error response
          schema:
            type: object
            required:
              - error
            properties:
              error:
                $ref: "#/definitions/error"

  /v1/device_group/{group_id}/ports:
    get:
      description: 获取设备组端口列表
      summary: describeDeviceGroupPorts
      operationId: describeDeviceGroupPorts
      tags:
        - device_group
      parameters:
        - name: group_id
          in: path
          required: true
          type: string
          minLength: 0
          maxLength: 32
      responses:
        200:
          description: detail of device_group port
          schema:
            type: object
            required:
              - result
            properties:
              result:
                $ref: "#/definitions/describe_device_group_ports_response"
        default:
          description: detail error response
          schema:
            type: object
            required:
              - error
            properties:
              error:
                $ref: "#/definitions/error"

  /v1/phy_direct_conn:
    get:
      description: 获取物理网络专线列表
      summary: describesPhyDirectConn
      operationId: describesPhyDirectConn
      tags:
        - phy_direct_conn
      parameters:
        - name: condition
          in: body
          schema:
            properties:
              uuid:
                type: array
                items:
                  type: string
                  minLength: 0
                  maxLength: 64
              uuid_like:
                type: string
                minLength: 0
                maxLength: 64
              region:
                type: string
                minLength: 0
                maxLength: 64
              name_like:
                type: string
                minLength: 0
                maxLength: 64
              conn_type:
                type: array
                items:
                  type: integer
                  format: int32
                  minimum: 0
                  maximum: 16777216
                  x-omitempty: false
              deploy_type:
                type: array
                items:
                  type: integer
                  format: int32
                  minimum: 0
                  maximum: 16777216
                  x-omitempty: false
              conn_state:
                type: array
                items:
                  type: integer
                  format: int32
                  minimum: 0
                  maximum: 16777216
                  x-omitempty: false
              status:
                type: array
                items:
                  type: integer
                  format: int32
                  minimum: 0
                  maximum: 16777216
                  x-omitempty: false
              sorts:
                type: array
                items:
                  $ref: "#/definitions/Sort"
        - name: page_size
          in: query
          required: true
          type: integer
          format: int
          minimum: 1
          maximum: 16777216
        - name: page_no
          in: query
          required: true
          type: integer
          format: int
          minimum: -1
          maximum: 16777216
      responses:
        200:
          description: list of phy direct connect
          schema:
            type: object
            required:
              - result
            properties:
              result:
                $ref: "#/definitions/describe_phy_direct_connects_response"
        default:
          description: detail error response
          schema:
            type: object
            required:
              - error
            properties:
              error:
                $ref: "#/definitions/error"
    post:
      description: 创建物理专线
      summary: createPhyDirectConns
      operationId: createPhyDirectConns
      tags:
        - phy_direct_conn
      parameters:
        - name: phy_direct_conns
          in: body
          schema:
            type: array
            items:
              $ref: "#/definitions/phy_direct_connect"
      responses:
        200:
          description: create phy_direct_conn
          schema:
            type: object
            properties:
              ids:
                type: array
                items:
                  type: string
        default:
          description: create error response
          schema:
            type: object
            required:
              - error
            properties:
              error:
                $ref: "#/definitions/error"

  /v1/phy_direct_conn:refurbishState:
    post:
      description: 触发物理网络专线数据更新
      summary: refurbishPhyDirectConnState
      operationId: refurbishPhyDirectConnState
      tags:
        - phy_direct_conn
      responses:
        200:
          description: refurbish state phy_direct_conn
          schema:
            type: object
            required:
              - result
            properties:
              result:
                type: object
        default:
          description: refurbish state error response
          schema:
            type: object
            required:
              - error
            properties:
              error:
                $ref: "#/definitions/error"

  /v1/phy_direct_conn/{pdc_id}:
    get:
      description: 获取物理专线详情
      summary: describePhyDirectConn
      operationId: describePhyDirectConn
      tags:
        - phy_direct_conn
      parameters:
        - name: pdc_id
          in: path
          required: true
          type: string
          minLength: 0
          maxLength: 32
      responses:
        200:
          description: list of phy direct connect conf
          schema:
            type: object
            required:
              - result
            properties:
              result:
                $ref: "#/definitions/describe_phy_direct_connect_response"
        default:
          description: detail error response
          schema:
            type: object
            required:
              - error
            properties:
              error:
                $ref: "#/definitions/error"
    put:
      description: 修改物理专线
      summary: updatePhyDirectConn
      operationId: updatePhyDirectConn
      tags:
        - phy_direct_conn
      parameters:
        - name: pdc_id
          in: path
          required: true
          type: string
          minLength: 0
          maxLength: 32
        - name: body
          in: body
          schema:
            properties:
              description:
                type: string
                minLength: 0
                maxLength: 256
                x-omitempty: false
      responses:
        200:
          description: create phy_direct_conn
          schema:
            type: object
            required:
              - result
            properties:
              result:
                type: object
        default:
          description: create error response
          schema:
            type: object
            required:
              - error
            properties:
              error:
                $ref: "#/definitions/error"

    delete:
      description: 删除专线
      summary: deletePhyDirectConn
      operationId: deletePhyDirectConn
      tags:
        - phy_direct_conn
      parameters:
        - name: pdc_id
          in: path
          required: true
          type: string
          minLength: 0
          maxLength: 32
      responses:
        200:
          description: delete of phy_direct_conn
          schema:
            type: object
            required:
              - result
            properties:
              result:
                type: object
        default:
          description: delete error response
          schema:
            type: object
            required:
              - error
            properties:
              error:
                $ref: "#/definitions/error"

  /v1/phy_direct_conn/{pdc_id}/lines:
    get:
      description: 获取网络专线连接配置
      summary: describePhyDirectConnLines
      operationId: describePhyDirectConnLines
      tags:
        - phy_direct_conn
      parameters:
        - name: pdc_id
          in: path
          required: true
          type: string
          minLength: 0
          maxLength: 32
        - name: condition
          in: body
          schema:
            properties:
              group_id:
                type: string
                minLength: 0
                maxLength: 32
        - name: page_size
          in: query
          required: true
          type: integer
          format: int
          minimum: 1
          maximum: 16777216
        - name: page_no
          in: query
          required: true
          type: integer
          format: int
          minimum: -1
          maximum: 16777216
      responses:
        200:
          description: list of phy direct connect
          schema:
            type: object
            required:
              - result
            properties:
              result:
                $ref: "#/definitions/describe_phy_direct_connect_line_response"
        default:
          description: detail error response
          schema:
            type: object
            required:
              - error
            properties:
              error:
                $ref: "#/definitions/error"
    post:
      description: 创建物理专线连接配置
      summary: savePhyDirectConnLines
      operationId: savePhyDirectConnLines
      tags:
        - phy_direct_conn
      parameters:
        - name: pdc_id
          in: path
          required: true
          type: string
          minLength: 0
          maxLength: 32
        - name: phy_direct_conn_lines
          in: body
          schema:
            type: array
            items:
              $ref: "#/definitions/phy_direct_connect_line"
      responses:
        200:
          description: create phy_direct_conn lines
          schema:
            type: object
            required:
              - result
            properties:
              result:
                type: object
        default:
          description: create error response
          schema:
            type: object
            required:
              - error
            properties:
              error:
                $ref: "#/definitions/error"

  /v1/phy_direct_conn/{pdc_id}/segment:
    get:
      description: 获取网络专线关联网段列表
      summary: describePhyDirectConnUserSegments
      operationId: describePhyDirectConnUserSegments
      tags:
        - phy_direct_conn
      parameters:
        - name: pdc_id
          in: path
          required: true
          type: string
          minLength: 0
          maxLength: 32
        - name: condition
          in: body
          schema:
            properties:
              pin:
                type: string
                minLength: 0
                maxLength: 32
        - name: page_size
          in: query
          required: true
          type: integer
          format: int
          minimum: 1
          maximum: 16777216
        - name: page_no
          in: query
          required: true
          type: integer
          format: int
          minimum: -1
          maximum: 16777216
      responses:
        200:
          description: list of phy direct connect
          schema:
            type: object
            required:
              - result
            properties:
              result:
                $ref: "#/definitions/describe_phy_direct_connect_segments_response"
        default:
          description: detail error response
          schema:
            type: object
            required:
              - error
            properties:
              error:
                $ref: "#/definitions/error"
    post:
      description: 保存物理专线关联网段
      summary: savePhyDirectConnUserSegments
      operationId: savePhyDirectConnUserSegments
      tags:
        - phy_direct_conn
      parameters:
        - name: pdc_id
          in: path
          required: true
          type: string
          minLength: 0
          maxLength: 32
        - name: phy_direct_conn_user_segments
          in: body
          schema:
            type: array
            items:
              $ref: "#/definitions/phy_direct_connect_user_segment"
      responses:
        200:
          description: create phy_direct_conn segment
          schema:
            type: object
            required:
              - result
            properties:
              result:
                type: object
        default:
          description: create error response
          schema:
            type: object
            required:
              - error
            properties:
              error:
                $ref: "#/definitions/error"

  /v1/phy_direct_conn/{pdc_id}/topology:
    get:
      description: 获取网络专线拓扑信息
      summary: describePhyDirectConnTopology
      operationId: describePhyDirectConnTopology
      tags:
        - phy_direct_conn
      parameters:
        - name: pdc_id
          in: path
          required: true
          type: string
          minLength: 0
          maxLength: 32
      responses:
        200:
          description: data of phy direct connect topology
          schema:
            type: object
            required:
              - result
            properties:
              result:
                $ref: "#/definitions/describe_phy_direct_connect_topology_response"
        default:
          description: detail error response
          schema:
            type: object
            required:
              - error
            properties:
              error:
                $ref: "#/definitions/error"

  /v1/phy_direct_conn/{pdc_id}/status:
    put:
      description: 修改物理专线状态
      summary: updatePhyDirectConnStatus
      operationId: updatePhyDirectConnStatus
      tags:
        - phy_direct_conn
      parameters:
        - name: pdc_id
          in: path
          required: true
          type: string
          minLength: 0
          maxLength: 32
        - name: body
          in: body
          schema:
            properties:
              status:
                type: integer
                format: int32
                minimum: 0
                maximum: 16777216
                x-omitempty: false
      responses:
        200:
          description: update phy_direct_conn status
          schema:
            type: object
            required:
              - result
            properties:
              result:
                type: object
        default:
          description: update error response
          schema:
            type: object
            required:
              - error
            properties:
              error:
                $ref: "#/definitions/error"

  /v1/classic_link_pool:
    get:
      description: 获取网络互通资源池列表
      summary: describeClassicLinkPools
      operationId: describeClassicLinkPools
      tags:
        - classic_link_pool
      parameters:
        - name: page_size
          in: query
          required: true
          type: integer
          format: int
          minimum: 1
          maximum: 16777216
        - name: page_no
          in: query
          required: true
          type: integer
          format: int
          minimum: -1
          maximum: 16777216

      responses:
        200:
          description: list of classic link pool
          schema:
            type: object
            required:
              - result
            properties:
              result:
                $ref: "#/definitions/describe_classic_link_pools_response"
        default:
          description: detail error response
          schema:
            type: object
            required:
              - error
            properties:
              error:
                $ref: "#/definitions/error"
    post:
      description: 创建网络互通资源池
      summary: createClassicLinkPool
      operationId: createClassicLinkPool
      tags:
        - classic_link_pool
      parameters:
        - name: body
          in: body
          schema:
            type: object
            required:
              - cidr0
              - cidr1
              - bgp_asn_start
              - bgp_asn_end
              - vlan_start
              - vlan_end
            properties:
              pool_id:
                type: string
                minLength: 0
                maxLength: 64
                pattern: ^([A-Za-z0-9-_]{1,64})$
                x-go-custom-tag: db:"pool_id"
                x-nullable: true
              bgp_asn_end:
                type: integer
                format: int64
                minimum: 65001
                maximum: 65500
                description: BGP ASN结束始端口 范围(65001~65500)
              bgp_asn_start:
                type: integer
                format: int64
                minimum: 65001
                maximum: 65500
                description: BGP ASN开始端口 范围(65001~65500)
              cidr0:
                type: string
                description: 网络互通网段0
                pattern: ^(?:(?:[0-9]|[1-9][0-9]|1[0-9]{2}|2[0-4][0-9]|25[0-5])\.){3}(?:[0-9]|[1-9][0-9]|1[0-9]{2}|2[0-4][0-9]|25[0-5])\/([1-9]|[1-2]\d|3[0-2])$
              cidr1:
                type: string
                description: 网络互通网段1
                pattern: ^(?:(?:[0-9]|[1-9][0-9]|1[0-9]{2}|2[0-4][0-9]|25[0-5])\.){3}(?:[0-9]|[1-9][0-9]|1[0-9]{2}|2[0-4][0-9]|25[0-5])\/([1-9]|[1-2]\d|3[0-2])$
              vlan_end:
                type: integer
                format: int64
                minimum: 1
                maximum: 4094
                description: VLAN结束始端口 范围(1~4094)
              vlan_start:
                type: integer
                format: int64
                minimum: 1
                maximum: 4094
                description: VLAN开始始端口 范围(1~4094)
      responses:
        200:
          description: create device_group
          schema:
            type: object
            required:
              - result
            properties:
              result:
                type: object
                properties:
                  pool_id:
                    type: string
                    minLength: 0
                    maxLength: 64
        default:
          description: create error response
          schema:
            type: object
            required:
              - error
            properties:
              error:
                $ref: "#/definitions/error"
  /v1/classic_link_pool_id:
    get:
      description: 获取网络互通资源池ID列表
      summary: describeClassicLinkPoolIds
      operationId: describeClassicLinkPoolIds
      tags:
        - classic_link_pool
      responses:
        200:
          description: list of classic link pool
          schema:
            type: object
            required:
              - result
            properties:
              result:
                $ref: "#/definitions/describe_classic_link_pool_ids_response"
        default:
          description: detail error response
          schema:
            type: object
            required:
              - error
            properties:
              error:
                $ref: "#/definitions/error"
  /v1/classic_link_pool/{pool_id}:
    get:
      description: 获取网络互通资源池详情
      summary: describeClassicLinkPool
      operationId: describeClassicLinkPool
      tags:
        - classic_link_pool
      parameters:
        - name: pool_id
          in: path
          required: true
          type: string
          minLength: 0
          maxLength: 64
      responses:
        200:
          description: detail of classic link pool
          schema:
            type: object
            required:
              - result
            properties:
              result:
                $ref: "#/definitions/classic_link_pool"
        default:
          description: detail error response
          schema:
            type: object
            required:
              - error
            properties:
              error:
                $ref: "#/definitions/error"
    put:
      description: 修改网络互通资源池信息
      summary: updateClassicLinkPool
      operationId: updateClassicLinkPool
      tags:
        - classic_link_pool
      parameters:
        - name: pool_id
          in: path
          required: true
          type: string
          minLength: 0
          maxLength: 64
        - name: body
          in: body
          schema:
            type: object
            required:
              - cidr0
              - cidr1
              - bgp_asn_start
              - bgp_asn_end
              - vlan_start
              - vlan_end
            properties:
              bgp_asn_end:
                type: integer
                format: int64
                minimum: 65000
                maximum: 65500
                description: BGP ASN结束始端口 范围(65001~65500)
              bgp_asn_start:
                type: integer
                format: int64
                minimum: 65000
                maximum: 65500
                description: BGP ASN开始端口 范围(65001~65500)
              cidr0:
                type: string
                description: 网络互通网段0
                pattern: ^(?:(?:[0-9]|[1-9][0-9]|1[0-9]{2}|2[0-4][0-9]|25[0-5])\.){3}(?:[0-9]|[1-9][0-9]|1[0-9]{2}|2[0-4][0-9]|25[0-5])\/([1-9]|[1-2]\d|3[0-2])$
              cidr1:
                type: string
                description: 网络互通网段1
                pattern: ^(?:(?:[0-9]|[1-9][0-9]|1[0-9]{2}|2[0-4][0-9]|25[0-5])\.){3}(?:[0-9]|[1-9][0-9]|1[0-9]{2}|2[0-4][0-9]|25[0-5])\/([1-9]|[1-2]\d|3[0-2])$
              vlan_end:
                type: integer
                format: int64
                minimum: 1
                maximum: 4094
                description: VLAN结束始端口 范围(1~4094)
              vlan_start:
                type: integer
                format: int64
                minimum: 1
                maximum: 4094
                description: VLAN开始始端口 范围(1~4094)
      responses:
        200:
          description: update of device_group
          schema:
            type: object
            required:
              - result
            properties:
              result:
                type: object
        default:
          description: update error response
          schema:
            type: object
            required:
              - error
            properties:
              error:
                $ref: "#/definitions/error"
    delete:
      description: 删除网络互通资源池
      summary: deleteClassicLinkPool
      operationId: deleteClassicLinkPool
      tags:
        - classic_link_pool
      parameters:
        - name: pool_id
          in: path
          required: true
          type: string
          minLength: 0
          maxLength: 64
      responses:
        200:
          description: delete of classic link pool
          schema:
            type: object
            required:
              - result
            properties:
              result:
                type: object
        default:
          description: delete error response
          schema:
            type: object
            required:
              - error
            properties:
              error:
                $ref: "#/definitions/error"

  /v1/network_interface:
    get:
      description: 获取网络设备的端口列表
      summary: describeNetworkInterfaces
      operationId: describeNetworkInterfaces
      tags:
        - network
      parameters:
        - name: device_ip
          in: query
          required: false
          type: string
        - name: port_name
          in: query
          required: false
          type: string
      responses:
        200:
          description: list of network interface
          schema:
            type: object
            required:
              - result
            properties:
              result:
                $ref: "#/definitions/describe_network_interfaces_response"
        default:
          description: detail error response
          schema:
            type: object
            required:
              - error
            properties:
              error:
                $ref: "#/definitions/error"

  /v1/cmdb_view:
    get:
      description: 获取CMDB视图
      summary: describeCmdbView
      operationId: describeCmdbView
      tags:
        - cmdb
      parameters:
        - name: condition
          in: body
          schema:
            required:
              - region
            properties:
              region:
                type: string
                minLength: 0
                maxLength: 32
              az:
                type: string
                minLength: 0
                maxLength: 32
              service_code:
                type: string
                minLength: 0
                maxLength: 32
              resource_code:
                type: string
                minLength: 0
                maxLength: 32
              host_ip:
                type: string
                minLength: 0
                maxLength: 32
              role:
                type: string
                minLength: 0
                maxLength: 32
              category:
                type: string
                minLength: 0
                maxLength: 32
      responses:
        200:
          description: list of cmdb view
          schema:
            type: object
            required:
              - result
            properties:
              result:
                $ref: "#/definitions/describe_cmdb_view_response"
        default:
          description: detail error response
          schema:
            type: object
            required:
              - error
            properties:
              error:
                $ref: "#/definitions/error"

definitions:
  describe_server_allroles_response:
    type: object
    properties:
      server_roles:
        type: array
        items:
          $ref: "#/definitions/server_all_role"

  server_role_serial_number_list_response:
    type: object
    properties:
      list:
        type: array
        items:
          type: string

  server_all_role:
    type: object
    properties:
      role:
        type: string
        x-go-custom-tag: db:"role"
        x-nullable: false
      device_type:
        type: string
        x-go-custom-tag: db:"device_type"
        x-nullable: false
      metadata_key:
        type: string
        x-go-custom-tag: db:"metadata_key"
        x-nullable: true
      metadata_values:
        type: string
        x-go-custom-tag: db:"metadata_values"
        x-nullable: true
      category:
        type: string

  server_role:
    type: object
    properties:
      serverId:
        type: string
        x-go-custom-tag: db:"uuid"
        x-nullable: false
      role:
        type: string
        x-go-custom-tag: db:"role"
        x-nullable: false
      metadata_key:
        type: string
        x-go-custom-tag: db:"metadata_key"
        x-nullable: true
      metadata_value:
        type: string
        x-go-custom-tag: db:"metadata_value"
        x-nullable: true

  server_roles_response:
    type: object
    required:
      - server_roles
    properties:
      server_roles:
        type: array
        items:
          $ref: "#/definitions/server_role"

  resource_role_metadata:
    type: object
    properties:
      release_version:
        type: string
        x-nullable: false
      category:
        type: string
        x-nullable: false
      service_code:
        type: string
        x-nullable: false
      resource_code:
        type: string
        x-nullable: false
      role:
        type: string
        x-nullable: false
      metadata_key:
        type: string
        x-nullable: true
      metadata_value:
        type: string
        x-nullable: true

  resource_role_metadata_response:
    type: object
    required:
      - resource_roles
    properties:
      resource_roles:
        type: array
        items:
          $ref: "#/definitions/resource_role_metadata"

  error:
    type: object
    required:
      - code
      - message
    properties:
      code:
        description: 错误码
        type: integer
        format: int32
        minimum: 0
        maximum: 16777216
      status:
        description: 错误码的描述
        type: string
        minLength: 0
        maxLength: 64
      message:
        description: 错误信息描述
        type: string
        minLength: 0
        maxLength: 64
  create_response:
    type: object
  update_response:
    type: object
  delete_response:
    type: object

  available_zone:
    type: object
    properties:
      uuid:
        description: uuid
        type: string
        minLength: 0
        maxLength: 32
        x-go-custom-tag: db:"uuid"
        x-nullable: false
      region_uuid:
        description: region uuid
        type: string
        minLength: 0
        maxLength: 32
        x-go-custom-tag: db:"region_uuid"
        x-nullable: false
      name:
        description: 可用区名称
        type: string
        minLength: 0
        maxLength: 64
        x-go-custom-tag: db:"name"
        x-nullable: false
      use_way:
        description: region 用途
        type: string
        minLength: 0
        maxLength: 64
        x-go-custom-tag: db:"use_way"
        x-nullable: true
      cn_name:
        description: az中文名称
        type: string
        minLength: 0
        maxLength: 64
        x-go-custom-tag: db:"cn_name"
        x-nullable: false
      is_default:
        description: az名称
        type: string
        minLength: 0
        maxLength: 32
        x-go-custom-tag: db:"is_default"
        x-nullable: false
      status:
        description: 状态
        type: string
        minLength: 0
        maxLength: 32
        x-go-custom-tag: db:"status"
        x-nullable: false
      description:
        description: 描述
        type: string
        minLength: 0
        maxLength: 128
        x-go-custom-tag: db:"description"
        x-nullable: true
      created_at:
        description: 创建时间
        type: string
        format: dateTime
        x-go-custom-tag: db:"created_at,omitempty"
        x-nullable: false
      updated_at:
        description: 更新时间
        type: string
        format: dateTime
        x-go-custom-tag: db:"updated_at,omitempty"
        x-nullable: false
  describe_available_zone:
    type: object
    properties:
      uuid:
        description: uuid
        type: string
        minLength: 0
        maxLength: 32
        x-go-custom-tag: db:"uuid"
        x-nullable: false
      region_uuid:
        description: region uuid
        type: string
        minLength: 0
        maxLength: 32
        x-go-custom-tag: db:"region_uuid"
        x-nullable: false
      region_name:
        description: region uuid
        type: string
        minLength: 0
        maxLength: 32
        x-go-custom-tag: db:"region_name"
        x-nullable: false
      name:
        description: 可用区名称
        type: string
        minLength: 0
        maxLength: 64
        x-go-custom-tag: db:"name"
        x-nullable: false
      use_way:
        description: az 用途
        type: string
        minLength: 0
        maxLength: 64
        x-go-custom-tag: db:"use_way"
        x-nullable: true
      cn_name:
        description: az中文名称
        type: string
        minLength: 0
        maxLength: 64
        x-go-custom-tag: db:"cn_name"
        x-nullable: false
      is_default:
        description: 是否默认
        type: string
        minLength: 0
        maxLength: 32
        x-go-custom-tag: db:"is_default"
        x-nullable: false
      status:
        description: 状态
        type: string
        minLength: 0
        maxLength: 32
        x-go-custom-tag: db:"status"
        x-nullable: false
      description:
        description: 描述
        type: string
        minLength: 0
        maxLength: 128
        x-go-custom-tag: db:"description"
        x-nullable: true
      created_at:
        description: 创建时间
        type: string
        format: dateTime
        x-go-custom-tag: db:"created_at,omitempty"
        x-nullable: false
      updated_at:
        description: 更新时间
        type: string
        format: dateTime
        x-go-custom-tag: db:"updated_at,omitempty"
        x-nullable: false
  available_zone_list:
    type: object
    properties:
      uuid:
        description: uuid
        type: string
        minLength: 0
        maxLength: 32
        x-go-custom-tag: db:"uuid"
        x-nullable: false
      name:
        description: 可用区名称
        type: string
        minLength: 0
        maxLength: 64
        x-go-custom-tag: db:"name"
        x-nullable: false
  available_zone_response:
    type: object
    required:
      - available_zone
    properties:
      available_zone:
        $ref: "#/definitions/describe_available_zone"
  available_zones_response:
    type: object
    required:
      - available_zones
    properties:
      available_zones:
        type: array
        items:
          $ref: "#/definitions/describe_available_zone"
      total_count:
        type: integer
        format: int32
        minimum: 0
        maximum: 16777216
        x-go-custom-tag: db:"total_count"
        x-nullable: false
  available_zone_in_response:
    type: object
    required:
      - available_zones
    properties:
      available_zones:
        type: array
        items:
          $ref: "#/definitions/available_zone_list"
  datacenter:
    type: object
    properties:
      uuid:
        description: uuid
        type: string
        minLength: 0
        maxLength: 32
        x-go-custom-tag: db:"uuid"
        x-nullable: false
      region_uuid:
        description: region uuid
        type: string
        minLength: 0
        maxLength: 32
        x-go-custom-tag: db:"region_uuid"
        x-nullable: true
      az_uuid:
        description: az uuid
        type: string
        minLength: 0
        maxLength: 32
        x-go-custom-tag: db:"az_uuid"
        x-nullable: true
      name:
        description: 机房名
        type: string
        minLength: 0
        maxLength: 64
        x-go-custom-tag: db:"name"
        x-nullable: false
      address:
        description: 机房地址
        type: string
        minLength: 0
        maxLength: 128
        x-go-custom-tag: db:"address"
        x-nullable: true
      level:
        description: 机房级别
        type: string
        minLength: 0
        maxLength: 64
        x-go-custom-tag: db:"level"
        x-nullable: false
      provider:
        description: 运营商
        type: string
        minLength: 1
        maxLength: 64
        x-go-custom-tag: db:"provider"
        x-nullable: false
      room_count:
        description: 机房房间数量, 最小值为0，但是不能为空
        type: integer
        format: int32
        minimum: 0
        maximum: 16777216
        x-go-custom-tag: db:"room_count"
        x-nullable: true
      province:
        description: 省份
        type: string
        minLength: 1
        maxLength: 64
        x-go-custom-tag: db:"province"
        x-nullable: false
      city:
        description: 地级市
        type: string
        minLength: 1
        maxLength: 64
        x-go-custom-tag: db:"city"
        x-nullable: false
      electrical_spec:
        description: 电力规格
        type: string
        minLength: 0
        maxLength: 64
        x-go-custom-tag: db:"electrical_spec"
        x-nullable: false
      created_at:
        description: 创建时间
        type: string
        format: dateTime
        x-go-custom-tag: db:"created_at,omitempty"
      updated_at:
        description: 更新时间
        type: string
        format: dateTime
        x-go-custom-tag: db:"updated_at,omitempty"
  describe_datacenter:
    type: object
    properties:
      uuid:
        description: uuid
        type: string
        minLength: 0
        maxLength: 32
        x-go-custom-tag: db:"uuid"
        x-nullable: false
      name:
        description: 机房名
        type: string
        minLength: 0
        maxLength: 64
        x-go-custom-tag: db:"name"
        x-nullable: false
      level:
        description: 机房级别
        type: string
        minLength: 0
        maxLength: 64
        x-go-custom-tag: db:"level"
        x-nullable: false
      provider:
        description: 运营商
        type: string
        minLength: 1
        maxLength: 64
        x-go-custom-tag: db:"provider"
        x-nullable: false
      province:
        description: 省份
        type: string
        minLength: 1
        maxLength: 64
        x-go-custom-tag: db:"province"
        x-nullable: false
      city:
        description: 地级市
        type: string
        minLength: 1
        maxLength: 64
        x-go-custom-tag: db:"city"
        x-nullable: false
      address:
        description: 机房地址
        type: string
        minLength: 0
        maxLength: 128
        x-go-custom-tag: db:"address"
        x-nullable: true
      room_count:
        description: 机房房间数量, 最小值为0，但是不能为空
        type: integer
        format: int32
        minimum: 0
        maximum: 16777216
        x-go-custom-tag: db:"room_count"
        x-nullable: true
      electrical_spec:
        description: 电力规格
        type: string
        minLength: 0
        maxLength: 64
        x-go-custom-tag: db:"electrical_spec"
        x-nullable: false
      az_uuid:
        description: 所属az_uuid
        type: string
        minLength: 0
        maxLength: 64
        x-go-custom-tag: db:"az_uuid"
        x-nullable: true
      region_uuid:
        description: 所属region_uuid
        type: string
        minLength: 0
        maxLength: 64
        x-go-custom-tag: db:"region_uuid"
        x-nullable: true
  describe_detail_datacenter:
    description: 机房详情页
    type: object
    properties:
      uuid:
        description: uuid
        type: string
        minLength: 0
        maxLength: 32
        x-go-custom-tag: db:"uuid"
        x-nullable: false
      name:
        description: 机房名
        type: string
        minLength: 0
        maxLength: 64
        x-go-custom-tag: db:"name"
        x-nullable: false
      address:
        description: 机房地址
        type: string
        minLength: 0
        maxLength: 128
        x-go-custom-tag: db:"address"
        x-nullable: true
      level:
        description: 机房级别
        type: string
        minLength: 0
        maxLength: 64
        x-go-custom-tag: db:"level"
        x-nullable: false
      provider:
        description: 运营商
        type: string
        minLength: 0
        maxLength: 64
        x-go-custom-tag: db:"provider"
        x-nullable: false
      room_count:
        description: 机房房间数量, 最小值为0，但是不能为空
        type: integer
        format: int32
        minimum: 0
        maximum: 16777216
        x-go-custom-tag: db:"room_count"
        x-nullable: true
      province:
        description: 省份
        type: string
        minLength: 0
        maxLength: 64
        x-go-custom-tag: db:"province"
        x-nullable: false
      city:
        description: 地级市
        type: string
        minLength: 0
        maxLength: 64
        x-go-custom-tag: db:"city"
        x-nullable: false
      electrical_spec:
        description: 电力规格
        type: string
        minLength: 0
        maxLength: 64
        x-go-custom-tag: db:"electrical_spec"
        x-nullable: false
      created_at:
        description: 创建时间
        type: string
        format: dateTime
        x-go-custom-tag: db:"created_at,omitempty"
      updated_at:
        description: 更新时间
        type: string
        format: dateTime
        x-go-custom-tag: db:"updated_at,omitempty"
      az_uuid:
        description: 所属az_uuid
        type: string
        minLength: 0
        maxLength: 64
        x-go-custom-tag: db:"az_uuid"
        x-nullable: true
      az_name:
        description: 所属az
        type: string
        minLength: 0
        maxLength: 64
        x-go-custom-tag: db:"az_name"
        x-nullable: true
      region_uuid:
        description: 所属region_uuid
        type: string
        minLength: 0
        maxLength: 64
        x-go-custom-tag: db:"region_uuid"
        x-nullable: true
      region_name:
        description: 所属region
        type: string
        minLength: 0
        maxLength: 64
        x-go-custom-tag: db:"region_name"
        x-nullable: true
      maintainer_list:
        description: 机房管理人员信息
        type: array
        items:
          $ref: "#/definitions/maintainer"
  datacenter_list:
    type: object
    properties:
      uuid:
        description: uuid
        type: string
        minLength: 0
        maxLength: 32
        x-go-custom-tag: db:"uuid"
        x-nullable: false
      name:
        description: 机房名
        type: string
        minLength: 0
        maxLength: 64
        x-go-custom-tag: db:"name"
        x-nullable: false
  datacenter_response:
    type: object
    required:
      - datacenter_response
    properties:
      datacenter_response:
        $ref: "#/definitions/datacenter"
  describe_datacenter_response:
    type: object
    required:
      - describe_datacenters
    properties:
      describe_datacenters:
        type: array
        items:
          $ref: "#/definitions/describe_datacenter"
      total_count:
        description: total count
        type: integer
        format: int32
        minimum: 0
        maximum: 16777216
        x-go-custom-tag: db:"total_count"
        x-nullable: false
  describe_detail_datacenter_response:
    type: object
    required:
      - describe_detail_datacenter
    properties:
      describe_detail_datacenter:
        $ref: "#/definitions/describe_detail_datacenter"
  datacenter_in_response:
    type: object
    required:
      - datacenters
    properties:
      datacenters:
        type: array
        items:
          $ref: "#/definitions/datacenter_list"
  eip:
    type: object
    properties:
      uuid:
        description: uuid
        type: string
        minLength: 0
        maxLength: 32
        x-go-custom-tag: db:"uuid"
        x-nullable: false
      description:
        description: 描述
        type: string
        minLength: 0
        maxLength: 64
        x-go-custom-tag: db:"description"
        x-nullable: true
      network_segment:
        description: 网段/掩码
        type: string
        pattern: ^(?:(?:[0-9]|[1-9][0-9]|1[0-9]{2}|2[0-4][0-9]|25[0-5])\.){3}(?:[0-9]|[1-9][0-9]|1[0-9]{2}|2[0-4][0-9]|25[0-5])\/([1-9]|[1-2]\d|3[0-2])$
        minLength: 0
        maxLength: 64
        x-go-custom-tag: db:"network_segment"
        x-nullable: false
      start_ip_address:
        description: 起始地址
        type: string
        pattern: ^(?:(?:[0-9]|[1-9][0-9]|1[0-9]{2}|2[0-4][0-9]|25[0-5])\.){3}(?:[0-9]|[1-9][0-9]|1[0-9]{2}|2[0-4][0-9]|25[0-5])$
        minLength: 0
        maxLength: 64
        x-go-custom-tag: db:"start_ip_address"
        x-nullable: false
      total_ip_count:
        description: 总ip数量, 最小值为0, 但不能为空
        type: integer
        format: int32
        minimum: 0
        maximum: 16777216
        x-go-custom-tag: db:"total_ip_count"
        x-nullable: false
      created_at:
        description: 创建时间
        type: string
        format: dateTime
        x-go-custom-tag: db:"created_at,omitempty"
      updated_at:
        description: 更新时间
        type: string
        format: dateTime
        x-go-custom-tag: db:"updated_at,omitempty"
  eip_list:
    type: object
    properties:
      uuid:
        description: uuid
        type: string
        minLength: 0
        maxLength: 32
        x-go-custom-tag: db:"uuid"
        x-nullable: false
      network_segment:
        description: 网段/掩码
        type: string
        pattern: ^(?:(?:[0-9]|[1-9][0-9]|1[0-9]{2}|2[0-4][0-9]|25[0-5])\.){3}(?:[0-9]|[1-9][0-9]|1[0-9]{2}|2[0-4][0-9]|25[0-5])\/([1-9]|[1-2]\d|3[0-2])$
        minLength: 0
        maxLength: 64
        x-go-custom-tag: db:"network_segment"
        x-nullable: false
  describe_eip:
    type: object
    properties:
      uuid:
        description: uuid
        type: string
        minLength: 0
        maxLength: 32
        x-go-custom-tag: db:"uuid"
        x-nullable: false
      description:
        description: 描述
        type: string
        minLength: 0
        maxLength: 64
        x-go-custom-tag: db:"description"
        x-nullable: true
      network_segment:
        description: 网段/掩码
        type: string
        pattern: ^(?:(?:[0-9]|[1-9][0-9]|1[0-9]{2}|2[0-4][0-9]|25[0-5])\.){3}(?:[0-9]|[1-9][0-9]|1[0-9]{2}|2[0-4][0-9]|25[0-5])\/([1-9]|[1-2]\d|3[0-2])$
        minLength: 0
        maxLength: 64
        x-go-custom-tag: db:"network_segment"
        x-nullable: false
      total_ip_count:
        description: 总ip数量, 最小值为0, 但不能为空
        type: integer
        format: int32
        minimum: 1
        maximum: 16777216
        x-go-custom-tag: db:"total_ip_count"
        x-nullable: false
      use_ip_count:
        description: 已经使用的ip数量
        type: integer
        format: int32
        minimum: 0
        maximum: 16777216
        x-go-custom-tag: db:"use_ip_count"
        x-nullable: true
      start_ip_address:
        description: 网段起始地址
        type: string
        pattern: ^(?:(?:[0-9]|[1-9][0-9]|1[0-9]{2}|2[0-4][0-9]|25[0-5])\.){3}(?:[0-9]|[1-9][0-9]|1[0-9]{2}|2[0-4][0-9]|25[0-5])$
        minLength: 0
        maxLength: 64
        x-go-custom-tag: db:"start_ip_address"
        x-nullable: false
      tags:
        $ref: "#/definitions/tags"
  describe_detail_eip:
    type: object
    properties:
      uuid:
        description: uuid
        type: string
        minLength: 0
        maxLength: 32
        x-go-custom-tag: db:"uuid"
        x-nullable: false
      description:
        description: 描述
        type: string
        minLength: 0
        maxLength: 64
        x-go-custom-tag: db:"description"
        x-nullable: true
      network_segment:
        description: 网段/掩码
        type: string
        pattern: ^(?:(?:[0-9]|[1-9][0-9]|1[0-9]{2}|2[0-4][0-9]|25[0-5])\.){3}(?:[0-9]|[1-9][0-9]|1[0-9]{2}|2[0-4][0-9]|25[0-5])\/([1-9]|[1-2]\d|3[0-2])$
        minLength: 0
        maxLength: 64
        x-go-custom-tag: db:"network_segment"
        x-nullable: false
      total_ip_count:
        description: 总ip数量, 最小值为0, 但不能为空
        type: integer
        format: int32
        minimum: 1
        maximum: 16777216
        x-go-custom-tag: db:"total_ip_count"
        x-nullable: false
      use_ip_count:
        description: 已经使用的ip数量
        type: integer
        format: int32
        minimum: 0
        maximum: 16777216
        x-go-custom-tag: db:"use_ip_count"
        x-nullable: true
  eip_response:
    type: object
    required:
      - eip
    properties:
      eip:
        $ref: "#/definitions/eip"
  describe_eip_response:
    type: object
    required:
      - describe_eip
    properties:
      describe_eip:
        type: array
        items:
          $ref: "#/definitions/describe_eip"
      total_count:
        description: total count
        type: integer
        format: int32
        minimum: 0
        maximum: 16777216
        x-go-custom-tag: db:"total_count"
        x-nullable: false
  describe_detail_eip_response:
    type: object
    required:
      - describe_detail_eip
    properties:
      describe_detail_eip:
        $ref: "#/definitions/describe_detail_eip"
  eip_in_response:
    type: object
    required:
      - eips
    properties:
      eips:
        type: array
        items:
          $ref: "#/definitions/eip_list"

  eip_used_record:
    type: object
    properties:
      uuid:
        description: uuid
        type: string
        minLength: 0
        maxLength: 32
        x-go-custom-tag: db:"uuid"
        x-nullable: false
      eip_uuid:
        description: eip_uuid
        type: string
        minLength: 0
        maxLength: 32
        x-go-custom-tag: db:"eip_uuid"
        x-nullable: false
      start_ip_address:
        description: 占用起始地址
        type: string
        pattern: ^(?:(?:[0-9]|[1-9][0-9]|1[0-9]{2}|2[0-4][0-9]|25[0-5])\.){3}(?:[0-9]|[1-9][0-9]|1[0-9]{2}|2[0-4][0-9]|25[0-5])$
        minLength: 0
        maxLength: 64
        x-go-custom-tag: db:"start_ip_address"
        x-nullable: false
      ip_address:
        description: IP地址
        type: string
        minLength: 0
        maxLength: 64
        x-go-custom-tag: db:"ip_address"
        x-nullable: false
      state:
        description: 占用状态, 0未占用 ,1占用 ,但不能为空
        type: integer
        format: int32
        minimum: 0
        maximum: 5
        x-go-custom-tag: db:"state"
        x-nullable: false
      count:
        description: 占用数量, 最小值为0, 但不能为空
        type: integer
        format: int32
        minimum: 0
        maximum: 16777216
        x-go-custom-tag: db:"count"
        x-nullable: false
      resType:
        description: 占用此IP的资源类型
        type: string
        format: int32
        minLength: 0
        maxLength: 64
        x-go-custom-tag: db:"res_type"
        x-nullable: false
      resId:
        description: 占用此IP的资源ID
        type: string
        format: int32
        minLength: 0
        maxLength: 64
        x-go-custom-tag: db:"res_type"
        x-nullable: false
      reason:
        description: 占用原因, 可以为空
        type: string
        minLength: 0
        maxLength: 64
        x-go-custom-tag: db:"reason"
        x-nullable: true
      start_time:
        description: 占用时间, 可以为空
        type: string
        format: dateTime
        x-go-custom-tag: db:"start_time,omitempty"
        x-nullable: true
      end_time:
        description: 归还日期, 可以为空
        type: string
        format: dateTime
        x-go-custom-tag: db:"end_time,omitempty"
        x-nullable: true
  describe_eip_used_record:
    type: object
    properties:
      uuid:
        description: uuid
        type: string
        minLength: 0
        maxLength: 32
        x-go-custom-tag: db:"uuid"
        x-nullable: false
      eip_uuid:
        description: eip_uuid
        type: string
        minLength: 0
        maxLength: 32
        x-go-custom-tag: db:"eip_uuid"
        x-nullable: false
      start_ip_address:
        description: 占用起始地址
        type: string
        pattern: ^(?:(?:[0-9]|[1-9][0-9]|1[0-9]{2}|2[0-4][0-9]|25[0-5])\.){3}(?:[0-9]|[1-9][0-9]|1[0-9]{2}|2[0-4][0-9]|25[0-5])$
        minLength: 0
        maxLength: 64
        x-go-custom-tag: db:"start_ip_address"
        x-nullable: false
      ip_address:
        description: IP地址
        type: string
        minLength: 0
        maxLength: 64
        x-go-custom-tag: db:"ip_address"
        x-nullable: false
      state:
        description: 占用状态, 0未占用 ,1占用 ,但不能为空
        type: integer
        format: int32
        minimum: 0
        maximum: 1
        x-go-custom-tag: db:"state"
        x-nullable: false
      count:
        description: 占用数量, 最小值为0, 但不能为空
        type: integer
        format: int32
        minimum: 0
        maximum: 16777216
        x-go-custom-tag: db:"count"
        x-nullable: false
      resType:
        description: 占用此IP的资源类型
        type: string
        format: int32
        minLength: 0
        maxLength: 64
        x-go-custom-tag: db:"res_type"
        x-nullable: false
      resId:
        description: 占用此IP的资源ID
        type: string
        format: int32
        minLength: 0
        maxLength: 64
        x-go-custom-tag: db:"res_type"
        x-nullable: false
      reason:
        description: 占用原因, 可以为空
        type: string
        minLength: 0
        maxLength: 64
        x-go-custom-tag: db:"reason"
        x-nullable: true
      start_time:
        description: 占用时间, 可以为空
        type: string
        format: dateTime
        x-go-custom-tag: db:"start_time,omitempty"
        x-nullable: true
      end_time:
        description: 归还日期, 可以为空
        type: string
        format: dateTime
        x-go-custom-tag: db:"end_time,omitempty"
        x-nullable: true
      pin:
        description: PIN
        type: string
      regionId:
        description: 区域
        type: string
  eip_used_record_response:
    type: object
    required:
      - eip_used_record
    properties:
      eip_used_record:
        $ref: "#/definitions/eip_used_record"
      total_count:
        description: total count
        type: integer
        format: int32
        minimum: 0
        maximum: 16777216
        x-go-custom-tag: db:"total_count"
        x-nullable: false
  describe_eip_used_record_response:
    type: object
    required:
      - describe_eip_used_record
    properties:
      describe_eip_used_record:
        type: array
        items:
          $ref: "#/definitions/describe_eip_used_record"
      total_count:
        description: total count
        type: integer
        format: int32
        minimum: 0
        maximum: 16777216
        x-go-custom-tag: db:"total_count"
        x-nullable: false

  maintainer:
    type: object
    properties:
      uuid:
        description: uuid
        type: string
        minLength: 0
        maxLength: 32
        x-go-custom-tag: db:"uuid"
        x-nullable: false
      name:
        description: 运维人
        type: string
        minLength: 1
        maxLength: 64
        x-go-custom-tag: db:"name"
        x-nullable: false
      position:
        description: 职务
        type: string
        minLength: 1
        maxLength: 64
        x-go-custom-tag: db:"position"
        x-nullable: false
      phone:
        description: 手机号
        type: string
        pattern: ^1[3-9]\d{9}$
        minLength: 1
        maxLength: 64
        x-go-custom-tag: db:"phone"
        x-nullable: false
      notes:
        description: 备注
        type: string
        minLength: 1
        maxLength: 64
        x-go-custom-tag: db:"notes"
        x-nullable: true
      created_at:
        description: 创建时间
        type: string
        format: dateTime
        x-go-custom-tag: db:"created_at,omitempty"
      updated_at:
        description: 更新时间
        type: string
        format: dateTime
        x-go-custom-tag: db:"updated_at,omitempty"
  maintainer_list:
    type: object
    properties:
      uuid:
        description: uuid
        type: string
        minLength: 0
        maxLength: 32
        x-go-custom-tag: db:"uuid"
        x-nullable: false
      name:
        description: 运维人
        type: string
        minLength: 1
        maxLength: 64
        x-go-custom-tag: db:"name"
        x-nullable: false
  maintainers_response:
    type: object
    required:
      - maintainers
    properties:
      maintainers:
        type: array
        items:
          $ref: "#/definitions/maintainer"
  maintainer_response:
    type: object
    required:
      - maintainer
    properties:
      maintainer:
        $ref: "#/definitions/maintainer"
      total_count:
        description: total count
        type: integer
        format: int32
        minimum: 0
        maximum: 16777216
        x-go-custom-tag: db:"total_count"
        x-nullable: false
  maintainer_in_response:
    type: object
    required:
      - maintainer
    properties:
      maintainer:
        type: array
        items:
          $ref: "#/definitions/maintainer_list"
  maintainers_datacenter:
    type: object
    properties:
      uuid:
        description: uuid
        type: string
        minLength: 0
        maxLength: 32
        x-go-custom-tag: db:"uuid"
        x-nullable: false
      datacenter_uuid:
        description: uuid
        type: string
        minLength: 0
        maxLength: 32
        x-go-custom-tag: db:"datacenter_uuid"
        x-nullable: false
      maintainer_uuid:
        description: uuid
        type: string
        minLength: 0
        maxLength: 32
        x-go-custom-tag: db:"maintainer_uuid"
        x-nullable: false
      created_at:
        description: 创建时间
        type: string
        format: dateTime
        x-go-custom-tag: db:"created_at,omitempty"
      updated_at:
        description: 更新时间
        type: string
        format: dateTime
        x-go-custom-tag: db:"updated_at,omitempty"
  network_equipment:
    type: object
    properties:
      uuid:
        description: uuid
        type: string
        minLength: 0
        maxLength: 32
        x-go-custom-tag: db:"uuid"
        x-nullable: false
      datacenter_uuid:
        description: 机房id
        type: string
        minLength: 0
        maxLength: 32
        x-go-custom-tag: db:"datacenter_uuid"
        x-nullable: false
      rack_uuid:
        description: 机架id
        type: string
        minLength: 0
        maxLength: 32
        x-go-custom-tag: db:"rack_uuid"
        x-nullable: false
      region_uuid:
        description: region uuid
        type: string
        minLength: 0
        maxLength: 32
        x-go-custom-tag: db:"region_uuid"
        x-nullable: true
      az_uuid:
        description: az uuid
        type: string
        minLength: 0
        maxLength: 32
        x-go-custom-tag: db:"az_uuid"
        x-nullable: true
      asset_state:
        description: 资产状态id 故障1、维修中2、离线3、在线4、装机中5
        type: integer
        format: int32
        minimum: 1
        maximum: 4
        x-go-custom-tag: db:"asset_state"
        x-nullable: false
      u_count:
        description: U位
        type: integer
        format: int32
        minimum: 0
        maximum: 16777216
        x-go-custom-tag: db:"u_count"
        x-nullable: false
      asset_number:
        description: 资产编号
        type: string
        minLength: 0
        maxLength: 64
        x-go-custom-tag: db:"asset_number"
        x-nullable: false
      serial_number:
        description: 序列号
        type: string
        minLength: 0
        maxLength: 64
        x-go-custom-tag: db:"serial_number"
        x-nullable: false
      brand:
        description: 品牌
        type: string
        minLength: 0
        maxLength: 64
        x-go-custom-tag: db:"brand"
        x-nullable: true
      model:
        description: 型号
        type: string
        minLength: 0
        maxLength: 64
        x-go-custom-tag: db:"model"
        x-nullable: true
      outofband_ip:
        description: 带外Ip
        type: string
        pattern: ^(?:(?:[0-9]|[1-9][0-9]|1[0-9]{2}|2[0-4][0-9]|25[0-5])\.){3}(?:[0-9]|[1-9][0-9]|1[0-9]{2}|2[0-4][0-9]|25[0-5])$
        minLength: 0
        maxLength: 64
        x-go-custom-tag: db:"outofband_ip"
        x-nullable: true
      manage_ip:
        description: 管理ip
        type: string
        pattern: ^(?:(?:[0-9]|[1-9][0-9]|1[0-9]{2}|2[0-4][0-9]|25[0-5])\.){3}(?:[0-9]|[1-9][0-9]|1[0-9]{2}|2[0-4][0-9]|25[0-5])$
        minLength: 0
        maxLength: 64
        x-go-custom-tag: db:"manage_ip"
        x-nullable: true
      way_to_use:
        description: 用途，网络设备只有 网络设备1
        type: integer
        format: int32
        minimum: 0
        maximum: 2
        x-go-custom-tag: db:"way_to_use"
        x-nullable: false
      mc_name:
        description: 厂商联系人姓名
        type: string
        minLength: 0
        maxLength: 64
        x-go-custom-tag: db:"mc_name"
        x-nullable: true
      mc_phone:
        description: 厂商联系人电话
        type: string
        pattern: ^1[3-9]\d{9}$
        minLength: 0
        maxLength: 64
        x-go-custom-tag: db:"mc_phone"
        x-nullable: true
      mc_email:
        description: 厂商联系人邮箱
        type: string
        pattern: ^[a-zA-Z0-9_-]+@[a-zA-Z0-9_-]+(\.[a-zA-Z0-9_-]+)+$
        minLength: 0
        maxLength: 64
        x-go-custom-tag: db:"mc_email"
        x-nullable: true
      created_at:
        description: 创建时间
        type: string
        format: dateTime
        x-go-custom-tag: db:"created_at,omitempty"
      updated_at:
        description: 更新时间
        type: string
        format: dateTime
        x-go-custom-tag: db:"updated_at,omitempty"
      warranty_start:
        description: 维保开始时间
        type: string
        format: dateTime
        x-go-custom-tag: db:"warranty_start"
      warranty_end:
        description: 维保结束时间
        type: string
        format: dateTime
        x-go-custom-tag: db:"warranty_end"
      procurement_time:
        description: 采购时间
        type: string
        format: dateTime
        x-go-custom-tag: db:"procurement_time"
  describe_network_equipment:
    type: object
    properties:
      uuid:
        description: uuid
        type: string
        minLength: 0
        maxLength: 32
        x-go-custom-tag: db:"uuid"
        x-nullable: false
      datacenter_uuid:
        description: 机房id
        type: string
        minLength: 0
        maxLength: 32
        x-go-custom-tag: db:"datacenter_uuid"
        x-nullable: false
      datacenter_name:
        description: 机房name
        type: string
        minLength: 0
        maxLength: 32
        x-go-custom-tag: db:"datacenter_name"
        x-nullable: false
      rack_uuid:
        description: 机架id
        type: string
        minLength: 0
        maxLength: 32
        x-go-custom-tag: db:"rack_uuid"
        x-nullable: false
      rack_name:
        description: 机架name
        type: string
        minLength: 0
        maxLength: 32
        x-go-custom-tag: db:"rack_name"
        x-nullable: false
      asset_state:
        description: 资产状态id 故障1、维修中2、离线3、在线4、装机中5
        type: integer
        format: int32
        minimum: 1
        maximum: 4
        x-go-custom-tag: db:"asset_state"
        x-nullable: false
      u_count:
        description: U位
        type: integer
        format: int32
        minimum: 0
        maximum: 16777216
        x-go-custom-tag: db:"u_count"
        x-nullable: false
      asset_number:
        description: 资产编号
        type: string
        minLength: 0
        maxLength: 64
        x-go-custom-tag: db:"asset_number"
        x-nullable: false
      serial_number:
        description: 序列号
        type: string
        minLength: 0
        maxLength: 64
        x-go-custom-tag: db:"serial_number"
        x-nullable: false
      brand:
        description: 品牌
        type: string
        minLength: 0
        maxLength: 64
        x-go-custom-tag: db:"brand"
        x-nullable: true
      model:
        description: 型号
        type: string
        minLength: 0
        maxLength: 64
        x-go-custom-tag: db:"model"
        x-nullable: true
      outofband_ip:
        description: 带外Ip
        type: string
        pattern: ^(?:(?:[0-9]|[1-9][0-9]|1[0-9]{2}|2[0-4][0-9]|25[0-5])\.){3}(?:[0-9]|[1-9][0-9]|1[0-9]{2}|2[0-4][0-9]|25[0-5])$
        minLength: 0
        maxLength: 64
        x-go-custom-tag: db:"outofband_ip"
        x-nullable: true
      manage_ip:
        description: 管理ip
        type: string
        pattern: ^(?:(?:[0-9]|[1-9][0-9]|1[0-9]{2}|2[0-4][0-9]|25[0-5])\.){3}(?:[0-9]|[1-9][0-9]|1[0-9]{2}|2[0-4][0-9]|25[0-5])$
        minLength: 1
        maxLength: 64
        x-go-custom-tag: db:"manage_ip"
        x-nullable: true
      way_to_use:
        description: 用途，网络设备只有 网络设备1
        type: integer
        format: int32
        minimum: 0
        maximum: 2
        x-go-custom-tag: db:"way_to_use"
        x-nullable: false
      mc_name:
        description: 厂商联系人姓名
        type: string
        minLength: 0
        maxLength: 64
        x-go-custom-tag: db:"mc_name"
        x-nullable: true
      mc_phone:
        description: 厂商联系人电话
        type: string
        pattern: ^1[3-9]\d{9}$
        minLength: 0
        maxLength: 64
        x-go-custom-tag: db:"mc_phone"
        x-nullable: true
      mc_email:
        description: 厂商联系人邮箱
        type: string
        pattern: ^[a-zA-Z0-9_-]+@[a-zA-Z0-9_-]+(\.[a-zA-Z0-9_-]+)+$
        minLength: 0
        maxLength: 64
        x-go-custom-tag: db:"mc_email"
        x-nullable: true
      warranty_start:
        description: 维保开始时间
        type: string
        format: dateTime
        x-go-custom-tag: db:"warranty_start"
        x-nullable: false
      warranty_end:
        description: 维保结束时间
        type: string
        format: dateTime
        x-go-custom-tag: db:"warranty_end"
        x-nullable: false
      procurement_time:
        description: 采购时间
        type: string
        format: dateTime
        x-go-custom-tag: db:"procurement_time"
        x-nullable: false
      conn_type:
        description: 连接方式
        type: string
        x-nullable: false
      conn_port:
        description: 连接端口
        type: integer
        x-nullable: false
      conn_user:
        description: 连接使用的用户
        type: string
        x-nullable: false
      conn_pwd:
        description: 连接使用的用户
        type: string
        x-nullable: false
      roles:
        type: array
        items:
          $ref: "#/definitions/server_role"

  describe_detail_network_equipment:
    type: object
    properties:
      uuid:
        description: uuid
        type: string
        minLength: 0
        maxLength: 32
        x-go-custom-tag: db:"uuid"
        x-nullable: false
      datacenter_uuid:
        description: 机房id
        type: string
        minLength: 0
        maxLength: 32
        x-go-custom-tag: db:"datacenter_uuid"
        x-nullable: false
      datacenter_name:
        description: 机房name
        type: string
        minLength: 0
        maxLength: 32
        x-go-custom-tag: db:"datacenter_name"
        x-nullable: false
      az_uuid:
        description: az_id
        type: string
        minLength: 0
        maxLength: 32
        x-go-custom-tag: db:"az_uuid"
        x-nullable: true
      az_name:
        description: az_name
        type: string
        minLength: 0
        maxLength: 32
        x-go-custom-tag: db:"az_name"
        x-nullable: true
      region_uuid:
        description: region_id
        type: string
        minLength: 0
        maxLength: 32
        x-go-custom-tag: db:"region_uuid"
        x-nullable: true
      region_name:
        description: region_name
        type: string
        minLength: 0
        maxLength: 64
        x-go-custom-tag: db:"region_name"
        x-nullable: true
      rack_uuid:
        description: 机架id
        type: string
        minLength: 0
        maxLength: 32
        x-go-custom-tag: db:"rack_uuid"
        x-nullable: false
      rack_name:
        description: 机架name
        type: string
        minLength: 0
        maxLength: 64
        x-go-custom-tag: db:"rack_name"
        x-nullable: false
      asset_state:
        description: 资产状态id 故障1、维修中2、离线3、在线4、装机中5
        type: integer
        format: int32
        minimum: 1
        maximum: 4
        x-go-custom-tag: db:"asset_state"
        x-nullable: false
      u_count:
        description: U位
        type: integer
        format: int32
        minimum: 0
        maximum: 16777216
        x-go-custom-tag: db:"u_count"
        x-nullable: false
      asset_number:
        description: 资产编号
        type: string
        minLength: 0
        maxLength: 64
        x-go-custom-tag: db:"asset_number"
        x-nullable: false
      serial_number:
        description: 序列号
        type: string
        minLength: 0
        maxLength: 64
        x-go-custom-tag: db:"serial_number"
        x-nullable: false
      brand:
        description: 品牌
        type: string
        minLength: 0
        maxLength: 64
        x-go-custom-tag: db:"brand"
        x-nullable: true
      model:
        description: 型号
        type: string
        minLength: 0
        maxLength: 64
        x-go-custom-tag: db:"model"
        x-nullable: true
      outofband_ip:
        description: 带外Ip
        type: string
        pattern: ^(?:(?:[0-9]|[1-9][0-9]|1[0-9]{2}|2[0-4][0-9]|25[0-5])\.){3}(?:[0-9]|[1-9][0-9]|1[0-9]{2}|2[0-4][0-9]|25[0-5])$
        minLength: 0
        maxLength: 64
        x-go-custom-tag: db:"outofband_ip"
        x-nullable: true
      manage_ip:
        description: 管理ip
        type: string
        pattern: ^(?:(?:[0-9]|[1-9][0-9]|1[0-9]{2}|2[0-4][0-9]|25[0-5])\.){3}(?:[0-9]|[1-9][0-9]|1[0-9]{2}|2[0-4][0-9]|25[0-5])$
        minLength: 1
        maxLength: 64
        x-go-custom-tag: db:"manage_ip"
        x-nullable: true
      way_to_use:
        description: 用途，网络设备只有 网络设备1
        type: integer
        format: int32
        minimum: 0
        maximum: 2
        x-go-custom-tag: db:"way_to_use"
        x-nullable: true
      mc_name:
        description: 厂商联系人姓名
        type: string
        minLength: 0
        maxLength: 64
        x-go-custom-tag: db:"mc_name"
        x-nullable: true
      mc_phone:
        description: 厂商联系人电话
        type: string
        pattern: ^1[3-9]\d{9}$
        minLength: 0
        maxLength: 64
        x-go-custom-tag: db:"mc_phone"
        x-nullable: true
      mc_email:
        description: 厂商联系人邮箱
        type: string
        pattern: ^[a-zA-Z0-9_-]+@[a-zA-Z0-9_-]+(\.[a-zA-Z0-9_-]+)+$
        minLength: 0
        maxLength: 64
        x-go-custom-tag: db:"mc_email"
        x-nullable: true
      warranty_start:
        description: 维保开始时间
        type: string
        format: dateTime
        x-go-custom-tag: db:"warranty_start"
        x-nullable: false
      warranty_end:
        description: 维保结束时间
        type: string
        format: dateTime
        x-go-custom-tag: db:"warranty_end"
        x-nullable: false
      procurement_time:
        description: 采购时间
        type: string
        format: dateTime
        x-go-custom-tag: db:"procurement_time"
        x-nullable: false
      conn_type:
        description: 连接方式
        type: string
        x-nullable: false
      conn_port:
        description: 连接端口
        type: integer
        x-nullable: false
      conn_user:
        description: 连接使用的用户
        type: string
        x-nullable: false
      conn_pwd:
        description: 连接使用的用户
        type: string
        x-nullable: false
  network_equipment_list:
    type: object
    properties:
      uuid:
        description: uuid
        type: string
        minLength: 0
        maxLength: 32
        x-go-custom-tag: db:"uuid"
        x-nullable: false
      serial_number:
        description: 序列号
        type: string
        minLength: 0
        maxLength: 64
        x-go-custom-tag: db:"serial_number"
        x-nullable: false
      asset_number:
        description: 资产编号
        type: string
        minLength: 0
        maxLength: 64
        x-go-custom-tag: db:"asset_number"
        x-nullable: false
  network_equipment_response:
    type: object
    required:
      - network_equipment
    properties:
      network_equipment:
        $ref: "#/definitions/network_equipment"
  describe_network_equipment_response:
    type: object
    required:
      - describe_network_equipment
    properties:
      describe_network_equipment:
        type: array
        items:
          $ref: "#/definitions/describe_network_equipment"
      total_count:
        description: total count
        type: integer
        format: int32
        minimum: 0
        maximum: 16777216
        x-go-custom-tag: db:"total_count"
        x-nullable: false
  describe_detail_network_equipment_response:
    type: object
    required:
      - describe_detail_network_equipment
    properties:
      describe_detail_network_equipment:
        $ref: "#/definitions/describe_detail_network_equipment"
  network_equipment_in_response:
    type: object
    required:
      - network_equipment
    properties:
      network_equipment:
        type: array
        items:
          $ref: "#/definitions/network_equipment_list"
  os_package:
    type: object
    properties:
      uuid:
        description: uuid
        type: string
        minLength: 0
        maxLength: 32
        x-go-custom-tag: db:"uuid"
        x-nullable: false
      package_name:
        description: 名称
        type: string
        minLength: 0
        maxLength: 45
        x-go-custom-tag: db:"package_name"
        x-nullable: false
      package_desc:
        description: 描述
        type: string
        minLength: 0
        maxLength: 255
        x-go-custom-tag: db:"package_desc"
        x-nullable: true
      created_at:
        description: 创建时间
        type: string
        format: dateTime
        x-go-custom-tag: db:"created_at,omitempty"
      updated_at:
        description: 更新时间
        type: string
        format: dateTime
        x-go-custom-tag: db:"updated_at,omitempty"
  os_package_list:
    type: object
    properties:
      uuid:
        description: uuid
        type: string
        minLength: 0
        maxLength: 32
        x-go-custom-tag: db:"uuid"
        x-nullable: false
      package_name:
        description: 名称
        type: string
        minLength: 0
        maxLength: 45
        x-go-custom-tag: db:"package_name"
        x-nullable: false
  os_package_response:
    type: object
    required:
      - os_package
    properties:
      os_package:
        $ref: "#/definitions/os_package"
  os_packages_response:
    type: object
    required:
      - os_packages
    properties:
      os_packages:
        type: array
        items:
          $ref: "#/definitions/os_package"
      total_count:
        description: total count
        type: integer
        format: int32
        minimum: 0
        maximum: 16777216
        x-go-custom-tag: db:"total_count"
        x-nullable: false
  os_package_in_response:
    type: object
    required:
      - os_package
    properties:
      os_package:
        type: array
        items:
          $ref: "#/definitions/os_package_list"
  rack:
    type: object
    properties:
      uuid:
        description: uuid
        type: string
        minLength: 0
        maxLength: 32
        x-go-custom-tag: db:"uuid"
        x-nullable: false
      region_uuid:
        description: region uuid
        type: string
        minLength: 0
        maxLength: 32
        x-go-custom-tag: db:"region_uuid"
        x-nullable: true
      az_uuid:
        description: az uuid
        type: string
        minLength: 0
        maxLength: 32
        x-go-custom-tag: db:"az_uuid"
        x-nullable: true
      datacenter_uuid:
        description: 机房uuid
        type: string
        minLength: 0
        maxLength: 32
        x-go-custom-tag: db:"datacenter_uuid"
        x-nullable: false
      rack_number:
        description: 机架编号
        type: string
        minLength: 0
        maxLength: 64
        x-go-custom-tag: db:"rack_number"
        x-nullable: false
      electrical_spec:
        description: 电力规格
        type: string
        minLength: 0
        maxLength: 64
        x-go-custom-tag: db:"electrical_spec"
        x-nullable: true
      room_number:
        description: 房间编号
        type: string
        minLength: 0
        maxLength: 64
        x-go-custom-tag: db:"room_number"
        x-nullable: true
      u_count:
        description: 总U位, 最小值为1, 不能为空
        type: integer
        format: int32
        minimum: 0
        maximum: 16777216
        x-go-custom-tag: db:"u_count"
        x-nullable: false
      created_at:
        description: 创建时间
        type: string
        format: dateTime
        x-go-custom-tag: db:"created_at,omitempty"
      updated_at:
        description: 更新时间
        type: string
        format: dateTime
        x-go-custom-tag: db:"updated_at,omitempty"
  describe_rack:
    type: object
    properties:
      uuid:
        description: uuid
        type: string
        minLength: 0
        maxLength: 32
        x-go-custom-tag: db:"uuid"
        x-nullable: false
      datacenter_uuid:
        description: 机房uuid
        type: string
        minLength: 0
        maxLength: 32
        x-go-custom-tag: db:"datacenter_uuid"
        x-nullable: false
      datacenter_name:
        description: datacenetr name
        type: string
        minLength: 0
        maxLength: 64
        x-go-custom-tag: db:"datacenter_name"
        x-nullable: false
      rack_number:
        description: 机架编号
        type: string
        minLength: 0
        maxLength: 64
        x-go-custom-tag: db:"rack_number"
        x-nullable: false
      electrical_spec:
        description: 电力规格
        type: string
        minLength: 0
        maxLength: 64
        x-go-custom-tag: db:"electrical_spec"
        x-nullable: true
      u_count:
        description: 总U位, 最小值为1, 不能为空
        type: integer
        format: int32
        minimum: 0
        maximum: 16777216
        x-go-custom-tag: db:"u_count"
        x-nullable: false
      room_number:
        description: 房间编号
        type: string
        minLength: 0
        maxLength: 64
        x-go-custom-tag: db:"room_number"
        x-nullable: true
      network:
        description: 网段
        type: string
        minLength: 0
        maxLength: 64
        x-go-custom-tag: db:"network"
        x-nullable: true
      network_mask:
        description: 网段MASK
        type: string
        minLength: 0
        maxLength: 64
        x-go-custom-tag: db:"network_mask"
        x-nullable: true
      tor_ip:
        description: 网关IP
        type: string
        minLength: 0
        maxLength: 64
        x-go-custom-tag: db:"tor_ip"
        x-nullable: true
      az_uuid:
        description: 所属az_uuid
        type: string
        minLength: 0
        maxLength: 64
        x-go-custom-tag: db:"az_uuid"
        x-nullable: true
      az_name:
        description: 所属az_name
        type: string
        minLength: 0
        maxLength: 64
        x-go-custom-tag: db:"az_name"
        x-nullable: true
      region_uuid:
        description: 所属region_uuid
        type: string
        minLength: 0
        maxLength: 64
        x-go-custom-tag: db:"region_uuid"
        x-nullable: true
      region_name:
        description: 所属region_name
        type: string
        minLength: 0
        maxLength: 64
        x-go-custom-tag: db:"region_name"
        x-nullable: true
  describe_detail_rack:
    type: object
    properties:
      uuid:
        description: uuid
        type: string
        minLength: 0
        maxLength: 32
        x-go-custom-tag: db:"uuid"
        x-nullable: false
      datacenter_uuid:
        description: 机房uuid
        type: string
        minLength: 0
        maxLength: 32
        x-go-custom-tag: db:"datacenter_uuid"
        x-nullable: false
      datacenter_name:
        description: 机房name
        type: string
        minLength: 0
        maxLength: 32
        x-go-custom-tag: db:"datacenter_name"
        x-nullable: false
      az_uuid:
        description: az_id
        type: string
        minLength: 0
        maxLength: 32
        x-go-custom-tag: db:"az_uuid"
        x-nullable: true
      az_name:
        description: az_name
        type: string
        minLength: 0
        maxLength: 32
        x-go-custom-tag: db:"az_name"
        x-nullable: true
      region_uuid:
        description: region_id
        type: string
        minLength: 0
        maxLength: 32
        x-go-custom-tag: db:"region_uuid"
        x-nullable: true
      region_name:
        description: region_name
        type: string
        minLength: 0
        maxLength: 32
        x-go-custom-tag: db:"region_name"
        x-nullable: true
      rack_number:
        description: 机架编号
        type: string
        minLength: 0
        maxLength: 64
        x-go-custom-tag: db:"rack_number"
        x-nullable: false
      electrical_spec:
        description: 电力规格
        type: string
        minLength: 0
        maxLength: 64
        x-go-custom-tag: db:"electrical_spec"
        x-nullable: true
      u_count:
        description: 总U位, 最小值为1, 不能为空
        type: integer
        format: int32
        minimum: 0
        maximum: 16777216
        x-go-custom-tag: db:"u_count"
        x-nullable: false
      room_number:
        description: 房间编号
        type: string
        minLength: 0
        maxLength: 64
        x-go-custom-tag: db:"room_number"
        x-nullable: true
      network:
        description: 网段
        type: string
        minLength: 0
        maxLength: 64
        x-go-custom-tag: db:"network"
        x-nullable: true
      network_mask:
        description: 网段MASK
        type: string
        minLength: 0
        maxLength: 64
        x-go-custom-tag: db:"network_mask"
        x-nullable: true
      tor_ip:
        description: 网关IP
        type: string
        minLength: 0
        maxLength: 64
        x-go-custom-tag: db:"tor_ip"
        x-nullable: true
  rack_list:
    type: object
    properties:
      uuid:
        description: uuid
        type: string
        minLength: 0
        maxLength: 32
        x-go-custom-tag: db:"uuid"
        x-nullable: false
      rack_number:
        description: 机架编号
        type: string
        minLength: 0
        maxLength: 64
        x-go-custom-tag: db:"rack_number"
        x-nullable: false
  rack_response:
    type: object
    required:
      - rack
    properties:
      rack:
        $ref: "#/definitions/rack"
  describe_rack_response:
    type: object
    required:
      - describe_rack
    properties:
      describe_rack:
        type: array
        items:
          $ref: "#/definitions/describe_rack"
      total_count:
        description: total count
        type: integer
        format: int32
        minimum: 0
        maximum: 16777216
        x-go-custom-tag: db:"total_count"
        x-nullable: false
  describe_detail_rack_response:
    type: object
    required:
      - describe_detail_rack
    properties:
      describe_detail_rack:
        $ref: "#/definitions/describe_detail_rack"
  rack_in_response:
    type: object
    required:
      - describe_rack
    properties:
      describe_rack:
        type: array
        items:
          $ref: "#/definitions/rack_list"
  region:
    type: object
    properties:
      uuid:
        description: uuid
        type: string
        minLength: 0
        maxLength: 32
        x-go-custom-tag: db:"uuid"
        x-nullable: false
      name:
        description: region名称
        type: string
        minLength: 0
        maxLength: 64
        x-go-custom-tag: db:"name"
        x-nullable: false
      use_way:
        description: region 用途
        type: string
        minLength: 0
        maxLength: 64
        x-go-custom-tag: db:"use_way"
        x-nullable: true
      cn_name:
        description: region中文名称
        type: string
        minLength: 0
        maxLength: 64
        x-go-custom-tag: db:"cn_name"
        x-nullable: false
      is_default:
        description: region名称
        type: string
        minLength: 0
        maxLength: 32
        x-go-custom-tag: db:"is_default"
        x-nullable: false
      status:
        description: 状态
        type: string
        minLength: 0
        maxLength: 32
        x-go-custom-tag: db:"status"
        x-nullable: false
      description:
        description: 描述
        type: string
        minLength: 0
        maxLength: 128
        x-go-custom-tag: db:"description"
        x-nullable: true
      created_at:
        description: 创建时间
        type: string
        format: dateTime
        x-go-custom-tag: db:"created_at,omitempty"
        x-nullable: false
      updated_at:
        description: 更新时间
        type: string
        format: dateTime
        x-go-custom-tag: db:"updated_at,omitempty"
        x-nullable: false
  describe_region:
    type: object
    properties:
      uuid:
        description: uuid
        type: string
        minLength: 0
        maxLength: 32
        x-go-custom-tag: db:"uuid"
        x-nullable: false
      name:
        description: region名称
        type: string
        minLength: 0
        maxLength: 64
        x-go-custom-tag: db:"name"
        x-nullable: false
      cn_name:
        description: region中文名称
        type: string
        minLength: 0
        maxLength: 64
        x-go-custom-tag: db:"cn_name"
        x-nullable: false
      is_default:
        description: region名称
        type: string
        minLength: 0
        maxLength: 32
        x-go-custom-tag: db:"is_default"
        x-nullable: false
      status:
        description: 状态
        type: string
        minLength: 0
        maxLength: 32
        x-go-custom-tag: db:"status"
        x-nullable: false
      description:
        description: 描述
        type: string
        minLength: 0
        maxLength: 128
        x-go-custom-tag: db:"description"
        x-nullable: true
      use_way:
        description: region 用途
        type: string
        minLength: 0
        maxLength: 64
        x-go-custom-tag: db:"use_way"
        x-nullable: true
      created_at:
        description: 创建时间
        type: string
        format: dateTime
        x-go-custom-tag: db:"created_at,omitempty"
        x-nullable: false
      updated_at:
        description: 更新时间
        type: string
        format: dateTime
        x-go-custom-tag: db:"updated_at,omitempty"
        x-nullable: false
  region_list:
    type: object
    properties:
      uuid:
        description: uuid
        type: string
        minLength: 0
        maxLength: 32
        x-go-custom-tag: db:"uuid"
        x-nullable: false
      name:
        description: region名称
        type: string
        minLength: 0
        maxLength: 64
        x-go-custom-tag: db:"name"
        x-nullable: false
  region_response:
    type: object
    required:
      - region
    properties:
      region:
        $ref: "#/definitions/describe_region"
  regions_response:
    type: object
    required:
      - regions
    properties:
      regions:
        type: array
        items:
          $ref: "#/definitions/describe_region"
      total_count:
        description: total count
        type: integer
        format: int32
        minimum: 0
        maximum: 16777216
        x-go-custom-tag: db:"total_count"
        x-nullable: false
  regions_in_response:
    type: object
    required:
      - regions
    properties:
      regions:
        type: array
        items:
          $ref: "#/definitions/region_list"
  server:
    type: object
    properties:
      uuid:
        description: uuid
        type: string
        minLength: 0
        maxLength: 32
        x-go-custom-tag: db:"uuid"
        x-nullable: false
      host_name:
        description: 机器name
        type: string
        minLength: 0
        maxLength: 64
        x-go-custom-tag: db:"host_name"
      region_uuid:
        description: region uuid
        type: string
        minLength: 0
        maxLength: 32
        x-go-custom-tag: db:"region_uuid"
        x-nullable: true
      az_uuid:
        description: az uuid
        type: string
        minLength: 0
        maxLength: 32
        x-go-custom-tag: db:"az_uuid"
        x-nullable: true
      rack_uuid:
        description: rack uuid
        type: string
        minLength: 0
        maxLength: 32
        x-go-custom-tag: db:"rack_uuid"
        x-nullable: false
      datacenter_uuid:
        description: uuid
        type: string
        minLength: 0
        maxLength: 32
        x-go-custom-tag: db:"datacenter_uuid"
        x-nullable: false
      asset_state:
        description: 资产状态id 故障1、维修中2、离线3、在线4、装机中5
        type: integer
        format: int32
        minimum: 1
        maximum: 4
        x-go-custom-tag: db:"asset_state"
        x-nullable: false
      server_package_uuid:
        description: uuid
        type: string
        minLength: 0
        maxLength: 32
        x-go-custom-tag: db:"server_package_uuid"
        x-nullable: false
      os_package_uuid:
        description: uuid
        type: string
        minLength: 0
        maxLength: 32
        x-go-custom-tag: db:"os_package_uuid"
        x-nullable: true
      asset_number:
        description: 资产编号
        type: string
        minLength: 1
        maxLength: 64
        x-go-custom-tag: db:"asset_number"
        x-nullable: false
      serial_number:
        description: 序列号
        type: string
        minLength: 1
        maxLength: 64
        x-go-custom-tag: db:"serial_number"
        x-nullable: false
      brand:
        description: 品牌
        type: string
        minLength: 0
        maxLength: 64
        x-go-custom-tag: db:"brand"
        x-nullable: true
      model:
        description: 型号
        type: string
        minLength: 0
        maxLength: 64
        x-go-custom-tag: db:"model"
        x-nullable: true
      system_ip:
        description: 系统ip
        type: string
        pattern: ^(?:(?:[0-9]|[1-9][0-9]|1[0-9]{2}|2[0-4][0-9]|25[0-5])\.){3}(?:[0-9]|[1-9][0-9]|1[0-9]{2}|2[0-4][0-9]|25[0-5])$
        minLength: 0
        maxLength: 64
        x-go-custom-tag: db:"system_ip"
        x-nullable: true
      manage_ip:
        description: 管理ip
        type: string
        pattern: ^(?:(?:[0-9]|[1-9][0-9]|1[0-9]{2}|2[0-4][0-9]|25[0-5])\.){3}(?:[0-9]|[1-9][0-9]|1[0-9]{2}|2[0-4][0-9]|25[0-5])$
        minLength: 1
        maxLength: 64
        x-go-custom-tag: db:"manage_ip"
        x-nullable: true
      data_ip:
        description: 数据ip
        type: string
        pattern: ^(?:(?:[0-9]|[1-9][0-9]|1[0-9]{2}|2[0-4][0-9]|25[0-5])\.){3}(?:[0-9]|[1-9][0-9]|1[0-9]{2}|2[0-4][0-9]|25[0-5])$
        minLength: 0
        maxLength: 64
        x-go-custom-tag: db:"data_ip"
        x-nullable: true
      smartnic_man_ip:
        description: SmartNic 管理ip
        type: string
        pattern: ^(?:(?:[0-9]|[1-9][0-9]|1[0-9]{2}|2[0-4][0-9]|25[0-5])\.){3}(?:[0-9]|[1-9][0-9]|1[0-9]{2}|2[0-4][0-9]|25[0-5])$
        minLength: 0
        maxLength: 64
        x-go-custom-tag: db:"smartnic_man_ip"
        x-nullable: true
      smartnic_data_ip:
        description: SmartNic 数据ip
        type: string
        pattern: ^(?:(?:[0-9]|[1-9][0-9]|1[0-9]{2}|2[0-4][0-9]|25[0-5])\.){3}(?:[0-9]|[1-9][0-9]|1[0-9]{2}|2[0-4][0-9]|25[0-5])$
        minLength: 0
        maxLength: 64
        x-go-custom-tag: db:"smartnic_data_ip"
        x-nullable: true
      way_to_use:
        description: 用途
        type: integer
        format: int32
        minimum: 1
        maximum: 4
        x-go-custom-tag: db:"way_to_use"
        x-nullable: false
      u_count:
        description: u位
        type: integer
        format: int32
        minimum: 0
        maximum: 16777216
        x-go-custom-tag: db:"u_count"
        x-nullable: false
      mc_name:
        description: 厂商联系人姓名
        type: string
        minLength: 0
        maxLength: 64
        x-go-custom-tag: db:"mc_name"
        x-nullable: true
      mc_phone:
        description: 厂商联系人电话
        type: string
        pattern: ^1[3-9]\d{9}$
        minLength: 0
        maxLength: 64
        x-go-custom-tag: db:"mc_phone"
        x-nullable: true
      mc_email:
        description: 厂商联系人邮箱
        type: string
        pattern: ^[a-zA-Z0-9_-]+@[a-zA-Z0-9_-]+(\.[a-zA-Z0-9_-]+)+$
        minLength: 0
        maxLength: 64
        x-go-custom-tag: db:"mc_email"
        x-nullable: true
      created_at:
        description: 创建时间
        type: string
        format: dateTime
        x-go-custom-tag: db:"created_at,omitempty"
      updated_at:
        description: 更新时间
        type: string
        format: dateTime
        x-go-custom-tag: db:"updated_at,omitempty"
      warranty_start:
        description: 维保开始时间
        type: string
        format: dateTime
        x-go-custom-tag: db:"warranty_start,omitempty"
      warranty_end:
        description: 维保结束时间
        type: string
        format: dateTime
        x-go-custom-tag: db:"warranty_end,omitempty"
      procurement_time:
        description: 采购时间
        type: string
        format: dateTime
        x-go-custom-tag: db:"procurement_time,omitempty"
      arbitration_az:
        description: 逻辑仲裁az的服务器标识. true-标识为逻辑仲裁az
        type: boolean
        default: false
        x-go-custom-tag: db:"arbitration_az"
      server_category:
        description: 服务器分类.1-内部机器，2-外部机器(公有云)
        type: integer
        format: int32
        minimum: 1
        maximum: 2
        x-go-custom-tag: db:"server_category"
      ipmi_user:
        description: 管理口ipmi用户名
        type: string
        minLength: 0
        maxLength: 64
        x-go-custom-tag: db:"ipmi_user"
      ipmi_password:
        description: 管理口ipmi密码,使用base64传输
        type: string
        minLength: 0
        maxLength: 64
        x-go-custom-tag: db:"ipmi_password"
      root_password:
        description: 服务器root密码,使用base64传输
        type: string
        minLength: 0
        maxLength: 64
        x-go-custom-tag: db:"root_password"
  describe_server:
    type: object
    properties:
      uuid:
        description: uuid
        type: string
        minLength: 0
        maxLength: 32
        x-go-custom-tag: db:"uuid"
        x-nullable: false
      az_uuid:
        description: az_id
        type: string
        minLength: 0
        maxLength: 32
        x-go-custom-tag: db:"az_uuid"
        x-nullable: true
      region_uuid:
        description: region_id
        type: string
        minLength: 0
        maxLength: 32
        x-go-custom-tag: db:"region_uuid"
        x-nullable: true
      rack_uuid:
        description: rack uuid
        type: string
        minLength: 0
        maxLength: 32
        x-go-custom-tag: db:"rack_uuid"
        x-nullable: false
      rack_name:
        description: rack name
        type: string
        minLength: 0
        maxLength: 32
        x-go-custom-tag: db:"rack_name"
        x-nullable: false
      rack_network:
        type: string
        minLength: 0
        maxLength: 64
        x-go-custom-tag: db:"rack_network"
        x-nullable: true
      rack_network_mask:
        type: string
        minLength: 0
        maxLength: 64
        x-go-custom-tag: db:"rack_network_mask"
        x-nullable: true
      rack_tor_ip:
        type: string
        minLength: 0
        maxLength: 64
        x-go-custom-tag: db:"rack_tor_ip"
        x-nullable: true
      datacenter_uuid:
        description: uuid
        type: string
        minLength: 0
        maxLength: 32
        x-go-custom-tag: db:"datacenter_uuid"
        x-nullable: false
      datacenter_name:
        description: uuid
        type: string
        minLength: 0
        maxLength: 32
        x-go-custom-tag: db:"datacenter_name"
        x-nullable: false
      asset_state:
        description: 资产状态id 故障1、维修中2、离线3、在线4、装机中5
        type: integer
        format: int32
        minimum: 1
        maximum: 4
        x-go-custom-tag: db:"asset_state"
        x-nullable: false
      asset_number:
        description: 资产编号
        type: string
        minLength: 1
        maxLength: 64
        x-go-custom-tag: db:"asset_number"
        x-nullable: false
      serial_number:
        description: 序列号
        type: string
        minLength: 1
        maxLength: 64
        x-go-custom-tag: db:"serial_number"
        x-nullable: false
      brand:
        description: 品牌
        type: string
        minLength: 0
        maxLength: 64
        x-go-custom-tag: db:"brand"
        x-nullable: true
      model:
        description: 型号
        type: string
        minLength: 0
        maxLength: 64
        x-go-custom-tag: db:"model"
        x-nullable: true
      system_ip:
        description: 系统ip
        type: string
        pattern: ^(?:(?:[0-9]|[1-9][0-9]|1[0-9]{2}|2[0-4][0-9]|25[0-5])\.){3}(?:[0-9]|[1-9][0-9]|1[0-9]{2}|2[0-4][0-9]|25[0-5])$
        minLength: 0
        maxLength: 64
        x-go-custom-tag: db:"system_ip"
        x-nullable: true
      system_ip_mask:
        description: 系统IP MASK
        type: string
        minLength: 0
        maxLength: 64
        x-go-custom-tag: db:"system_ip_mask,omitempty"
        x-nullable: true
      system_ip_mac:
        description: 系统IP MAC
        type: string
        minLength: 0
        maxLength: 64
        x-go-custom-tag: db:"system_ip_mac,omitempty"
        x-nullable: true
      manage_ip:
        description: 管理ip
        type: string
        pattern: ^(?:(?:[0-9]|[1-9][0-9]|1[0-9]{2}|2[0-4][0-9]|25[0-5])\.){3}(?:[0-9]|[1-9][0-9]|1[0-9]{2}|2[0-4][0-9]|25[0-5])$
        minLength: 0
        maxLength: 64
        x-go-custom-tag: db:"manage_ip"
        x-nullable: true
      data_ip:
        description: 数据ip
        type: string
        pattern: ^(?:(?:[0-9]|[1-9][0-9]|1[0-9]{2}|2[0-4][0-9]|25[0-5])\.){3}(?:[0-9]|[1-9][0-9]|1[0-9]{2}|2[0-4][0-9]|25[0-5])$
        minLength: 0
        maxLength: 64
        x-go-custom-tag: db:"data_ip"
        x-nullable: true
      data_ip_mask:
        description: 数据IP MASK
        type: string
        minLength: 0
        maxLength: 64
        x-go-custom-tag: db:"data_ip_mask,omitempty"
        x-nullable: true
      smartnic_man_ip:
        description: SmartNic 管理ip
        type: string
        pattern: ^(?:(?:[0-9]|[1-9][0-9]|1[0-9]{2}|2[0-4][0-9]|25[0-5])\.){3}(?:[0-9]|[1-9][0-9]|1[0-9]{2}|2[0-4][0-9]|25[0-5])$
        minLength: 0
        maxLength: 64
        x-go-custom-tag: db:"smartnic_man_ip"
        x-nullable: true
      smartnic_man_ip_gateway:
        description: SmartNic 管理ip网关
        type: string
        minLength: 0
        maxLength: 64
        x-go-custom-tag: db:"smartnic_man_ip_gateway"
        x-nullable: true
      smartnic_data_ip:
        description: SmartNic 数据ip
        type: string
        pattern: ^(?:(?:[0-9]|[1-9][0-9]|1[0-9]{2}|2[0-4][0-9]|25[0-5])\.){3}(?:[0-9]|[1-9][0-9]|1[0-9]{2}|2[0-4][0-9]|25[0-5])$
        minLength: 0
        maxLength: 64
        x-go-custom-tag: db:"smartnic_data_ip"
        x-nullable: true
      mc_name:
        description: 厂商联系人姓名
        type: string
        minLength: 0
        maxLength: 64
        x-go-custom-tag: db:"mc_name"
        x-nullable: true
      mc_phone:
        description: 厂商联系人电话
        type: string
        pattern: ^1[3-9]\d{9}$
        minLength: 0
        maxLength: 64
        x-go-custom-tag: db:"mc_phone"
        x-nullable: true
      mc_email:
        description: 厂商联系人邮箱
        type: string
        pattern: ^[a-zA-Z0-9_-]+@[a-zA-Z0-9_-]+(\.[a-zA-Z0-9_-]+)+$
        minLength: 0
        maxLength: 64
        x-go-custom-tag: db:"mc_email"
        x-nullable: true
      u_count:
        description: u位
        type: integer
        format: int32
        minimum: 0
        maximum: 16777216
        x-go-custom-tag: db:"u_count"
        x-nullable: false
      warranty_start:
        description: 维保开始时间
        type: string
        format: dateTime
        x-go-custom-tag: db:"warranty_start,omitempty"
      warranty_end:
        description: 维保结束时间
        type: string
        format: dateTime
        x-go-custom-tag: db:"warranty_end,omitempty"
      procurement_time:
        description: 采购时间
        type: string
        format: dateTime
        x-go-custom-tag: db:"procurement_time,omitempty"
      tags:
        $ref: "#/definitions/tags"
      server_roles:
        type: array
        items:
          $ref: "#/definitions/server_role"
      arbitration_az:
        description: 逻辑仲裁az的服务器标识. true-标识为逻辑仲裁az
        type: boolean
        default: false
        x-go-custom-tag: db:"arbitration_az"
      server_category:
        description: 服务器分类.1-内部机器，2-外部机器(公有云)
        type: integer
        format: int32
        minimum: 1
        maximum: 2
        x-go-custom-tag: db:"server_category"
      ipmi_user:
        description: 管理口ipmi用户名
        type: string
        minLength: 0
        maxLength: 64
        x-go-custom-tag: db:"ipmi_user"
      ipmi_password:
        description: 管理口ipmi密码,使用base64传输
        type: string
        minLength: 0
        maxLength: 64
        x-go-custom-tag: db:"ipmi_password"
      root_password:
        description: 服务器root密码,使用base64传输
        type: string
        minLength: 0
        maxLength: 64
        x-go-custom-tag: db:"root_password"
      cluster_id:
        description: 集群id
        type: string
        x-go-custom-tag: db:"cluster_id"
  describe_detail_server:
    type: object
    properties:
      uuid:
        description: uuid
        type: string
        minLength: 0
        maxLength: 32
        x-go-custom-tag: db:"uuid"
        x-nullable: false
      datacenter_uuid:
        description: 机房uuid
        type: string
        minLength: 0
        maxLength: 32
        x-go-custom-tag: db:"datacenter_uuid"
        x-nullable: false
      datacenter_name:
        description: 机房name
        type: string
        minLength: 0
        maxLength: 64
        x-go-custom-tag: db:"datacenter_name"
        x-nullable: false
      az_uuid:
        description: az_id
        type: string
        minLength: 0
        maxLength: 32
        x-go-custom-tag: db:"az_uuid"
        x-nullable: true
      az_name:
        description: az_name
        type: string
        minLength: 0
        maxLength: 32
        x-go-custom-tag: db:"az_name"
        x-nullable: true
      region_uuid:
        description: region_id
        type: string
        minLength: 0
        maxLength: 32
        x-go-custom-tag: db:"region_uuid"
        x-nullable: true
      region_name:
        description: region_name
        type: string
        minLength: 0
        maxLength: 32
        x-go-custom-tag: db:"region_name"
        x-nullable: true
      rack_uuid:
        description: 机架UUID
        type: string
        minLength: 0
        maxLength: 32
        x-go-custom-tag: db:"rack_uuid"
        x-nullable: false
      rack_number:
        description: 机架编号
        type: string
        minLength: 0
        maxLength: 64
        x-go-custom-tag: db:"rack_number"
        x-nullable: false
      rack_network:
        description: 机架网段
        type: string
        minLength: 0
        maxLength: 64
        x-go-custom-tag: db:"rack_network"
        x-nullable: true
      rack_network_mask:
        description: 机架网段MASK
        type: string
        minLength: 0
        maxLength: 64
        x-go-custom-tag: db:"rack_network_mask"
        x-nullable: true
      rack_tor_ip:
        description: 机架网关IP
        type: string
        minLength: 0
        maxLength: 64
        x-go-custom-tag: db:"rack_tor_ip"
        x-nullable: true
      asset_state:
        description: 资产状态id 故障1、维修中2、离线3、在线4、装机中5
        type: integer
        format: int32
        minimum: 1
        maximum: 4
        x-go-custom-tag: db:"asset_state"
        x-nullable: false
      server_package_uuid:
        description: uuid
        type: string
        minLength: 0
        maxLength: 32
        x-go-custom-tag: db:"server_package_uuid"
        x-nullable: false
      os_package_uuid:
        description: uuid
        type: string
        minLength: 0
        maxLength: 32
        x-go-custom-tag: db:"os_package_uuid"
        x-nullable: true
      asset_number:
        description: 资产编号
        type: string
        minLength: 1
        maxLength: 64
        x-go-custom-tag: db:"asset_number"
        x-nullable: false
      serial_number:
        description: 序列号
        type: string
        minLength: 1
        maxLength: 64
        x-go-custom-tag: db:"serial_number"
        x-nullable: false
      brand:
        description: 品牌
        type: string
        minLength: 0
        maxLength: 64
        x-go-custom-tag: db:"brand"
        x-nullable: true
      model:
        description: 型号
        type: string
        minLength: 0
        maxLength: 64
        x-go-custom-tag: db:"model"
        x-nullable: true
      system_ip:
        description: 系统ip
        type: string
        pattern: ^(?:(?:[0-9]|[1-9][0-9]|1[0-9]{2}|2[0-4][0-9]|25[0-5])\.){3}(?:[0-9]|[1-9][0-9]|1[0-9]{2}|2[0-4][0-9]|25[0-5])$
        minLength: 0
        maxLength: 64
        x-go-custom-tag: db:"system_ip"
        x-nullable: true
      system_ip_mask:
        description: 系统IP MASK
        type: string
        minLength: 0
        maxLength: 64
        x-go-custom-tag: db:"system_ip_mask,omitempty"
        x-nullable: true
      system_ip_mac:
        description: 系统IP MAC
        type: string
        minLength: 0
        maxLength: 64
        x-go-custom-tag: db:"system_ip_mac,omitempty"
        x-nullable: true
      manage_ip:
        description: 管理ip
        type: string
        pattern: ^(?:(?:[0-9]|[1-9][0-9]|1[0-9]{2}|2[0-4][0-9]|25[0-5])\.){3}(?:[0-9]|[1-9][0-9]|1[0-9]{2}|2[0-4][0-9]|25[0-5])$
        minLength: 0
        maxLength: 64
        x-go-custom-tag: db:"manage_ip"
        x-nullable: true
      data_ip:
        description: 数据ip
        type: string
        pattern: ^(?:(?:[0-9]|[1-9][0-9]|1[0-9]{2}|2[0-4][0-9]|25[0-5])\.){3}(?:[0-9]|[1-9][0-9]|1[0-9]{2}|2[0-4][0-9]|25[0-5])$
        minLength: 0
        maxLength: 64
        x-go-custom-tag: db:"data_ip"
        x-nullable: true
      data_ip_mask:
        description: 数据IP MASK
        type: string
        minLength: 0
        maxLength: 64
        x-go-custom-tag: db:"data_ip_mask,omitempty"
        x-nullable: true
      smartnic_man_ip:
        description: SmartNic 管理ip
        type: string
        pattern: ^(?:(?:[0-9]|[1-9][0-9]|1[0-9]{2}|2[0-4][0-9]|25[0-5])\.){3}(?:[0-9]|[1-9][0-9]|1[0-9]{2}|2[0-4][0-9]|25[0-5])$
        minLength: 0
        maxLength: 64
        x-go-custom-tag: db:"smartnic_man_ip"
        x-nullable: true
      smartnic_man_ip_gateway:
        description: SmartNic 管理ip网关
        type: string
        minLength: 0
        maxLength: 64
        x-go-custom-tag: db:"smartnic_man_ip_gateway"
        x-nullable: true
      smartnic_data_ip:
        description: SmartNic 数据ip
        type: string
        pattern: ^(?:(?:[0-9]|[1-9][0-9]|1[0-9]{2}|2[0-4][0-9]|25[0-5])\.){3}(?:[0-9]|[1-9][0-9]|1[0-9]{2}|2[0-4][0-9]|25[0-5])$
        minLength: 0
        maxLength: 64
        x-go-custom-tag: db:"smartnic_data_ip"
        x-nullable: true
      way_to_use:
        description: 用途
        type: string
        minLength: 1
        maxLength: 4
        x-go-custom-tag: db:"way_to_use"
        x-nullable: false
      u_count:
        description: u位
        type: integer
        format: int32
        minimum: 0
        maximum: 16777216
        x-go-custom-tag: db:"u_count"
        x-nullable: false
      mc_name:
        description: 厂商联系人姓名
        type: string
        minLength: 0
        maxLength: 64
        x-go-custom-tag: db:"mc_name"
        x-nullable: true
      mc_phone:
        description: 厂商联系人电话
        type: string
        pattern: ^1[3-9]\d{9}$
        minLength: 0
        maxLength: 64
        x-go-custom-tag: db:"mc_phone"
        x-nullable: true
      mc_email:
        description: 厂商联系人邮箱
        type: string
        pattern: ^[a-zA-Z0-9_-]+@[a-zA-Z0-9_-]+(\.[a-zA-Z0-9_-]+)+$
        minLength: 0
        maxLength: 64
        x-go-custom-tag: db:"mc_email"
        x-nullable: true
      warranty_start:
        description: 维保开始时间
        type: string
        format: dateTime
        x-go-custom-tag: db:"warranty_start,omitempty"
        x-nullable: false
      warranty_end:
        description: 维保结束时间
        type: string
        format: dateTime
        x-go-custom-tag: db:"warranty_end,omitempty"
        x-nullable: false
      procurement_time:
        description: 采购时间
        type: string
        format: dateTime
        x-go-custom-tag: db:"procurement_time,omitempty"
        x-nullable: false
      arbitration_az:
        description: 逻辑仲裁az的服务器标识. true-标识为逻辑仲裁az
        type: boolean
        default: false
        x-go-custom-tag: db:"arbitration_az"
      server_category:
        description: 服务器分类.1-内部机器，2-外部机器(公有云)
        type: integer
        format: int32
        minimum: 1
        maximum: 2
        x-go-custom-tag: db:"server_category"
      ipmi_user:
        description: 管理口ipmi用户名
        type: string
        minLength: 0
        maxLength: 64
        x-go-custom-tag: db:"ipmi_user"
      ipmi_password:
        description: 管理口ipmi密码,使用base64传输
        type: string
        minLength: 0
        maxLength: 64
        x-go-custom-tag: db:"ipmi_password"
      root_password:
        description: 服务器root密码,使用base64传输
        type: string
        minLength: 0
        maxLength: 64
        x-go-custom-tag: db:"root_password"
  server_list:
    type: object
    properties:
      uuid:
        description: uuid
        type: string
        minLength: 0
        maxLength: 32
        x-go-custom-tag: db:"uuid"
        x-nullable: false
      serial_number:
        description: 序列号
        type: string
        minLength: 1
        maxLength: 64
        x-go-custom-tag: db:"serial_number"
        x-nullable: false
      asset_number:
        description: 资产编号
        type: string
        minLength: 1
        maxLength: 64
        x-go-custom-tag: db:"asset_number"
        x-nullable: false
  server_response:
    type: object
    required:
      - server
    properties:
      server:
        $ref: "#/definitions/server"
  describe_server_response:
    type: object
    required:
      - describe_server
    properties:
      describe_server:
        type: array
        items:
          $ref: "#/definitions/describe_server"
      total_count:
        description: total count
        type: integer
        format: int32
        minimum: 0
        maximum: 16777216
        x-go-custom-tag: db:"total_count"
        x-nullable: false
  #  describe_server_tag_response:
  #    type: object
  #    required:
  #      - tags
  #    properties:
  #      tags:
  #        type: array
  #        items:
  #          $ref: "#/definitions/server_tags"
  #          properties:
  #            server_uuid:
  #              type: string
  #              minLength: 0
  #              maxLength: 64
  #            tags:
  #              type: array
  #              items:
  #                $ref: "#/definitions/server_tag"
  describe_detail_server_response:
    type: object
    required:
      - describe_detail_server
    properties:
      describe_detail_server:
        $ref: "#/definitions/describe_detail_server"
  server_in_response:
    type: object
    required:
      - server
    properties:
      server:
        type: array
        items:
          $ref: "#/definitions/server_list"
  server_package:
    type: object
    properties:
      uuid:
        description: uuid
        type: string
        minLength: 0
        maxLength: 32
        x-go-custom-tag: db:"uuid"
        x-nullable: false
      package_name:
        description: 名称
        type: string
        minLength: 0
        maxLength: 25
        x-go-custom-tag: db:"package_name"
        x-nullable: false
      min_cpu_core_num:
        type: integer
        minimum: 0
        maximum: 16777216
        x-go-custom-tag: db:"min_cpu_core_num"
        x-nullable: true
      cpu_model:
        type: string
        minLength: 0
        maxLength: 255
        x-go-custom-tag: db:"cpu_model"
        x-nullable: true
      min_memory_size:
        type: integer
        minimum: 0
        maximum: 16777216
        x-go-custom-tag: db:"min_memory_size"
        x-nullable: true
      disk_num:
        type: integer
        minimum: 0
        maximum: 16777216
        x-go-custom-tag: db:"disk_num"
        x-nullable: true
      disk_size:
        type: integer
        minimum: 0
        maximum: 16777216
        x-go-custom-tag: db:"disk_size"
        x-nullable: true
      nic_num:
        type: integer
        minimum: 0
        maximum: 16777216
        x-go-custom-tag: db:"nic_num"
        x-nullable: true
      nic_model:
        type: string
        minLength: 0
        maxLength: 255
        x-go-custom-tag: db:"nic_model"
        x-nullable: true
      package_desc:
        type: string
        minLength: 0
        maxLength: 255
        x-go-custom-tag: db:"package_desc"
        x-nullable: true
      server_module_name:
        type: string
        minLength: 0
        maxLength: 64
        x-go-custom-tag: db:"server_module_name"
        x-nullable: true
      server_module_params:
        type: string
        minLength: 0
        maxLength: 2048
        x-go-custom-tag: db:"server_module_params"
        x-nullable: true
      sys_disk_hdd_num:
        type: integer
        minimum: 0
        maximum: 16777216
        x-go-custom-tag: db:"sys_disk_hdd_num"
        x-nullable: true
      sys_disk_hdd_size:
        type: integer
        minimum: 0
        maximum: 16777216
        x-go-custom-tag: db:"sys_disk_hdd_size"
        x-nullable: true
      sys_disk_ssd_num:
        type: integer
        minimum: 0
        maximum: 16777216
        x-go-custom-tag: db:"sys_disk_ssd_num"
        x-nullable: true
      sys_disk_ssd_size:
        type: integer
        minimum: 0
        maximum: 16777216
        x-go-custom-tag: db:"sys_disk_ssd_size"
        x-nullable: true
      sys_disk_raid_type:
        type: string
        minLength: 0
        maxLength: 45
        x-go-custom-tag: db:"sys_disk_raid_type"
        x-nullable: true
      data_disk_hdd_num:
        type: integer
        minimum: 0
        maximum: 16777216
        x-go-custom-tag: db:"data_disk_hdd_num"
        x-nullable: true
      data_disk_hdd_size:
        type: integer
        minimum: 0
        maximum: 16777216
        x-go-custom-tag: db:"data_disk_hdd_size"
        x-nullable: true
      data_disk_ssd_num:
        type: integer
        minimum: 0
        maximum: 16777216
        x-go-custom-tag: db:"data_disk_ssd_num"
        x-nullable: true
      data_disk_ssd_size:
        type: integer
        minimum: 0
        maximum: 16777216
        x-go-custom-tag: db:"data_disk_ssd_size"
        x-nullable: true
      data_disk_raid_type:
        type: string
        minLength: 0
        maxLength: 45
        x-go-custom-tag: db:"data_disk_raid_type"
        x-nullable: true
      data_disk_nvme_num:
        type: integer
        minimum: 0
        maximum: 16777216
        x-go-custom-tag: db:"data_disk_nvme_num"
        x-nullable: true
      data_disk_nvme_size:
        type: integer
        minimum: 0
        maximum: 16777216
        x-go-custom-tag: db:"data_disk_nvme_size"
        x-nullable: true
      gpu:
        type: string
        minLength: 0
        maxLength: 64
        x-go-custom-tag: db:"gpu"
        x-nullable: true
      created_at:
        type: string
        format: dateTime
        x-go-custom-tag: db:"created_at,omitempty"
      updated_at:
        type: string
        format: dateTime
        x-go-custom-tag: db:"updated_at,omitempty"
  server_package_list:
    type: object
    properties:
      uuid:
        description: uuid
        type: string
        minLength: 0
        maxLength: 32
        x-go-custom-tag: db:"uuid"
        x-nullable: false
      name:
        description: 名称
        type: string
        minLength: 1
        maxLength: 64
        x-go-custom-tag: db:"name"
        x-nullable: false
  server_package_response:
    type: object
    required:
      - server_package
    properties:
      server_package:
        $ref: "#/definitions/server_package"
  server_packages_response:
    type: object
    required:
      - server_packages
    properties:
      server_packages:
        type: array
        items:
          $ref: "#/definitions/server_package"
      total_count:
        description: total count
        type: integer
        format: int32
        minimum: 0
        maximum: 16777216
        x-go-custom-tag: db:"total_count"
        x-nullable: false
  server_package_in_response:
    type: object
    required:
      - server_packages
    properties:
      server_packages:
        type: array
        items:
          $ref: "#/definitions/server_package_list"
  asset_state:
    type: object
    properties:
      server:
        type: object
        properties:
          malfunction:
            description: 故障
            type: integer
            format: int32
            minimum: 0
            maximum: 16777216
          maintenance:
            description: 维修中
            type: integer
            format: int32
            minimum: 0
            maximum: 16777216
          offline:
            description: 离线
            type: integer
            format: int32
            minimum: 0
            maximum: 16777216
          online:
            description: 在线
            type: integer
            format: int32
            minimum: 0
            maximum: 16777216
      network_equipment:
        type: object
        properties:
          malfunction:
            description: 故障
            type: integer
            format: int32
            minimum: 0
            maximum: 16777216
          maintenance:
            description: 维修中
            type: integer
            format: int32
            minimum: 0
            maximum: 16777216
          offline:
            description: 离线
            type: integer
            format: int32
            minimum: 0
            maximum: 16777216
          online:
            description: 在线
            type: integer
            format: int32
            minimum: 0
            maximum: 16777216
  asset_years:
    type: object
    properties:
      server:
        type: object
        properties:
          year:
            type: array
            items:
              $ref: "#/definitions/years"
      network_equipment:
        type: object
        properties:
          year:
            type: array
            items:
              $ref: "#/definitions/years"
  years:
    type: object
    properties:
      year:
        type: string
        minLength: 0
        maxLength: 64
        x-go-custom-tag: db:"years"
      count:
        type: integer
        format: int32
        minimum: 0
        maximum: 16777216
        x-go-custom-tag: db:"count"
  asset_way_to_use:
    type: object
    properties:
      server:
        type: object
        properties:
          way_to_use:
            type: array
            items:
              $ref: "#/definitions/way_to_uses"
      network_equipment:
        type: object
        properties:
          way_to_use:
            type: array
            items:
              $ref: "#/definitions/way_to_uses"
  way_to_uses:
    type: object
    properties:
      way_to_use:
        type: string
        minLength: 0
        maxLength: 64
        x-go-custom-tag: db:"description"
      count:
        type: integer
        format: int32
        minimum: 0
        maximum: 16777216
        x-go-custom-tag: db:"count"
  asset_warranty_state:
    type: object
    properties:
      server:
        type: object
        properties:
          in_warranty:
            type: integer
            format: int32
            minimum: 0
            maximum: 16777216
            x-go-custom-tag: db:"in_warranty"
            x-nullable: false
          out_warranty:
            type: integer
            format: int32
            minimum: 0
            maximum: 16777216
            x-go-custom-tag: db:"out_warranty"
            x-nullable: false
      network_equipment:
        type: object
        properties:
          in_warranty:
            type: integer
            format: int32
            minimum: 0
            maximum: 16777216
            x-go-custom-tag: db:"in_warranty"
            x-nullable: false
          out_warranty:
            type: integer
            format: int32
            minimum: 0
            maximum: 16777216
            x-go-custom-tag: db:"out_warranty"
            x-nullable: false
  datacenter_values:
    type: object
    properties:
      uuid:
        description: uuid
        type: string
        minLength: 0
        maxLength: 32
        x-go-custom-tag: db:"uuid"
        x-nullable: false
      name:
        description: 机房名称
        type: string
        minLength: 0
        maxLength: 64
        x-go-custom-tag: db:"name"
        x-nullable: false
      az_uuid:
        description: uuid
        type: string
        minLength: 0
        maxLength: 32
        x-go-custom-tag: db:"az_uuid"
        x-nullable: true
      az_name:
        description: 机房名称
        type: string
        minLength: 0
        maxLength: 64
        x-go-custom-tag: db:"az_name"
        x-nullable: true
      region_uuid:
        description: uuid
        type: string
        minLength: 0
        maxLength: 32
        x-go-custom-tag: db:"region_uuid"
        x-nullable: true
      region_name:
        description: 机房名称
        type: string
        minLength: 0
        maxLength: 64
        x-go-custom-tag: db:"region_name"
        x-nullable: true
      rack_count:
        type: integer
        minimum: 0
        maximum: 16777216
        x-go-custom-tag: db:"rack_count"
        x-nullable: false
      server_count:
        type: integer
        minimum: 0
        maximum: 16777216
        x-go-custom-tag: db:"server_count"
        x-nullable: false
      network_equipment_count:
        type: integer
        minimum: 0
        maximum: 16777216
        x-go-custom-tag: db:"network_equipment_count"
        x-nullable: false
      u_count:
        type: integer
        minimum: 0
        maximum: 16777216
        x-go-custom-tag: db:"u_count"
        x-nullable: false
      u_used_count:
        type: integer
        minimum: 0
        maximum: 16777216
        x-go-custom-tag: db:"u_used_count"
        x-nullable: false
  kind_count:
    type: object
    properties:
      count:
        type: integer
        format: int32
        minimum: 0
        maximum: 16777216
        x-go-custom-tag: db:"count"
        x-nullable: true
  provider:
    type: object
    properties:
      name:
        type: string
        minLength: 0
        maxLength: 64
        x-go-custom-tag: db:"name"
        x-nullable: true
  provider_list:
    type: object
    properties:
      providers:
        type: array
        items:
          $ref: "#/definitions/provider"
  electrical_spec:
    type: object
    properties:
      name:
        type: string
        minLength: 0
        maxLength: 64
        x-go-custom-tag: db:"name"
        x-nullable: true
  electrical_spec_list:
    type: object
    properties:
      electrical_specs:
        type: array
        items:
          $ref: "#/definitions/electrical_spec"
  level:
    type: object
    properties:
      name:
        type: string
        minLength: 0
        maxLength: 64
        x-go-custom-tag: db:"name"
        x-nullable: true
  level_list:
    type: object
    properties:
      levels:
        type: array
        items:
          $ref: "#/definitions/level"
  brand:
    type: object
    properties:
      brand:
        type: string
        minLength: 0
        maxLength: 64
        x-go-custom-tag: db:"brand"
        x-nullable: true
  model:
    type: object
    properties:
      model:
        type: string
        minLength: 0
        maxLength: 64
        x-go-custom-tag: db:"model"
        x-nullable: true
  brand_model:
    type: object
    properties:
      brands:
        type: array
        items:
          $ref: "#/definitions/brand"
      models:
        type: array
        items:
          $ref: "#/definitions/model"
  tag:
    type: object
    properties:
      name:
        type: string
        minLength: 0
        maxLength: 64
        x-go-custom-tag: db:"name"
      value:
        type: string
        minLength: 0
        maxLength: 64
        x-go-custom-tag: db:"value"
  tags:
    type: array
    items:
      $ref: "#/definitions/tag"

  server_tag:
    type: object
    properties:
      server_uuid:
        type: string
        minLength: 0
        maxLength: 64
        x-go-custom-tag: db:"server_uuid"
      tags:
        $ref: "#/definitions/tags"

  server_tags:
    type: array
    items:
      $ref: "#/definitions/server_tag"

  eip_tag:
    type: object
    properties:
      eip_uuid:
        type: string
        minLength: 0
        maxLength: 64
        x-go-custom-tag: db:"eip_uuid"
      tags:
        $ref: "#/definitions/tags"
  eip_tags:
    type: array
    items:
      $ref: "#/definitions/eip_tag"
  tags_values:
    type: array
    items:
      $ref: "#/definitions/tag"

  user_pin:
    type: object
    properties:
      id:
        description: id
        type: string
        minLength: 0
        maxLength: 32
        x-go-custom-tag: db:"id"
        x-nullable: false
      name:
        description: name
        type: string
        minLength: 0
        maxLength: 64
        x-go-custom-tag: db:"name"
        x-nullable: false
      extra:
        description: extra
        type: string
        minLength: 0
        maxLength: 64
        x-go-custom-tag: db:"extra"
        x-nullable: true
      enabled:
        description: enabled
        type: integer
        format: int64
        x-go-custom-tag: db:"enabled"
        x-nullable: false
      domain_id:
        description: domain_id
        type: string
        minLength: 0
        maxLength: 64
        x-go-custom-tag: db:"domain_id"
        x-nullable: true
      default_project_id:
        description: default_project_id
        type: string
        minLength: 0
        maxLength: 64
        x-go-custom-tag: db:"default_project_id"
        x-nullable: true

  describes_delivery_plan_response:
    type: object
    properties:
      list:
        type: array
        items:
          $ref: "#/definitions/delivery_plan_response"
      total_count:
        description: total count
        type: integer
        format: int32
        minimum: 0
        maximum: 16777216
        x-nullable: false
  delivery_plan_response:
    type: object
    properties:
      uuid:
        description: uuid
        type: string
        minLength: 0
        maxLength: 32
        x-nullable: false
      region_name:
        type: string
        minLength: 0
        maxLength: 32
        x-nullable: false
      az_name:
        type: string
        minLength: 0
        maxLength: 32
        x-nullable: false
      release_id:
        description: 部署平台版本
        type: string
        minLength: 0
        maxLength: 32
        x-nullable: false
      product:
        description: 部署产品
        type: array
        items:
          type: string
        x-nullable: false
      description:
        description: 部署描述
        type: string
        minLength: 0
        maxLength: 32
        x-nullable: false
      state:
        description: 状态
        type: integer
        format: int32
        x-nullable: false
      arch:
        type: string
        description: 交付CPU架构
        maxLength: 10
        x-nullable: false
      quota:
        type: string
        description: 交付配额：light、small、medium、large
        maxLength: 10
        x-nullable: false
      createdAt:
        description: createdAt
        type: string
        minLength: 0
        maxLength: 32
        x-nullable: false

  describe_network_plan_targets_response:
    type: object
    required:
      - network_plan_targets
    properties:
      network_plan_targets:
        type: array
        items:
          $ref: "#/definitions/network_plan_target"
      total_count:
        description: total count
        type: integer
        format: int32
        minimum: 0
        maximum: 16777216
        x-go-custom-tag: db:"total_count"
        x-nullable: false

  network_plan_target:
    type: object
    properties:
      target:
        description: target
        type: string
      name:
        description: name
        type: string
      parent_target:
        description: 所属父级target
        type: string
      isOnly:
        description: 是否只能有一个
        type: boolean
      isMust:
        description: 是否必须有至少一条，否则影响交付执行
        type: boolean
      rule:
        type: string

  describe_network_plans_response:
    type: object
    required:
      - network_plans
    properties:
      network_plans:
        type: array
        items:
          $ref: "#/definitions/network_plan"
      total_count:
        description: total count
        type: integer
        format: int32
        minimum: 0
        maximum: 16777216
        x-go-custom-tag: db:"total_count"
        x-nullable: false

  describe_network_plan_detail_response:
    type: object
    required:
      - plan
    properties:
      plan:
        $ref: "#/definitions/network_plan"

  network_plan_status:
    type: object
    properties:
      networkPlanId:
        description: network plan 资源ID
        type: string
        minLength: 0
        maxLength: 64
        x-go-custom-tag: orm:"network_plan_id"
        x-nullable: false
      status:
        description: 状态
        type: string
        minLength: 0
        maxLength: 32
        x-go-custom-tag: orm:"status"
        x-nullable: true
      statusInfo:
        description: 状态详情
        type: string
        minLength: 0
        maxLength: 128
        x-go-custom-tag: orm:"status_info"
        x-nullable: true
      createdAt:
        description: 创建时间
        type: string
        format: dateTime
        x-go-custom-tag: orm:"created_at"
        x-nullable: false
      updatedAt:
        description: 更新时间
        type: string
        format: dateTime
        x-go-custom-tag: db:"updated_at,omitempty"

  network_plan:
    type: object
    properties:
      id:
        description: uuid
        type: string
        minLength: 0
        maxLength: 32
        x-go-custom-tag: orm:"ref_id"
        x-nullable: false
      target:
        description: target
        type: string
        minLength: 0
        maxLength: 32
        x-go-custom-tag: orm:"target"
        x-nullable: false
      name:
        description: name
        type: string
        minLength: 0
        maxLength: 64
        x-go-custom-tag: orm:"name"
        x-nullable: false
      subnet:
        description: subnet
        type: string
        minLength: 0
        maxLength: 32
        x-go-custom-tag: orm:"subnet"
        x-nullable: false
      gateway:
        description: gateway
        type: string
        minLength: 0
        maxLength: 32
        x-go-custom-tag: orm:"gateway"
        x-nullable: false
      mask:
        description: mask
        type: string
        minLength: 0
        maxLength: 32
        x-go-custom-tag: orm:"mask"
        x-nullable: false
      vlan:
        description: vlan
        type: integer
        x-go-custom-tag: orm:"vlan"
        x-nullable: false
      resId:
        description: 资源ID
        type: string
        minLength: 0
        maxLength: 64
        x-go-custom-tag: orm:"res_id"
        x-nullable: false
      resSn:
        description: 资源SN
        type: string
        minLength: 0
        maxLength: 128
        x-go-custom-tag: orm:"res_sn"
        x-nullable: false
      resName:
        description: 资源名称
        type: string
      resType:
        description: 资源类型
        type: string
        minLength: 0
        maxLength: 64
        x-go-custom-tag: orm:"res_type"
        x-nullable: false
      underlayIp:
        description: underlayIp
        type: string
        minLength: 0
        maxLength: 32
        x-go-custom-tag: orm:"underlay_ip"
        x-nullable: false
      overlayIp:
        description: overlayIp
        type: string
        minLength: 0
        maxLength: 32
        x-go-custom-tag: orm:"overlay_ip"
        x-nullable: false
      totalIpCount:
        description: totalIpCount
        type: integer
        x-go-custom-tag: orm:"total_ip_count"
        x-nullable: false
      regionUuid:
        description: region
        type: string
        minLength: 0
        maxLength: 32
        x-nullable: false
      regionName:
        description: region
        type: string
        minLength: 0
        maxLength: 32
        x-nullable: false
      description:
        description: description
        type: string
        minLength: 0
        maxLength: 32
        x-go-custom-tag: orm:"description"
        x-nullable: true
      createdAt:
        description: createdAt
        type: string
        minLength: 0
        maxLength: 32
        x-go-custom-tag: orm:"created_at"
        x-nullable: false
      attribute1:
        description: createdAt
        type: string
      attribute2:
        description: createdAt
        type: string
      attribute3:
        description: createdAt
        type: string
      network_plan_status:
        $ref: "#/definitions/network_plan_status"

  describe_device_conn_response:
    type: object
    required:
      - data
    properties:
      data:
        $ref: "#/definitions/device_conn"

  device_conn:
    type: object
    properties:
      deviceId:
        description: 设备ID
        type: string
        minLength: 0
        maxLength: 32
        x-go-custom-tag: orm:"device_id"
        x-nullable: false
      connType:
        description: 连接方式
        type: string
        minLength: 0
        maxLength: 32
        x-go-custom-tag: orm:"conn_type"
        x-nullable: false
      port:
        description: 端口
        type: integer
        format: int32
        minimum: 1
        maximum: 16777216
        x-go-custom-tag: orm:"port"
        x-nullable: false
      userName:
        description: 用户名
        type: string
        minLength: 0
        maxLength: 64
        x-go-custom-tag: orm:"user_name"
        x-nullable: false
      password:
        description: 密码
        type: string
        minLength: 0
        maxLength: 64
        x-go-custom-tag: orm:"password"
        x-nullable: false
      secretKey:
        description: 秘钥
        type: string
        minLength: 0
        maxLength: 64
        x-go-custom-tag: orm:"secret_key"
        x-nullable: false

  describe_network_topo_response:
    type: object
    required:
      - topo_data
    properties:
      topo_data:
        type: array
        items:
          $ref: "#/definitions/network_topology"

  network_topology:
    type: object
    properties:
      deviceId:
        description: 设备ID
        type: string
        minLength: 0
        maxLength: 64
        x-nullable: false
        x-omitempty: false
      model:
        description: 型号
        type: string
        x-omitempty: false
      serialNumber:
        type: string
        x-omitempty: false
      role:
        type: string
        x-omitempty: false
      level:
        type: integer
        format: int32
        x-omitempty: false
      inCmdb:
        type: boolean
        x-omitempty: false
      manageIp:
        type: string
        x-omitempty: false
      brand:
        type: string
        x-omitempty: false
      deviceType:
        type: string
        x-omitempty: false
      groupId:
        type: string
        x-omitempty: false
      datacenter:
        type: string
        x-omitempty: false
      rackNumber:
        type: string
        x-omitempty: false
      connections:
        type: array
        items:
          $ref: "#/definitions/network_topo_conn"

  network_topo_conn:
    type: object
    properties:
      interfaceId:
        description: ID
        type: string
        x-omitempty: false
      interfaceName:
        type: string
        x-omitempty: false
      speed:
        type: string
        x-omitempty: false
      peerDeviceId:
        type: string
        x-omitempty: false
      peerInterfaceId:
        type: string
        x-omitempty: false

  validate_network_plans_response:
    type: object
    required:
      - validates
    properties:
      validates:
        type: array
        items:
          $ref: "#/definitions/network_plan_validate"

  network_plan_validate:
    type: object
    properties:
      id:
        type: string
      target:
        description: target
        type: string
      subnet:
        type: string
      region:
        type: string
      resId:
        type: string
      underlayIp:
        description: underlayIp
        type: string
      overlayIp:
        description: overlayIp
        type: string
      err_message:
        type: string

  describe_bgp_as_number_descs_response:
    type: object
    properties:
      desc:
        type: array
        items:
          type: string

  describe_cmdb_op_log_response:
    type: object
    required:
      - cmdb_logs
    properties:
      cmdb_logs:
        type: array
        items:
          $ref: "#/definitions/cmdb_op_log"
      total_count:
        description: total count
        type: integer
        format: int32

  cmdb_op_log:
    type: object
    properties:
      uuid:
        description: uuid
        type: string
      res_id:
        type: string
      res_type:
        type: string
      op_user:
        type: string
      op_detail:
        type: array
        items:
          $ref: "#/definitions/cmdb_op_log_detail"
      created_at:
        description: createdAt
        type: string

  cmdb_op_log_detail:
    type: object
    properties:
      op_id:
        description: uuid
        type: string
      op_item:
        type: string
      data_before:
        type: string
        x-nullable: true
      data_after:
        type: string
        x-nullable: true
      created_at:
        description: createdAt
        type: string

  cmdb_log_save_request:
    type: object
    properties:
      az:
        $ref: "#/definitions/describe_available_zone"
      region:
        $ref: "#/definitions/region"
      datacenter:
        $ref: "#/definitions/datacenter"
      rack:
        $ref: "#/definitions/rack"
      server:
        $ref: "#/definitions/server"
      server_role:
        type: array
        items:
          $ref: "#/definitions/server_role"
      network_equipment:
        $ref: "#/definitions/network_equipment"
      network_plan:
        $ref: "#/definitions/network_plan"

  describe_role_summary_response:
    type: object
    required:
      - data
    properties:
      data:
        type: array
        items:
          $ref: "#/definitions/server_role_summary_data"

  server_role_summary_data:
    type: object
    properties:
      category:
        type: string
      count:
        type: integer
        format: int64

  describe_device_groups_response:
    type: object
    required:
      - groups
    properties:
      groups:
        type: array
        items:
          $ref: "#/definitions/device_group"
      total_count:
        description: total count
        type: integer
        format: int32

  device_group:
    type: object
    properties:
      region:
        type: string
      group_id:
        description: group_id
        type: string
      group_name:
        description: group名称
        type: string
      group_type:
        type: string
      device_type:
        type: string
      description:
        type: string
      devices:
        type: array
        items:
          $ref: "#/definitions/device_group_rel_res"
      created_at:
        description: createdAt
        type: string
      operate_flag:
        type: boolean

  describe_device_group_ports_response:
    type: object
    required:
      - group_ports
    properties:
      group_ports:
        type: array
        items:
          $ref: "#/definitions/device_group_port"

  device_group_port:
    type: object
    properties:
      port_id:
        type: string
      port_type:
        type: string
      in_use:
        type: boolean

  device_group_rel_res:
    type: object
    properties:
      uuid:
        type: string
      serial_number:
        type: string
      ip_addr:
        type: string

  Sort:
    type: object
    properties:
      direction:
        type: string
        description: |-
          排序条件的方向:
          asc - 正序
          desc - 倒序
      name:
        type: string
        description: 排序条件的名称

  describe_phy_direct_connects_response:
    type: object
    required:
      - pd_conns
    properties:
      pd_conns:
        type: array
        items:
          $ref: "#/definitions/phy_direct_connect"
      total_count:
        description: total count
        type: integer
        format: int32

  describe_phy_direct_connect_line_response:
    type: object
    required:
      - pdc_lines
    properties:
      pdc_lines:
        type: array
        items:
          $ref: "#/definitions/phy_direct_connect_line"
      total_count:
        description: total count
        type: integer
        format: int32

  describe_phy_direct_connect_segments_response:
    type: object
    required:
      - pdc_user_segments
    properties:
      pdc_user_segments:
        type: array
        items:
          $ref: "#/definitions/phy_direct_connect_user_segment"
      total_count:
        description: total count
        type: integer
        format: int32

  describe_phy_direct_connect_topology_response:
    type: object
    required:
      - topology_data
    properties:
      topology_data:
        type: array
        items:
          $ref: "#/definitions/phy_direct_connect_topology_data"

  describe_phy_direct_connect_response:
    type: object
    required:
      - pd_conn
    properties:
      pd_conn:
        $ref: "#/definitions/phy_direct_connect"

  phy_direct_connect:
    type: object
    properties:
      uuid:
        description: id
        type: string
      region:
        type: string
      name:
        type: string
      conn_state:
        type: integer
        format: int32
        x-omitempty: false
      status:
        type: integer
        format: int32
        x-omitempty: false
      conn_type:
        type: integer
        format: int32
        x-omitempty: false
      deploy_type:
        type: integer
        format: int32
        x-omitempty: false
      peer_region:
        type: string
      local_bgw_id:
        type: string
      peer_bgw_id:
        type: string
      local_cr_id:
        type: string
      peer_cr_id:
        type: string
      ce_id:
        type: string
      ce_port:
        type: string
      ce_port_index:
        type: integer
        format: int64
      fw_strategy:
        type: boolean
      fw_share:
        type: boolean
      description:
        type: string
      created_at:
        type: string
      pdc_lines:
        type: array
        items:
          $ref: "#/definitions/phy_direct_connect_line"
      pdc_user_segments:
        type: array
        items:
          $ref: "#/definitions/phy_direct_connect_user_segment"
      bandwidth_total:
        type: integer
        format: int32
        x-omitempty: false

  phy_direct_connect_topology_data:
    type: object
    properties:
      group_id:
        type: string
      group_type:
        type: string
      device_id:
        type: string
      device_ip:
        type: string
      device_sn:
        type: string
      up_port_id:
        type: string
      down_port_id:
        type: string
      region:
        type: string
      conn_state:
        type: integer
        format: int64
      vlan_list:
        type: array
        items:
          $ref: "#/definitions/network_vlan_data"
      opposite:
        $ref: "#/definitions/phy_direct_connect_topology_data"

  network_vlan_data:
    type: object
    properties:
      device_id:
        type: string
      vlan_id:
        type: integer
        format: int64
      vlan_name:
        type: string
      ip_addr:
        type: string

  describe_classic_link_pools_response:
    type: object
    properties:
      list:
        type: array
        items:
          $ref: "#/definitions/classic_link_pool"
      total_count:
        description: total count
        type: integer
        format: int32

  describe_classic_link_pool_ids_response:
    type: object
    properties:
      list:
        type: array
        items:
          type: string
          #$ref: "#/definitions/classic_link_pool_id"
      total_count:
        description: total count
        type: integer
        format: int32

  classic_link_pool:
    type: object
    properties:
      bgp_asn_end:
        type: integer
        format: int64
        description: BGP ASN结束始端口 范围(65001~65500)
      bgp_asn_start:
        type: integer
        format: int64
        description: BGP ASN开始端口 范围(65001~65500)
      created_at:
        type: string
        description: 创建时间,UTC格式
      cidr0:
        type: string
        description: 网络互通网段0
      cidr1:
        type: string
        description: 网络互通网段1
      pool_id:
        type: string
        description: ID
      vlan_end:
        type: integer
        format: int64
        description: VLAN结束始端口 范围(1~4094)
      vlan_start:
        type: integer
        format: int64
        description: VLAN开始始端口 范围(1~4094)

  phy_direct_connect_line:
    type: object
    properties:
      pdc_id:
        type: string
        description: 物理专线ID
      line_type:
        format: int32
        type: integer
      source_group_id:
        type: string
      source_group_name:
        type: string
      source_group_type:
        type: string
      source_port:
        type: string
      source_port_index:
        type: integer
        format: int64
      remote_group_id:
        type: string
      remote_group_name:
        type: string
      remote_group_type:
        type: string
      remote_port:
        type: string
      remote_port_index:
        type: integer
        format: int64

  phy_direct_connect_user_segment:
    type: object
    properties:
      pdc_id:
        type: string
        description: 物理专线ID
      user_pin:
        type: string
      tenant_id:
        type: string
      local_segment:
        type: string
      vpc_segment:
        type: string

  delete_network_slots_params:
    type: object
    properties:
      slot_ids:
        type: array
        items:
          type: string
      device_sns_not_in:
        type: array
        items:
          type: string
      device_mac_not_in:
        type: array
        items:
          type: string
      device_sns:
        type: array
        items:
          type: string

  describe_network_interfaces_response:
    type: object
    properties:
      data:
        type: array
        items:
          $ref: "#/definitions/network_interface_data"

  network_interface_data:
    type: object
    properties:
      device_ip:
        type: string
      device_id:
        type: string
      port_index:
        type: integer
        format: int64
      port_id:
        type: string
      port_name:
        type: string

  describe_cmdb_view_response:
    type: object
    properties:
      total_count:
        type: integer
        format: int32
      data:
        type: array
        items:
          $ref: "#/definitions/cmdb_view"

  cmdb_view:
    type: object
    properties:
      region:
        type: string
        description: Region
      az:
        type: string
        description: 可用区
      dc_name:
        type: string
        description: 机房名称
      rack_number:
        type: string
        description: 机柜编号
      category:
        type: string
        description: 产品角色分类
        x-omitempty: false
      service_code:
        type: string
        description: service code
        x-omitempty: false
      resource_code:
        type: string
        description: resource_code
        x-omitempty: false
      host_id:
        type: string
        description: 服务器ID
      host_ip:
        type: string
        description: 服务器IP
      host_status:
        type: integer
        description: 服务器状态
      role:
        type: string
        description: 服务器角色
        x-omitempty: false
      plane:
        type: string
        description: plane
        x-omitempty: false
      plane_category:
        type: string
        description: plane category
        x-omitempty: false

