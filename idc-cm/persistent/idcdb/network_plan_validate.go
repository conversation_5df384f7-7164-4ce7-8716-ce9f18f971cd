package idcdb

import (
	"database/sql"
	"errors"
	"fmt"
	"net"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"

	"coding.jd.com/fabric/zeusV2/idc-cm/gen/goframe/dao"
	"coding.jd.com/fabric/zeusV2/idc-cm/gen/goframe/model/entity"
	"coding.jd.com/fabric/zeusV2/idc-cm/gen/models"
	"coding.jd.com/fabric/zeusV2/idc-cm/gen/restapi/operations/network"
	"coding.jd.com/fabric/zeusV2/idc-cm/tools"
)

var (
	bgpAsNumberDesc = []string{
		"TOR交换机BGP AS号",
		"核心交换机BGP AS号",
		"边界交换机BGP AS号",
		"NF1服务器BGP AS号",
		"DNS服务器BGP AS号",
		"LB服务器BGP AS号",
		"K8S服务器BGP AS号",
		"抗DDOS服务器BGP AS号",
		"SDN-DR服务器BGP AS号",
	}
	rackPlans = []string{ark, storage, sdnVM, sdnDRVR, sdnVRDR, sdnVRVM, sdnDRSW}
)

func (cli *IdcDB) ValidateNetworkPlans(db *gdb.TX, params network.ValidateNetworkPlansParams) (res *models.ValidateNetworkPlansResponse) {

	if err := cli.ValidateNetworkPlanFields(db, "create", params.Plans); err != nil {
		n := &models.NetworkPlanValidate{
			ErrMessage: "校验失败，" + err.Error(),
		}
		res.Validates = append(res.Validates, n)
		return
	}
	return

	// res = &models.ValidateNetworkPlansResponse{Validates: make([]*models.NetworkPlanValidate, 0)}
	// // 先判断UUID是否重复
	// validateUUID := cli.validateUUID(db, params.Plans)
	// res.Validates = append(res.Validates, validateUUID...)
	// // 根据类型进行判断
	// npTargetMap := make(map[string][]*models.NetworkPlan)
	// for _, plan := range params.Plans {
	// 	if npTargetMap[plan.Target] == nil {
	// 		npTargetMap[plan.Target] = make([]*models.NetworkPlan, 0)
	// 	}
	// 	npTargetMap[plan.Target] = append(npTargetMap[plan.Target], plan)
	// }
	// // 其他类型判断，规则如下
	// //
	// // 1. subnet全局不能重复或者交叉
	// // 2. 机柜网络规划，同一个机柜，同一个target只能有一个
	// var records []*entity.PrimaryNetworkPlan
	// if err := db.Model(daoNetworkPlan.Table()).Scan(&records); err != nil && err != sql.ErrNoRows {
	// 	n := &models.NetworkPlanValidate{
	// 		ErrMessage: "规划查询失败，" + err.Error(),
	// 	}
	// 	res.Validates = append(res.Validates, n)
	// 	return
	// }
	//
	// for t, plans := range npTargetMap {
	// 	if plans == nil {
	// 		continue
	// 	}
	// 	if t == dns {
	// 		// DNS单独判断
	// 		validateDNS := cli.validateDNS(db, plans, true)
	// 		if validateDNS != nil {
	// 			res.Validates = append(res.Validates, validateDNS)
	// 		}
	// 	} else if t == lbs {
	// 		// LBS单独判断
	// 		validateLBS := cli.validateLBS(db, plans)
	// 		if validateLBS != nil && len(validateLBS) > 0 {
	// 			res.Validates = append(res.Validates, validateLBS...)
	// 		}
	// 	} else if t == bgpAsNumber {
	// 		validateNP := cli.validateBgpAsNumber(db, plans)
	// 		if validateNP != nil && len(validateNP) > 0 {
	// 			res.Validates = append(res.Validates, validateNP...)
	// 		}
	// 	} else {
	// 		validateNP := cli.validateSubnet(plans, records)
	// 		if validateNP != nil && len(validateNP) > 0 {
	// 			res.Validates = append(res.Validates, validateNP...)
	// 		}
	// 		validateRegion := cli.validateRegionInfo(plans)
	// 		if validateRegion != nil && len(validateRegion) > 0 {
	// 			res.Validates = append(res.Validates, validateRegion...)
	// 		}
	// 		validateName := cli.validateNpName(db, plans)
	// 		if validateName != nil && len(validateName) > 0 {
	// 			res.Validates = append(res.Validates, validateName...)
	// 		}
	// 	}
	// }
	// return
}

func (cli *IdcDB) validateUUID(db *gdb.TX, plans []*models.NetworkPlan) []*models.NetworkPlanValidate {
	// 如果传uuid，则需要判断uuid是否重复
	var (
		valResp = make([]*models.NetworkPlanValidate, 0)
		records []*struct {
			PLAN struct {
				RefId string `orm:"ref_id"`
			}
		}
	)
	data := make([]string, 0)
	// 首先，传入的数据内部不能重复
	for _, plan := range plans {
		if plan.ID != "" {
			appendFlag := true
			for _, datum := range data {
				if plan.ID == datum {
					validate := &models.NetworkPlanValidate{
						ErrMessage: fmt.Sprintf("数据内存在ID[%s]冲突", plan.ID),
						ID:         plan.ID,
					}
					valResp = append(valResp, validate)
					appendFlag = false
					break
				}
			}
			if appendFlag {
				data = append(data, plan.ID)
			}
		}
	}
	// 其次，不能和数据库冲突
	if len(data) > 0 {
		if err := db.Model(daoNetworkPlan.Table()).
			Where(dao.PrimaryOdinNetworkPlan.Columns().Uuid, data).
			ScanList(&records, "PLAN"); err != nil && err != sql.ErrNoRows {
			validate := &models.NetworkPlanValidate{
				ErrMessage: "ID查询数据库出错，" + err.Error(),
				ID:         "",
			}
			valResp = append(valResp, validate)
		}
		if len(records) > 0 {
			for _, record := range records {
				validate := &models.NetworkPlanValidate{
					ErrMessage: fmt.Sprintf("与已有数据的ID[%s]冲突", record.PLAN.RefId),
					ID:         record.PLAN.RefId,
				}
				valResp = append(valResp, validate)
			}
		}
	}
	return valResp
}

func (cli *IdcDB) validateDNS(db *gdb.TX, plans []*models.NetworkPlan, isCreate bool) *models.NetworkPlanValidate {
	if len(plans) > 1 {
		return &models.NetworkPlanValidate{
			Target:     dns,
			ErrMessage: "DNS只能配置一条",
		}
	}

	var dnsPlan entity.PrimaryOdinNetworkPlan
	// 导入数据中只有一个DNS配置，则需要判断数据库是否有
	if err := db.Model(daoNetworkPlan.Table()).
		Fields(&dnsPlan).Scan(&dnsPlan, g.Map{
		daoNetworkPlanColumns.TargetId: "dns",
	}); err != nil && err != sql.ErrNoRows {
		return &models.NetworkPlanValidate{
			Target:     dns,
			ErrMessage: "DNS查询失败，" + err.Error(),
		}
	}
	// 数据库已有DNS，则必须一样
	if isCreate && dnsPlan.Uuid != "" {
		if dnsPlan.UnderlayIp != plans[0].UnderlayIP || dnsPlan.OverlayIp != plans[0].OverlayIP {
			return &models.NetworkPlanValidate{
				Target:     dns,
				ErrMessage: "DNS配置冲突，请确认",
			}
		}
	} else {
		// 数据库没有dns配置，则添加的数据需要格式正确
		errMsg := ""
		_, _, err := net.ParseCIDR(plans[0].UnderlayIP)
		if err != nil {
			errMsg = errMsg + "UnderlayIp"
		}
		_, _, err = net.ParseCIDR(plans[0].OverlayIP)
		if err != nil {
			if len(errMsg) > 0 {
				errMsg = errMsg + "/"
			}
			errMsg = errMsg + "OverlayIP格式不正确;"
		}
		if errMsg != "" {
			return &models.NetworkPlanValidate{
				Target:     dns,
				ErrMessage: errMsg,
			}
		} else {
			cidr, err := tools.IntersectCIDR(plans[0].UnderlayIP, plans[0].OverlayIP)
			if err != nil {
				return &models.NetworkPlanValidate{
					Target:     dns,
					ErrMessage: "OverlayIP或者UnderlayIP异常",
				}
			} else if cidr {
				return &models.NetworkPlanValidate{
					Target:     dns,
					ErrMessage: "UnderlayIp和OverlayIP相等或存在交集，请确认",
				}
			}
		}
	}

	return nil
}

func (cli *IdcDB) validateLBS(db *gdb.TX, plans []*models.NetworkPlan) []*models.NetworkPlanValidate {
	var (
		dpErrs  = make([]*models.NetworkPlanValidate, 0)
		records []*struct {
			LBS entity.PrimaryOdinNetworkPlan
		}
	)

	if err := db.Model(daoNetworkPlan.Table()).Where(daoNetworkPlanColumns.TargetId, "lbs").
		ScanList(&records, "LBS"); err != nil && err != sql.ErrNoRows {
		n := &models.NetworkPlanValidate{
			Target:     lbs,
			ErrMessage: "查询失败，" + err.Error(),
		}
		dpErrs = append(dpErrs, n)
		return dpErrs
	}

	for _, plan := range plans {
		// 首先需要格式正确
		_, _, err := net.ParseCIDR(plan.UnderlayIP)
		_, _, err = net.ParseCIDR(plan.OverlayIP)
		if err != nil {
			pv1 := &models.NetworkPlanValidate{
				ID:         plan.ID,
				Target:     lbs,
				ErrMessage: "OverlayIP/OverlayIP格式异常，" + err.Error(),
			}
			dpErrs = append(dpErrs, pv1)
			continue
		}
		inter, _ := tools.IntersectCIDR(plan.UnderlayIP, plan.OverlayIP)
		if inter {
			pv1 := &models.NetworkPlanValidate{
				ID:         plan.ID,
				Target:     lbs,
				ErrMessage: "OverlayIP和OverlayIP相等或相交，请确认",
			}
			dpErrs = append(dpErrs, pv1)
			continue
		}
		if plan.RegionUUID == "" && plan.RegionName == "" {
			pv1 := &models.NetworkPlanValidate{
				ID:         plan.ID,
				Target:     lbs,
				ErrMessage: "Region信息缺失",
			}
			dpErrs = append(dpErrs, pv1)
		}
		// 若格式正确，其次需要满足不相交
		for _, lbsPlan := range records {
			// overlayIp和underlayIp都是cidr，判断是否相交
			if lbsPlan.LBS.Uuid != plan.ID {
				uiInt, err := tools.IntersectCIDR(lbsPlan.LBS.UnderlayIp, plan.UnderlayIP)
				if err != nil || uiInt {
					pv := &models.NetworkPlanValidate{
						ID:         plan.ID,
						Target:     "lbs",
						ErrMessage: "UnderlayIp与已有规划存在交集，请确认",
					}
					dpErrs = append(dpErrs, pv)
				}

				oiInt, err := tools.IntersectCIDR(lbsPlan.LBS.OverlayIp, plan.OverlayIP)
				if err != nil || oiInt {
					pv := &models.NetworkPlanValidate{
						ID:         plan.ID,
						Target:     "lbs",
						ErrMessage: "OverlayIp与已有规划存在交集，请确认",
					}
					dpErrs = append(dpErrs, pv)
				}
			}
		}
	}
	return dpErrs
}

func (cli *IdcDB) validateBgpAsNumber(db *gdb.TX, plans []*models.NetworkPlan) []*models.NetworkPlanValidate {
	var (
		dpErrs = make([]*models.NetworkPlanValidate, 0)
	)
	for _, plan := range plans {
		if plan.ResType != "server" && plan.ResType != "switch" {
			pv1 := &models.NetworkPlanValidate{
				ID:         plan.ID,
				Target:     plan.Target,
				ErrMessage: "不支持的资源类型",
			}
			dpErrs = append(dpErrs, pv1)
			continue
		}
		if plan.ResSn == "" {
			pv1 := &models.NetworkPlanValidate{
				ID:         plan.ID,
				Target:     plan.Target,
				ErrMessage: "请选择SN",
			}
			dpErrs = append(dpErrs, pv1)
			continue
		}
		if plan.ResType == "server" {
			if total, err := db.Model(daoServer.Table()).Where(daoServerColumns.SerialNumber, plan.ResSn).Count(); total == 0 || err != nil {
				pv1 := &models.NetworkPlanValidate{
					ID:         plan.ID,
					Target:     plan.Target,
					ErrMessage: "所选SN资源不存在",
				}
				dpErrs = append(dpErrs, pv1)
				continue
			}
		}
		if plan.ResType == "switch" {
			if total, err := db.Model(daoNetworkEquipment.Table()).Where(daoNetworkEquipmentColumns.SerialNumber, plan.ResSn).Count(); total == 0 || err != nil {
				pv1 := &models.NetworkPlanValidate{
					ID:         plan.ID,
					Target:     plan.Target,
					ErrMessage: "所选SN资源不存在",
				}
				dpErrs = append(dpErrs, pv1)
				continue
			}
		}
		// bgp as number的描述是固定的，且不能为空
		if !isItemExit(*plan.Description, bgpAsNumberDesc) {
			pv1 := &models.NetworkPlanValidate{
				ID:         plan.ID,
				Target:     plan.Target,
				ErrMessage: "BgpAsNumber的描述信息不正确",
			}
			dpErrs = append(dpErrs, pv1)
			continue
		}
	}
	return dpErrs
}

// validateSubnet
//
// subnet需要全局唯一，全局不相交
func (cli *IdcDB) validateSubnet(plans []*models.NetworkPlan, dbNps []*entity.PrimaryOdinNetworkPlan) []*models.NetworkPlanValidate {
	var (
		dpErrs     = make([]*models.NetworkPlanValidate, 0)
		subnetList = make([]string, 0)
	)
	for _, plan := range plans {
		if plan.Subnet == "" {
			continue
		}
		// 首先确认subnet是否符合格式要求
		_, _, err := net.ParseCIDR(plan.Subnet)
		if err != nil {
			pv1 := &models.NetworkPlanValidate{
				ID:         plan.ID,
				Target:     plan.Target,
				ErrMessage: "Subnet格式不正确",
			}
			dpErrs = append(dpErrs, pv1)
			continue
		}
		// 判断传入的数据内部是否存在重复
		for _, subnetOri := range subnetList {
			oiInt, err := tools.IntersectCIDR(subnetOri, plan.Subnet)
			if err != nil || oiInt {
				pv1 := &models.NetworkPlanValidate{
					ID:         plan.ID,
					Target:     plan.Target,
					ErrMessage: fmt.Sprintf("数据中Subnet[%s]存在重复数据，请确认", plan.Subnet),
				}
				dpErrs = append(dpErrs, pv1)
				break
			}
		}
		mask, err := tools.GetMask(plan.Subnet)
		if err != nil || (plan.Mask != "" && mask != plan.Mask) {
			pv1 := &models.NetworkPlanValidate{
				ID:         plan.ID,
				Target:     plan.Target,
				ErrMessage: "Mask与subnet不匹配，应为" + mask,
			}
			dpErrs = append(dpErrs, pv1)
		}
		subnetList = append(subnetList, plan.Subnet)
		// 再与数据库进行对比
		for _, np := range dbNps {
			if np.SubnetCidr != "" {
				oiInt, err := tools.IntersectCIDR(np.SubnetCidr, plan.Subnet)
				if err != nil || oiInt {
					pv1 := &models.NetworkPlanValidate{
						ID:         plan.ID,
						Target:     plan.Target,
						ErrMessage: "Subnet与[" + targetMap[np.TargetId] + "]的[" + np.SubnetCidr + "]重复或相交，请确认",
					}
					dpErrs = append(dpErrs, pv1)
					break
				}
			}
		}
	}
	return dpErrs
}

// 2. 机柜网络规划，同一个机柜，同一个target只能有一个，不能交叉
//
// @params plans 接口传入的数据
// @params dbNps 数据库中存在的数据
func (cli *IdcDB) validateByResId(plans []*models.NetworkPlan, dbNps []*entity.PrimaryOdinNetworkPlan) []*models.NetworkPlanValidate {
	var (
		dpErrs       = make([]*models.NetworkPlanValidate, 0)
		resPlanMap   = make(map[string][]*models.NetworkPlan)
		resPlanDBMap = make(map[string][]*entity.PrimaryOdinNetworkPlan)
	)

	for _, plan := range plans {
		// 首先确认subnet是否符合格式要求
		_, _, err := net.ParseCIDR(plan.Subnet)
		if err != nil {
			pv1 := &models.NetworkPlanValidate{
				ID:         plan.ID,
				Target:     plan.Target,
				ErrMessage: "Subnet格式不正确",
			}
			dpErrs = append(dpErrs, pv1)
			continue
		}
		if plan.ResID == "" {
			continue
		}
		if resPlanMap[plan.ResID] == nil {
			resPlanMap[plan.ResID] = make([]*models.NetworkPlan, 0)
		}

		resPlanMap[plan.ResID] = append(resPlanMap[plan.ResID], plan)
	}

	for _, np := range dbNps {
		if np.ResUuid == "" {
			continue
		}
		if resPlanDBMap[np.ResUuid] == nil {
			resPlanDBMap[np.ResUuid] = make([]*entity.PrimaryOdinNetworkPlan, 0)
		}

		resPlanDBMap[np.ResUuid] = append(resPlanDBMap[np.ResUuid], np)
	}

	for resId, networkPlans := range resPlanMap {
		// 如果大于1，则表示同一个机柜下同一个target存在多个，不允许
		if len(networkPlans) > 1 {
			pv1 := &models.NetworkPlanValidate{
				ID:         networkPlans[0].ID,
				Target:     networkPlans[0].Target,
				ResID:      networkPlans[0].ResID,
				ErrMessage: "机柜" + networkPlans[0].Target + "网络规划重复，请确认",
			}
			dpErrs = append(dpErrs, pv1)
		}
		dbPlans := resPlanDBMap[resId]
		if dbPlans != nil && len(dbPlans) > 0 {
			pv1 := &models.NetworkPlanValidate{
				ID:         networkPlans[0].ID,
				Target:     networkPlans[0].Target,
				ErrMessage: "机柜已有" + targetMap[networkPlans[0].Target] + "，请勿重复添加",
			}
			dpErrs = append(dpErrs, pv1)

		}
	}

	return dpErrs
}

func (cli *IdcDB) validateUpdate(db *gdb.TX, plan *models.NetworkPlan) *models.NetworkPlanValidate {
	var (
		record  entity.PrimaryOdinNetworkPlan
		records []*entity.PrimaryOdinNetworkPlan
	)
	if err := db.Model(daoNetworkPlan.Table()).Fields(record).Scan(&record, g.Map{
		daoNetworkPlanColumns.Uuid: plan.ID,
	}); err != nil {
		if err == sql.ErrNoRows {
			err = errors.New("所选规划不存在")
		} else {
			err = errors.New(fmt.Sprintf("规划查询失败: %s", err.Error()))
		}
		return &models.NetworkPlanValidate{
			ID:         plan.ID,
			Target:     plan.Target,
			ErrMessage: err.Error(),
		}
	}

	if err := db.Model(daoNetworkPlan.Table()).
		WhereNotIn(daoNetworkPlanColumns.Uuid, plan.ID).
		Where(daoNetworkPlanColumns.TargetId, record.TargetId).
		Scan(&records); err != nil && err != sql.ErrNoRows {
		return &models.NetworkPlanValidate{
			ID:         plan.ID,
			Target:     plan.Target,
			ErrMessage: err.Error(),
		}
	}
	// 如果网段发生变化，则需要重新校验
	if record.SubnetCidr != plan.Subnet {
		validateByRegion := cli.validateSubnet([]*models.NetworkPlan{plan}, records)
		if validateByRegion != nil && len(validateByRegion) > 0 {
			return validateByRegion[0]
		}
	}
	if record.SubnetMask != plan.Mask {
		mask, err := tools.GetMask(plan.Subnet)
		if err != nil || (plan.Mask != "" && mask != plan.Mask) {
			pv1 := &models.NetworkPlanValidate{
				ID:         plan.ID,
				Target:     plan.Target,
				ErrMessage: "Mask与subnet不匹配，应为" + mask,
			}
			return pv1
		}
	}
	if record.ResUuid != plan.ResID {
		if isItemExit(plan.Target, rackPlans) {
			validateRack := cli.validateRack(db, []*models.NetworkPlan{plan})
			if validateRack != nil && len(validateRack) > 0 {
				return validateRack[0]
			}
			validateByResId := cli.validateByResId([]*models.NetworkPlan{plan}, records)
			if validateByResId != nil && len(validateByResId) > 0 {
				return validateByResId[0]
			}
		}
	}
	if record.TargetId == "bgpAsNumber" {
		validateNP := cli.validateBgpAsNumber(db, []*models.NetworkPlan{plan})
		if validateNP != nil && len(validateNP) > 0 {
			return validateNP[0]
		}
	}
	if record.TargetId == "lbs" {
		validateNP := cli.validateLBS(db, []*models.NetworkPlan{plan})
		if validateNP != nil && len(validateNP) > 0 {
			return validateNP[0]
		}
	}
	if record.TargetId == "dns" {
		validateNP := cli.validateDNS(db, []*models.NetworkPlan{plan}, false)
		if validateNP != nil {
			return validateNP
		}
	}
	validateRegion := cli.validateRegionInfo([]*models.NetworkPlan{plan})
	if validateRegion != nil && len(validateRegion) > 0 {
		return validateRegion[0]
	}

	validateName := cli.validateNpName(db, []*models.NetworkPlan{plan})
	if validateName != nil && len(validateName) > 0 {
		return validateName[0]
	}

	return nil
}

func (cli *IdcDB) validateRegion(db *gdb.TX, plans []*models.NetworkPlan) []*models.NetworkPlanValidate {
	regionIds := make([]string, 0)
	for _, plan := range plans {
		if plan.RegionUUID != "" {
			if !isItemExit(plan.RegionUUID, regionIds) {
				regionIds = append(regionIds, plan.RegionUUID)
			}
		}
	}
	if len(regionIds) == 0 {
		return nil
	}
	var (
		records []*entity.PrimaryOdinRegion
		dpErrs  = make([]*models.NetworkPlanValidate, 0)
	)
	if err := cli.FetchRegionAll(db, &records, g.Map{daoRegionColumns.Uuid: regionIds}); err != nil {
		n := &models.NetworkPlanValidate{
			ErrMessage: "规划关联的region不正确，" + err.Error(),
		}
		dpErrs = append(dpErrs, n)
	} else if len(regionIds) != len(records) {
		n := &models.NetworkPlanValidate{
			ErrMessage: "规划关联的region不正确",
		}
		dpErrs = append(dpErrs, n)
	}
	// 检查region是否为启用状态
	isAvailable, err := cli.CheckRegionsIsAvailable(db, regionIds)
	if err != nil {
		n := &models.NetworkPlanValidate{
			ErrMessage: "规划关联的region状态不正确，" + err.Error(),
		}
		dpErrs = append(dpErrs, n)
	} else if !isAvailable { // 状态为false即不可创建或修改
		n := &models.NetworkPlanValidate{
			ErrMessage: "规划关联的region状态不正确",
		}
		dpErrs = append(dpErrs, n)
	}

	return dpErrs
}

func (cli *IdcDB) validateRack(db *gdb.TX, plans []*models.NetworkPlan) []*models.NetworkPlanValidate {
	var (
		racks   []*entity.PrimaryOdinRack
		dpErrs  = make([]*models.NetworkPlanValidate, 0)
		rackIds = make([]string, 0)
	)
	for _, plan := range plans {
		if isItemExit(plan.Target, rackPlans) && plan.ResID == "" {
			n := &models.NetworkPlanValidate{
				Target:     plan.Target,
				ErrMessage: "所属机柜为空",
			}
			dpErrs = append(dpErrs, n)
			continue
		}
		if isItemExit(plan.Target, rackPlans) && plan.ResID != "" {
			if !isItemExit(plan.ResID, rackIds) {
				rackIds = append(rackIds, plan.ResID)
			}
		}
	}
	if len(rackIds) == 0 {
		return dpErrs
	}

	if err := cli.FetchRackAll(db, &racks, g.Map{daoRackColumns.Uuid: rackIds}); err != nil {
		n := &models.NetworkPlanValidate{
			ErrMessage: "规划关联的机柜不正确，" + err.Error(),
		}
		dpErrs = append(dpErrs, n)
	} else if len(racks) != len(rackIds) {
		n := &models.NetworkPlanValidate{
			ErrMessage: "规划关联的机柜不正确",
		}
		dpErrs = append(dpErrs, n)
	}
	return dpErrs
}

func (cli *IdcDB) validateRegionInfo(plans []*models.NetworkPlan) []*models.NetworkPlanValidate {
	dpErrs := make([]*models.NetworkPlanValidate, 0)
	if plans != nil && len(plans) > 0 {
		regionPlan := []string{lbs, ispBGPs, jdIPv6s, interPriOverlaps, inats}
		for _, plan := range plans {
			if isItemExit(plan.Target, regionPlan) && plan.RegionUUID == "" && plan.RegionName == "" {
				n := &models.NetworkPlanValidate{
					Target:     plan.Target,
					ErrMessage: "Region信息缺失",
				}
				dpErrs = append(dpErrs, n)
			}
		}
	}
	return dpErrs
}

func (cli *IdcDB) validateNpName(db *gdb.TX, plans []*models.NetworkPlan) []*models.NetworkPlanValidate {
	dpErrs := make([]*models.NetworkPlanValidate, 0)
	regionPlan := []string{ispBGPs, jdIPv6s, interPriOverlaps, inats}
	if plans != nil && len(plans) > 0 {
		for _, plan := range plans {
			if isItemExit(plan.Target, regionPlan) && plan.Name == "" {
				n := &models.NetworkPlanValidate{
					Target:     plan.Target,
					ErrMessage: "名称信息为空",
				}
				dpErrs = append(dpErrs, n)
				continue
			}
			if plan.Name == "" {
				continue
			}
			query := db.Model(daoNetworkPlan.Table())
			query.Where(daoNetworkPlanColumns.Name, plan.Name)
			query.Where(daoNetworkPlanColumns.TargetId, plan.Target)
			if plan.ID != "" {
				query.WhereNot(daoNetworkPlanColumns.Uuid, plan.ID)
			}
			if count, err := query.Count(); err != nil {
				n := &models.NetworkPlanValidate{
					Target:     plan.Target,
					ErrMessage: "名称检查失败，" + err.Error(),
				}
				dpErrs = append(dpErrs, n)
			} else if count > 0 {
				n := &models.NetworkPlanValidate{
					Target:     plan.Target,
					ErrMessage: "名称[" + plan.Name + "]已存在",
				}
				dpErrs = append(dpErrs, n)
			}
		}
	}
	return dpErrs
}

func isItemExit(item string, arr []string) bool {
	if arr == nil || len(arr) == 0 {
		return false
	}
	for _, str := range arr {
		if str == item {
			return true
		}
	}
	return false
}
