package idcdb

import (
	"context"
	"encoding/json"
	"fmt"
	"strings"
	"testing"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gctx"

	"coding.jd.com/fabric/zeusV2/idc-cm/gen/models"
	"coding.jd.com/fabric/zeusV2/idc-cm/gen/restapi/operations/network"
	"coding.jd.com/fabric/zeusV2/idc-cm/tools"
)

func Test_NPT_Describes(t *testing.T) {
	var (
		db = g.DB() // 开数据库
	)
	subnet := "2004:fe37::/32"
	mask, err := tools.GetMask(subnet)
	if err != nil {
		fmt.Println(err.Error())
		return
	}
	fmt.Println(mask)

	_ = db.Transaction(gctx.New(), func(ctx context.Context, tx *gdb.TX) error {
		req := network.DescribeNetworkPlanTargetsParams{}
		rest, err := Root.DescribeNetworkPlanTargets(tx, req)
		if err != nil {
			t.Errorf("失败， %s", err.Error())
		}
		marshal, _ := json.MarshalIndent(rest, "", "\t")
		fmt.Println(string(marshal))
		return nil
	})
}

func Test_NPT_CreateValidate(t *testing.T) {
	var (
		db = g.DB() // 开数据库
	)

	// 定义测试任务预期数据
	tests := []*struct {
		task   string
		params []*models.NetworkPlan
		want   string
	}{
		{
			task: "dns全局只能有一条校验测试",
			params: []*models.NetworkPlan{
				{
					OverlayIP:  "*******/27",
					Target:     "dns",
					UnderlayIP: "*******/27",
				},
			},
			want: "dns is allowed only one, but already exists",
		},
		{
			task: "机柜sdnVM网络IP字段校验",
			params: []*models.NetworkPlan{
				{
					ResID:   "test23",
					Target:  "sdnVM",
					Subnet:  "*******/27",
					Gateway: "4.2.81",
					Vlan:    123,
				},
			},
			want: "网关[Gateway] value must be ip",
		},
		{
			task: "机柜sdnVM网络必须在compute内校验",
			params: []*models.NetworkPlan{
				{
					Target:     "compute",
					Subnet:     "*******/24",
					RegionUUID: "cn-north-1",
				},
				{
					ResID:   "test23",
					Target:  "sdnVM",
					Subnet:  "*******/27",
					Gateway: "*******",
					Vlan:    123,
				},
			},
			want: "[sdnVM] subnet value *******/27 must in [compute] subnet",
		},
		{
			task: "机柜sdnVM网络正常校验",
			params: []*models.NetworkPlan{
				{
					Target:     "compute",
					Subnet:     "*******/24",
					RegionUUID: "cn-north-1",
				},
				{
					ResID:   "test22",
					Target:  "sdnVM",
					Subnet:  "*******/27",
					Gateway: "*******",
					Vlan:    123,
				},
			},
			want: "",
		},
	}

	_ = db.Transaction(gctx.New(), func(ctx context.Context, tx *gdb.TX) error {
		for _, ta := range tests {
			t.Run(ta.task, func(t *testing.T) {
				err := Root.ValidateNetworkPlanFields(tx, "create", ta.params)
				if err != nil {
					fmt.Println(err.Error())
				}
				if ta.want != "" {
					if err == nil {
						t.Errorf("预期 %s ,但是结果为 nil", ta.want)
					} else if strings.Index(err.Error(), ta.want) == -1 {
						t.Errorf("预期 %s ,但是结果为 %s", ta.want, err)
					}
				} else if err != nil {
					t.Errorf("预期 nil ,但是结果为 %s", err)
				}
			})
		}
		return nil
	})
}

func Test_NPT_UpdateValidate(t *testing.T) {
	var (
		db = g.DB() // 开数据库
	)

	// 定义测试任务预期数据
	tests := []*struct {
		task   string
		params []*models.NetworkPlan
		want   string
	}{
		{
			task: "dns全局只能有一条校验测试",
			params: []*models.NetworkPlan{
				{
					ID:         "xxx",
					OverlayIP:  "*******/27",
					Target:     "dns",
					UnderlayIP: "*******/27",
				},
			},
			want: "dns is allowed only one, but already exists",
		},
	}

	_ = db.Transaction(gctx.New(), func(ctx context.Context, tx *gdb.TX) error {
		for _, ta := range tests {
			t.Run(ta.task, func(t *testing.T) {
				err := Root.ValidateNetworkPlanFields(tx, "update", ta.params)
				if err != nil {
					fmt.Println(err.Error())
				}
				if ta.want != "" {
					if err == nil {
						t.Errorf("预期 %s ,但是结果为 nil", ta.want)
					} else if strings.Index(err.Error(), ta.want) == -1 {
						t.Errorf("预期 %s ,但是结果为 %s", ta.want, err)
					}
				} else if err != nil {
					t.Errorf("预期 nil ,但是结果为 %s", err)
				}
			})
		}
		return nil
	})
}
