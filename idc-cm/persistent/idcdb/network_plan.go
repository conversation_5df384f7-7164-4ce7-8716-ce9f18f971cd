package idcdb

import (
	"database/sql"
	"errors"
	"fmt"
	"strings"

	"github.com/gogf/gf/v2/container/gvar"
	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/util/gconv"
	"github.com/gogf/gf/v2/util/gutil"
	"github.com/openlyinc/pointy"

	"coding.jd.com/fabric/zeusV2/idc-cm/gen/goframe/dao"
	"coding.jd.com/fabric/zeusV2/idc-cm/gen/goframe/model/entity"
	"coding.jd.com/fabric/zeusV2/idc-cm/gen/models"
	"coding.jd.com/fabric/zeusV2/idc-cm/gen/restapi/operations/network"
	"coding.jd.com/fabric/zeusV2/idc-cm/gen/restapi/operations/network_equipment"
	"coding.jd.com/fabric/zeusV2/idc-cm/gen/restapi/operations/server"
	"coding.jd.com/fabric/zeusV2/idc-cm/tools"
)

var (
	daoNetworkPlan              = dao.PrimaryOdinNetworkPlan
	daoNetworkPlanColumns       = daoNetworkPlan.Columns()
	daoNetworkPlanStatus        = dao.PrimaryNetworkPlanStatus
	daoNetworkPlanStatusColumns = daoNetworkPlanStatus.Columns()
	targetMap                   = map[string]string{
		"ispBGPs":          "EIP-IPv4",
		"jdIPv6s":          "EIP-IPv6",
		"interPriOverlaps": "PaaS资源网段规划",
		"inats":            "inat资源网段规划",
		"local":            "本地物理接入网段规划",
		"others":           "其他资源网段规划",
		"lbs":              "负载均衡网段规划",
		"dns":              "DNS规划",
		"ark":              "云翼网段规划",
		"container":        "容器网段规划",
		"storage":          "存储数据网段规划",
		"sdnDRVR":          "dr和vr通信",
		"sdnIPv6DRVR":      "dr和vr通信(IPv6)",
		"sdnVRDR":          "vr和dr通信",
		"sdnIPv6VRDR":      "vr和dr通信(IPv6)",
		"sdnVRVM":          "vr和vm通信",
		"sdnIPv6VRVM":      "vr和vm通信(IPv6)",
		"sdnDRSW":          "dr和交换机通信",
		"sdnIPv6DRSW":      "dr和交换机通信(IPv6)",
		"sdnVM":            "虚机数据口网段规划",
		"bgpAsNumber":      "BGP AS号规划",
		"k8sCalico":        "k8s_calico容器网段规划",
		"k8sClusterIp":     "K8s_ClusterIp网段规划",
		"sdnVRBGW":         "vr配置-vr和bgw通信",
		"sdnVRNATGW":       "vr配置-vr和natgw通信",
		"sdnDRNATGW":       "dr配置-dr和natgw通信",
		"sdnIPv6DRBGP":     "dr配置-dr和bgp通信(IPv6)",
	}
)

const (
	local            = "local"
	others           = "others"
	dns              = "dns"
	lbs              = "lbs"
	ispBGPs          = "ispBGPs"
	jdIPv6s          = "jdIPv6s"
	interPriOverlaps = "interPriOverlaps"
	inats            = "inats"
	bgpAsNumber      = "bgpAsNumber"
	ark              = "ark"
	storage          = "storage"
	sdnVM            = "sdnVM"
	sdnDRVR          = "sdnDRVR"
	sdnVRDR          = "sdnVRDR"
	sdnVRVM          = "sdnVRVM"
	sdnDRSW          = "sdnDRSW"
	k8sCalico        = "k8sCalico"
	k8sClusterIp     = "k8sClusterIp"
)

func (cli *IdcDB) getDeviceSnMap(db *gdb.TX) map[string]string {
	idSnMap := make(map[string]string)
	servResp, _ := cli.DescribeServer(db, server.DescribeServerParams{
		Condition: server.DescribeServerBody{},
		PageNo:    -1,
		PageSize:  10,
	})
	if servResp != nil && len(servResp.DescribeServer) > 0 {
		for _, serv := range servResp.DescribeServer {
			idSnMap[serv.UUID] = serv.SerialNumber
		}
	}

	netResp, _ := cli.DescribeNetworkEquipment(db, network_equipment.DescribeNetworkEquipmentParams{
		Condition: network_equipment.DescribeNetworkEquipmentBody{},
		PageNo:    -1,
		PageSize:  10,
	})
	if netResp != nil && len(netResp.NetworkEquipments) > 0 {
		for _, netEqp := range netResp.NetworkEquipments {
			idSnMap[netEqp.UUID] = netEqp.SerialNumber
		}
	}

	return idSnMap
}

func (cli *IdcDB) DescribeNetworkPlans(db *gdb.TX, params network.DescribeNetworkPlansParams) (res *models.DescribeNetworkPlansResponse, err error) {
	var (
		total       int
		query       = db.Model(daoNetworkPlan.Table())
		statusQuery = db.Model(daoNetworkPlanStatus.Table())
		records     []*struct {
			PLAN entity.PrimaryOdinNetworkPlan
			RACK struct { // Rack
				UUID       string `orm:"uuid"`
				RackNumber string `orm:"rack_number"`
			}
			Region     entity.PrimaryOdinRegion
			PlanStatus entity.PrimaryNetworkPlanStatus
		}
	)

	// 获取所有物理机和网络设备的id/sn对应关系map
	idSnMap := cli.getDeviceSnMap(db)

	res = &models.DescribeNetworkPlansResponse{
		NetworkPlans: []*models.NetworkPlan{},
		TotalCount:   0,
	}

	//  筛选条件
	if params.Condition.Subnet != nil {
		query = query.Where(daoNetworkPlanColumns.SubnetCidr, params.Condition.Subnet)
	}
	if params.Condition.RegionUUID != nil {
		query = query.Where(daoNetworkPlanColumns.RegionUuid, params.Condition.RegionUUID)
	}
	if params.Condition.Target != nil {
		query = query.Where(daoNetworkPlanColumns.TargetId, params.Condition.Target)
	}
	if params.Condition.BatchTarget != nil {
		query = query.Where(daoNetworkPlanColumns.TargetId, params.Condition.BatchTarget)
	}
	if params.Condition.ResID != nil {
		if idSnMap[*params.Condition.ResID] != "" {
			query = query.Where("res_uuid = ? or res_sn = ?", params.Condition.ResID, idSnMap[*params.Condition.ResID])
		} else {
			query = query.Where(daoNetworkPlanColumns.ResUuid, params.Condition.ResID)
		}
	}
	if params.Condition.ResSN != nil {
		resId := ""
		for id, sn := range idSnMap {
			if sn == *params.Condition.ResSN {
				resId = id
				break
			}
		}
		if resId != "" {
			query = query.Where("res_uuid = ? or res_sn = ?", resId, params.Condition.ResSN)
		} else {
			query = query.Where(daoNetworkPlanColumns.ResSn, params.Condition.ResSN)
		}
	}
	if params.Condition.ResType != nil {
		query = query.Where(daoNetworkPlanColumns.ResKind, params.Condition.ResType)
	}
	if params.Condition.AsNumber != nil {
		query = query.Where(daoNetworkPlanColumns.Attribute1, params.Condition.AsNumber)
	}
	if params.Condition.Description != nil {
		query = query.Where(daoNetworkPlanColumns.Description, params.Condition.Description)
	}
	// 获取状态信息
	if params.Condition.Status != nil {
		var values []*gvar.Var
		statusQuery = statusQuery.
			Where(daoNetworkPlanStatusColumns.Status, params.Condition.Status)
		if values, err = statusQuery.
			Fields(npNetworkPlanStatusColumns.NetworkPlanId).Distinct().Array(); err != nil {
			cli.logger.Warn(err)
			return
		} else if len(values) == 0 {
			// 当筛选条件结果为空时直接跳过后续查询
			return
		}
		query = query.Where(daoNetworkPlanColumns.Uuid, values)
	}
	// 分页及查询总条数
	if params.PageNo > -1 {
		query = query.Page(int(params.PageNo)+1, int(params.PageSize))
		if total, err = query.Count(); err != nil {
			return res, errors.New("failed to describe")
		}
	}

	// 排序及提交查询
	if err = query.
		OrderDesc(daoNetworkPlanColumns.TargetId).
		OrderDesc(daoNetworkPlanColumns.SubnetCidr).
		OrderDesc(daoNetworkPlanColumns.UpdatedAt).
		ScanList(&records, "PLAN"); err != nil {
		return res, errors.New("failed to describe")
	}
	if params.PageNo <= -1 {
		total = len(records)
	}

	// 获取机架信息
	if err = db.Model(daoRack.Table()).
		Where(daoRackColumns.Uuid, gutil.ListItemValuesUnique(records, "PLAN", "ResUuid")).
		ScanList(&records, "RACK", "PLAN", "uuid:ResUuid"); err != nil {
	}

	if err = db.Model(daoRegion.Table()).
		Where(daoRegionColumns.Uuid, gutil.ListItemValuesUnique(records, "PLAN", "RegionUuid")).
		ScanList(&records, "Region", "PLAN", "uuid:RegionUuid"); err != nil {
	}

	if err = db.Model(daoNetworkPlanStatus.Table()).
		Where(npNetworkPlanStatusColumns.NetworkPlanId, gutil.ListItemValuesUnique(records, "PLAN", "Uuid")).
		ScanList(&records, "PlanStatus", "PLAN", "NetworkPlanId:Uuid"); err != nil {
	}

	// 构建返回结果
	data := make([]*models.NetworkPlan, len(records))
	for i, record := range records {
		data[i] = &models.NetworkPlan{
			CreatedAt:    record.PLAN.CreatedAt.UTC().Layout(RFC3339TZ),
			Description:  &record.PLAN.Description,
			Gateway:      record.PLAN.Gateway,
			ID:           record.PLAN.Uuid,
			Mask:         record.PLAN.SubnetMask,
			Name:         record.PLAN.Name,
			OverlayIP:    record.PLAN.OverlayIp,
			RegionUUID:   record.PLAN.RegionUuid,
			RegionName:   record.Region.Uuid,
			ResID:        record.PLAN.ResUuid,
			ResSn:        record.PLAN.ResSn,
			ResType:      record.PLAN.ResKind,
			Subnet:       record.PLAN.SubnetCidr,
			Target:       record.PLAN.TargetId,
			TotalIPCount: int64(record.PLAN.TotalIpCount),
			UnderlayIP:   record.PLAN.UnderlayIp,
			Vlan:         int64(record.PLAN.Vlan),
			ResName:      record.RACK.RackNumber,
			Attribute1:   record.PLAN.Attribute1,
			Attribute2:   record.PLAN.Attribute2,
			Attribute3:   record.PLAN.Attribute3,
			NetworkPlanStatus: &models.NetworkPlanStatus{
				Status:     &record.PlanStatus.Status,
				StatusInfo: &record.PlanStatus.StatusInfo,
			},
		}
	}

	res = &models.DescribeNetworkPlansResponse{
		NetworkPlans: data,
		TotalCount:   int32(total),
	}
	return
}

func (cli *IdcDB) DescribeNetworkPlanDetail(db *gdb.TX, params network.DescribeNetworkPlanDetailParams) (res *models.DescribeNetworkPlanDetailResponse, err error) {
	var record entity.PrimaryOdinNetworkPlan
	res = &models.DescribeNetworkPlanDetailResponse{}
	if err = db.Model(daoNetworkPlan.Table()).Fields(record).Scan(&record, g.Map{
		daoNetworkPlanColumns.Uuid: params.ID,
	}); err != nil {
		if err == sql.ErrNoRows {
			err = errors.New(fmt.Sprintf("no plan matched with id= %s", gconv.String(params.ID)))
		} else {
			err = errors.New(fmt.Sprintf("failed to query network plan: %s", err))
		}
		return
	}

	res.Plan = &models.NetworkPlan{
		CreatedAt:    record.CreatedAt.UTC().Layout(RFC3339TZ),
		Description:  &record.Description,
		Gateway:      record.Gateway,
		ID:           record.Uuid,
		Mask:         record.SubnetMask,
		Name:         record.Name,
		OverlayIP:    record.OverlayIp,
		RegionUUID:   record.RegionUuid,
		ResID:        record.ResUuid,
		ResSn:        record.ResSn,
		ResType:      record.ResKind,
		Subnet:       record.SubnetCidr,
		Target:       record.TargetId,
		TotalIPCount: int64(record.TotalIpCount),
		UnderlayIP:   record.UnderlayIp,
		Vlan:         int64(record.Vlan),
		Attribute1:   record.Attribute1,
		Attribute2:   record.Attribute2,
		Attribute3:   record.Attribute3,
	}

	// 获取机架信息
	if record.ResUuid != "" && (record.ResKind == "rack" || record.ResSn == "") {
		var rack = &entity.PrimaryOdinRack{}
		if err = db.Model(daoRack.Table()).Where(daoRackColumns.Uuid, record.ResUuid).Scan(rack); err == nil {
			res.Plan.ResName = rack.RackNumber
		}
	}
	if record.RegionUuid != "" {
		var r = &entity.PrimaryOdinRegion{}
		if err = db.Model(daoRegion.Table()).Where(daoRegionColumns.Uuid, record.RegionUuid).Scan(r); err == nil {
			res.Plan.RegionName = r.Uuid
		}
	}

	return
}

func (cli *IdcDB) DescribeBgpAsNumberDescs(db *gdb.TX, params network.DescribeBgpAsNumberDescsParams) (res *models.DescribeBgpAsNumberDescsResponse, err error) {
	res = &models.DescribeBgpAsNumberDescsResponse{Desc: bgpAsNumberDesc}
	return
}

func (cli *IdcDB) CreateNetworkPlans(db *gdb.TX, params network.CreateNetworkPlansParams) (ids []string, err error) {
	// vp := network.ValidateNetworkPlansParams{Plans: params.Plans}
	// validateResponse := cli.ValidateNetworkPlans(db, vp)
	// if validateResponse.Validates != nil && len(validateResponse.Validates) > 0 {
	// 	return errors.New(validateResponse.Validates[0].ErrMessage)
	// }
	if err = cli.ValidateNetworkPlanFields(db, "create", params.Plans); err != nil {
		return nil, err
	}

	// 判断关联的机房机架是否存在
	validates := cli.validateRegion(db, params.Plans)
	if validates != nil && len(validates) > 0 {
		return nil, errors.New(validates[0].ErrMessage)
	}
	rackValidates := cli.validateRack(db, params.Plans)
	if rackValidates != nil && len(rackValidates) > 0 {
		return nil, errors.New(rackValidates[0].ErrMessage)
	}
	ids = make([]string, 0)
	for _, plan := range params.Plans {
		if plan.Subnet != "" {
			mask, err := tools.GetMask(plan.Subnet)
			if err == nil && mask != "" {
				plan.Mask = mask
			}
		}

		planDB := entity.PrimaryOdinNetworkPlan{
			Name:        plan.Name,
			TargetId:    plan.Target,
			SubnetCidr:  plan.Subnet,
			SubnetMask:  plan.Mask,
			Gateway:     plan.Gateway,
			Vlan:        uint(plan.Vlan),
			RegionUuid:  plan.RegionUUID,
			ResUuid:     plan.ResID,
			ResSn:       plan.ResSn,
			ResKind:     plan.ResType,
			UnderlayIp:  plan.UnderlayIP,
			OverlayIp:   plan.OverlayIP,
			Description: "",
			Attribute1:  plan.Attribute1,
			Attribute2:  plan.Attribute2,
			Attribute3:  plan.Attribute3,
		}
		if plan.Description != nil {
			planDB.Description = *plan.Description
		}

		if plan.RegionUUID == "" && plan.RegionName != "" {
			var regionRecords []*entity.PrimaryOdinRegion
			if err = db.Model(daoRegion.Table()).Where(daoRegionColumns.Uuid, plan.RegionName).Scan(&regionRecords); err != nil {
				return nil, errors.New("region查询失败")
			}
			if len(regionRecords) == 0 {
				return nil, errors.New("region不存在")
			} else {
				planDB.RegionUuid = regionRecords[0].Uuid
			}
		}

		// DNS 单独处理
		if plan.Target == "dns" {
			var dnsPlan entity.PrimaryNetworkPlan
			if err = db.Model(daoNetworkPlan.Table()).
				Fields(&dnsPlan).Scan(&dnsPlan, g.Map{
				daoNetworkPlanColumns.TargetId: "dns",
			}); err != nil && err != sql.ErrNoRows {
				return nil, err
			}
			// 已存在
			if dnsPlan.RefId != "" {
				if _, err = db.Model(daoNetworkPlan.Table()).Where(g.Map{
					daoNetworkPlanColumns.Uuid: dnsPlan.RefId,
				}).OmitEmpty().Update(planDB); err != nil {
					return nil, errors.New(fmt.Sprintf("failed to update: %s %s", err, gconv.String(params)))
				}
				continue
			}
		}

		if plan.ID == "" {
			planDB.Uuid = tools.GenerateUuid("np")
		} else {
			planDB.Uuid = plan.ID
		}
		if plan.Subnet != "" && plan.TotalIPCount == 0 {
			if !strings.Contains(plan.Subnet, ":") {
				num, err := tools.GetNetSize(plan.Subnet)
				if err != nil || num < 1 {
					return nil, errors.New(fmt.Sprintf("subnet do not has enough ipv4 or ipv6: %s %s", err, gconv.String(plan)))
				}
				planDB.TotalIpCount = uint(num)
			}
		}

		if _, err = db.Model(daoNetworkPlan.Table()).OmitEmpty().Insert(planDB); err != nil {
			return nil, errors.New(fmt.Sprintf("failed to create: %s %s", err.Error(), gconv.String(plan)))
		}
		ids = append(ids, planDB.Uuid)
	}
	return
}

func (cli *IdcDB) UpdateNetworkPlan(db *gdb.TX, params network.UpdateNetworkPlanParams) (err error) {
	var (
		result sql.Result
		data   = g.Map{
			daoNetworkPlanColumns.TargetId:     params.Plan.Target,
			daoNetworkPlanColumns.SubnetCidr:   params.Plan.Subnet,
			daoNetworkPlanColumns.Name:         params.Plan.Name,
			daoNetworkPlanColumns.SubnetMask:   params.Plan.Mask,
			daoNetworkPlanColumns.Gateway:      params.Plan.Gateway,
			daoNetworkPlanColumns.Vlan:         params.Plan.Vlan,
			daoNetworkPlanColumns.RegionUuid:   params.Plan.RegionUUID,
			daoNetworkPlanColumns.UnderlayIp:   params.Plan.UnderlayIP,
			daoNetworkPlanColumns.OverlayIp:    params.Plan.OverlayIP,
			daoNetworkPlanColumns.ResUuid:      params.Plan.ResID,
			daoNetworkPlanColumns.ResSn:        params.Plan.ResSn,
			daoNetworkPlanColumns.ResKind:      params.Plan.ResType,
			daoNetworkPlanColumns.Description:  pointy.String(""),
			daoNetworkPlanColumns.TotalIpCount: params.Plan.TotalIPCount,
			daoNetworkPlanColumns.Attribute1:   params.Plan.Attribute1,
			daoNetworkPlanColumns.Attribute2:   params.Plan.Attribute2,
			daoNetworkPlanColumns.Attribute3:   params.Plan.Attribute3,
		}
	)

	if params.Plan.Description != nil {
		data[daoNetworkPlanColumns.Description] = params.Plan.Description
	}

	// 更新前置验证
	params.Plan.ID = params.ID
	// validate := cli.validateUpdate(db, params.Plan)
	// if validate != nil {
	// 	return errors.New(validate.ErrMessage)
	// }
	if err = cli.ValidateNetworkPlanFields(db, "update", []*models.NetworkPlan{params.Plan}); err != nil {
		return err
	}
	// 判断关联的机房机架是否存在
	validates := cli.validateRegion(db, []*models.NetworkPlan{params.Plan})
	if validates != nil && len(validates) > 0 {
		return errors.New(validates[0].ErrMessage)
	}
	rackValidates := cli.validateRack(db, []*models.NetworkPlan{params.Plan})
	if rackValidates != nil && len(rackValidates) > 0 {
		return errors.New(rackValidates[0].ErrMessage)
	}

	if params.Plan.Subnet != "" {
		num, err := tools.GetNetSize(params.Plan.Subnet)
		if err != nil || num < 1 {
			return errors.New(fmt.Sprintf("subnet do not has enough ipv4 or ipv6: %s %s", err, gconv.String(params)))
		}
		data[daoNetworkPlanColumns.TotalIpCount] = num

		mask, err := tools.GetMask(params.Plan.Subnet)
		if err == nil && mask != "" {
			data[daoNetworkPlanColumns.SubnetMask] = mask
		}
	}

	if params.Plan.RegionUUID == "" && params.Plan.RegionName != "" {
		var regionRecords []*entity.PrimaryRegion
		if err = db.Model(daoRegion.Table()).Where(daoRegionColumns.Uuid, params.Plan.RegionName).Scan(&regionRecords); err != nil {
			return errors.New("region 查询失败")
		}
		if len(regionRecords) == 0 {
			return errors.New("region不存在")
		} else {
			data[daoNetworkPlanColumns.RegionUuid] = regionRecords[0].Uuid
		}
	}

	if result, err = db.Model(daoNetworkPlan.Table()).Where(g.Map{
		daoNetworkPlanColumns.Uuid: params.ID,
	}).OmitEmpty().Update(data); err != nil {
		return errors.New(fmt.Sprintf("failed to update: %s %s", err, gconv.String(params)))
	}

	if rowsNum, _ := result.RowsAffected(); rowsNum == 0 {
		return errors.New(fmt.Sprintf("uuid [%s]: The version has changed, the update operation failed, please refresh and try again", params.ID))
	}
	return
}

func (cli *IdcDB) DeleteNetworkPlan(db *gdb.TX, params network.DeleteNetworkPlanParams) (err error) {
	del := g.Map{
		daoNetworkPlanColumns.Uuid: params.ID,
	}

	count, sqlErr := db.Model(daoNetworkPlan.Table()).Count(del)
	if sqlErr != nil {
		err = errors.New("failed to fetch network plan")
	} else if count == 0 {
		return errors.New(fmt.Sprintf("no network plan matched with uuid %s", params.ID))
	}

	if _, err = db.Model(daoNetworkPlan.Table()).Delete(del); err != nil {
		return errors.New(fmt.Sprintf("failed to delete: %s %s", err, params.ID))
	}

	return
}
