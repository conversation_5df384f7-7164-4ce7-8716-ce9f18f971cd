package worker

import (
	"context"
	"encoding/base64"
	"errors"
	"fmt"
	"net"
	"strconv"
	"strings"
	"time"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gctx"

	"coding.jd.com/fabric/zeusV2/idc-cm/gen/models"
	"coding.jd.com/fabric/zeusV2/idc-cm/gen/restapi/operations/device"
	"coding.jd.com/fabric/zeusV2/idc-cm/gen/restapi/operations/network"
	"coding.jd.com/fabric/zeusV2/idc-cm/gen/restapi/operations/network_equipment"
	"coding.jd.com/fabric/zeusV2/idc-cm/gen/restapi/operations/server"
	"coding.jd.com/fabric/zeusV2/idc-cm/persistent/idcdb/model"
	"coding.jd.com/fabric/zeusV2/idc-cm/tools"
	ncclient "coding.jd.com/pcd-application/openapi-ncclient"
	"coding.jd.com/pcd-application/openapi-ncclient/constants"
	"coding.jd.com/pcd-application/openapi-ncclient/core"
)

// 网络设备扫描
// var netCheckScanLock sync.RWMutex

const (
	Normal   = "0"
	Abnormal = "1"
)

const (
	Established = 5
)

func (t *TaskWorker) DescribeSwitchConn(rackId string) (res *model.DeviceConnWithBrief, err error) {
	if err = g.DB().Transaction(gctx.New(), func(ctx context.Context, tx *gdb.TX) error {
		equipmentParams := network_equipment.DescribeNetworkEquipmentParams{
			Condition: network_equipment.DescribeNetworkEquipmentBody{RackUUID: &rackId},
			PageNo:    -1,
			PageSize:  10,
		}
		equipmentRes, err := t.service.DescribeNetworkEquipment(tx, equipmentParams)
		if err != nil {
			return err
		}
		if equipmentRes.TotalCount == 0 {
			return errors.New("机架:" + rackId + ",无TOR交换机\n")
		}
		deviceParams := device.DescribeDeviceConnParams{
			ID: equipmentRes.NetworkEquipments[0].UUID,
		}
		deviceRes, err := t.service.DescribeDeviceConn(tx, deviceParams)
		if err != nil {
			return err
		}
		if deviceRes == nil {
			return errors.New("TOR交换机:" + equipmentRes.NetworkEquipments[0].UUID + "," + "无Device Conn信息\n")
		}

		res = &model.DeviceConnWithBrief{
			ConnType:  deviceRes.Data.ConnType,
			DeviceID:  equipmentRes.NetworkEquipments[0].UUID,
			DeviceSn:  equipmentRes.NetworkEquipments[0].SerialNumber,
			DeviceIp:  *equipmentRes.NetworkEquipments[0].ManageIP,
			Password:  deviceRes.Data.Password,
			Port:      deviceRes.Data.Port,
			SecretKey: deviceRes.Data.SecretKey,
			UserName:  deviceRes.Data.UserName,
		}
		return nil
	}); err != nil {
		return nil, err
	}
	return res, nil
}

func (t *TaskWorker) CheckNetworkPlans() error {
	// 获取网络规划target列表
	if err := g.DB().Transaction(gctx.New(), func(ctx context.Context, tx *gdb.TX) error {
		targetParams := network.DescribeNetworkPlanTargetsParams{
			PageNo:   -1,
			PageSize: 10,
		}
		targets, err := t.service.DescribeNetworkPlanTargets(tx, targetParams)
		if err != nil {
			return err
		}
		if len(targets) == 0 {
			return errors.New("获取网络规划Target信息为空")
		}

		// 根据target获取网络规划列表
		for _, target := range targets {
			plansParams := network.DescribeNetworkPlansParams{
				Condition: network.DescribeNetworkPlansBody{Target: &target.Target},
				PageNo:    -1,
				PageSize:  10,
			}
			res, err := t.service.DescribeNetworkPlans(tx, plansParams)
			if err != nil {
				t.logger.Errorf("Target: %s,查询网络规划失败:%s", target.Name, err)
				continue
			}
			if res.NetworkPlans == nil || len(res.NetworkPlans) == 0 {
				t.logger.Debugf("Target: %s,网络规划为空跳过检查", target.Name)
				continue
			}

			if err := t.CheckNetworkConf(*target, res.NetworkPlans); err != nil {
				t.logger.Errorf("Target %s 网络规划检查失败，%s", target.Name, err.Error())
			}
		}
		return nil
	}); err != nil {
		return err
	}
	return nil
}

func (t *TaskWorker) getNetworkSwitch(target model.NetPlanTargetInfo, plan models.NetworkPlan, rackID string) (res *model.DeviceConnWithBrief, err error) {
	switch target.Rule {
	case "bgpAsNumber":
		if plan.ResType == "switch" {
			if err := g.DB().Transaction(gctx.New(), func(ctx context.Context, tx *gdb.TX) error {
				// 1. 获取所有网络设备以及连接信息，没有连接信息的忽略
				brief, err := t.service.DescribeDeviceConnWithBrief(tx, []string{plan.Attribute2})
				if err != nil {
					return err
				}
				if brief != nil && len(brief) > 0 {
					res = brief[0]
				}
				return nil
			}); err != nil {
				t.logger.Infof("获取网络设备信息失败，%s", err.Error())
				return nil, err
			}
			return res, err
		} else {
			return nil, errors.New("不支持检查此网络规划配置\n")
		}
	case "rack_plan":
		res, err := t.DescribeSwitchConn(plan.ResID)
		if err != nil {
			return nil, err
		}
		return res, err
	case "dns", "lb", "k8sClusterIp", "ntp":
		if target.Target == "ntpSource" {
			return nil, errors.New("不支持检查此网络规划配置\n")
		}
		res, err = t.DescribeSwitchConn(rackID)
		if err != nil {
			return nil, err
		}
		return res, err
	case "region_plan":
		if target.Target == "ispBGPs" ||
			target.Target == "interPriOverlaps" || target.Target == "inats" ||
			target.Target == "stardbVip" {
			res, err = t.DescribeSwitchConn(rackID)
			if err != nil {
				return nil, err
			}
			return res, err
		} else {
			return nil, errors.New("不支持检查此网络规划配置\n")
		}
	case "rack_region_plan":
		return nil, errors.New("不支持检查此网络规划配置\n")
	case "k8sCalico":
		var serverInfo *models.DescribeServerResponse
		if err := g.DB().Transaction(gctx.New(), func(ctx context.Context, tx *gdb.TX) (err error) {
			params := server.DescribeServerParams{
				Condition: server.DescribeServerBody{ServerID: []*string{&plan.ResID}},
				PageNo:    -1,
				PageSize:  10,
			}
			serverInfo, err = t.service.DescribeServer(tx, params)
			if err != nil {
				return err
			}
			return nil
		}); err != nil {
			t.logger.Infof("获取服务器信息失败，%s", err.Error())
			return nil, err
		}
		if serverInfo.TotalCount != 0 {
			res, err = t.DescribeSwitchConn(serverInfo.DescribeServer[0].RackUUID)
			if err != nil {
				return nil, err
			}
			return res, err
		} else {
			return nil, errors.New("未查询到相关服务器信息\n")
		}
	default:
		return nil, errors.New("不支持检查此网络规划配置\n")
	}
}

func (t *TaskWorker) DescribeServerByRoles(roles []*string, roleMetadataValue []*string, region *string, az *string) (serverInfo *models.DescribeServerResponse, err error) {
	params := server.DescribeServerParams{
		Condition: server.DescribeServerBody{
			RegionUUID: region,
			AzUUID:     az,
			ServerRole: &server.DescribeServerParamsBodyServerRole{
				Role:          roles,
				MetadataValue: roleMetadataValue,
			},
		},
		PageNo:   -1,
		PageSize: 10,
	}
	if err := g.DB().Transaction(gctx.New(), func(ctx context.Context, tx *gdb.TX) (err error) {
		serverInfo, err = t.service.DescribeServer(tx, params)
		return
	}); err != nil {
		return nil, err
	}
	return serverInfo, err
}

func (t *TaskWorker) getNetSwitchConn(ctx context.Context, target model.NetPlanTargetInfo, plan models.NetworkPlan, keykID string) (brief *model.DeviceConnWithBrief, err error) {
	key := keykID + "-" + "switch-conn"
	val, err := t.adapter.GetCache().Get(ctx, key)
	if err != nil {
		t.logger.Errorf("goCahce.Get() error = %v, key= %v", err, key)
		return nil, err
	}
	if val == nil {
		brief, err = t.getNetworkSwitch(target, plan, keykID)
		if err != nil {
			t.logger.Errorf("get switch connection error  plan = %v, error = %v", plan, err)
			return nil, err
		}
		if brief != nil {
			_, err = t.adapter.GetCache().SetIfNotExist(ctx, key, brief, 30*time.Minute)
			if err != nil {
				t.logger.Errorf("go cache 设置缓存失败 key = %v, error = %v", key, err)
				return nil, err
			}
		}
	} else {
		if d, ok := val.Val().(*model.DeviceConnWithBrief); ok {
			brief = d
		}
	}
	return brief, nil
}

func (t *TaskWorker) getServersBytarget(target string, region string, az string) (serverInfo *models.DescribeServerResponse, err error) {
	switch target {
	case "stardbVip":
		roleName := "base_mysql"
		roles := []*string{&roleName}
		servers, err := t.DescribeServerByRoles(roles, nil, &region, nil)
		return servers, err
	case "k8sClusterIp":
		roleName := "base_k8s"
		roles := []*string{&roleName}
		roleMetadata := "master"
		roleMetadataValue := []*string{&roleMetadata}
		servers, err := t.DescribeServerByRoles(roles, roleMetadataValue, nil, &az)
		return servers, err
	case "dns":
		roleName := "base_dns"
		roles := []*string{&roleName}
		servers, err := t.DescribeServerByRoles(roles, nil, nil, nil)
		return servers, err
	case "ntpVip":
		roleName := "base_ntp"
		roles := []*string{&roleName}
		servers, err := t.DescribeServerByRoles(roles, nil, nil, nil)
		return servers, err
	case "lb":
		roleName := "base_lb"
		roles := []*string{&roleName}
		servers, err := t.DescribeServerByRoles(roles, nil, nil, nil)
		return servers, err
	case "ispBGPs", "interPriOverlaps", "inats":
		role1, role2, role3 := "sdn_std_dr_ipv4", "sdn_light_vr_dr_ipv4", "sdn_light_vr_dr_natgw"
		roles := []*string{&role1, &role2, &role3}
		servers, err := t.DescribeServerByRoles(roles, nil, &region, nil)
		return servers, err
	}

	return nil, err
}

func (t *TaskWorker) CheckNetworkConf(target model.NetPlanTargetInfo, plans []*models.NetworkPlan) error {
	var (
		ctx = context.Background()
	)
	status := Normal
	t.logger.Debugf("[check net plans]开始执行 %s 网络规划检查任务...", target)
	for _, plan := range plans {
		if plan.Target == "ispBGPs" || 
			plan.Target == "interPriOverlaps" || plan.Target == "inats" ||
			plan.Target == "dns" || plan.Target == "lb" || plan.Target == "ntpVip" ||
			plan.Target == "k8sClusterIp" || plan.Target == "stardbVip" {
			servers, err := t.getServersBytarget(plan.Target, plan.RegionUUID, plan.Attribute1)
			if err != nil {
				t.logger.Errorf("getServersBytarget Error = %v ", err)
				info := "查找服务器失败\n"
				_ = t.updateNetPlanStatus(plan.ID, Abnormal, info)
				continue
			}
			if servers.TotalCount == 0 {
				info := "无" + plan.Target + "相关服务器\n"
				_ = t.updateNetPlanStatus(plan.ID, Abnormal, info)
				continue
			}
			statusInfo := ""
			for _, serverInfo := range servers.DescribeServer {
				// 获取交换机连接信息
				brief, err := t.getNetSwitchConn(ctx, target, *plan, serverInfo.RackUUID)
				if err != nil {
					info := err.Error()
					statusInfo += info
					continue
				} else {
					if brief == nil {
						info := "未获取到服务器:" + *serverInfo.SystemIP + "的交换机连接信息，请检查交换机连接配置\n"
						statusInfo += info
						continue
					}
				}
				// 连接交换机获取配置信息
				switchConf, err := t.getNetSwitchConf(ctx, brief)
				if err != nil {
					info := "获取交换机:" + brief.DeviceIp + "配置失败, 错误信息: " + err.Error() + "\n"
					t.logger.Errorf(info)
					statusInfo += info
					continue
				}
				info, err := t.checkNetPlaneSwitchConf(switchConf, *plan, *serverInfo.SystemIP)
				if err != nil {
					errInfo := "交换机:" + brief.DeviceIp + "配置检查失败,错误信息是" + err.Error() + "\n"
					t.logger.Errorf(errInfo)
					statusInfo += errInfo
					continue
				}
				statusInfo += info
			}
			if statusInfo == "" {
				statusInfo = "配置正确"
				status = Normal
			} else {
				status = Abnormal
			}
			_ = t.updateNetPlanStatus(plan.ID, status, statusInfo)
			continue
		}
		// 其他类型Target 处理流程
		brief, err := t.getNetSwitchConn(ctx, target, *plan, plan.ID)
		if err != nil {
			info := err.Error()
			_ = t.updateNetPlanStatus(plan.ID, Abnormal, info)
			continue
		} else {
			if brief == nil {
				info := "未获取到交换机连接信息,请检查交换机连接配置\n"
				_ = t.updateNetPlanStatus(plan.ID, Abnormal, info)
				continue
			}
		}
		// 连接交换机获取配置信息
		switchConf, err := t.getNetSwitchConf(ctx, brief)
		if err != nil {
			info := "获取交换机:" + brief.DeviceIp + "配置失败, 错误信息: " + err.Error() + "\n"
			t.logger.Errorf(info)
			_ = t.updateNetPlanStatus(plan.ID, Abnormal, info)
			continue
		}
		statusInfo, err := t.checkNetPlaneSwitchConf(switchConf, *plan, plan.Attribute1)
		if err != nil {
			errInfo := "交换机:" + brief.DeviceIp + "配置检查失败,错误信息是" + err.Error() + "\n"
			t.logger.Errorf(errInfo)
			statusInfo += errInfo
		}
		if statusInfo == "" {
			statusInfo = "配置正确"
			status = Normal
		} else {
			status = Abnormal
		}
		err = t.updateNetPlanStatus(plan.ID, status, statusInfo)
		if err != nil {
			return err
		}
	}
	return nil
}

func (t *TaskWorker) updateNetPlanStatus(networkPlanId string, status string, statusInfo string) error {
	if err := g.DB().Transaction(gctx.New(), func(ctx context.Context, tx *gdb.TX) error {
		err := t.service.UpdateNetworkPlanCheck(tx, networkPlanId, status, statusInfo)
		if err != nil {
			return err
		}
		return nil
	}); err != nil {
		t.logger.Errorf("更新：%s, 网络规划状态信息失败，%s", networkPlanId, err.Error())
		return err
	}

	return nil
}

// 获取网络地址
func (t *TaskWorker) calculateNetworkAddress(ipAddr string, mask string) string {
	ip := net.ParseIP(ipAddr)
	maskNum := net.IPMask(net.ParseIP(mask).To4())
	ipBytes := ip.Mask(maskNum)
	ipNet := net.IP(ipBytes)
	return ipNet.String()
}

// 掩码地址转换为掩码长度
func (t *TaskWorker) maskToLength(mask string) int64 {
	parts := strings.Split(mask, ".")
	binaryMask := ""
	for _, part := range parts {
		num, _ := strconv.Atoi(part)
		binaryMask += fmt.Sprintf("%08b", num)
	}

	length := 0
	for _, bit := range binaryMask {
		if bit == '1' {
			length++
		}
	}

	return int64(length)
}

func (t *TaskWorker) getServerDataIP(serverIp string) (dataIP string, err error) {
	var serverInfo *models.DescribeServerResponse
	if err := g.DB().Transaction(gctx.New(), func(ctx context.Context, tx *gdb.TX) (err error) {
		params := server.DescribeServerParams{
			Condition: server.DescribeServerBody{SystemIP: []*string{&serverIp}},
			PageNo:    -1,
			PageSize:  10,
		}
		serverInfo, err = t.service.DescribeServer(tx, params)
		if err != nil {
			return err
		}
		return nil
	}); err != nil {
		t.logger.Infof("获取服务器信息失败，%s", err.Error())
		return "", err
	}
	if serverInfo.TotalCount != 0 {
		dataIP = *serverInfo.DescribeServer[0].DataIP
		return dataIP, nil
	} else {
		return "", errors.New("未获取到服务器\n")
	}
}

func (t *TaskWorker) checkNetPlaneSwitchConf(switchConf *core.NetConfStatus, plan models.NetworkPlan, serverIp string) (statusInfo string, err error) {
	match := 0
	switch plan.Target {
	case "stardbVip":
		// 检查服务器和交换机的BGP邻居状态
		for _, bgpNeighInfo := range switchConf.BgpNeighborInfo {
			if bgpNeighInfo.IpAddress == serverIp && bgpNeighInfo.State == Established {
				match = 1
				break
			}
		}
		if match == 0 {
			statusInfo += "服务器:" + serverIp + "和交换机的BGP邻居状态非Established状态\n"
		}
		return statusInfo, nil
	case "dns", "lb", "ntpVip":
		// 检查服务器和交换机的BGP邻居状态ntpVip
		for _, bgpNeighInfo := range switchConf.BgpNeighborInfo {
			if bgpNeighInfo.IpAddress == serverIp && bgpNeighInfo.State == Established {
				match = 1
				break
			}
		}
		if match == 0 {
			statusInfo += "服务器:" + serverIp + "和交换机的BGP邻居状态非Established状态\n"
		}
		match = 0
		// 检查服务器宣告到交换机的网段
		for _, ipCidr := range []string{plan.UnderlayIP, plan.OverlayIP} {
			_, ipaddr, _ := net.ParseCIDR(ipCidr)
			ipNet := net.IP(ipaddr.IP)
			maskLength, _ := ipaddr.Mask.Size()
			for _, routeEntry := range switchConf.RouteEntryInfo {
				if routeEntry.IPv4RouteEntry.Ipv4Address == ipNet.String() && routeEntry.IPv4RouteEntry.Ipv4PrefixLength == int64(maskLength) {
					match = 1
					break
				}
			}
			if match == 0 {
				statusInfo += "服务器网段:" + ipCidr + "未宣告到交换机\n"
			}
		}
		return statusInfo, nil
	case "ispBGPs", "interPriOverlaps", "inats", "k8sClusterIp", "k8sCalico":
		// 获取k8sCalico节点的业务口IP
		if plan.Target == "k8sCalico" {
			dataIP, err := t.getServerDataIP(serverIp)
			if err != nil {
				return "", err
			}
			serverIp = dataIP
		}
		// 检查服务器和交换机的BGP邻居状态
		for _, bgpNeighInfo := range switchConf.BgpNeighborInfo {
			if bgpNeighInfo.IpAddress == serverIp && bgpNeighInfo.State == Established {
				match = 1
				break
			}
		}
		if match == 0 {
			statusInfo += "服务器:" + serverIp + "和交换机的BGP邻居状态非Established状态\n"
		}
		match = 0
		// 检查服务器宣告到交换机的网段
		maskLength := t.maskToLength(plan.Mask)
		ipBytes, _, _ := net.ParseCIDR(plan.Subnet)
		ipNet := net.IP(ipBytes)
		for _, routeEntry := range switchConf.RouteEntryInfo {
			if routeEntry.IPv4RouteEntry.Ipv4Address == ipNet.String() && routeEntry.IPv4RouteEntry.Ipv4PrefixLength == maskLength {
				match = 1
				break
			}
		}
		if match == 0 {
			statusInfo += "服务器网段:" + plan.Subnet + "未宣告到交换机\n"
		}
		return statusInfo, nil
	case "bgpAsNumber":
		if plan.ResType == "switch" {
			if switchConf.BgpAsInfo != nil && len(switchConf.BgpAsInfo) != 0 {
				asNumber, err := strconv.ParseInt(plan.Attribute1, 10, 64)
				if err != nil {
					return "", err
				}
				for _, asInfo := range switchConf.BgpAsInfo {
					if asInfo.ASNumber == asNumber {
						match = 1
						break
					}
				}
			}
			if match == 0 {
				statusInfo += "交换机未配置BGP AS号" + plan.Attribute1 + "\n"
			}
			return statusInfo, nil
		} else {
			statusInfo += "不支持校验此网络规划\n"
			return statusInfo, nil
		}
	case "sdnBGWSegment", "sdnCRDXR", "sdnDXRCR", "sdnCLR", "sdnVM", "sdnDRVR", "sdnDRSW", "sdnVRDR", "sdnVRVM", "sdnVRBGW", "sdnVRNATGW", "sdnDRNATGW", "sdnBGWVR", "sdnBGWDXR", "sdnVpnDr", "sdnVpnBgw":
		// 检查vlan下网关地址配置是否正确
		var vlanIfdex int64 = 0
		for _, vlanInfo := range switchConf.VlanIfIndexInfo {
			if vlanInfo.ID == plan.Vlan {
				vlanIfdex = vlanInfo.RouteIfIndex
				break
			}
		}
		if vlanIfdex != 0 {
			for _, ipInfo := range switchConf.IPv4AddesssInfo {
				if vlanIfdex == ipInfo.IfIndex {
					if ipInfo.Ipv4Address == plan.Gateway {
						if ipInfo.Ipv4Mask == plan.Mask {
							match = 1
							break
						} else {
							statusInfo += "交换机网关掩码配置错误,交换机掩码配置为: " + ipInfo.Ipv4Mask + "\n"
							match = 2
							break
						}
					}
				}
			}
			if match == 0 {
				statusInfo += "交换机vlan" + strconv.FormatInt(plan.Vlan, 10) + "下未配置网关地址: " + plan.Gateway + "\n"
			}
		} else {
			statusInfo += "交换机无vlan" + strconv.FormatInt(plan.Vlan, 10) + "信息\n"
		}
		// 检查网段是否宣告到核心交换机
		match = 0
		netAddr := t.calculateNetworkAddress(plan.Gateway, plan.Mask)
		maskLength := t.maskToLength(plan.Mask)
		for _, bgpInfo := range switchConf.BgpDeclarInfo {
			if bgpInfo.IpAddress == netAddr && bgpInfo.Mask == maskLength {
				match = 1
				break
			}
		}
		if match == 0 {
			statusInfo += "网段地址:" + plan.Subnet + "未宣告到核心交换机\n"
		}
		return statusInfo, nil
	}

	return "", err
}

func (t *TaskWorker) getNetSwitchConf(ctx context.Context, conn *model.DeviceConnWithBrief) (switchConf *core.NetConfStatus, err error) {
	if conn.DeviceIp == "" {
		err = errors.New("TOR manage ip is empty")
	}
	if conn.ConnType == "" || conn.Port == 0 || conn.UserName == "" || conn.Password == "" {
		err = errors.New("TOSR login info is empty")
	}
	if err != nil {
		t.logger.Errorf("TOR connection info is invalid, %s", err.Error())
		return nil, err
	}
	// 密码解密
	decodeString, err := base64.StdEncoding.DecodeString(conn.Password)
	if err != nil {
		t.logger.Errorf("TOR connection password decode error,%s ", err.Error())
		return nil, err
	}
	pwd, err := tools.AesDecrypt(decodeString)
	if err != nil {
		t.logger.Errorf("TOR connection password aes decrypt error,%s ", err.Error())
		return nil, err
	}

	var connType constants.ConnType
	if conn.ConnType == "ssh" {
		connType = constants.SSH
	} else {
		connType = constants.NetConf
	}
	key := conn.DeviceID + "-" + "switch-conf"
	val, err := t.adapter.GetCache().Get(ctx, key)
	if err != nil {
		t.logger.Infof("goCahce.Get() error = %v, key= %v", err, key)
		return nil, err
	}
	if val == nil {
		t.logger.Infof("[network plan check] 获取网络设备[%s:%v-%s-%s]信息开始......", conn.DeviceIp, conn.Port, conn.UserName, string(pwd))
		switchConf, err = ncclient.NetConfCheck(conn.DeviceIp, conn.UserName, string(pwd), int(conn.Port), connType, nil)
		if err != nil {
			t.logger.Infof("[network plane check] 获取网络设备[%s:%v-%s-%s]信息失败，原因为：%s", conn.DeviceIp, conn.Port, conn.UserName, conn.ConnType, err.Error())
			return nil, err
		}
		if switchConf != nil {
			_, err = t.adapter.GetCache().SetIfNotExist(ctx, key, switchConf, 30*time.Minute)
			if err != nil {
				t.logger.Infof("go cache 设置缓存失败 key = %v, error = %v", key, err)
				return nil, err
			}
		}
	} else {
		if d, err := val.Val().(*core.NetConfStatus); err {
			switchConf = d
		}
	}

	return switchConf, err
}
