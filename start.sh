#!/bin/bash

# 脚本功能：启动服务并监控服务状态
# 新增功能：在pod环境中执行时，从MySQL数据库查询负载均衡器IP地址并更新配置文件

# 创建日志函数 - 输出到标准错误，避免污染命令替换的结果
log() {
  echo "$(date '+%Y-%m-%d %H:%M:%S') - $1" >&2
}

# 检测是否在pod环境中执行
is_running_in_pod() {
  # 检查Kubernetes服务账号令牌文件是否存在
  if [ -f /var/run/secrets/kubernetes.io/serviceaccount/token ]; then
    return 0  # 在pod中运行
  else
    return 1  # 不在pod中运行
  fi
}

# 从配置文件中提取MySQL连接信息并执行查询
query_lb_ip() {
  local config_file=$1
  
  # 检查配置文件是否存在
  if [ ! -f "$config_file" ]; then
    log "错误: 配置文件 $config_file 不存在"
    return 1
  fi
  
  # 提取MySQL连接信息
  local host=$(grep "mysql_jdstack_zeus_host" "$config_file" | awk -F"'" '{print $2}')
  local port=$(grep "mysql_jdstack_zeus_port" "$config_file" | awk -F"'" '{print $2}')
  local user=$(grep "mysql_jdstack_zeus_user" "$config_file" | awk -F"'" '{print $2}')
  local password=$(grep "mysql_jdstack_zeus_password" "$config_file" | awk -F"'" '{print $2}')
  
  # 验证连接信息是否完整
  if [ -z "$host" ] || [ -z "$port" ] || [ -z "$user" ] || [ -z "$password" ]; then
    log "错误: 无法从配置文件中提取完整的MySQL连接信息"
    return 1
  fi
  
  # 执行MySQL查询，最多尝试3次
  local max_attempts=3
  local attempt=1
  local success=false
  local lb_ip=""
  
  while [ $attempt -le $max_attempts ] && [ "$success" = false ]; do
    log "尝试第 $attempt 次查询负载均衡器IP..."
    
    # 构建查询语句
    local query="select system_ip from odin.odin_server where uuid in (select uuid from odin.odin_hardware_role_metadata where role = 'base_lb' and metadata_value = 'master')"
    
    # 执行查询
    lb_ip=$(mysql -h "$host" -P "$port" -u "$user" -p"$password" -e "$query" --skip-column-names 2>/dev/null)
    
    # 检查查询结果
    if [ $? -eq 0 ] && [ -n "$lb_ip" ]; then
      success=true
      log "成功查询到负载均衡器IP: $lb_ip"
    else
      log "尝试 $attempt 失败，正在重试..."
      attempt=$((attempt+1))
      sleep 2
    fi
  done
  
  if [ "$success" = false ]; then
    log "错误: 无法查询负载均衡器IP，已尝试 $max_attempts 次"
    return 1
  fi
  
  # 只返回IP地址，不包含日志输出
  echo "$lb_ip"
  return 0
}

# 创建带有更新IP的临时配置文件
create_temp_config() {
  local original_config=$1
  local lb_ip=$2
  local temp_config="/tmp/config.jdock.yaml"
  
  # 检查参数
  if [ -z "$original_config" ] || [ -z "$lb_ip" ]; then
    log "错误: 创建临时配置文件参数不完整"
    return 1
  fi
  
  # 检查原始配置文件是否存在
  if [ ! -f "$original_config" ]; then
    log "错误: 原始配置文件 $original_config 不存在"
    return 1
  fi
  
  # 创建临时配置文件目录
  mkdir -p /tmp
  
  # 使用awk处理配置文件，避免sed的引号问题
  awk -v ip="$lb_ip" '{
    if ($0 ~ /filecenter_jdstack_zeus_server:/) {
      print "filecenter_jdstack_zeus_server: '\''" ip "'\''";
    } else {
      print $0;
    }
  }' "$original_config" > "$temp_config"
  
  # 验证更新是否成功
  if grep -q "filecenter_jdstack_zeus_server: '$lb_ip'" "$temp_config"; then
    log "成功创建临时配置文件，负载均衡器IP: $lb_ip"
    # 只返回临时配置文件路径，不包含日志输出
    echo "$temp_config"
    return 0
  else
    log "错误: 临时配置文件更新失败"
    rm -f "$temp_config"
    return 1
  fi
}

mkdir -pv -m 0700 /root/.ssh
# 下载ssh私钥
if [ "$CLOUD_TYPE" = "jdstack" ]; then
    if [ ! -f "/root/.ssh/id_rsa" ]; then
        max_attempts=3
        attempt=1
        success=false

        while [ $attempt -le $max_attempts ] && [ "$success" = false ]; do
            if curl -s -o /root/.ssh/id_rsa http://minio.jdcloud.local/other/id_rsa; then
                chmod 600 /root/.ssh/id_rsa
                success=true
                log "下载id_rsa至/root/.ssh/id_rsa成功。"
            else
                log "尝试 $attempt 失败，正在重试..."
                attempt=$((attempt+1))
            fi
        done

        if [ "$success" = false ]; then
            log "错误：无法下载 id_rsa 文件，已尝试 $max_attempts 次。退出程序。"
            exit 1
        fi
    fi
fi

# 定义配置文件路径变量
ZEUS_CONFIG_PATH="/config/config.jdock.yaml"
TEMP_CONFIG_PATH=""
LB_IP=""

# 在pod环境中创建带有更新IP的临时配置文件
# 注意：这里只查询一次IP地址，然后在所有服务启动和重启时都使用这个查询结果
if is_running_in_pod; then
    log "检测到pod环境，正在准备配置..."
    
    # 检查mysql客户端是否安装
    if ! command -v mysql &> /dev/null; then
        log "警告: mysql客户端未安装，无法查询负载均衡器IP"
    else
        # 查询负载均衡器IP（只查询一次）
        LB_IP=$(query_lb_ip "$ZEUS_CONFIG_PATH")
        if [ $? -eq 0 ] && [ -n "$LB_IP" ]; then
            # 创建临时配置文件
            TEMP_CONFIG_PATH=$(create_temp_config "$ZEUS_CONFIG_PATH" "$LB_IP")
            if [ $? -eq 0 ] && [ -n "$TEMP_CONFIG_PATH" ]; then
                # 验证临时配置文件是否存在
                if [ -f "$TEMP_CONFIG_PATH" ]; then
                    log "临时配置文件创建成功: $TEMP_CONFIG_PATH"
                else
                    log "警告: 临时配置文件不存在，将使用默认配置启动服务"
                    TEMP_CONFIG_PATH=""
                fi
            else
                log "警告: 临时配置文件创建失败，将使用默认配置启动服务"
                TEMP_CONFIG_PATH=""
            fi
        else
            log "警告: 无法获取负载均衡器IP，将使用默认配置启动服务"
        fi
    fi
else
    log "非pod环境，使用默认配置"
fi

start_service() {
  case $1 in
    zeus)
      cd /zeus
      if [ -n "$TEMP_CONFIG_PATH" ] && [ -f "$TEMP_CONFIG_PATH" ]; then
        log "使用临时配置文件启动zeus服务: $TEMP_CONFIG_PATH"
        ./zeus -c "config/config.yaml,$TEMP_CONFIG_PATH" &
      else
        log "使用默认配置文件启动zeus服务"
        ./zeus -c 'config/config.yaml,/config/config.jdock.yaml' &
      fi
      local pid=$!
      sleep 2
      if ps -p $pid > /dev/null; then
        log "服务 zeus 启动成功，PID: $pid"
      else
        log "警告: 服务 zeus 可能启动失败"
      fi
      ;;
    odin)
      cd /odin
      if [ -n "$TEMP_CONFIG_PATH" ] && [ -f "$TEMP_CONFIG_PATH" ]; then
        log "使用临时配置文件启动odin服务: $TEMP_CONFIG_PATH"
        ./odin -c "config/config.yaml,$TEMP_CONFIG_PATH" &
      else
        log "使用默认配置文件启动odin服务"
        ./odin -c 'config/config.yaml,/config/config.jdock.yaml' &
      fi
      local pid=$!
      sleep 2
      if ps -p $pid > /dev/null; then
        log "服务 odin 启动成功，PID: $pid"
      else
        log "警告: 服务 odin 可能启动失败"
      fi
      ;;
    idc_cm)
      cd /idc-cm
      PROC_PORT=7788
      LISTENHOST=$(ip -4 addr show | awk '/inet /{print $2}' | awk -F/ '{print $1}' | grep -v '^127\.' | head -n 1)
      if [ -z "$LISTENHOST" ]; then
        LISTENHOST="0.0.0.0"
        log "警告: 无法确定主机IP，使用默认IP: $LISTENHOST"
      fi
      ./idc_cm --host=$LISTENHOST --port=$PROC_PORT &
      local pid=$!
      sleep 2
      if ps -p $pid > /dev/null; then
        log "服务 idc_cm 启动成功，PID: $pid，监听地址: $LISTENHOST:$PROC_PORT"
      else
        log "警告: 服务 idc_cm 可能启动失败"
      fi
      ;;
  esac
}

check_and_restart() {
  # 检查服务是否停止运行
  if ! pgrep -f "^./$(basename $1) " > /dev/null
  then
    log "服务 $1 已停止运行。正在重新启动..."
    # 使用全局变量TEMP_CONFIG_PATH，不重新查询IP
    start_service $1
  fi
}

# 启动所有服务
log "开始启动所有服务..."
start_service zeus
start_service odin
start_service idc_cm
log "所有服务启动完成"

# 持续监控和重启服务
log "开始监控服务状态..."
while true
do
  check_and_restart zeus
  check_and_restart odin
  check_and_restart idc_cm
  sleep 60  # 每60秒检查一次
done
