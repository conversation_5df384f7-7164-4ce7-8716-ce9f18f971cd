package doAvailabilityZone

import (
	"context"

	pbAvailabilityZone "coding.jd.com/fabric/zeusV2/odin/api/server/pb/availability_zone/v1"
	"coding.jd.com/fabric/zeusV2/odin/internal/server/data/db_master/dao"
	jPolicy "coding.jd.com/pcd-application/win-go/biz_policy"
	jErr "coding.jd.com/pcd-application/win-go/error"
	moUuid "coding.jd.com/pcd-application/win-go/project/data/task/model/uuid"
	"coding.jd.com/pcd-application/win-go/third_party/winapi"
)

type AvailabilityZoneCase interface {
	// Create 创建-可用区
	Create(ctx context.Context, req *pbAvailabilityZone.CreateReq) (*dao.AvailabilityZone, jErr.Error)

	// Modify 修改-可用区
	Modify(ctx context.Context, req *pbAvailabilityZone.ModifyReq) jErr.Error

	// DescribeReq2UuidX 取ID-可用区
	DescribeReq2UuidX(ctx context.Context, req *pbAvailabilityZone.DescribeReq) *moUuid.Uuid

	// Delete 删除-可用区
	Delete(ctx context.Context, req *pbAvailabilityZone.DeleteReq) jErr.Error
}

type AvailabilityZoneRepo interface {
	// Create 创建-可用区
	Create(ctx context.Context, node *dao.AvailabilityZone) (*dao.AvailabilityZone, jErr.Error)
	// Update 修改-可用区
	Update(ctx context.Context, node *dao.AvailabilityZone, clearFields ...string) jErr.Error
	// Delete 删除-可用区
	Delete(ctx context.Context, uuid string) jErr.Error
	// DeletePolicy 删除策略-可用区
	DeletePolicy(ctx context.Context, node *dao.AvailabilityZone, policy *jPolicy.PolicyStatus) jErr.Error
	// GetUUID 查询-可用区
	GetUUID(ctx context.Context, uuid string, filters ...*winapi.Filter) (*dao.AvailabilityZone, jErr.Error)
	// GetName 查询-可用区Name
	GetName(ctx context.Context, name string, filters ...*winapi.Filter) (*dao.AvailabilityZone, jErr.Error)
	// Exists 存在-可用区
	Exists(ctx context.Context, filters ...*winapi.Filter) (exists bool, err jErr.Error)

	// List 列表-可用区
	List(ctx context.Context, pageNumber, pageSize int64, filters winapi.Filters, sorts winapi.Sorts) ([]*dao.AvailabilityZone, jErr.Error)
	// TotalCount 总数-可用区
	TotalCount(ctx context.Context, filters winapi.Filters) int64

	// PolicyOperate 操作策略-可用区
	PolicyOperate(ctx context.Context, node *dao.AvailabilityZone, policy *jPolicy.PolicyStatus) (*dao.AvailabilityZone, jErr.Error)

	// QueryGroupAggregateScan 获取聚合相关自定义结构体 in 返回结果
	//  in 限使用传址的方式传递切片类型，必须在 filters 使用 winapi.SelectFields 过滤出要使用的字段（否则会提示 missing struct field for column）
	//  若只想获取单字段原始数据类型组成的切片，在指定 filters 使用 winapi.SelectFields 挑选一个字段即可
	//  fields 要聚合的字段，必需存在 in 结构体
	QueryGroupAggregateScan(ctx context.Context, in any, fields []string, filters []*winapi.Filter, sorts winapi.Sorts, fns ...dao.AggregateFunc) jErr.Error
}
