package service

import (
	"github.com/go-kratos/kratos/v2/log"

	jErr "coding.jd.com/pcd-application/win-go/error"
	doTask "coding.jd.com/pcd-application/win-go/project/domain/task"
)

type JobFunc func() (err jErr.Error)

type JobService struct {
	log *log.Helper

	TaskCase doTask.TaskCase
}

func NewJobService(
	logger log.Logger,

	TaskCase doTask.TaskCase,
) *JobService {
	return &JobService{
		log: log.NewHelper(log.With(logger, "module", "job")),

		TaskCase: TaskCase,
	}
}

// JobList 本任务列表是面向配置提供服务
//
//	若不想通过配置添加，也允许通过 v2Cron.AddFuncLock 注册
func (job *JobService) JobList() map[string]JobFunc {
	return map[string]JobFunc{
		"detectTaskPools": job.TaskCase.DetectPools,
		"syncTaskPools":   job.TaskCase.SyncPools,
	}
}
