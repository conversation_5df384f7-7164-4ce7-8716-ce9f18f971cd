// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.30.0
// 	protoc        v3.21.1
// source: code.proto

package conf

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type DomainCode int32

const (
	DomainCode_All                         DomainCode = 0 // 全部（预留，不可删除）
	DomainCode_Common                      DomainCode = 1
	DomainCode_Task                        DomainCode = 2
	DomainCode_User                        DomainCode = 3
	DomainCode_Config                      DomainCode = 4
	DomainCode_DMutexRedis                 DomainCode = 11 // 分布式排他锁-Redis/Memory
	DomainCode_Demo                        DomainCode = 100
	DomainCode_DeliveryPlan                DomainCode = 101
	DomainCode_BaseConfig                  DomainCode = 102
	DomainCode_Region                      DomainCode = 103
	DomainCode_AvailabilityZone            DomainCode = 104
	DomainCode_Warehouse                   DomainCode = 105
	DomainCode_Rack                        DomainCode = 106
	DomainCode_Server                      DomainCode = 107
	DomainCode_ServerRole                  DomainCode = 108
	DomainCode_ServerTag                   DomainCode = 109
	DomainCode_Network                     DomainCode = 110
	DomainCode_WarehouseMaintainer         DomainCode = 111
	DomainCode_OssPool                     DomainCode = 112
	DomainCode_TenantBills                 DomainCode = 114
	DomainCode_DeviceGroup                 DomainCode = 115
	DomainCode_DeviceGroupServer           DomainCode = 116
	DomainCode_DeviceGroupNetwork          DomainCode = 117
	DomainCode_Vdc                         DomainCode = 118
	DomainCode_KickOsPackage               DomainCode = 119
	DomainCode_NetworkEquipment            DomainCode = 120
	DomainCode_NetworkRole                 DomainCode = 121
	DomainCode_ClassicLinkPool             DomainCode = 122
	DomainCode_PhyDirectConn               DomainCode = 123
	DomainCode_Aip                         DomainCode = 124
	DomainCode_Dnat                        DomainCode = 125
	DomainCode_ZbsPool                     DomainCode = 126
	DomainCode_Cfs                         DomainCode = 127
	DomainCode_ServerEnv                   DomainCode = 128
	DomainCode_DeliveryUpgrade             DomainCode = 129
	DomainCode_BasicDeploy                 DomainCode = 130
	DomainCode_NetworkTopology             DomainCode = 131
	DomainCode_AssetOverview               DomainCode = 132
	DomainCode_ResourceRole                DomainCode = 133
	DomainCode_TestCase                    DomainCode = 134
	DomainCode_MonitorScreen               DomainCode = 135
	DomainCode_Platform                    DomainCode = 136
	DomainCode_GrayTag                     DomainCode = 137
	DomainCode_Disk                        DomainCode = 140
	DomainCode_HardWareRoleMetadata        DomainCode = 141
	DomainCode_ComputePool                 DomainCode = 198
	DomainCode_Argo                        DomainCode = 199
	DomainCode_NetworkPool                 DomainCode = 200
	DomainCode_NetworkPlan                 DomainCode = 201 // 网络规划
	DomainCode_NetworkPlanIspBGPs          DomainCode = 202 // 网络规划 - ipv4
	DomainCode_NetworkPlanJdIPv6s          DomainCode = 203 // 网络规划 - ipv6
	DomainCode_NetworkPlanInterPriOverlaps DomainCode = 205 // 网络规划 - paas
	DomainCode_NetworkPlanInats            DomainCode = 206 // 网络规划 - inat
	DomainCode_NetworkPlanLocal            DomainCode = 207 // 网络规划 - 本地网络
	DomainCode_NetworkPlanOthers           DomainCode = 208 // 网络规划 - 其他网络
	DomainCode_NetworkPlanLbs              DomainCode = 209 // 网络规划 - lbs
	DomainCode_NetworkPlanDns              DomainCode = 210 // 网络规划 - dns
	DomainCode_NetworkPlanArk              DomainCode = 211 // 网络规划 - 云翼
	DomainCode_NetworkPlanContainer        DomainCode = 212 // 网络规划 - 容器
	DomainCode_NetworkPlanSdnDRVR          DomainCode = 213 // 网络规划 - dr配置, dr和vr通信使用
	DomainCode_NetworkPlanSdnVRDR          DomainCode = 214 // 网络规划 - vr配置, vr和dr通信使用
	DomainCode_NetworkPlanSdnVRVM          DomainCode = 215 // 网络规划 - vr配置, vr和vm通信使用
	DomainCode_NetworkPlanSdnDRSW          DomainCode = 216 // 网络规划 - dr配置, bgp引流地址,dr和交换机通信
	DomainCode_NetworkPlanBgpAsNumber      DomainCode = 217 // bgpAsNumber
	DomainCode_NetworkPlanSdnVM            DomainCode = 218 // SdnVM - 虚机数据口网段规划
	DomainCode_NetworkPlanStorage          DomainCode = 221 // storage - 存储数据网段规划
	DomainCode_NetworkPlanK8sCalico        DomainCode = 222 // k8sCalico - calico容器网段规划
	DomainCode_NetworkPlanK8sClusterIp     DomainCode = 223 // k8sClusterIp - K8sClusterIp网段规划
	DomainCode_NetworkPlanSdnIPv6DRVR      DomainCode = 224
	DomainCode_NetworkPlanSdnIPv6VRDR      DomainCode = 225
	DomainCode_NetworkPlanSdnIPv6VRVM      DomainCode = 226
	DomainCode_NetworkPlanSdnIPv6DRSW      DomainCode = 227
	DomainCode_NetworkPlanSdnIPv6DRBGP     DomainCode = 228
	DomainCode_NetworkPlanSdnVRBGW         DomainCode = 229
	DomainCode_NetworkPlanSdnVRNATGW       DomainCode = 230
	DomainCode_NetworkPlanSdnDRNATGW       DomainCode = 231
	DomainCode_NetworkPlanBgwTunnel        DomainCode = 232
	DomainCode_NetworkPlanClrTunnel        DomainCode = 233
	DomainCode_NetworkPlanBGW2DLR          DomainCode = 234
	DomainCode_NetworkPlanCLR              DomainCode = 235
	// region级的所有机柜网络规划
	DomainCode_NetworkPlanCompute   DomainCode = 241
	DomainCode_NetworkPlanVm2vr     DomainCode = 242
	DomainCode_NetworkPlanVm2vrIPv6 DomainCode = 243
	DomainCode_NetworkPlanDr2vr     DomainCode = 244
	DomainCode_NetworkPlanDr2vrIPv6 DomainCode = 245
	DomainCode_NetworkPlanNatgw2vm  DomainCode = 246
	DomainCode_NetworkPlanVr2bgw    DomainCode = 247
	DomainCode_NetworkPlanEnd       DomainCode = 250
	// 计量占用 251 ~ 260
	DomainCode_MeasureTenantVmResource   DomainCode = 251 // 租户资源用量
	DomainCode_MeasureTenantDiskResource DomainCode = 252 // 租户资源用量
	DomainCode_MeasureTenantEipResource  DomainCode = 253 // 租户资源用量
	DomainCode_MeasureVdcVmResource      DomainCode = 254 // VDC资源用量
	DomainCode_MeasureVdcDiskResource    DomainCode = 255 // VDC资源用量
	DomainCode_MeasureVdcEipResource     DomainCode = 256 // VDC资源用量
	DomainCode_MeasureEnd                DomainCode = 260
	// 云盘迁移
	DomainCode_DiskMigrate     DomainCode = 261
	DomainCode_DiskMigrateTask DomainCode = 262
	DomainCode_DiskMigrateEnd  DomainCode = 265
	// 物理服务器纳管 280 ~ 300
	DomainCode_CloudHostingServer        DomainCode = 280 // 物理服务器
	DomainCode_CloudHostingKVData        DomainCode = 281 // kv数据
	DomainCode_CloudHostingEncryption    DomainCode = 282 // 加密
	DomainCode_CloudHostingServerMonitor DomainCode = 283 // 物理服务器监控数据
	DomainCode_CloudHostingLatestReport  DomainCode = 284 // ifrit master 中latest_report数据
	DomainCode_CloudHostingEnd           DomainCode = 300
	DomainCode_ArbitrationAz             DomainCode = 320 // 逻辑仲裁区
	DomainCode_Lord                      DomainCode = 321 // 云翼Lord
	DomainCode_DeliveryTopology          DomainCode = 322 // 交付拓扑图
)

// Enum value maps for DomainCode.
var (
	DomainCode_name = map[int32]string{
		0:   "All",
		1:   "Common",
		2:   "Task",
		3:   "User",
		4:   "Config",
		11:  "DMutexRedis",
		100: "Demo",
		101: "DeliveryPlan",
		102: "BaseConfig",
		103: "Region",
		104: "AvailabilityZone",
		105: "Warehouse",
		106: "Rack",
		107: "Server",
		108: "ServerRole",
		109: "ServerTag",
		110: "Network",
		111: "WarehouseMaintainer",
		112: "OssPool",
		114: "TenantBills",
		115: "DeviceGroup",
		116: "DeviceGroupServer",
		117: "DeviceGroupNetwork",
		118: "Vdc",
		119: "KickOsPackage",
		120: "NetworkEquipment",
		121: "NetworkRole",
		122: "ClassicLinkPool",
		123: "PhyDirectConn",
		124: "Aip",
		125: "Dnat",
		126: "ZbsPool",
		127: "Cfs",
		128: "ServerEnv",
		129: "DeliveryUpgrade",
		130: "BasicDeploy",
		131: "NetworkTopology",
		132: "AssetOverview",
		133: "ResourceRole",
		134: "TestCase",
		135: "MonitorScreen",
		136: "Platform",
		137: "GrayTag",
		140: "Disk",
		141: "HardWareRoleMetadata",
		198: "ComputePool",
		199: "Argo",
		200: "NetworkPool",
		201: "NetworkPlan",
		202: "NetworkPlanIspBGPs",
		203: "NetworkPlanJdIPv6s",
		205: "NetworkPlanInterPriOverlaps",
		206: "NetworkPlanInats",
		207: "NetworkPlanLocal",
		208: "NetworkPlanOthers",
		209: "NetworkPlanLbs",
		210: "NetworkPlanDns",
		211: "NetworkPlanArk",
		212: "NetworkPlanContainer",
		213: "NetworkPlanSdnDRVR",
		214: "NetworkPlanSdnVRDR",
		215: "NetworkPlanSdnVRVM",
		216: "NetworkPlanSdnDRSW",
		217: "NetworkPlanBgpAsNumber",
		218: "NetworkPlanSdnVM",
		221: "NetworkPlanStorage",
		222: "NetworkPlanK8sCalico",
		223: "NetworkPlanK8sClusterIp",
		224: "NetworkPlanSdnIPv6DRVR",
		225: "NetworkPlanSdnIPv6VRDR",
		226: "NetworkPlanSdnIPv6VRVM",
		227: "NetworkPlanSdnIPv6DRSW",
		228: "NetworkPlanSdnIPv6DRBGP",
		229: "NetworkPlanSdnVRBGW",
		230: "NetworkPlanSdnVRNATGW",
		231: "NetworkPlanSdnDRNATGW",
		232: "NetworkPlanBgwTunnel",
		233: "NetworkPlanClrTunnel",
		234: "NetworkPlanBGW2DLR",
		235: "NetworkPlanCLR",
		241: "NetworkPlanCompute",
		242: "NetworkPlanVm2vr",
		243: "NetworkPlanVm2vrIPv6",
		244: "NetworkPlanDr2vr",
		245: "NetworkPlanDr2vrIPv6",
		246: "NetworkPlanNatgw2vm",
		247: "NetworkPlanVr2bgw",
		250: "NetworkPlanEnd",
		251: "MeasureTenantVmResource",
		252: "MeasureTenantDiskResource",
		253: "MeasureTenantEipResource",
		254: "MeasureVdcVmResource",
		255: "MeasureVdcDiskResource",
		256: "MeasureVdcEipResource",
		260: "MeasureEnd",
		261: "DiskMigrate",
		262: "DiskMigrateTask",
		265: "DiskMigrateEnd",
		280: "CloudHostingServer",
		281: "CloudHostingKVData",
		282: "CloudHostingEncryption",
		283: "CloudHostingServerMonitor",
		284: "CloudHostingLatestReport",
		300: "CloudHostingEnd",
		320: "ArbitrationAz",
		321: "Lord",
		322: "DeliveryTopology",
	}
	DomainCode_value = map[string]int32{
		"All":                         0,
		"Common":                      1,
		"Task":                        2,
		"User":                        3,
		"Config":                      4,
		"DMutexRedis":                 11,
		"Demo":                        100,
		"DeliveryPlan":                101,
		"BaseConfig":                  102,
		"Region":                      103,
		"AvailabilityZone":            104,
		"Warehouse":                   105,
		"Rack":                        106,
		"Server":                      107,
		"ServerRole":                  108,
		"ServerTag":                   109,
		"Network":                     110,
		"WarehouseMaintainer":         111,
		"OssPool":                     112,
		"TenantBills":                 114,
		"DeviceGroup":                 115,
		"DeviceGroupServer":           116,
		"DeviceGroupNetwork":          117,
		"Vdc":                         118,
		"KickOsPackage":               119,
		"NetworkEquipment":            120,
		"NetworkRole":                 121,
		"ClassicLinkPool":             122,
		"PhyDirectConn":               123,
		"Aip":                         124,
		"Dnat":                        125,
		"ZbsPool":                     126,
		"Cfs":                         127,
		"ServerEnv":                   128,
		"DeliveryUpgrade":             129,
		"BasicDeploy":                 130,
		"NetworkTopology":             131,
		"AssetOverview":               132,
		"ResourceRole":                133,
		"TestCase":                    134,
		"MonitorScreen":               135,
		"Platform":                    136,
		"GrayTag":                     137,
		"Disk":                        140,
		"HardWareRoleMetadata":        141,
		"ComputePool":                 198,
		"Argo":                        199,
		"NetworkPool":                 200,
		"NetworkPlan":                 201,
		"NetworkPlanIspBGPs":          202,
		"NetworkPlanJdIPv6s":          203,
		"NetworkPlanInterPriOverlaps": 205,
		"NetworkPlanInats":            206,
		"NetworkPlanLocal":            207,
		"NetworkPlanOthers":           208,
		"NetworkPlanLbs":              209,
		"NetworkPlanDns":              210,
		"NetworkPlanArk":              211,
		"NetworkPlanContainer":        212,
		"NetworkPlanSdnDRVR":          213,
		"NetworkPlanSdnVRDR":          214,
		"NetworkPlanSdnVRVM":          215,
		"NetworkPlanSdnDRSW":          216,
		"NetworkPlanBgpAsNumber":      217,
		"NetworkPlanSdnVM":            218,
		"NetworkPlanStorage":          221,
		"NetworkPlanK8sCalico":        222,
		"NetworkPlanK8sClusterIp":     223,
		"NetworkPlanSdnIPv6DRVR":      224,
		"NetworkPlanSdnIPv6VRDR":      225,
		"NetworkPlanSdnIPv6VRVM":      226,
		"NetworkPlanSdnIPv6DRSW":      227,
		"NetworkPlanSdnIPv6DRBGP":     228,
		"NetworkPlanSdnVRBGW":         229,
		"NetworkPlanSdnVRNATGW":       230,
		"NetworkPlanSdnDRNATGW":       231,
		"NetworkPlanBgwTunnel":        232,
		"NetworkPlanClrTunnel":        233,
		"NetworkPlanBGW2DLR":          234,
		"NetworkPlanCLR":              235,
		"NetworkPlanCompute":          241,
		"NetworkPlanVm2vr":            242,
		"NetworkPlanVm2vrIPv6":        243,
		"NetworkPlanDr2vr":            244,
		"NetworkPlanDr2vrIPv6":        245,
		"NetworkPlanNatgw2vm":         246,
		"NetworkPlanVr2bgw":           247,
		"NetworkPlanEnd":              250,
		"MeasureTenantVmResource":     251,
		"MeasureTenantDiskResource":   252,
		"MeasureTenantEipResource":    253,
		"MeasureVdcVmResource":        254,
		"MeasureVdcDiskResource":      255,
		"MeasureVdcEipResource":       256,
		"MeasureEnd":                  260,
		"DiskMigrate":                 261,
		"DiskMigrateTask":             262,
		"DiskMigrateEnd":              265,
		"CloudHostingServer":          280,
		"CloudHostingKVData":          281,
		"CloudHostingEncryption":      282,
		"CloudHostingServerMonitor":   283,
		"CloudHostingLatestReport":    284,
		"CloudHostingEnd":             300,
		"ArbitrationAz":               320,
		"Lord":                        321,
		"DeliveryTopology":            322,
	}
)

func (x DomainCode) Enum() *DomainCode {
	p := new(DomainCode)
	*p = x
	return p
}

func (x DomainCode) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (DomainCode) Descriptor() protoreflect.EnumDescriptor {
	return file_code_proto_enumTypes[0].Descriptor()
}

func (DomainCode) Type() protoreflect.EnumType {
	return &file_code_proto_enumTypes[0]
}

func (x DomainCode) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use DomainCode.Descriptor instead.
func (DomainCode) EnumDescriptor() ([]byte, []int) {
	return file_code_proto_rawDescGZIP(), []int{0}
}

var File_code_proto protoreflect.FileDescriptor

var file_code_proto_rawDesc = []byte{
	0x0a, 0x0a, 0x63, 0x6f, 0x64, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x1c, 0x6a, 0x64,
	0x73, 0x74, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x74, 0x65, 0x67, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x2e, 0x63, 0x6f, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x74, 0x73, 0x2a, 0xb6, 0x12, 0x0a, 0x0a, 0x44,
	0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x07, 0x0a, 0x03, 0x41, 0x6c, 0x6c,
	0x10, 0x00, 0x12, 0x0a, 0x0a, 0x06, 0x43, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x10, 0x01, 0x12, 0x08,
	0x0a, 0x04, 0x54, 0x61, 0x73, 0x6b, 0x10, 0x02, 0x12, 0x08, 0x0a, 0x04, 0x55, 0x73, 0x65, 0x72,
	0x10, 0x03, 0x12, 0x0a, 0x0a, 0x06, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x10, 0x04, 0x12, 0x0f,
	0x0a, 0x0b, 0x44, 0x4d, 0x75, 0x74, 0x65, 0x78, 0x52, 0x65, 0x64, 0x69, 0x73, 0x10, 0x0b, 0x12,
	0x08, 0x0a, 0x04, 0x44, 0x65, 0x6d, 0x6f, 0x10, 0x64, 0x12, 0x10, 0x0a, 0x0c, 0x44, 0x65, 0x6c,
	0x69, 0x76, 0x65, 0x72, 0x79, 0x50, 0x6c, 0x61, 0x6e, 0x10, 0x65, 0x12, 0x0e, 0x0a, 0x0a, 0x42,
	0x61, 0x73, 0x65, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x10, 0x66, 0x12, 0x0a, 0x0a, 0x06, 0x52,
	0x65, 0x67, 0x69, 0x6f, 0x6e, 0x10, 0x67, 0x12, 0x14, 0x0a, 0x10, 0x41, 0x76, 0x61, 0x69, 0x6c,
	0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x5a, 0x6f, 0x6e, 0x65, 0x10, 0x68, 0x12, 0x0d, 0x0a,
	0x09, 0x57, 0x61, 0x72, 0x65, 0x68, 0x6f, 0x75, 0x73, 0x65, 0x10, 0x69, 0x12, 0x08, 0x0a, 0x04,
	0x52, 0x61, 0x63, 0x6b, 0x10, 0x6a, 0x12, 0x0a, 0x0a, 0x06, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72,
	0x10, 0x6b, 0x12, 0x0e, 0x0a, 0x0a, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x52, 0x6f, 0x6c, 0x65,
	0x10, 0x6c, 0x12, 0x0d, 0x0a, 0x09, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x54, 0x61, 0x67, 0x10,
	0x6d, 0x12, 0x0b, 0x0a, 0x07, 0x4e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x10, 0x6e, 0x12, 0x17,
	0x0a, 0x13, 0x57, 0x61, 0x72, 0x65, 0x68, 0x6f, 0x75, 0x73, 0x65, 0x4d, 0x61, 0x69, 0x6e, 0x74,
	0x61, 0x69, 0x6e, 0x65, 0x72, 0x10, 0x6f, 0x12, 0x0b, 0x0a, 0x07, 0x4f, 0x73, 0x73, 0x50, 0x6f,
	0x6f, 0x6c, 0x10, 0x70, 0x12, 0x0f, 0x0a, 0x0b, 0x54, 0x65, 0x6e, 0x61, 0x6e, 0x74, 0x42, 0x69,
	0x6c, 0x6c, 0x73, 0x10, 0x72, 0x12, 0x0f, 0x0a, 0x0b, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x47,
	0x72, 0x6f, 0x75, 0x70, 0x10, 0x73, 0x12, 0x15, 0x0a, 0x11, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65,
	0x47, 0x72, 0x6f, 0x75, 0x70, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x10, 0x74, 0x12, 0x16, 0x0a,
	0x12, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x4e, 0x65, 0x74, 0x77,
	0x6f, 0x72, 0x6b, 0x10, 0x75, 0x12, 0x07, 0x0a, 0x03, 0x56, 0x64, 0x63, 0x10, 0x76, 0x12, 0x11,
	0x0a, 0x0d, 0x4b, 0x69, 0x63, 0x6b, 0x4f, 0x73, 0x50, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x10,
	0x77, 0x12, 0x14, 0x0a, 0x10, 0x4e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x45, 0x71, 0x75, 0x69,
	0x70, 0x6d, 0x65, 0x6e, 0x74, 0x10, 0x78, 0x12, 0x0f, 0x0a, 0x0b, 0x4e, 0x65, 0x74, 0x77, 0x6f,
	0x72, 0x6b, 0x52, 0x6f, 0x6c, 0x65, 0x10, 0x79, 0x12, 0x13, 0x0a, 0x0f, 0x43, 0x6c, 0x61, 0x73,
	0x73, 0x69, 0x63, 0x4c, 0x69, 0x6e, 0x6b, 0x50, 0x6f, 0x6f, 0x6c, 0x10, 0x7a, 0x12, 0x11, 0x0a,
	0x0d, 0x50, 0x68, 0x79, 0x44, 0x69, 0x72, 0x65, 0x63, 0x74, 0x43, 0x6f, 0x6e, 0x6e, 0x10, 0x7b,
	0x12, 0x07, 0x0a, 0x03, 0x41, 0x69, 0x70, 0x10, 0x7c, 0x12, 0x08, 0x0a, 0x04, 0x44, 0x6e, 0x61,
	0x74, 0x10, 0x7d, 0x12, 0x0b, 0x0a, 0x07, 0x5a, 0x62, 0x73, 0x50, 0x6f, 0x6f, 0x6c, 0x10, 0x7e,
	0x12, 0x07, 0x0a, 0x03, 0x43, 0x66, 0x73, 0x10, 0x7f, 0x12, 0x0e, 0x0a, 0x09, 0x53, 0x65, 0x72,
	0x76, 0x65, 0x72, 0x45, 0x6e, 0x76, 0x10, 0x80, 0x01, 0x12, 0x14, 0x0a, 0x0f, 0x44, 0x65, 0x6c,
	0x69, 0x76, 0x65, 0x72, 0x79, 0x55, 0x70, 0x67, 0x72, 0x61, 0x64, 0x65, 0x10, 0x81, 0x01, 0x12,
	0x10, 0x0a, 0x0b, 0x42, 0x61, 0x73, 0x69, 0x63, 0x44, 0x65, 0x70, 0x6c, 0x6f, 0x79, 0x10, 0x82,
	0x01, 0x12, 0x14, 0x0a, 0x0f, 0x4e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x54, 0x6f, 0x70, 0x6f,
	0x6c, 0x6f, 0x67, 0x79, 0x10, 0x83, 0x01, 0x12, 0x12, 0x0a, 0x0d, 0x41, 0x73, 0x73, 0x65, 0x74,
	0x4f, 0x76, 0x65, 0x72, 0x76, 0x69, 0x65, 0x77, 0x10, 0x84, 0x01, 0x12, 0x11, 0x0a, 0x0c, 0x52,
	0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x52, 0x6f, 0x6c, 0x65, 0x10, 0x85, 0x01, 0x12, 0x0d,
	0x0a, 0x08, 0x54, 0x65, 0x73, 0x74, 0x43, 0x61, 0x73, 0x65, 0x10, 0x86, 0x01, 0x12, 0x12, 0x0a,
	0x0d, 0x4d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x53, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x10, 0x87,
	0x01, 0x12, 0x0d, 0x0a, 0x08, 0x50, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x10, 0x88, 0x01,
	0x12, 0x0c, 0x0a, 0x07, 0x47, 0x72, 0x61, 0x79, 0x54, 0x61, 0x67, 0x10, 0x89, 0x01, 0x12, 0x09,
	0x0a, 0x04, 0x44, 0x69, 0x73, 0x6b, 0x10, 0x8c, 0x01, 0x12, 0x19, 0x0a, 0x14, 0x48, 0x61, 0x72,
	0x64, 0x57, 0x61, 0x72, 0x65, 0x52, 0x6f, 0x6c, 0x65, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74,
	0x61, 0x10, 0x8d, 0x01, 0x12, 0x10, 0x0a, 0x0b, 0x43, 0x6f, 0x6d, 0x70, 0x75, 0x74, 0x65, 0x50,
	0x6f, 0x6f, 0x6c, 0x10, 0xc6, 0x01, 0x12, 0x09, 0x0a, 0x04, 0x41, 0x72, 0x67, 0x6f, 0x10, 0xc7,
	0x01, 0x12, 0x10, 0x0a, 0x0b, 0x4e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x50, 0x6f, 0x6f, 0x6c,
	0x10, 0xc8, 0x01, 0x12, 0x10, 0x0a, 0x0b, 0x4e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x50, 0x6c,
	0x61, 0x6e, 0x10, 0xc9, 0x01, 0x12, 0x17, 0x0a, 0x12, 0x4e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b,
	0x50, 0x6c, 0x61, 0x6e, 0x49, 0x73, 0x70, 0x42, 0x47, 0x50, 0x73, 0x10, 0xca, 0x01, 0x12, 0x17,
	0x0a, 0x12, 0x4e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x50, 0x6c, 0x61, 0x6e, 0x4a, 0x64, 0x49,
	0x50, 0x76, 0x36, 0x73, 0x10, 0xcb, 0x01, 0x12, 0x20, 0x0a, 0x1b, 0x4e, 0x65, 0x74, 0x77, 0x6f,
	0x72, 0x6b, 0x50, 0x6c, 0x61, 0x6e, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x50, 0x75, 0x62, 0x4f, 0x76,
	0x65, 0x72, 0x6c, 0x61, 0x79, 0x73, 0x10, 0xcc, 0x01, 0x12, 0x20, 0x0a, 0x1b, 0x4e, 0x65, 0x74,
	0x77, 0x6f, 0x72, 0x6b, 0x50, 0x6c, 0x61, 0x6e, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x50, 0x72, 0x69,
	0x4f, 0x76, 0x65, 0x72, 0x6c, 0x61, 0x70, 0x73, 0x10, 0xcd, 0x01, 0x12, 0x15, 0x0a, 0x10, 0x4e,
	0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x50, 0x6c, 0x61, 0x6e, 0x49, 0x6e, 0x61, 0x74, 0x73, 0x10,
	0xce, 0x01, 0x12, 0x15, 0x0a, 0x10, 0x4e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x50, 0x6c, 0x61,
	0x6e, 0x4c, 0x6f, 0x63, 0x61, 0x6c, 0x10, 0xcf, 0x01, 0x12, 0x16, 0x0a, 0x11, 0x4e, 0x65, 0x74,
	0x77, 0x6f, 0x72, 0x6b, 0x50, 0x6c, 0x61, 0x6e, 0x4f, 0x74, 0x68, 0x65, 0x72, 0x73, 0x10, 0xd0,
	0x01, 0x12, 0x13, 0x0a, 0x0e, 0x4e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x50, 0x6c, 0x61, 0x6e,
	0x4c, 0x62, 0x73, 0x10, 0xd1, 0x01, 0x12, 0x13, 0x0a, 0x0e, 0x4e, 0x65, 0x74, 0x77, 0x6f, 0x72,
	0x6b, 0x50, 0x6c, 0x61, 0x6e, 0x44, 0x6e, 0x73, 0x10, 0xd2, 0x01, 0x12, 0x13, 0x0a, 0x0e, 0x4e,
	0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x50, 0x6c, 0x61, 0x6e, 0x41, 0x72, 0x6b, 0x10, 0xd3, 0x01,
	0x12, 0x19, 0x0a, 0x14, 0x4e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x50, 0x6c, 0x61, 0x6e, 0x43,
	0x6f, 0x6e, 0x74, 0x61, 0x69, 0x6e, 0x65, 0x72, 0x10, 0xd4, 0x01, 0x12, 0x17, 0x0a, 0x12, 0x4e,
	0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x50, 0x6c, 0x61, 0x6e, 0x53, 0x64, 0x6e, 0x44, 0x52, 0x56,
	0x52, 0x10, 0xd5, 0x01, 0x12, 0x17, 0x0a, 0x12, 0x4e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x50,
	0x6c, 0x61, 0x6e, 0x53, 0x64, 0x6e, 0x56, 0x52, 0x44, 0x52, 0x10, 0xd6, 0x01, 0x12, 0x17, 0x0a,
	0x12, 0x4e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x50, 0x6c, 0x61, 0x6e, 0x53, 0x64, 0x6e, 0x56,
	0x52, 0x56, 0x4d, 0x10, 0xd7, 0x01, 0x12, 0x17, 0x0a, 0x12, 0x4e, 0x65, 0x74, 0x77, 0x6f, 0x72,
	0x6b, 0x50, 0x6c, 0x61, 0x6e, 0x53, 0x64, 0x6e, 0x44, 0x52, 0x53, 0x57, 0x10, 0xd8, 0x01, 0x12,
	0x1b, 0x0a, 0x16, 0x4e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x50, 0x6c, 0x61, 0x6e, 0x42, 0x67,
	0x70, 0x41, 0x73, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x10, 0xd9, 0x01, 0x12, 0x15, 0x0a, 0x10,
	0x4e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x50, 0x6c, 0x61, 0x6e, 0x53, 0x64, 0x6e, 0x56, 0x4d,
	0x10, 0xda, 0x01, 0x12, 0x1b, 0x0a, 0x16, 0x4e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x50, 0x6c,
	0x61, 0x6e, 0x41, 0x72, 0x6b, 0x55, 0x6e, 0x64, 0x65, 0x72, 0x6c, 0x61, 0x79, 0x10, 0xdb, 0x01,
	0x12, 0x1a, 0x0a, 0x15, 0x4e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x50, 0x6c, 0x61, 0x6e, 0x41,
	0x72, 0x6b, 0x4f, 0x76, 0x65, 0x72, 0x6c, 0x61, 0x79, 0x10, 0xdc, 0x01, 0x12, 0x17, 0x0a, 0x12,
	0x4e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x50, 0x6c, 0x61, 0x6e, 0x53, 0x74, 0x6f, 0x72, 0x61,
	0x67, 0x65, 0x10, 0xdd, 0x01, 0x12, 0x19, 0x0a, 0x14, 0x4e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b,
	0x50, 0x6c, 0x61, 0x6e, 0x4b, 0x38, 0x73, 0x43, 0x61, 0x6c, 0x69, 0x63, 0x6f, 0x10, 0xde, 0x01,
	0x12, 0x1c, 0x0a, 0x17, 0x4e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x50, 0x6c, 0x61, 0x6e, 0x4b,
	0x38, 0x73, 0x43, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x49, 0x70, 0x10, 0xdf, 0x01, 0x12, 0x1b,
	0x0a, 0x16, 0x4e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x50, 0x6c, 0x61, 0x6e, 0x53, 0x64, 0x6e,
	0x49, 0x50, 0x76, 0x36, 0x44, 0x52, 0x56, 0x52, 0x10, 0xe0, 0x01, 0x12, 0x1b, 0x0a, 0x16, 0x4e,
	0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x50, 0x6c, 0x61, 0x6e, 0x53, 0x64, 0x6e, 0x49, 0x50, 0x76,
	0x36, 0x56, 0x52, 0x44, 0x52, 0x10, 0xe1, 0x01, 0x12, 0x1b, 0x0a, 0x16, 0x4e, 0x65, 0x74, 0x77,
	0x6f, 0x72, 0x6b, 0x50, 0x6c, 0x61, 0x6e, 0x53, 0x64, 0x6e, 0x49, 0x50, 0x76, 0x36, 0x56, 0x52,
	0x56, 0x4d, 0x10, 0xe2, 0x01, 0x12, 0x1b, 0x0a, 0x16, 0x4e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b,
	0x50, 0x6c, 0x61, 0x6e, 0x53, 0x64, 0x6e, 0x49, 0x50, 0x76, 0x36, 0x44, 0x52, 0x53, 0x57, 0x10,
	0xe3, 0x01, 0x12, 0x1c, 0x0a, 0x17, 0x4e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x50, 0x6c, 0x61,
	0x6e, 0x53, 0x64, 0x6e, 0x49, 0x50, 0x76, 0x36, 0x44, 0x52, 0x42, 0x47, 0x50, 0x10, 0xe4, 0x01,
	0x12, 0x18, 0x0a, 0x13, 0x4e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x50, 0x6c, 0x61, 0x6e, 0x53,
	0x64, 0x6e, 0x56, 0x52, 0x42, 0x47, 0x57, 0x10, 0xe5, 0x01, 0x12, 0x1a, 0x0a, 0x15, 0x4e, 0x65,
	0x74, 0x77, 0x6f, 0x72, 0x6b, 0x50, 0x6c, 0x61, 0x6e, 0x53, 0x64, 0x6e, 0x56, 0x52, 0x4e, 0x41,
	0x54, 0x47, 0x57, 0x10, 0xe6, 0x01, 0x12, 0x1a, 0x0a, 0x15, 0x4e, 0x65, 0x74, 0x77, 0x6f, 0x72,
	0x6b, 0x50, 0x6c, 0x61, 0x6e, 0x53, 0x64, 0x6e, 0x44, 0x52, 0x4e, 0x41, 0x54, 0x47, 0x57, 0x10,
	0xe7, 0x01, 0x12, 0x19, 0x0a, 0x14, 0x4e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x50, 0x6c, 0x61,
	0x6e, 0x42, 0x67, 0x77, 0x54, 0x75, 0x6e, 0x6e, 0x65, 0x6c, 0x10, 0xe8, 0x01, 0x12, 0x19, 0x0a,
	0x14, 0x4e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x50, 0x6c, 0x61, 0x6e, 0x43, 0x6c, 0x72, 0x54,
	0x75, 0x6e, 0x6e, 0x65, 0x6c, 0x10, 0xe9, 0x01, 0x12, 0x17, 0x0a, 0x12, 0x4e, 0x65, 0x74, 0x77,
	0x6f, 0x72, 0x6b, 0x50, 0x6c, 0x61, 0x6e, 0x42, 0x47, 0x57, 0x32, 0x44, 0x4c, 0x52, 0x10, 0xea,
	0x01, 0x12, 0x13, 0x0a, 0x0e, 0x4e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x50, 0x6c, 0x61, 0x6e,
	0x43, 0x4c, 0x52, 0x10, 0xeb, 0x01, 0x12, 0x17, 0x0a, 0x12, 0x4e, 0x65, 0x74, 0x77, 0x6f, 0x72,
	0x6b, 0x50, 0x6c, 0x61, 0x6e, 0x43, 0x6f, 0x6d, 0x70, 0x75, 0x74, 0x65, 0x10, 0xf1, 0x01, 0x12,
	0x15, 0x0a, 0x10, 0x4e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x50, 0x6c, 0x61, 0x6e, 0x56, 0x6d,
	0x32, 0x76, 0x72, 0x10, 0xf2, 0x01, 0x12, 0x19, 0x0a, 0x14, 0x4e, 0x65, 0x74, 0x77, 0x6f, 0x72,
	0x6b, 0x50, 0x6c, 0x61, 0x6e, 0x56, 0x6d, 0x32, 0x76, 0x72, 0x49, 0x50, 0x76, 0x36, 0x10, 0xf3,
	0x01, 0x12, 0x15, 0x0a, 0x10, 0x4e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x50, 0x6c, 0x61, 0x6e,
	0x44, 0x72, 0x32, 0x76, 0x72, 0x10, 0xf4, 0x01, 0x12, 0x19, 0x0a, 0x14, 0x4e, 0x65, 0x74, 0x77,
	0x6f, 0x72, 0x6b, 0x50, 0x6c, 0x61, 0x6e, 0x44, 0x72, 0x32, 0x76, 0x72, 0x49, 0x50, 0x76, 0x36,
	0x10, 0xf5, 0x01, 0x12, 0x18, 0x0a, 0x13, 0x4e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x50, 0x6c,
	0x61, 0x6e, 0x4e, 0x61, 0x74, 0x67, 0x77, 0x32, 0x76, 0x6d, 0x10, 0xf6, 0x01, 0x12, 0x16, 0x0a,
	0x11, 0x4e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x50, 0x6c, 0x61, 0x6e, 0x56, 0x72, 0x32, 0x62,
	0x67, 0x77, 0x10, 0xf7, 0x01, 0x12, 0x13, 0x0a, 0x0e, 0x4e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b,
	0x50, 0x6c, 0x61, 0x6e, 0x45, 0x6e, 0x64, 0x10, 0xfa, 0x01, 0x12, 0x1c, 0x0a, 0x17, 0x4d, 0x65,
	0x61, 0x73, 0x75, 0x72, 0x65, 0x54, 0x65, 0x6e, 0x61, 0x6e, 0x74, 0x56, 0x6d, 0x52, 0x65, 0x73,
	0x6f, 0x75, 0x72, 0x63, 0x65, 0x10, 0xfb, 0x01, 0x12, 0x1e, 0x0a, 0x19, 0x4d, 0x65, 0x61, 0x73,
	0x75, 0x72, 0x65, 0x54, 0x65, 0x6e, 0x61, 0x6e, 0x74, 0x44, 0x69, 0x73, 0x6b, 0x52, 0x65, 0x73,
	0x6f, 0x75, 0x72, 0x63, 0x65, 0x10, 0xfc, 0x01, 0x12, 0x1d, 0x0a, 0x18, 0x4d, 0x65, 0x61, 0x73,
	0x75, 0x72, 0x65, 0x54, 0x65, 0x6e, 0x61, 0x6e, 0x74, 0x45, 0x69, 0x70, 0x52, 0x65, 0x73, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x10, 0xfd, 0x01, 0x12, 0x19, 0x0a, 0x14, 0x4d, 0x65, 0x61, 0x73, 0x75,
	0x72, 0x65, 0x56, 0x64, 0x63, 0x56, 0x6d, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x10,
	0xfe, 0x01, 0x12, 0x1b, 0x0a, 0x16, 0x4d, 0x65, 0x61, 0x73, 0x75, 0x72, 0x65, 0x56, 0x64, 0x63,
	0x44, 0x69, 0x73, 0x6b, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x10, 0xff, 0x01, 0x12,
	0x1a, 0x0a, 0x15, 0x4d, 0x65, 0x61, 0x73, 0x75, 0x72, 0x65, 0x56, 0x64, 0x63, 0x45, 0x69, 0x70,
	0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x10, 0x80, 0x02, 0x12, 0x0f, 0x0a, 0x0a, 0x4d,
	0x65, 0x61, 0x73, 0x75, 0x72, 0x65, 0x45, 0x6e, 0x64, 0x10, 0x84, 0x02, 0x12, 0x10, 0x0a, 0x0b,
	0x44, 0x69, 0x73, 0x6b, 0x4d, 0x69, 0x67, 0x72, 0x61, 0x74, 0x65, 0x10, 0x85, 0x02, 0x12, 0x14,
	0x0a, 0x0f, 0x44, 0x69, 0x73, 0x6b, 0x4d, 0x69, 0x67, 0x72, 0x61, 0x74, 0x65, 0x54, 0x61, 0x73,
	0x6b, 0x10, 0x86, 0x02, 0x12, 0x13, 0x0a, 0x0e, 0x44, 0x69, 0x73, 0x6b, 0x4d, 0x69, 0x67, 0x72,
	0x61, 0x74, 0x65, 0x45, 0x6e, 0x64, 0x10, 0x89, 0x02, 0x12, 0x17, 0x0a, 0x12, 0x43, 0x6c, 0x6f,
	0x75, 0x64, 0x48, 0x6f, 0x73, 0x74, 0x69, 0x6e, 0x67, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x10,
	0x98, 0x02, 0x12, 0x17, 0x0a, 0x12, 0x43, 0x6c, 0x6f, 0x75, 0x64, 0x48, 0x6f, 0x73, 0x74, 0x69,
	0x6e, 0x67, 0x4b, 0x56, 0x44, 0x61, 0x74, 0x61, 0x10, 0x99, 0x02, 0x12, 0x1b, 0x0a, 0x16, 0x43,
	0x6c, 0x6f, 0x75, 0x64, 0x48, 0x6f, 0x73, 0x74, 0x69, 0x6e, 0x67, 0x45, 0x6e, 0x63, 0x72, 0x79,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x10, 0x9a, 0x02, 0x12, 0x1e, 0x0a, 0x19, 0x43, 0x6c, 0x6f, 0x75,
	0x64, 0x48, 0x6f, 0x73, 0x74, 0x69, 0x6e, 0x67, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x4d, 0x6f,
	0x6e, 0x69, 0x74, 0x6f, 0x72, 0x10, 0x9b, 0x02, 0x12, 0x1d, 0x0a, 0x18, 0x43, 0x6c, 0x6f, 0x75,
	0x64, 0x48, 0x6f, 0x73, 0x74, 0x69, 0x6e, 0x67, 0x4c, 0x61, 0x74, 0x65, 0x73, 0x74, 0x52, 0x65,
	0x70, 0x6f, 0x72, 0x74, 0x10, 0x9c, 0x02, 0x12, 0x14, 0x0a, 0x0f, 0x43, 0x6c, 0x6f, 0x75, 0x64,
	0x48, 0x6f, 0x73, 0x74, 0x69, 0x6e, 0x67, 0x45, 0x6e, 0x64, 0x10, 0xac, 0x02, 0x12, 0x12, 0x0a,
	0x0d, 0x41, 0x72, 0x62, 0x69, 0x74, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x41, 0x7a, 0x10, 0xc0,
	0x02, 0x12, 0x09, 0x0a, 0x04, 0x4c, 0x6f, 0x72, 0x64, 0x10, 0xc1, 0x02, 0x12, 0x15, 0x0a, 0x10,
	0x44, 0x65, 0x6c, 0x69, 0x76, 0x65, 0x72, 0x79, 0x54, 0x6f, 0x70, 0x6f, 0x6c, 0x6f, 0x67, 0x79,
	0x10, 0xc2, 0x02, 0x42, 0x1b, 0x5a, 0x19, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x2f,
	0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2f, 0x63, 0x6f, 0x6e, 0x66, 0x3b, 0x63, 0x6f, 0x6e, 0x66,
	0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_code_proto_rawDescOnce sync.Once
	file_code_proto_rawDescData = file_code_proto_rawDesc
)

func file_code_proto_rawDescGZIP() []byte {
	file_code_proto_rawDescOnce.Do(func() {
		file_code_proto_rawDescData = protoimpl.X.CompressGZIP(file_code_proto_rawDescData)
	})
	return file_code_proto_rawDescData
}

var file_code_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_code_proto_goTypes = []interface{}{
	(DomainCode)(0), // 0: jdstackintegration.constants.DomainCode
}
var file_code_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_code_proto_init() }
func file_code_proto_init() {
	if File_code_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_code_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   0,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_code_proto_goTypes,
		DependencyIndexes: file_code_proto_depIdxs,
		EnumInfos:         file_code_proto_enumTypes,
	}.Build()
	File_code_proto = out.File
	file_code_proto_rawDesc = nil
	file_code_proto_goTypes = nil
	file_code_proto_depIdxs = nil
}
