syntax = "proto3";

package jdstackintegration.constants;

option go_package = "internal/server/conf;conf";

// 定义领域代码，为前后端提供唯一识别码
// 撰写格式：领域唯一代码 = 领域唯一编号; // 注释
// ！！！！！！！！！！！！！！！！！！！！！！！
// ！唯一代码及编号都不允许更改，否则会造成错误！
// ！为避免发生歧义，永远不要把编号曝露给客户端！
// ！后端逻辑一律使用唯一代码，绝对不要使用编号！
// ！！！！！！！！！！！！！！！！！！！！！！！

enum DomainCode {
  All = 0; // 全部（预留，不可删除）
  Common = 1;
  Task = 2;
  User = 3;
  Config = 4;
  DMutexRedis = 11; // 分布式排他锁-Redis/Memory

  Demo = 100;
  DeliveryPlan = 101;
  BaseConfig = 102;
  Region = 103;
  AvailabilityZone = 104;
  Warehouse = 105;
  Rack = 106;
  Server = 107;
  ServerRole = 108;
  ServerTag = 109;
  Network = 110;
  WarehouseMaintainer = 111;
  OssPool = 112;
  TenantBills = 114;

  DeviceGroup = 115;
  DeviceGroupServer = 116;
  DeviceGroupNetwork = 117;

  Vdc = 118;
  KickOsPackage = 119;
  NetworkEquipment = 120;
  NetworkRole = 121;
  ClassicLinkPool = 122;
  PhyDirectConn = 123;
  Aip = 124;
  Dnat = 125;
  ZbsPool = 126;
  Cfs = 127;
  ServerEnv = 128;
  DeliveryUpgrade = 129;
  BasicDeploy = 130;
  NetworkTopology = 131;
  AssetOverview = 132;
  ResourceRole = 133;
  TestCase = 134;
  MonitorScreen = 135;
  Platform = 136;

  GrayTag = 137;

  Disk = 140;
  HardWareRoleMetadata = 141;

  ComputePool = 198;
  Argo = 199;
  NetworkPool = 200;

  // 网络规划占用 201 - 250

  NetworkPlan = 201; // 网络规划
  NetworkPlanIspBGPs = 202; // 网络规划 - ipv4
  NetworkPlanJdIPv6s = 203; // 网络规划 - ipv6
  NetworkPlanInterPriOverlaps = 205; // 网络规划 - paas
  NetworkPlanInats = 206; // 网络规划 - inat
  NetworkPlanLocal = 207; // 网络规划 - 本地网络
  NetworkPlanOthers = 208; // 网络规划 - 其他网络
  NetworkPlanLbs = 209; // 网络规划 - lbs
  NetworkPlanDns = 210; // 网络规划 - dns
  NetworkPlanArk = 211; // 网络规划 - 云翼
  NetworkPlanContainer = 212; // 网络规划 - 容器
  NetworkPlanSdnDRVR = 213; // 网络规划 - dr配置, dr和vr通信使用
  NetworkPlanSdnVRDR = 214; // 网络规划 - vr配置, vr和dr通信使用
  NetworkPlanSdnVRVM = 215; // 网络规划 - vr配置, vr和vm通信使用
  NetworkPlanSdnDRSW = 216; // 网络规划 - dr配置, bgp引流地址,dr和交换机通信
  NetworkPlanBgpAsNumber = 217; // bgpAsNumber
  NetworkPlanSdnVM = 218; // SdnVM - 虚机数据口网段规划
  NetworkPlanStorage = 221; // storage - 存储数据网段规划
  NetworkPlanK8sCalico = 222; // k8sCalico - calico容器网段规划
  NetworkPlanK8sClusterIp = 223; // k8sClusterIp - K8sClusterIp网段规划
  NetworkPlanSdnIPv6DRVR = 224;
  NetworkPlanSdnIPv6VRDR = 225;
  NetworkPlanSdnIPv6VRVM = 226;
  NetworkPlanSdnIPv6DRSW = 227;
  NetworkPlanSdnIPv6DRBGP = 228;
  NetworkPlanSdnVRBGW = 229;
  NetworkPlanSdnVRNATGW = 230;
  NetworkPlanSdnDRNATGW = 231;
  NetworkPlanBgwTunnel = 232;
  NetworkPlanClrTunnel = 233;
  NetworkPlanBGW2DLR = 234;
  NetworkPlanCLR = 235;
  // region级的所有机柜网络规划
  NetworkPlanCompute = 241;
  NetworkPlanVm2vr = 242;
  NetworkPlanVm2vrIPv6 = 243;
  NetworkPlanDr2vr = 244;
  NetworkPlanDr2vrIPv6 = 245;
  NetworkPlanNatgw2vm = 246;
  NetworkPlanVr2bgw = 247;

  NetworkPlanEnd = 250;

  // 计量占用 251 ~ 260
  MeasureTenantVmResource = 251; // 租户资源用量
  MeasureTenantDiskResource = 252; // 租户资源用量
  MeasureTenantEipResource = 253; // 租户资源用量
  MeasureVdcVmResource = 254; // VDC资源用量
  MeasureVdcDiskResource = 255; // VDC资源用量
  MeasureVdcEipResource = 256; // VDC资源用量

  MeasureEnd = 260;
  // 云盘迁移
  DiskMigrate = 261;
  DiskMigrateTask = 262;

  DiskMigrateEnd = 265;

  // 物理服务器纳管 280 ~ 300
  CloudHostingServer = 280; // 物理服务器
  CloudHostingKVData = 281; // kv数据
  CloudHostingEncryption = 282; // 加密
  CloudHostingServerMonitor = 283; // 物理服务器监控数据
  CloudHostingLatestReport = 284; // ifrit master 中latest_report数据

  CloudHostingEnd = 300;

  ArbitrationAz = 320; // 逻辑仲裁区

  Lord = 321; // 云翼Lord

  DeliveryTopology = 322; // 交付拓扑图
}
