package moSubnetMaskI6

import (
	"database/sql/driver"

	"github.com/gogf/gf/v2/util/gconv"

	jConvert "coding.jd.com/pcd-application/win-go/convert"
)

// Kind 集群规模
type Kind int16

func New(val any) *Kind {
	if jConvert.AnyIsString(val) {
		if v := Default.PickCode(gconv.String(val)); v != nil {
			return &v.Kind
		}
	}
	v := Kind(gconv.Int(val))
	return &v
}

// 返回 Kind 用于展示的文字
func (po Kind) String() string {
	v := Default.PickKind(po)
	if v != nil {
		return v.Text
	}
	return Default.PickKind(UnknownKind).Text
}

func (po Kind) Code() string {
	v := Default.PickKind(po)
	if v != nil {
		return v.Code
	}
	return Default.PickKind(UnknownKind).Code
}

func (po Kind) Int32() int32 {
	return int32(po)
}

func (po Kind) Bits() int {
	return int(po)
}

// Scan 从 SQL 类型解析到自定义类型
func (po *Kind) Scan(src any) error {
	if src == nil {
		*po = UnknownKind
		return nil
	}
	switch v := src.(type) { // ent 的数字字段默认类型为 int64
	case []byte:
		*po = Default.PickCode(string(v)).Kind
	case int64:
		*po = Kind(v)
	default:
		*po = UnknownKind
	}
	return nil
}

// Values provides list valid values for Enum.
func (Kind) Values() (kinds []string) {
	for _, s := range Default {
		kinds = append(kinds, s.Code)
	}
	return
}

// Value 校验枚举值是否合法，并返回 SQL 接受的类型
func (po Kind) Value() (driver.Value, error) {
	if Default.PickKind(po) == nil {
		return 0, ErrInvalidParams
	}
	return po.Code(), nil
}

// GetValue 返回 ent 接受的类型
func (po Kind) GetValue() *Kind {
	return &po
}

// GetAble 若为unknown时返回nil
func (po Kind) GetAble() *Kind {
	if po == UnknownKind {
		return nil
	}
	return &po
}

func (po Kind) GetKind() Kind {
	return po
}

// CheckValue 判断值是否合法，以及是否为非 Null 枚举值
func (po Kind) CheckValue() (bool, error) {
	if item := Default.PickKind(po); item == nil {
		return false, ErrInvalidParams
	} else if item.Nullable == false {
		return true, nil
	}
	return false, nil
}
