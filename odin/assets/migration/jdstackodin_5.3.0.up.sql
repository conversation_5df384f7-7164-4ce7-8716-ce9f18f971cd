-- --------------------------------------------------------
-- 主机:                           114.67.241.36
-- 服务器版本:                        10.2.41-MariaDB - MariaDB Server
-- 服务器操作系统:                      Linux
-- HeidiSQL 版本:                  11.2.0.6213
-- --------------------------------------------------------

/*!40101 SET @OLD_CHARACTER_SET_CLIENT = @@CHARACTER_SET_CLIENT */;
/*!40101 SET NAMES utf8 */;
/*!50503 SET NAMES utf8mb4 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS = @@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS = 0 */;
/*!40101 SET @OLD_SQL_MODE = @@SQL_MODE, SQL_MODE = 'NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES = @@SQL_NOTES, SQL_NOTES = 0 */;


CREATE DATABASE IF NOT EXISTS `idc_cm` /*!40100 DEFAULT CHARACTER SET latin1 */;
USE `idc_cm`;

-- 防止存储过程多次创建
DROP PROCEDURE if EXISTS `alter_column`;

DELIMITER $$

CREATE DEFINER=CURRENT_USER PROCEDURE `alter_column`(
    IN tb_name VARCHAR ( 64 ),
    IN col_name VARCHAR ( 64 ),
    IN col_type VARCHAR ( 64 ),
    IN col_len INT,
    IN is_null TINYINT(1),
    IN col_comment VARCHAR ( 64 )	)
BEGIN
    IF NOT EXISTS (SELECT column_name FROM information_schema.columns WHERE TABLE_SCHEMA = "idc_cm" and  table_name = tb_name and column_name = col_name) THEN
        SET @pre_sql = CONCAT("alter table ",tb_name," add ", col_name, " ", col_type);

        IF col_len > 0 THEN
            SET @pre_sql = CONCAT(@pre_sql, "(", col_len ,")");
END IF;

        IF is_null = FALSE THEN
            SET @pre_sql = CONCAT(@pre_sql, " NOT NULL ");
END IF;

        SET @pre_sql = CONCAT(@pre_sql, " COMMENT '", col_comment, "'");

PREPARE stmt from @pre_sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;
END IF;
END$$

DELIMITER ;

CREATE TABLE IF NOT EXISTS `available_zone`
(
    `id`          bigint(20) unsigned NOT NULL AUTO_INCREMENT,
    `uuid`        varchar(128)        NOT NULL,
    `region_uuid` varchar(128)        NOT NULL,
    `name`        varchar(64)         NOT NULL,
    `use_way`     varchar(64)                  DEFAULT NULL,
    `cn_name`     varchar(64)         NOT NULL DEFAULT '' COMMENT '中文名称',
    `description` varchar(128)                 DEFAULT NULL COMMENT '描述',
    `status`      varchar(32)         NOT NULL DEFAULT 'building' COMMENT '状态 building/available',
    `is_default`  varchar(32)         NOT NULL DEFAULT '0' COMMENT '是否为默认az',
    `version`     bigint(20)          NOT NULL DEFAULT 1 COMMENT '用于进行版本控制',
    `created_at`  datetime            NOT NULL DEFAULT current_timestamp(),
    `updated_at`  datetime            NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE KEY `uuid` (`uuid`),
    UNIQUE KEY `regionuuid_azname` (`region_uuid`, `name`)
    ) ENGINE = InnoDB
    DEFAULT CHARSET = utf8mb4
    ROW_FORMAT = DYNAMIC;

CREATE TABLE IF NOT EXISTS `datacenter`
(
    `id`              bigint(20) unsigned NOT NULL AUTO_INCREMENT,
    `uuid`            varchar(128)                 DEFAULT NULL,
    `az_uuid`         varchar(128)                 DEFAULT NULL,
    `region_uuid`     varchar(128)                 DEFAULT NULL,
    `name`            varchar(64)         NOT NULL COMMENT '机房名称',
    `address`         varchar(128)                 DEFAULT NULL COMMENT '机房地址',
    `level`           varchar(64)         NOT NULL COMMENT '机房等级',
    `provider`        varchar(64)         NOT NULL COMMENT '运营商',
    `province`        varchar(64)         NOT NULL COMMENT '省份',
    `city`            varchar(64)         NOT NULL COMMENT '地级市',
    `electrical_spec` varchar(64)         NOT NULL COMMENT '机房的电力规格',
    `room_count`      int(11)                      DEFAULT NULL COMMENT '房间数',
    `version`         bigint(20)          NOT NULL DEFAULT 1 COMMENT '用于进行版本控制',
    `created_at`      datetime            NOT NULL DEFAULT current_timestamp(),
    `updated_at`      datetime            NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE KEY `uuid` (`uuid`),
    UNIQUE KEY `AZ_NAME` (`name`, `region_uuid`),
    KEY `datname` (`name`) USING BTREE
    ) ENGINE = InnoDB
    DEFAULT CHARSET = utf8mb4
    ROW_FORMAT = DYNAMIC;

CREATE TABLE IF NOT EXISTS `eip`
(
    `id`               bigint(20) unsigned NOT NULL AUTO_INCREMENT,
    `uuid`             varchar(128)        NOT NULL,
    `network_segment`  varchar(64)         NOT NULL COMMENT '网段',
    `start_ip_address` varchar(64)         NOT NULL,
    `total_ip_count`   int(11)             NOT NULL COMMENT 'ip总数',
    `description`      varchar(64)                  DEFAULT NULL,
    `version`          bigint(20)          NOT NULL DEFAULT 1 COMMENT '用于进行版本控制',
    `created_at`       datetime            NOT NULL DEFAULT current_timestamp(),
    `updated_at`       datetime            NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE KEY `uuid` (`uuid`)
    ) ENGINE = InnoDB
    DEFAULT CHARSET = utf8mb4
    ROW_FORMAT = DYNAMIC;

CREATE TABLE IF NOT EXISTS `eip_tag`
(
    `id`       int(11)  NOT NULL AUTO_INCREMENT,
    `eip_uuid` char(64) NOT NULL,
    `name`     char(64) NOT NULL,
    `value`    char(64) NOT NULL,
    PRIMARY KEY (`id`),
    UNIQUE KEY `eip_uuid_name` (`eip_uuid`, `name`)
    ) ENGINE = InnoDB
    DEFAULT CHARSET = utf8mb4
    ROW_FORMAT = DYNAMIC;

CREATE TABLE IF NOT EXISTS `eip_used_record`
(
    `id`               bigint(24) unsigned NOT NULL AUTO_INCREMENT,
    `uuid`             varchar(128)        NOT NULL,
    `eip_uuid`         varchar(128)        NOT NULL,
    `start_ip_address` varchar(64)         NOT NULL COMMENT '占用起始地址',
    `count`            int(11)             NOT NULL COMMENT '占用数量',
    `state`            int(11)             NOT NULL COMMENT '0表示未被占用，1表示被占用。默认被占用',
    `reason`           varchar(64)                  DEFAULT NULL COMMENT '占用原因',
    `start_time`       datetime            NOT NULL DEFAULT current_timestamp() COMMENT '占用时间，不能为空',
    `end_time`         datetime                     DEFAULT NULL COMMENT '归还日期。当 进行  ‘归还’操作时，添加归还日期',
    `created_at`       datetime            NOT NULL DEFAULT current_timestamp(),
    `updated_at`       datetime            NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
    `version`          bigint(20)          NOT NULL DEFAULT 1 COMMENT '用于进行版本控制',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE KEY `uuid` (`uuid`)
    ) ENGINE = InnoDB
    DEFAULT CHARSET = utf8mb4
    ROW_FORMAT = DYNAMIC;

# ALTER TABLE eip_used_record ADD ip_address VARCHAR(64) NOT NULL DEFAULT '' COMMENT 'IP地址';
call `alter_column`("eip_used_record","ip_address","VARCHAR",64,1,"IP地址");

# ALTER TABLE eip_used_record ADD res_id VARCHAR(64) DEFAULT NULL COMMENT '占用资源的ID';
call `alter_column`("eip_used_record","res_id","VARCHAR",64,1,"占用资源的ID");

# ALTER TABLE eip_used_record ADD res_type VARCHAR(64) NOT NULL DEFAULT '' COMMENT '占用资源的类型';
call `alter_column`("eip_used_record","res_type","VARCHAR",64,1,"占用资源的类型");
call `alter_column`("eip_used_record","pin","VARCHAR",64,1,"PIN");
call `alter_column`("eip_used_record","region_id","VARCHAR",64,1,"区域");

CREATE TABLE IF NOT EXISTS `maintainer`
(
    `id`         bigint(20) unsigned NOT NULL AUTO_INCREMENT,
    `uuid`       varchar(128)        NOT NULL,
    `name`       varchar(64)         NOT NULL COMMENT '姓名',
    `position`   varchar(64)         NOT NULL COMMENT '职务',
    `phone`      varchar(64)         NOT NULL COMMENT '电话',
    `notes`      varchar(64)                  DEFAULT NULL COMMENT '备注',
    `version`    bigint(20)          NOT NULL DEFAULT 1 COMMENT '用于进行版本控制',
    `created_at` datetime            NOT NULL DEFAULT current_timestamp(),
    `updated_at` datetime            NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE KEY `uuid` (`uuid`)
    ) ENGINE = InnoDB
    DEFAULT CHARSET = utf8mb4
    ROW_FORMAT = DYNAMIC;

CREATE TABLE IF NOT EXISTS `maintainers_datacenter`
(
    `id`              int(20) unsigned NOT NULL AUTO_INCREMENT,
    `uuid`            varchar(128)     NOT NULL,
    `datacenter_uuid` varchar(128)     NOT NULL,
    `maintainer_uuid` varchar(128)     NOT NULL,
    `created_at`      datetime         NOT NULL DEFAULT current_timestamp(),
    `updated_at`      datetime         NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE KEY `uuid` (`uuid`),
    KEY `did_mid` (`datacenter_uuid`, `maintainer_uuid`) USING BTREE
    ) ENGINE = InnoDB
    DEFAULT CHARSET = utf8mb4
    ROW_FORMAT = DYNAMIC;

CREATE TABLE IF NOT EXISTS `network_equipment`
(
    `id`               bigint(20) unsigned NOT NULL AUTO_INCREMENT,
    `uuid`             varchar(128)        NOT NULL,
    `rack_uuid`        varchar(128)        NOT NULL,
    `datacenter_uuid`  varchar(128)        NOT NULL COMMENT '机房uuid',
    `az_uuid`          varchar(128)                 DEFAULT NULL,
    `region_uuid`      varchar(128)                 DEFAULT NULL,
    `asset_number`     varchar(64)         NOT NULL COMMENT '资产编号',
    `serial_number`    varchar(64)         NOT NULL COMMENT '序列号',
    `brand`            varchar(64)                  DEFAULT NULL COMMENT '品牌  DELL/Huawei',
    `model`            varchar(64)                  DEFAULT NULL COMMENT '型号',
    `outofband_ip`     varchar(64)                  DEFAULT NULL COMMENT '带外IP',
    `manage_ip`        varchar(64)                  DEFAULT NULL COMMENT '管理IP',
    `way_to_use`       int(20)             NOT NULL DEFAULT 2 COMMENT '网络设备用途：1网络设备、2未分配，默认未分配,不能为空',
    `asset_state`      int(11)             NOT NULL DEFAULT 3 COMMENT '设备状态：故障1、维修中2、离线3、在线4。默认状态离线，不能为空',
    `u_count`          int(11)             NOT NULL COMMENT '网络设备所占u位',
    `mc_name`          varchar(64)                  DEFAULT NULL COMMENT 'Manufacturer Contact,厂商联系人姓名',
    `mc_phone`         varchar(64)                  DEFAULT NULL COMMENT '厂商联系人电话',
    `mc_email`         varchar(64)                  DEFAULT NULL COMMENT '厂商联系人邮件',
    `version`          bigint(20)          NOT NULL DEFAULT 1 COMMENT '用于进行版本控制',
    `procurement_time` date                NOT NULL COMMENT '采购时间，不能为空',
    `warranty_start`   date                NOT NULL COMMENT '维保开始时间,不能为空',
    `warranty_end`     date                NOT NULL COMMENT '维保结束时间,不能为空',
    `created_at`       datetime            NOT NULL DEFAULT current_timestamp(),
    `updated_at`       datetime            NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE KEY `uuid` (`uuid`),
    UNIQUE KEY `asset_number` (`asset_number`),
    UNIQUE KEY `serial_number` (`serial_number`)
    ) ENGINE = InnoDB
    DEFAULT CHARSET = utf8mb4
    ROW_FORMAT = DYNAMIC;

CREATE TABLE IF NOT EXISTS `os_package`
(
    `id`           bigint(20) unsigned NOT NULL AUTO_INCREMENT,
    `uuid`         varchar(128)        NOT NULL,
    `package_name` varchar(45)         NOT NULL COMMENT '套餐名称',
    `package_desc` varchar(255)                 DEFAULT NULL,
    `version`      bigint(20)          NOT NULL DEFAULT 1 COMMENT '用于进行版本控制',
    `created_at`   datetime            NOT NULL DEFAULT current_timestamp(),
    `updated_at`   datetime            NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE KEY `uuid` (`uuid`),
    UNIQUE KEY `package_name` (`package_name`)
    ) ENGINE = InnoDB
    DEFAULT CHARSET = utf8mb4
    ROW_FORMAT = DYNAMIC;

CREATE TABLE IF NOT EXISTS `rack`
(
    `id`              bigint(20) unsigned NOT NULL AUTO_INCREMENT,
    `uuid`            varchar(128)        NOT NULL,
    `datacenter_uuid` varchar(128)        NOT NULL COMMENT '所属datacenter的uuid',
    `az_uuid`         varchar(128)                 DEFAULT '',
    `region_uuid`     varchar(128)                 DEFAULT '',
    `rack_number`     varchar(64)         NOT NULL COMMENT '机柜编号',
    `electrical_spec` varchar(64)                  DEFAULT '' COMMENT '机柜的电力规格',
    `u_count`         int(11)             NOT NULL DEFAULT 0 COMMENT '总u位数',
    `room_number`     varchar(64)                  DEFAULT '' COMMENT '房间号',
    `network`         varchar(64)                  DEFAULT '' COMMENT '网段',
    `network_mask`    varchar(64)                  DEFAULT '' COMMENT '网段MASK',
    `tor_ip`          varchar(64)                  DEFAULT '' COMMENT '网关IP',
    `version`         bigint(20)          NOT NULL DEFAULT 1 COMMENT '用于进行版本控制',
    `created_at`      datetime            NOT NULL DEFAULT current_timestamp(),
    `updated_at`      datetime            NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE KEY `uuid` (`uuid`),
    UNIQUE KEY `datuuid_rn` (`datacenter_uuid`, `rack_number`),
    KEY `rn` (`rack_number`) USING BTREE
    ) ENGINE = InnoDB
    DEFAULT CHARSET = utf8mb4
    ROW_FORMAT = DYNAMIC;

CREATE TABLE IF NOT EXISTS `region`
(
    `id`          bigint(20) unsigned NOT NULL AUTO_INCREMENT,
    `uuid`        varchar(128)        NOT NULL,
    `name`        varchar(64)         NOT NULL,
    `use_way`     varchar(64)                  DEFAULT NULL,
    `cn_name`     varchar(64)         NOT NULL DEFAULT '' COMMENT '中文名称',
    `description` varchar(128)                 DEFAULT NULL COMMENT '描述',
    `status`      varchar(32)         NOT NULL DEFAULT 'building' COMMENT '状态  building/available',
    `is_default`  varchar(32)         NOT NULL DEFAULT '0' COMMENT '是否为默认region',
    `version`     bigint(20)          NOT NULL DEFAULT 1 COMMENT '用于进行版本控制',
    `created_at`  datetime            NOT NULL DEFAULT current_timestamp(),
    `updated_at`  datetime            NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE KEY `uuid` (`uuid`)
    ) ENGINE = InnoDB
    DEFAULT CHARSET = utf8mb4
    ROW_FORMAT = DYNAMIC;

CREATE TABLE IF NOT EXISTS `server`
(
    `id`                      bigint(20) unsigned NOT NULL AUTO_INCREMENT,
    `uuid`                    varchar(128)        NOT NULL,
    `host_name`               varchar(128)                 DEFAULT NULL COMMENT '主机名',
    `rack_uuid`               varchar(128)        DEFAULT NULL COMMENT '所属rack的uuid',
    `datacenter_uuid`         varchar(128)        DEFAULT NULL COMMENT '机房uuid',
    `az_uuid`                 varchar(128)                 DEFAULT NULL,
    `region_uuid`             varchar(128)                 DEFAULT NULL,
    `asset_number`            varchar(64)         NOT NULL COMMENT '资产编号',
    `serial_number`           varchar(64)         NOT NULL COMMENT '序列号',
    `brand`                   varchar(64)                  DEFAULT NULL COMMENT '品牌  DELL/Huawei',
    `model`                   varchar(64)                  DEFAULT NULL COMMENT '型号',
    `server_package_uuid`     varchar(128)                 DEFAULT NULL COMMENT '页面新增server时必填，填时参照 server_package 表中name，默认 未知\n但是数据库中必须有 ，默认 未知',
    `os_package_uuid`         varchar(128)                 DEFAULT NULL COMMENT '页面新增server时非必填，填时参照 os_package 表中name，默认 未知\n但是数据库中必须有 ，默认 未知',
    `system_ip`               varchar(64)                  DEFAULT NULL COMMENT '系统IP',
    `system_ip_mask`          varchar(64)                  DEFAULT NULL COMMENT '系统IP MASK',
    `system_ip_mac`           varchar(64)                  DEFAULT NULL COMMENT '系统IP MAC',
    `manage_ip`               varchar(64)                  DEFAULT NULL COMMENT '管理IP',
    `data_ip`                 varchar(64)                  DEFAULT NULL COMMENT '数据IP',
    `data_ip_mask`            varchar(64)                  DEFAULT NULL COMMENT '数据IP MASK',
    `smartnic_man_ip`         varchar(64)                  DEFAULT NULL COMMENT 'SmartNIC管理IP',
    `smartnic_man_ip_gateway` varchar(64)                  DEFAULT NULL COMMENT 'SmartNIC管理IP网关',
    `smartnic_data_ip`        varchar(64)                  DEFAULT NULL COMMENT 'SmartNIC数据IP',
    `way_to_use`              int(20)             NOT NULL DEFAULT 4 COMMENT '用途：计算1、存储2、管理3、未分配4,默认未分配',
    `u_count`                 int(11)             NOT NULL DEFAULT 0 COMMENT '服务器 u 位,不能为空',
    `asset_state`             int(20)             NOT NULL COMMENT '设备状态：故障1、维修中2、离线3、在线4、5装机中、,默认为离线状态',
    `mc_name`                 varchar(64)                  DEFAULT NULL COMMENT 'Manufacturer Contact, 厂商联系人姓名',
    `mc_phone`                varchar(64)                  DEFAULT NULL COMMENT '厂商联系人电话',
    `mc_email`                varchar(64)                  DEFAULT NULL COMMENT '厂商联系人邮件',
    `version`                 bigint(20)          NOT NULL DEFAULT 1 COMMENT '用于进行版本控制',
    `procurement_time`        date                NOT NULL COMMENT '采购时间，前端根据当前时间-采购时间计算出 折旧时间,不能为空',
    `warranty_start`          date                NOT NULL COMMENT '维保开始时间',
    `warranty_end`            date                NOT NULL COMMENT '维保结束时间',
    `arbitration_az`          tinyint             NOT NULL DEFAULT '0' COMMENT '标识服务器是否属于逻辑仲裁az. 0 否，1是',
    `server_category`         int                 NOT NULL DEFAULT '1' COMMENT '服务器分类.  1-内部机器，2-外部机器(公有云). 默认值1\n若为外部机器，则一定是逻辑仲裁az的节点',
    `created_at`              datetime            NOT NULL DEFAULT current_timestamp(),
    `updated_at`              datetime            NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE KEY `uuid` (`uuid`),
    UNIQUE KEY `asset_number` (`asset_number`),
    UNIQUE KEY `serial_number` (`serial_number`),
    UNIQUE KEY `system_ip` (`system_ip`)
    ) ENGINE = InnoDB
    DEFAULT CHARSET = utf8mb4
    ROW_FORMAT = DYNAMIC;

CREATE TABLE IF NOT EXISTS `server_package`
(
    `id`                   bigint(20) unsigned NOT NULL AUTO_INCREMENT,
    `uuid`                 varchar(128)        NOT NULL,
    `package_name`         varchar(25)         NOT NULL COMMENT '套餐名称',
    `min_cpu_core_num`     int(10)                      DEFAULT NULL,
    `cpu_model`            varchar(255)                 DEFAULT NULL,
    `min_memory_size`      int(10)                      DEFAULT NULL,
    `disk_num`             int(10)                      DEFAULT NULL,
    `disk_size`            int(10)                      DEFAULT NULL,
    `nic_num`              int(10)                      DEFAULT NULL,
    `nic_model`            varchar(255)                 DEFAULT NULL,
    `package_desc`         varchar(255)                 DEFAULT NULL,
    `server_module_name`   varchar(64)                  DEFAULT NULL,
    `server_module_params` varchar(2048)                DEFAULT NULL,
    `sys_disk_hdd_num`     int(10)                      DEFAULT NULL,
    `sys_disk_hdd_size`    int(10)                      DEFAULT NULL,
    `sys_disk_ssd_num`     int(10)                      DEFAULT NULL,
    `sys_disk_ssd_size`    int(10)                      DEFAULT NULL,
    `sys_disk_raid_type`   varchar(45)                  DEFAULT NULL,
    `data_disk_hdd_num`    int(10)                      DEFAULT NULL,
    `data_disk_hdd_size`   int(10)                      DEFAULT NULL,
    `data_disk_ssd_num`    int(10)                      DEFAULT NULL,
    `data_disk_ssd_size`   int(10)                      DEFAULT NULL,
    `data_disk_raid_type`  varchar(45)                  DEFAULT NULL,
    `data_disk_nvme_num`   int(10)                      DEFAULT NULL,
    `data_disk_nvme_size`  int(10)                      DEFAULT NULL,
    `gpu`                  varchar(64)                  DEFAULT NULL COMMENT 'gpu，可以为空',
    `version`              bigint(20)          NOT NULL DEFAULT 1 COMMENT '用于进行版本控制',
    `created_at`           datetime            NOT NULL DEFAULT current_timestamp(),
    `updated_at`           datetime            NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE KEY `uuid` (`uuid`),
    UNIQUE KEY `package_name` (`package_name`)
    ) ENGINE = InnoDB
    DEFAULT CHARSET = utf8mb4
    ROW_FORMAT = DYNAMIC;

CREATE TABLE IF NOT EXISTS `server_tag`
(
    `id`          int(11)  NOT NULL AUTO_INCREMENT,
    `server_uuid` char(64) NOT NULL DEFAULT '0',
    `name`        char(64) NOT NULL DEFAULT '0',
    `value`       char(64) NOT NULL DEFAULT '0',
    PRIMARY KEY (`id`),
    UNIQUE KEY `server_id_name2` (`server_uuid`, `name`)
    ) ENGINE = InnoDB
    DEFAULT CHARSET = utf8mb4
    ROW_FORMAT = DYNAMIC;

CREATE TABLE IF NOT EXISTS `tag`
(
    `id`         bigint(20) unsigned NOT NULL AUTO_INCREMENT,
    `kind`       int(255)            NOT NULL,
    `ref_id`     bigint(24)          NOT NULL,
    `key`        varchar(64)         NOT NULL COMMENT '资产编号',
    `value`      varchar(64)         NOT NULL COMMENT '机柜编号',
    `created_at` datetime            NOT NULL DEFAULT current_timestamp(),
    `updated_at` datetime            NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
    PRIMARY KEY (`id`) USING BTREE
    ) ENGINE = InnoDB
    DEFAULT CHARSET = utf8mb4
    ROW_FORMAT = DYNAMIC;

CREATE TABLE IF NOT EXISTS `delivery_plan`
(
    `id`          bigint(20) unsigned NOT NULL AUTO_INCREMENT,
    `uuid`        varchar(32)                  DEFAULT NULL,
    `az_name`     varchar(32)                  DEFAULT NULL,
    `region_name` varchar(32)                  DEFAULT NULL,
    `release_id`  varchar(32)                  DEFAULT NULL COMMENT '部署平台版本ID',
    `arch`        varchar(10)                  DEFAULT NULL COMMENT '交付CPU架构：x86_64、arm',
    `quota`       varchar(10)                  DEFAULT NULL COMMENT '交付配额：light、small、medium、large',
    `product`     text                         DEFAULT NULL COMMENT '部署产品名称',
    `description` varchar(128)                 DEFAULT NULL COMMENT '部署描述',
    `state`       tinyint(3)          NOT NULL DEFAULT 1 COMMENT '部署状态：未下发1、已下发2、执行中3、已暫停4、取消中5、取消失败6、取消成功7、执行失败8、执行成功9',
    `version`     bigint(20)          NOT NULL DEFAULT 1 COMMENT '用于进行版本控制',
    `created_at`  datetime            NOT NULL DEFAULT current_timestamp(),
    `updated_at`  datetime            NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
    PRIMARY KEY (`id`) USING BTREE,
    KEY `release_az_region` (`release_id`, `az_name`, `region_name`)
    ) ENGINE = InnoDB
    DEFAULT CHARSET = utf8mb4
    ROW_FORMAT = DYNAMIC;

CREATE TABLE IF NOT EXISTS `network_plan`
(
    `id`             bigint(20)  NOT NULL AUTO_INCREMENT COMMENT '自增主键',
    `ref_id`         varchar(64) NOT NULL COMMENT '全局唯一主键',
    `name`           varchar(64)          DEFAULT NULL COMMENT '名称，可为空',
    `target`         varchar(64) NOT NULL COMMENT '规划的目标，比如ark、dns等',
    `subnet`         varchar(64)          DEFAULT NULL COMMENT '网段',
    `mask`           varchar(64)          DEFAULT NULL COMMENT '掩码',
    `gateway`        varchar(64)          DEFAULT NULL COMMENT '网关',
    `vlan`           bigint(20)           DEFAULT NULL COMMENT 'vlan',
    `region`         varchar(64)          DEFAULT NULL COMMENT 'region',
    `res_id`         varchar(64)          DEFAULT NULL COMMENT '如果是针对某个资源的网络规划，需要设置该字段，比如机架、服务器等',
    `res_sn`         varchar(64)          DEFAULT NULL COMMENT '资源SN号',
    `res_type`       varchar(64)          DEFAULT NULL COMMENT '资源类型',
    `underlay_ip`    varchar(64)          DEFAULT NULL COMMENT 'underlay ip',
    `overlay_ip`     varchar(64)          DEFAULT NULL COMMENT 'overlay ip',
    `total_ip_count` bigint(20)           DEFAULT NULL COMMENT 'total_ip_count',
    `description`    varchar(255)         DEFAULT NULL COMMENT '描述',
    `attribute_1`    varchar(255)         DEFAULT NULL COMMENT '扩展属性1',
    `attribute_2`    varchar(255)         DEFAULT NULL COMMENT '扩展属性2',
    `attribute_3`    varchar(255)         DEFAULT NULL COMMENT '扩展属性3',
    `created_at`     datetime    NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at`     datetime    NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    `version`        bigint(20)  NOT NULL DEFAULT 1 COMMENT '版本',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE KEY `uniq_refid` (`ref_id`)
    ) ENGINE = InnoDB
    DEFAULT CHARSET = utf8mb4
    ROW_FORMAT = DYNAMIC;


CREATE TABLE IF NOT EXISTS `device_conn`
(
    `id`         bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '自增ID',
    `ref_id`     varchar(64)         NOT NULL COMMENT '全局唯一ID',
    `device_id`  varchar(64)         NOT NULL COMMENT '设备ID，网络设备ID或服务器设备ID',
    `conn_type`  varchar(64)         NOT NULL COMMENT '连接方式，ssh、netconf等',
    `port`       bigint(10)          NOT NULL COMMENT '连接端口',
    `user_name`  varchar(255)        NOT NULL COMMENT '登录用户名',
    `password`   varchar(255)                 DEFAULT NULL COMMENT '登录密码，加密',
    `secret_key` varchar(255)                 DEFAULT NULL COMMENT '登录秘钥',
    `created_at` datetime            NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` datetime            NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `version`    bigint(20)          NOT NULL DEFAULT 1 COMMENT '版本',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE KEY `refid` (`ref_id`)
    ) COMMENT ='设备连接表' ENGINE = InnoDB
    DEFAULT CHARSET = utf8mb4
    ROW_FORMAT = DYNAMIC;


CREATE TABLE IF NOT EXISTS `network_slot`
(
    `id`           bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '自增ID',
    `ref_id`       varchar(64)         NOT NULL COMMENT 'UUID',
    `slot_index`   int(12)                      DEFAULT NULL COMMENT 'slot序号',
    `device_name`  varchar(128)                 DEFAULT NULL COMMENT '设备型号',
    `device_sn`    varchar(128)                 DEFAULT NULL COMMENT 'SN号',
    `mac_addr`     varchar(64)                  DEFAULT NULL COMMENT 'Mac地址',
    `equipment_id` varchar(64)                  DEFAULT NULL COMMENT '设备ID',
    `vendor`       varchar(64)                  DEFAULT NULL COMMENT '品牌',
    `group_id`     varchar(128)                 DEFAULT NULL COMMENT '堆叠组ID',
    `created_at`   datetime            NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at`   datetime            NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `version`      bigint(20)          NOT NULL DEFAULT 1 COMMENT '版本',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE KEY `refid` (`ref_id`)
    ) COMMENT ='网络设备表' CHARACTER SET = utf8mb4
    ENGINE = InnoDB
    ROW_FORMAT = DYNAMIC;

call alter_column("network_slot","ip_addr","varchar",64,1,"设备IP");


CREATE TABLE IF NOT EXISTS `network_interface`
(
    `id`         bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '自增ID',
    `ref_id`     varchar(32)         NOT NULL COMMENT 'UUID',
    `port_id`    varchar(64)         NOT NULL COMMENT '对应设备上portId属性',
    `mac`        varchar(64)         NOT NULL COMMENT '端口mac',
    `slot_id`    varchar(32)         NOT NULL COMMENT 'slot表唯一ID',
    `speed`      varchar(32)                  DEFAULT NULL COMMENT '速度',
    `status`     varchar(16)                  DEFAULT NULL COMMENT 'up  | down',
    `created_at` datetime            NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` datetime            NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `version`    bigint(20)          NOT NULL DEFAULT 1 COMMENT '版本',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE KEY `refid` (`ref_id`)
    ) COMMENT ='网络设备端口表' CHARACTER SET = utf8mb4
    ENGINE = InnoDB
    ROW_FORMAT = DYNAMIC;

call alter_column("network_interface","port_name","varchar",255,1,"底层端口name");
call `alter_column`("network_interface","port_index","bigint",20,1,"端口索引");
call alter_column("network_interface","is_logic","bit",1,1,"是否为逻辑口");
call alter_column("network_interface","logic_group_id","bigint",20,1,"逻辑口端口组ID");


CREATE TABLE IF NOT EXISTS `network_lldp`
(
    `id`             bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '自增ID',
    `interface_id`   varchar(32)                  DEFAULT NULL COMMENT '端口唯一ID',
    `peer_id`        varchar(64)                  DEFAULT NULL COMMENT '远端ID,即对端的interface_id，可为空，当对端为server时，为空',
    `peer_type`      varchar(32)                  DEFAULT NULL COMMENT '远端类型，network/server',
    `peer_device_id` varchar(64)                  DEFAULT NULL COMMENT '远端设备ID，即网络设备ID或者服务器ID',
    `created_at`     datetime            NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at`     datetime            NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `version`        bigint(20)          NOT NULL DEFAULT 1 COMMENT '版本',
    PRIMARY KEY (`id`) USING BTREE
    ) COMMENT ='网络设备LLDP表' CHARACTER SET = utf8mb4
    ENGINE = InnoDB
    ROW_FORMAT = DYNAMIC;

CREATE TABLE IF NOT EXISTS `cmdb_op_log`
(
    `id`         bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '自增ID',
    `uuid`       varchar(32)                  DEFAULT NULL COMMENT '唯一ID',
    `res_id`     varchar(64)                  DEFAULT NULL COMMENT '资源ID，比如机柜、机房、服务器等',
    `res_type`   varchar(32)                  DEFAULT NULL COMMENT '资源类型，比如机柜、机房、服务器等',
    `op_user`    varchar(64)                  DEFAULT NULL COMMENT '操作用户',
    `created_at` datetime            NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` datetime            NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `version`    bigint(20)          NOT NULL DEFAULT 1 COMMENT '版本',
    PRIMARY KEY (`id`) USING BTREE
    ) COMMENT ='CMDB操作记录' CHARACTER SET = utf8mb4
    ENGINE = InnoDB
    ROW_FORMAT = DYNAMIC;

CREATE TABLE IF NOT EXISTS `cmdb_op_log_detail`
(
    `id`          bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '自增ID',
    `op_id`       varchar(32)                  DEFAULT NULL COMMENT '操作ID',
    `op_item`     varchar(64)                  DEFAULT NULL COMMENT '具体操作项',
    `data_before` varchar(32)                  DEFAULT NULL COMMENT '操作前的值',
    `data_after`  varchar(64)                  DEFAULT NULL COMMENT '操作后的值',
    `created_at`  datetime            NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at`  datetime            NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `version`     bigint(20)          NOT NULL DEFAULT 1 COMMENT '版本',
    PRIMARY KEY (`id`) USING BTREE
    ) COMMENT ='CMDB操作记录详情' CHARACTER SET = utf8mb4
    ENGINE = InnoDB
    ROW_FORMAT = DYNAMIC;

CREATE TABLE IF NOT EXISTS `server_role_metadata`
(
    `id`             bigint(20)   NOT NULL AUTO_INCREMENT COMMENT '自增id, 主键，聚簇索引',
    `uuid`           varchar(128) NOT NULL COMMENT '服务器uuid',
    `role`           varchar(128) NOT NULL COMMENT '角色',
    `metadata_key`   varchar(255) DEFAULT NULL COMMENT '元数据key',
    `metadata_value` varchar(255) DEFAULT NULL COMMENT '元数据value',
    PRIMARY KEY (`id`) USING BTREE,
    KEY `index1` (`role`) USING BTREE COMMENT '根据角色搜索',
    KEY `index2` (`role`, `metadata_key`, `metadata_value`) USING BTREE COMMENT '联合索引',
    KEY `index3` (`uuid`) USING BTREE
    ) ENGINE = InnoDB
    DEFAULT CHARSET = utf8mb4;

CREATE TABLE IF NOT EXISTS `resource_role_metadata` (
    `release_version` varchar(32) NOT NULL COMMENT 'release_version',
    `category` varchar(32) NOT NULL COMMENT '产品分类',
    `service_code` varchar(32) NOT NULL COMMENT '产品英文名称',
    `resource_code` varchar(32) NOT NULL COMMENT '产品隶属的服务名称',
    `role` varchar(32) NOT NULL COMMENT '角色',
    `metadata_key` varchar(32) DEFAULT NULL COMMENT '元数据key',
    `metadata_value` varchar(32) DEFAULT NULL COMMENT '元数据value',
    `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY `service_code_resource_code_role_metadata_key_metadata_value` (`service_code`,`resource_code`,`role`,`metadata_key`,`metadata_value`,`release_version`) USING BTREE
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='产品服务与角色映射列表'
    DEFAULT CHARSET = utf8mb4;

REPLACE INTO `resource_role_metadata` (`release_version`, `category`, `service_code`, `resource_code`, `role`, `metadata_key`, `metadata_value`, `created_at`, `updated_at`) VALUES
('5.3.0', '基础服务', 'base_mysql', 'base_starDb_master', 'base_starDb', 'role', 'master', '2023-06-02 14:54:14', '2023-10-13 15:05:08'),
('5.3.0', '基础服务', 'base_mysql', 'base_starDb_slave', 'base_starDb', 'role', 'slave', '2023-06-02 14:54:14', '2023-10-13 15:05:08'),
('5.3.0', '基础服务', 'base_mysql', 'base_mysql_master', 'base_mysql', 'role', 'master', '2023-06-02 14:54:14', '2023-10-13 15:05:08'),
('5.3.0', '基础服务', 'base_mysql', 'base_mysql_slave', 'base_mysql', 'role', 'slave', '2023-06-02 14:54:14', '2023-10-13 15:05:08'),
('5.3.0', '基础服务', 'base_pgsql', 'base_pgsql_master', 'base_pgsql', 'role', 'master', '2023-06-02 14:54:14', '2023-10-13 15:05:08'),
('5.3.0', '基础服务', 'base_dns', 'base_dns_master', 'base_dns', 'role', 'master', '2023-06-02 14:54:14', '2023-10-13 15:05:08'),
('5.3.0', '基础服务', 'base_dns', 'base_dns_slave', 'base_dns', 'role', 'slave', '2023-06-02 14:54:14', '2023-10-13 15:05:08'),
('5.3.0', '基础服务', 'base_lb', 'base_lb_master', 'base_lb', 'role', 'master', '2023-06-02 14:54:14', '2023-10-13 15:05:08'),
('5.3.0', '基础服务', 'base_lb', 'base_lb_slave', 'base_lb', 'role', 'slave', '2023-06-02 14:54:14', '2023-10-13 15:05:08'),
('5.3.0', '基础服务', 'base_hub', 'base_hub_master', 'base_hub', 'role', 'master', '2023-06-02 14:54:14', '2023-10-13 15:05:08'),
('5.3.0', '基础服务', 'base_hub', 'base_hub_slave', 'base_hub', 'role', 'slave', '2023-06-02 14:54:14', '2023-10-13 15:05:08'),
('5.3.0', '基础服务', 'base_hub', 'base_hub_tpaas_master', 'base_hub_tpaas', 'role', 'master', '2023-06-02 14:54:14', '2023-10-13 15:05:08'),
('5.3.0', '基础服务', 'base_hub', 'base_hub_tpaas_slave', 'base_hub_tpaas', 'role', 'slave', '2023-06-02 14:54:14', '2023-10-13 15:05:08'),
('5.3.0', '基础服务', 'base_k8s', 'base_k8s_master', 'base_k8s', 'role', 'master', '2023-06-02 14:54:14', '2023-10-13 15:05:08'),
('5.3.0', '基础服务', 'base_k8s', 'base_k8s_node', 'base_k8s', 'role', 'node', '2023-06-02 14:54:14', '2023-10-13 15:05:08'),
('5.3.0', '基础服务', 'base_k8s', 'base_k8s_gray_master', 'base_k8s_gray', 'role', 'master', '2023-06-02 14:54:14', '2023-10-13 15:05:08'),
('5.3.0', '基础服务', 'base_k8s', 'base_k8s_gray_node', 'base_k8s_gray', 'role', 'node', '2023-06-02 14:54:14', '2023-10-13 15:05:08'),
('5.3.0', '基础服务', 'base_k8s', 'base_k8s_expan_node', 'base_k8s_expan', 'role', 'node', '2023-06-02 14:54:14', '2023-10-13 15:05:08'),
('5.3.0', '基础服务', 'cross_az_deploy', 'cross_az_deploy', 'cross_az_deploy', '', '', '2023-06-02 14:54:14', '2023-12-23 04:23:14'),
('5.3.0', '基础服务', 'base_yum', 'base_yum', 'base_yum', '', '', '2023-06-02 14:54:14', '2023-10-13 15:05:08'),
('5.3.0', '基础服务', 'base_ntp', 'base_ntp', 'base_ntp', '', '', '2023-06-02 14:54:14', '2023-10-13 15:05:08'),
('5.3.0', '基础服务', 'base_loader', 'base_loader', 'base_loader', '', '', '2023-06-02 14:54:14', '2023-10-13 15:05:08'),
('5.3.0', '基础服务', 'base_minio', 'base_minio', 'base_minio', '', '', '2023-06-02 14:54:14', '2023-10-13 15:05:08'),
('5.3.0', '基础服务', 'base_tpaas', 'base_tpaas', 'base_tpaas', '', '', '2023-06-02 14:54:14', '2023-10-13 15:05:08'),
('5.3.0', '基础服务', 'base_middleware_es', 'es', '', '', '', '2023-06-02 14:54:14', '2023-06-02 14:54:14'),
('5.3.0', '基础服务', 'base_middleware_etcd', 'base_middleware_etcd', '', '', '', '2023-06-02 14:54:14', '2023-06-02 14:54:14'),
('5.3.0', '基础服务', 'base_middleware_kafka', 'kafka', '', '', '', '2023-06-02 14:54:14', '2023-06-02 14:54:14'),
('5.3.0', '基础服务', 'base_middleware_redis', 'redis', '', '', '', '2023-06-02 14:54:14', '2023-06-02 14:54:14'),
('5.3.0', '基础服务', 'base_middleware_ck', 'ck', '', '', '', '2023-06-02 14:54:14', '2023-06-02 14:54:14'),
('5.3.0', '基础服务', 'base_middleware_zk', 'zk', '', '', '', '2023-06-02 14:54:14', '2023-06-02 14:54:14'),
('5.3.0', '基础服务', 'digger', 'digger', '', '', '', '2023-06-02 14:54:14', '2023-06-02 14:54:14'),
('5.3.0', '基础服务', 'foundation', 'foundation', '', '', '', '2023-06-02 14:54:14', '2023-06-02 14:54:14'),
('5.3.0', '基础服务', 'hawkeye', 'hawkeye', '', '', '', '2023-06-02 14:54:14', '2023-06-02 14:54:14'),
('5.3.0', '基础服务', 'jdccase', 'jdccase', '', '', '', '2023-06-02 14:54:14', '2023-06-02 14:54:14'),
('5.3.0', '基础服务', 'jdd_opm', 'jdd_opm', '', '', '', '2023-06-02 14:54:14', '2023-06-02 14:54:14'),
('5.3.0', '基础服务', 'logstore', 'logstore', '', '', '', '2023-06-02 14:54:14', '2023-06-02 14:54:14'),
('5.3.0', '基础服务', 'mate', 'mate', 'mate', '', '', '2023-06-02 14:54:14', '2023-06-02 14:54:14'),
('5.3.0', '基础服务', 'mate', 'mate_console', '', '', '', '2023-06-02 14:54:14', '2023-06-02 14:54:14'),
('5.3.0', '基础服务', 'mate', 'mate_daemon_agent', 'mate_daemon_agent', '', '', '2023-06-02 14:54:14', '2023-06-19 18:01:55'),
('5.3.0', '基础服务', 'matev2', 'matev2', 'mate', '', '', '2023-06-02 14:54:14', '2023-06-02 14:54:14'),
('5.3.0', '基础服务', 'matev2', 'mate_consolev2', '', '', '', '2023-06-02 14:54:14', '2023-06-02 14:54:14'),
('5.3.0', '基础服务', 'sgm', 'sgm', '', '', '', '2023-06-02 14:54:14', '2023-06-02 14:54:14'),
('5.3.0', '基础服务', 'skywing', 'skywing', 'skywing', '', '', '2023-06-02 14:54:14', '2023-06-02 14:54:14'),
('5.3.0', '基础服务', 'skywing', 'skywing_captain', '', '', '', '2023-06-02 14:54:14', '2023-06-02 14:54:14'),
('5.3.0', '商业平台', 'account', 'account_console', '', '', '', '2023-06-02 14:54:14', '2023-06-02 14:54:14'),
('5.3.0', '商业平台', 'account', 'account_controlplane', '', '', '', '2023-06-02 14:54:14', '2023-06-02 14:54:14'),
('5.3.0', '商业平台', 'audittrail', 'audittrail_console', '', '', '', '2023-06-02 14:54:14', '2023-06-02 14:54:14'),
('5.3.0', '商业平台', 'audittrail', 'audittrail_controlplane', '', '', '', '2023-06-02 14:54:14', '2023-06-02 14:54:14'),
('5.3.0', '商业平台', 'back_manage', 'back_manage', '', '', '', '2023-06-02 14:54:14', '2023-06-02 14:54:14'),
('5.3.0', '商业平台', 'back_manage', 'jdcloud_back', '', '', '', '2023-06-02 14:54:14', '2023-06-02 14:54:14'),
('5.3.0', '商业平台', 'billing', 'billing_console', '', '', '', '2023-06-02 14:54:14', '2023-06-02 14:54:14'),
('5.3.0', '商业平台', 'billing', 'billing_controlplane', '', '', '', '2023-06-02 14:54:14', '2023-06-02 14:54:14'),
('5.3.0', '商业平台', 'cloud_partner', 'partner_console', '', '', '', '2023-06-02 14:54:14', '2023-06-02 14:54:14'),
('5.3.0', '商业平台', 'cloud_partner', 'partner_controlplane', '', '', '', '2023-06-02 14:54:14', '2023-06-02 14:54:14'),
('5.3.0', '商业平台', 'console', 'console_fe', '', '', '', '2023-06-02 14:54:14', '2023-06-02 14:54:14'),
('5.3.0', '商业平台', 'console', 'cost_fe', '', '', '', '2023-06-02 14:54:14', '2023-06-02 14:54:14'),
('5.3.0', '商业平台', 'console', 'message_fe', '', '', '', '2023-06-02 14:54:14', '2023-06-02 14:54:14'),
('5.3.0', '商业平台', 'console', 'order_fe', '', '', '', '2023-06-02 14:54:14', '2023-06-02 14:54:14'),
('5.3.0', '商业平台', 'console', 'payment_fe', '', '', '', '2023-06-02 14:54:14', '2023-06-02 14:54:14'),
('5.3.0', '商业平台', 'console', 'resource_group_fe', '', '', '', '2023-06-02 14:54:14', '2023-06-02 14:54:14'),
('5.3.0', '商业平台', 'coupon', 'coupon_console', '', '', '', '2023-06-02 14:54:14', '2023-06-02 14:54:14'),
('5.3.0', '商业平台', 'coupon', 'coupon_controlplane', '', '', '', '2023-06-02 14:54:14', '2023-06-02 14:54:14'),
('5.3.0', '商业平台', 'discount', 'discount_console', '', '', '', '2023-06-02 14:54:14', '2023-06-02 14:54:14'),
('5.3.0', '商业平台', 'discount', 'discount_controlplane', '', '', '', '2023-06-02 14:54:14', '2023-06-02 14:54:14'),
('5.3.0', '商业平台', 'iam', 'iam_console', '', '', '', '2023-06-02 14:54:14', '2023-06-02 14:54:14'),
('5.3.0', '商业平台', 'iam', 'iam_controlplane', '', '', '', '2023-06-02 14:54:14', '2023-06-02 14:54:14'),
('5.3.0', '商业平台', 'jelement_biz', 'jelement_biz', '', '', '', '2023-06-02 14:54:14', '2023-06-02 14:54:14'),
('5.3.0', '商业平台', 'menu', 'menu_console', '', '', '', '2023-06-02 14:54:14', '2023-06-02 14:54:14'),
('5.3.0', '商业平台', 'menu', 'menu_controlplane', '', '', '', '2023-06-02 14:54:14', '2023-06-02 14:54:14'),
('5.3.0', '商业平台', 'message', 'message_controlplane', '', '', '', '2023-06-02 14:54:14', '2023-06-02 14:54:14'),
('5.3.0', '商业平台', 'oauth2', 'oauth2_console', '', '', '', '2023-06-02 14:54:14', '2023-06-02 14:54:14'),
('5.3.0', '商业平台', 'oauth2', 'oauth2_controlplane', '', '', '', '2023-06-02 14:54:14', '2023-06-02 14:54:14'),
('5.3.0', '商业平台', 'op_back', 'op_back_console', '', '', '', '2023-06-02 14:54:14', '2023-06-02 14:54:14'),
('5.3.0', '商业平台', 'op_back', 'op_back_controlplane', '', '', '', '2023-06-02 14:54:14', '2023-06-02 14:54:14'),
('5.3.0', '商业平台', 'order', 'order_console', '', '', '', '2023-06-02 14:54:14', '2023-06-02 14:54:14'),
('5.3.0', '商业平台', 'order', 'order_controlplane', '', '', '', '2023-06-02 14:54:14', '2023-06-02 14:54:14'),
('5.3.0', '商业平台', 'organization', 'org_back', '', '', '', '2023-06-02 14:54:14', '2023-06-02 14:54:14'),
('5.3.0', '商业平台', 'organization', 'org_console', '', '', '', '2023-06-02 14:54:14', '2023-06-02 14:54:14'),
('5.3.0', '商业平台', 'organization', 'org_controlplane', '', '', '', '2023-06-02 14:54:14', '2023-06-02 14:54:14'),
('5.3.0', '商业平台', 'quota', 'quota_console', '', '', '', '2023-06-02 14:54:14', '2023-06-02 14:54:14'),
('5.3.0', '商业平台', 'quota', 'quota_controlplane', '', '', '', '2023-06-02 14:54:14', '2023-06-02 14:54:14'),
('5.3.0', '商业平台', 'resources', 'resources_controlplane', '', '', '', '2023-06-02 14:54:14', '2023-06-02 14:54:14'),
('5.3.0', '商业平台', 'resource_center', 'resource_center_console', '', '', '', '2023-06-02 14:54:14', '2023-06-02 14:54:14'),
('5.3.0', '商业平台', 'resource_center', 'resource_center_controlplane', '', '', '', '2023-06-02 14:54:14', '2023-06-02 14:54:14'),
('5.3.0', '商业平台', 'resource_tag', 'resource_tag_console', '', '', '', '2023-06-02 14:54:14', '2023-06-02 14:54:14'),
('5.3.0', '商业平台', 'resource_tag', 'resource_tag_openapi', '', '', '', '2023-06-02 14:54:14', '2023-06-02 14:54:14'),
('5.3.0', '商业平台', 'usercenter', 'usercenter_console', '', '', '', '2023-06-02 14:54:14', '2023-06-02 14:54:14'),
('5.3.0', '商业平台', 'usercenter', 'usercenter_controlplane', '', '', '', '2023-06-02 14:54:14', '2023-06-02 14:54:14'),
('5.3.0', 'iaas控制台', 'iaas_console', 'cns_console', '', '', '', '2023-06-02 14:54:14', '2023-06-02 14:54:14'),
('5.3.0', 'iaas控制台', 'iaas_console', 'iaas_back', '', '', '', '2023-06-02 14:54:14', '2023-06-02 14:54:14'),
('5.3.0', 'iaas控制台', 'iaas_console', 'iaas_console', '', '', '', '2023-06-02 14:54:14', '2023-06-02 14:54:14'),
('5.3.0', 'iaas控制台', 'iaas_console', 'iaas_ops_back', '', '', '', '2023-06-02 14:54:14', '2023-06-02 14:54:14'),
('5.3.0', 'iaas控制台', 'iaas_console', 'network_console', '', '', '', '2023-06-02 14:54:14', '2023-06-02 14:54:14'),
('5.3.0', 'iaas控制台', 'iaas_console', 'vm_console', '', '', '', '2023-06-02 14:54:14', '2023-06-02 14:54:14'),
('5.3.0', '弹性计算', 'ag', 'ag_controlplane', 'iaas_controlplane', '', '', '2023-06-02 14:54:14', '2023-06-02 14:54:14'),
('5.3.0', '弹性计算', 'ag', 'ag_openapi', '', '', '', '2023-06-02 14:54:14', '2023-06-02 14:54:14'),
('5.3.0', '弹性计算', 'autoscaling', 'autoscaling', '', '', '', '2023-06-02 14:54:14', '2023-06-02 14:54:14'),
('5.3.0', '弹性计算', 'jdcloud_cost_optimization', 'jdcloud_cost_optimization', '', '', '', '2023-06-02 14:54:14', '2023-06-02 14:54:14'),
('5.3.0', '弹性计算', 'jvirt_cluster', 'jvirt_cluster_controlplane', 'iaas_controlplane', '', '', '2023-06-02 14:54:14', '2023-06-02 14:54:14'),
('5.3.0', '弹性计算', 'jvirt_dh', 'jcs_dh', 'iaas_controlplane', '', '', '2023-06-02 14:54:14', '2023-06-02 14:54:14'),
('5.3.0', '弹性计算', 'jvirt_rms', 'jvirt_rms_controlplane', 'iaas_controlplane', '', '', '2023-06-02 14:54:14', '2023-06-02 14:54:14'),
('5.3.0', '弹性计算', 'jvirt_service_health', 'jvirt_service_health', 'iaas_controlplane', '', '', '2023-06-02 14:54:14', '2023-06-02 14:54:14'),
('5.3.0', '弹性计算', 'jvirt_sli', 'jvirt_failover', '', '', '', '2023-06-02 14:54:14', '2023-06-02 14:54:14'),
('5.3.0', '弹性计算', 'jvirt_sli', 'jvirt_sli', 'iaas_controlplane', '', '', '2023-06-02 14:54:14', '2023-06-02 14:54:14'),
('5.3.0', '弹性计算', 'jvirt_ops', 'jvirt_ops', '', '', '', '2023-06-02 14:54:14', '2023-07-19 16:56:42'),
('5.3.0', '弹性计算', 'nc', 'nc_controlplane', 'iaas_controlplane', '', '', '2023-06-02 14:54:14', '2023-06-02 14:54:14'),
('5.3.0', '弹性计算', 'nc', 'nc_dataplane', '', '', '', '2023-06-02 14:54:14', '2023-06-02 14:54:14'),
('5.3.0', '弹性计算', 'nc', 'nc_openapi', '', '', '', '2023-06-02 14:54:14', '2023-06-02 14:54:14'),
('5.3.0', '弹性计算', 'vm', 'jcs_autotask_policy', '', '', '', '2023-06-02 14:54:14', '2023-06-02 14:54:14'),
('5.3.0', '弹性计算', 'vm', 'jcs_baremetal', 'iaas_controlplane', '', '', '2023-06-02 14:54:14', '2023-06-02 14:54:14'),
('5.3.0', '弹性计算', 'vm', 'jcs_cap', '', '', '', '2023-06-02 14:54:14', '2023-06-02 14:54:14'),
('5.3.0', '弹性计算', 'vm', 'jcs_controlplane', 'iaas_controlplane', '', '', '2023-06-02 14:54:14', '2023-06-02 14:54:14'),
('5.3.0', '弹性计算', 'vm', 'jcs_image', 'iaas_controlplane', '', '', '2023-06-02 14:54:14', '2023-06-02 14:54:14'),
('5.3.0', '弹性计算', 'vm', 'jcs_image_task', 'jcs_image', '', '', '2023-06-02 14:54:14', '2023-06-02 14:54:14'),
('5.3.0', '弹性计算', 'vm', 'jcs_openapi', '', '', '', '2023-06-02 14:54:14', '2023-06-02 14:54:14'),
('5.3.0', '弹性计算', 'vm', 'vm_dataplane', 'jcs_dataplane', '', '', '2023-06-02 14:54:14', '2023-06-19 19:07:33'),
('5.3.0', '弹性计算', 'web_terminal', 'web_terminal', '', '', '', '2023-06-02 14:54:14', '2023-06-02 14:54:14'),
('5.3.0', '存储', 'cfs', 'cfs_controlplane', 'cfs_controlplane', '', '', '2023-06-02 14:54:14', '2023-06-02 14:54:14'),
('5.3.0', '存储', 'cfs', 'cfs_global_api', 'cfs_global_api', '', '', '2023-06-02 14:54:14', '2023-06-14 23:18:58'),
('5.3.0', '存储', 'cfs', 'cfs_openapi', '', '', '', '2023-06-02 14:54:14', '2023-06-02 14:54:14'),
('5.3.0', '存储', 'oss', 'oss_cap', '', '', '', '2023-06-02 14:54:14', '2023-06-02 14:54:14'),
('5.3.0', '存储', 'oss', 'oss_app_set', '', '', '', '2023-06-02 14:54:14', '2023-06-02 14:54:14'),
('5.3.0', '存储', 'oss', 'oss_console', '', '', '', '2023-06-02 14:54:14', '2023-06-02 14:54:14'),
('5.3.0', '存储', 'oss', 'oss_controlplane', '', '', '', '2023-06-02 14:54:14', '2023-06-02 14:54:14'),
('5.3.0', '存储', 'oss', 'oss_crr', '', '', '', '2023-06-02 14:54:14', '2023-06-02 14:54:14'),
('5.3.0', '存储', 'oss_data_service', 'oss_datacap', 'oss_datacap', '', '', '2023-06-02 14:54:14', '2023-06-02 14:54:14'),
('5.3.0', '存储', 'oss', 'oss_dataplane', 'oss_dataplane', '', '', '2023-06-02 14:54:14', '2023-06-02 14:54:14'),
('5.3.0', '存储', 'oss', 'oss_meta', '', '', '', '2023-06-02 14:54:14', '2023-06-02 14:54:14'),
('5.3.0', '存储', 'oss', 'oss_pause', 'oss_ds_datanode', '', '', '2023-06-02 14:54:14', '2023-06-02 14:54:14'),
('5.3.0', '存储', 'zbs', 'zbs_controlplane', 'zbs_controlplane', '', '', '2023-06-02 14:54:14', '2023-06-02 14:54:14'),
('5.3.0', '存储', 'zbs', 'zbs_refresh_control', 'zbs_controlplane', '', '', '2023-06-02 14:54:14', '2023-06-02 14:54:14'),
('5.3.0', '存储', 'zbs', 'zbs_dataplane', 'zbs_pool', '', '', '2023-06-02 14:54:14', '2023-06-19 19:01:23'),
('5.3.0', '存储', 'zbs', 'zbs_openapi', '', '', '', '2023-06-02 14:54:14', '2023-06-02 14:54:14'),
('5.3.0', '统一运维平台', 'event_system', 'event_system', '', '', '', '2023-06-02 14:54:14', '2023-06-02 14:54:14'),
('5.3.0', '统一运维平台', 'hardware', 'hw_engine', '', '', '', '2023-06-02 14:54:14', '2023-06-02 14:54:14'),
('5.3.0', '统一运维平台', 'jdro', 'jdro', '', '', '', '2023-06-02 14:54:14', '2023-06-02 14:54:14'),
('5.3.0', '统一运维平台', 'jdstack_ops', 'jdstack_argo_controller', '', '', '', '2023-06-02 14:54:14', '2023-06-02 14:54:14'),
('5.3.0', '统一运维平台', 'jdstack_ops', 'jdstack_cmc', '', '', '', '2023-06-02 14:54:14', '2023-06-02 14:54:14'),
('5.3.0', '统一运维平台', 'jdstack_ops', 'jdstack_ops_zeus', '', '', '', '2023-06-02 14:54:14', '2023-06-02 14:54:14'),
('5.3.0', '统一运维平台', 'jdstack_ops', 'jdstack_dms', '', '', '', '2023-06-02 14:54:14', '2023-06-02 14:54:14'),
('5.3.0', '统一运维平台', 'jdstack_ops', 'jdstack_filecenter', '', '', '', '2023-06-02 14:54:14', '2023-06-02 14:54:14'),
('5.3.0', '统一运维平台', 'jdstack_ops', 'jdstack_kickstart', '', '', '', '2023-06-02 14:54:14', '2023-06-02 14:54:14'),
('5.3.0', '统一运维平台', 'jdstack_ops', 'jdstack_ops_brolly', '', '', '', '2023-06-02 14:54:14', '2023-06-02 14:54:14'),
('5.3.0', '统一运维平台', 'jdstack_ops', 'jdstack_ops_console', '', '', '', '2023-06-02 14:54:14', '2023-06-02 14:54:14'),
('5.3.0', '统一运维平台', 'jdstack_ops', 'jdstack_ops_odin', '', '', '', '2023-06-02 14:54:14', '2023-06-02 14:54:14'),
('5.3.0', '统一运维平台', 'jdstack_ops', 'jdstack_ops_openapi', '', '', '', '2023-06-02 14:54:14', '2023-06-02 14:54:14'),
('5.3.0', '统一运维平台', 'jdstack_ops', 'jdstack_ops_res_stat', '', '', '', '2023-06-02 14:54:14', '2023-06-02 14:54:14'),
('5.3.0', '统一运维平台', 'jdstack_ops', 'jdts_deploy', '', '', '', '2023-06-02 14:54:14', '2023-06-02 14:54:14'),
('5.3.0', '统一运维平台', 'jdstack_ops', 'oss_dn_ctrl', '', '', '', '2023-06-02 14:54:14', '2023-06-02 14:54:14'),
('5.3.0', '统一运维平台', 'jdstack_ops', 'jdstack_peak', '', '', '', '2023-06-02 14:54:14', '2023-06-02 14:54:14'),
('5.3.0', '统一运维平台', 'magicflow', 'magicflow', '', '', '', '2023-06-02 14:54:14', '2023-06-02 14:54:14'),
('5.3.0', '安全', 'bastion', 'bastion_console', '', '', '', '2023-06-02 14:54:14', '2023-06-02 14:54:14'),
('5.3.0', '安全', 'bastion', 'bastion_controlplane', '', '', '', '2023-06-02 14:54:14', '2023-06-02 14:54:14'),
('5.3.0', '安全', 'chaos', 'chaos', 'chaos', '', '', '2023-06-02 14:54:14', '2023-06-02 14:54:14'),
('5.3.0', '安全', 'chaosman', 'chaosman', 'chaosman', '', '', '2023-06-02 14:54:14', '2023-06-02 14:54:14'),
('5.3.0', '安全', 'csa', 'csa_cap', '', '', '', '2023-06-02 14:54:14', '2023-06-02 14:54:14'),
('5.3.0', '安全', 'csa', 'csa_console', '', '', '', '2023-06-02 14:54:14', '2023-06-02 14:54:14'),
('5.3.0', '安全', 'csa', 'csa_controlplane', '', '', '', '2023-06-02 14:54:14', '2023-06-02 14:54:14'),
('5.3.0', '安全', 'csa', 'csa_ids', 'csa_ids', '', '', '2023-06-02 14:54:14', '2023-06-02 14:54:14'),
('5.3.0', '安全', 'csa', 'csa_openapi', '', '', '', '2023-06-02 14:54:14', '2023-06-02 14:54:14'),
('5.3.0', '安全', 'dcap', 'dcap_cap', '', '', '', '2023-06-02 14:54:14', '2023-06-02 14:54:14'),
('5.3.0', '安全', 'dcap', 'dcap_console', '', '', '', '2023-06-02 14:54:14', '2023-06-02 14:54:14'),
('5.3.0', '安全', 'hips', 'hips_cap', '', '', '', '2023-06-02 14:54:14', '2023-06-02 14:54:14'),
('5.3.0', '安全', 'hips', 'hips_console', '', '', '', '2023-06-02 14:54:14', '2023-06-02 14:54:14'),
('5.3.0', '安全', 'hips', 'hips_controlplane', '', '', '', '2023-06-02 14:54:14', '2023-06-02 14:54:14'),
('5.3.0', '安全', 'jav', 'jav_controlplane', '', '', '', '2023-06-02 14:54:14', '2023-06-02 14:54:14'),
('5.3.0', '安全', 'jsec_dbaudit', 'dbaudit_console', '', '', '', '2023-06-02 14:54:14', '2023-06-02 14:54:14'),
('5.3.0', '安全', 'jsec_dbaudit', 'dbaudit_openapi', '', '', '', '2023-06-02 14:54:14', '2023-06-02 14:54:14'),
('5.3.0', '安全', 'kms', 'kms_cap', '', '', '', '2023-06-02 14:54:14', '2023-06-02 14:54:14'),
('5.3.0', '安全', 'kms', 'kms_console', '', '', '', '2023-06-02 14:54:14', '2023-06-02 14:54:14'),
('5.3.0', '安全', 'kms_server', 'kms_server', '', '', '', '2023-06-02 14:54:14', '2023-06-02 14:54:14'),
('5.3.0', '安全', 'nf1', 'nf1_gtc', '', '', '', '2023-06-02 14:54:14', '2023-06-02 14:54:14'),
('5.3.0', '安全', 'nf1', 'nf1_ltc', '', '', '', '2023-06-02 14:54:14', '2023-06-02 14:54:14'),
('5.3.0', '安全', 'nf1', 'nf1_tp_clean', 'tp_clean', '', '', '2023-06-02 14:54:14', '2023-06-02 14:54:14'),
('5.3.0', '安全', 'nf1', 'nf1_tp_detect', 'tp_detect', '', '', '2023-06-02 14:54:14', '2023-06-02 14:54:14'),
('5.3.0', '安全', 'privatezone', 'privatezone_cap', '', '', '', '2023-06-02 14:54:14', '2023-06-02 14:54:14'),
('5.3.0', '安全', 'privatezone', 'privatezone_console', '', '', '', '2023-06-02 14:54:14', '2023-06-02 14:54:14'),
('5.3.0', '安全', 'privatezone', 'privatezone_controlplane', '', '', '', '2023-06-02 14:54:14', '2023-06-02 14:54:14'),
('5.3.0', '安全', 'privatezone', 'privatezone_dataplane_adns', 'adns', '', '', '2023-06-02 14:54:14', '2023-06-02 14:54:14'),
('5.3.0', '安全', 'privatezone', 'privatezone_dataplane_pdns', 'pdns', '', '', '2023-06-02 14:54:14', '2023-06-02 14:54:14'),
('5.3.0', '安全', 'sgw', 'sgw_cap', '', '', '', '2023-06-02 14:54:14', '2023-06-02 14:54:14'),
('5.3.0', '安全', 'sgw', 'sgw_console', '', '', '', '2023-06-02 14:54:14', '2023-06-02 14:54:14'),
('5.3.0', '安全', 'sgw', 'sgw_controlplane', '', '', '', '2023-06-02 14:54:14', '2023-06-02 14:54:14'),
('5.3.0', '安全', 'sgw', 'sgw_deeplog', '', '', '', '2023-06-02 14:54:14', '2023-06-02 14:54:14'),
('5.3.0', '安全', 'ssl', 'ssl_cap', '', '', '', '2023-06-02 14:54:14', '2023-06-02 14:54:14'),
('5.3.0', '安全', 'ssl', 'ssl_console', '', '', '', '2023-06-02 14:54:14', '2023-06-02 14:54:14'),
('5.3.0', '安全', 'status', 'status_front_new', '', '', '', '2023-06-02 14:54:14', '2023-06-02 14:54:14'),
('5.3.0', '安全', 'status', 'status_job', '', '', '', '2023-06-02 14:54:14', '2023-06-02 14:54:14'),
('5.3.0', '安全', 'status', 'status_new', '', '', '', '2023-06-02 14:54:14', '2023-06-02 14:54:14'),
('5.3.0', '安全', 'threatscanner', 'threatscanner_cap', '', '', '', '2023-06-02 14:54:14', '2023-06-02 14:54:14'),
('5.3.0', '安全', 'threatscanner', 'threatscanner_console', '', '', '', '2023-06-02 14:54:14', '2023-06-02 14:54:14'),
('5.3.0', '安全', 'threatscanner', 'threatscanner_controlplane', '', '', '', '2023-06-02 14:54:14', '2023-06-02 14:54:14'),
('5.3.0', '安全', 'threatscanner', 'threatscanner_dataplane', '', '', '', '2023-06-02 14:54:14', '2023-06-02 14:54:14'),
('5.3.0', '安全', 'threatscanner', 'threatscanner_openapi', '', '', '', '2023-06-02 14:54:14', '2023-06-02 14:54:14'),
('5.3.0', '原生', 'apigateway', 'apigateway_cap', '', '', '', '2023-06-02 14:54:14', '2023-06-02 14:54:14'),
('5.3.0', '原生', 'apigateway', 'apigateway_console', '', '', '', '2023-06-02 14:54:14', '2023-06-02 14:54:14'),
('5.3.0', '原生', 'apigateway', 'apigateway_controlplane', '', '', '', '2023-06-02 14:54:14', '2023-06-02 14:54:14'),
('5.3.0', '原生', 'apigateway', 'apigateway_gateway', '', '', '', '2023-06-02 14:54:14', '2023-06-02 14:54:14'),
('5.3.0', '原生', 'containerregistry', 'containerregistry_controlplane', 'iaas_controlplane', '', '', '2023-06-02 14:54:14', '2023-06-02 14:54:14'),
('5.3.0', '原生', 'containerregistry', 'containerregistry_dataplane', 'iaas_controlplane', '', '', '2023-06-02 14:54:14', '2023-06-02 14:54:14'),
('5.3.0', '原生', 'containerregistry', 'containerregistry_openapi', 'iaas_controlplane', '', '', '2023-06-02 14:54:14', '2023-06-02 14:54:14'),
('5.3.0', '原生', 'jvessel', 'jvessel_controlplane', '', '', '', '2023-06-02 14:54:14', '2023-06-02 14:54:14'),
('5.3.0', '原生', 'jvessel', 'jvessel_middleware', '', '', '', '2023-06-02 14:54:14', '2023-06-02 14:54:14'),
('5.3.0', '原生', 'jvessel', 'jvessel_openapi', '', '', '', '2023-06-02 14:54:14', '2023-06-02 14:54:14'),
('5.3.0', '原生', 'jvessel2', 'jvessel2_console', '', '', '', '2023-06-02 14:54:14', '2023-06-02 14:54:14'),
('5.3.0', '原生', 'jvessel2', 'jvessel2_controlplane', '', '', '', '2023-06-02 14:54:14', '2023-06-02 14:54:14'),
('5.3.0', '原生', 'jvessel2', 'jvessel2_logs', '', '', '', '2023-06-02 14:54:14', '2023-06-02 14:54:14'),
('5.3.0', '原生', 'jvessel2', 'jvessel2_openapi', '', '', '', '2023-06-02 14:54:14', '2023-06-02 14:54:14'),
('5.3.0', '原生', 'jvessel2', 'jvessel2_repository', '', '', '', '2023-06-02 14:54:14', '2023-06-02 14:54:14'),
('5.3.0', '原生', 'kubernetes', 'kubernetes_console', '', '', '', '2023-06-02 14:54:14', '2023-06-02 14:54:14'),
('5.3.0', '原生', 'kubernetes', 'kubernetes_controlplane', 'iaas_controlplane', '', '', '2023-06-02 14:54:14', '2023-06-02 14:54:14'),
('5.3.0', '原生', 'kubernetes', 'kubernetes_openapi', '', '', '', '2023-06-02 14:54:14', '2023-06-02 14:54:14'),
('5.3.0', '原生', 'openapigw', 'openapigw_controlplane', '', '', '', '2023-06-02 14:54:14', '2023-06-02 14:54:14'),
('5.3.0', '原生', 'openapigw', 'openapigw_openapi', '', '', '', '2023-06-02 14:54:14', '2023-06-02 14:54:14'),
('5.3.0', '原生', 'openapigw', 'openapigw_console', '', '', '', '2023-06-02 14:54:14', '2023-06-02 14:54:14'),
('5.3.0', '企业服务部', 'jcloud_portal', 'jcloud_portal', '', '', '', '2023-06-02 14:54:14', '2023-06-02 14:54:14'),
('5.3.0', '企业服务部', 'jcloud_portal', 'jcloud_portal_console', '', '', '', '2023-06-02 14:54:14', '2023-06-02 14:54:14'),
('5.3.0', '云防火墙', 'cfw', 'cfw_console', '', '', '', '2023-06-02 14:54:14', '2023-06-02 14:54:14'),
('5.3.0', '云防火墙', 'cfw', 'cfw_cpa', '', '', '', '2023-06-02 14:54:14', '2023-06-02 14:54:14'),
('5.3.0', '云物理服务器纳管', 'jdstack_cloud_hosting', 'pcd_cloud_hosting', '', '', '', '2023-06-02 14:54:14', '2023-06-02 14:54:14'),
('5.3.0', '云物理服务器纳管', 'jdstack_cloud_hosting', 'pcd_cloud_hosting_console', '', '', '', '2023-06-02 14:54:14', '2023-06-02 14:54:14'),
('5.3.0', '云物理服务器', 'cps', 'cps_console', '', '', '', '2023-06-02 14:54:14', '2023-06-02 14:54:14'),
('5.3.0', '云物理服务器', 'cps', 'cps_controlplane', '', '', '', '2023-06-02 14:54:14', '2023-06-02 14:54:14'),
('5.3.0', '云物理服务器', 'cps', 'cps_dataplane', '', '', '', '2023-06-02 14:54:14', '2023-06-02 14:54:14'),
('5.3.0', '云物理服务器', 'cps', 'cps_monitor', '', '', '', '2023-06-02 14:54:14', '2023-06-02 14:54:14'),
('5.3.0', '云物理服务器', 'cps', 'cps_mq', '', '', '', '2023-06-02 14:54:14', '2023-06-02 14:54:14'),
('5.3.0', '云物理服务器', 'cps', 'cps_openapi', '', '', '', '2023-06-02 14:54:14', '2023-06-02 14:54:14'),
('5.3.0', '云物理服务器', 'cps', 'cps_tftp', '', '', '', '2023-06-02 14:54:14', '2023-06-02 14:54:14'),
('5.3.0', '云数据库', 'clickhouse', 'clickhouse_cap', '', '', '', '2023-06-02 14:54:14', '2023-06-02 14:54:14'),
('5.3.0', '云数据库', 'clickhouse', 'clickhouse_console', '', '', '', '2023-06-02 14:54:14', '2023-06-02 14:54:14'),
('5.3.0', '云数据库', 'clickhouse', 'clickhouse_controlplane', '', '', '', '2023-06-02 14:54:14', '2023-06-02 14:54:14'),
('5.3.0', '云数据库', 'dms', 'dms_console', '', '', '', '2023-06-02 14:54:14', '2023-06-02 14:54:14'),
('5.3.0', '云数据库', 'dms', 'dms_controlplane', '', '', '', '2023-06-02 14:54:14', '2023-06-02 14:54:14'),
('5.3.0', '云数据库', 'dts', 'dts_back', '', '', '', '2023-06-02 14:54:14', '2023-06-02 14:54:14'),
('5.3.0', '云数据库', 'dts', 'dts_cap', '', '', '', '2023-06-02 14:54:14', '2023-06-02 14:54:14'),
('5.3.0', '云数据库', 'dts', 'dts_console', '', '', '', '2023-06-02 14:54:14', '2023-06-02 14:54:14'),
('5.3.0', '云数据库', 'dts', 'dts_control_plane_mongodb', '', '', '', '2023-06-02 14:54:14', '2023-06-02 14:54:14'),
('5.3.0', '云数据库', 'dts', 'dts_control_plane_mysql', '', '', '', '2023-06-02 14:54:14', '2023-06-02 14:54:14'),
('5.3.0', '云数据库', 'dts', 'dts_control_plane_pgsql', '', '', '', '2023-06-02 14:54:14', '2023-06-02 14:54:14'),
('5.3.0', '云数据库', 'dts', 'dts_control_plane_sqlserver', '', '', '', '2023-06-02 14:54:14', '2023-06-02 14:54:14'),
('5.3.0', '云数据库', 'mongodb', 'mongodb_cap', '', '', '', '2023-06-02 14:54:14', '2023-06-02 14:54:14'),
('5.3.0', '云数据库', 'mongodb', 'mongodb_console', '', '', '', '2023-06-02 14:54:14', '2023-06-02 14:54:14'),
('5.3.0', '云数据库', 'mongodb', 'mongodb_controlplane', '', '', '', '2023-06-02 14:54:14', '2023-06-02 14:54:14'),
('5.3.0', '云数据库', 'mongodb', 'mongodb_dataplane', 'jcs_dataplane', 'servicecode', 'paas-mysql', '2023-06-02 14:54:14', '2023-06-02 14:54:14'),
('5.3.0', '云数据库', 'rds', 'rds_back', '', '', '', '2023-06-02 14:54:14', '2023-06-02 14:54:14'),
('5.3.0', '云数据库', 'rds', 'rds_cap_mysql', '', '', '', '2023-06-02 14:54:14', '2023-06-02 14:54:14'),
('5.3.0', '云数据库', 'rds', 'rds_cap_postgresql', '', '', '', '2023-06-02 14:54:14', '2023-06-02 14:54:14'),
('5.3.0', '云数据库', 'rds', 'rds_cap_sqlserver', '', '', '', '2023-06-02 14:54:14', '2023-06-02 14:54:14'),
('5.3.0', '云数据库', 'rds', 'rds_console', '', '', '', '2023-06-02 14:54:14', '2023-06-02 14:54:14'),
('5.3.0', '云数据库', 'rds', 'rds_controlplane_mysql', '', '', '', '2023-06-02 14:54:14', '2023-06-02 14:54:14'),
('5.3.0', '云数据库', 'rds', 'rds_controlplane_postgresql', '', '', '', '2023-06-02 14:54:14', '2023-06-02 14:54:14'),
('5.3.0', '云数据库', 'rds', 'rds_controlplane_sqlserver', '', '', '', '2023-06-02 14:54:14', '2023-06-02 14:54:14'),
('5.3.0', '云数据库', 'rds', 'rds_relay', '', '', '', '2023-06-02 14:54:14', '2023-06-02 14:54:14'),
('5.3.0', '云数据库', 'stardb', 'stardb', '', '', '', '2023-06-02 14:54:14', '2023-06-02 14:54:14'),
('5.3.0', '云数据库', 'tidb', 'tidb_cap', '', '', '', '2023-06-02 14:54:14', '2023-06-02 14:54:14'),
('5.3.0', '云数据库', 'tidb', 'tidb_console', '', '', '', '2023-06-02 14:54:14', '2023-06-02 14:54:14'),
('5.3.0', '云数据库', 'tidb', 'tidb_controplane', '', '', '', '2023-06-02 14:54:14', '2023-06-02 14:54:14'),
('5.3.0', '中间件', 'es', 'es_back', '', '', '', '2023-06-02 14:54:14', '2023-06-02 14:54:14'),
('5.3.0', '中间件', 'es', 'es_console', '', '', '', '2023-06-02 14:54:14', '2023-06-02 14:54:14'),
('5.3.0', '中间件', 'es', 'es_openapi', '', '', '', '2023-06-02 14:54:14', '2023-06-02 14:54:14'),
('5.3.0', '中间件', 'jcq', 'jcq_back', '', '', '', '2023-06-02 14:54:14', '2023-06-02 14:54:14'),
('5.3.0', '中间件', 'jcq', 'jcq_console', '', '', '', '2023-06-02 14:54:14', '2023-06-02 14:54:14'),
('5.3.0', '中间件', 'jcq', 'jcq_openapi', '', '', '', '2023-06-02 14:54:14', '2023-06-02 14:54:14'),
('5.3.0', '中间件', 'kafka', 'kafka_back', '', '', '', '2023-06-02 14:54:14', '2023-06-02 14:54:14'),
('5.3.0', '中间件', 'kafka', 'kafka_console', '', '', '', '2023-06-02 14:54:14', '2023-06-02 14:54:14'),
('5.3.0', '中间件', 'kafka', 'kafka_openapi', '', '', '', '2023-06-02 14:54:14', '2023-06-02 14:54:14'),
('5.3.0', '中间件', 'redis', 'redis_back', '', '', '', '2023-06-02 14:54:14', '2023-06-02 14:54:14'),
('5.3.0', '中间件', 'redis', 'redis_cap', '', '', '', '2023-06-02 14:54:14', '2023-06-02 14:54:14'),
('5.3.0', '中间件', 'redis', 'redis_console', '', '', '', '2023-06-02 14:54:14', '2023-06-02 14:54:14'),
('5.3.0', '中间件', 'redis', 'redis_openapi', '', '', '', '2023-06-02 14:54:14', '2023-06-02 14:54:14'),
('5.3.0', '中间件', 'zk', 'zk_back', '', '', '', '2023-06-02 14:54:14', '2023-06-02 14:54:14'),
('5.3.0', '中间件', 'zk', 'zk_console', '', '', '', '2023-06-02 14:54:14', '2023-06-02 14:54:14'),
('5.3.0', '中间件', 'zk', 'zk_openapi', '', '', '', '2023-06-02 14:54:14', '2023-06-02 14:54:14'),
('5.3.0', '虚拟网络', 'bgw', 'bgw_controlplane', 'iaas_controlplane', '', '', '2023-06-02 14:54:14', '2023-06-02 14:54:14'),
('5.3.0', '虚拟网络', 'bgw', 'bgw_dataplane', '', '', '', '2023-06-02 14:54:14', '2023-06-02 14:54:14'),
('5.3.0', '虚拟网络', 'bgw', 'bgw_openapi', '', '', '', '2023-06-02 14:54:14', '2023-06-02 14:54:14'),
('5.3.0', '虚拟网络', 'cc_ops', 'cc_ops_controlplane', 'iaas_controlplane', '', '', '2023-06-02 14:54:14', '2023-06-02 14:54:14'),
('5.3.0', '虚拟网络', 'cloudeye', 'cloudeye_controlplane', 'iaas_controlplane', '', '', '2023-06-02 14:54:14', '2023-06-02 14:54:14'),
('5.3.0', '虚拟网络', 'sdn_lb', 'lb_controlplane', 'iaas_controlplane', '', '', '2023-06-02 14:54:14', '2023-06-02 14:54:14'),
('5.3.0', '虚拟网络', 'sdn_lb', 'lb_controlplane_az', 'iaas_controlplane', '', '', '2023-06-02 14:54:14', '2023-06-02 14:54:14'),
('5.3.0', '虚拟网络', 'sdn_lb', 'lb_dataplane', '', '', '', '2023-06-02 14:54:14', '2023-06-02 14:54:14'),
('5.3.0', '虚拟网络', 'sdn_lb', 'lb_openapi', '', '', '', '2023-06-02 14:54:14', '2023-06-02 14:54:14'),
('5.3.0', '虚拟网络', 'sdn_mn', 'mn_controlplane', 'iaas_controlplane', '', '', '2023-06-02 14:54:14', '2023-06-02 14:54:14'),
('5.3.0', '虚拟网络', 'sdn_mn', 'mn_sli', 'sdn_mn_agent_sli', '', '', '2023-06-02 14:54:14', '2023-06-02 14:54:14'),
('5.3.0', '虚拟网络', 'sdn_natgw', 'natgw_controlplane', 'iaas_controlplane', '', '', '2023-06-02 14:54:14', '2023-06-02 14:54:14'),
('5.3.0', '虚拟网络', 'sdn_natgw', 'natgw_controlplane_az', 'iaas_controlplane', '', '', '2023-06-02 14:54:14', '2023-06-02 14:54:14'),
('5.3.0', '虚拟网络', 'sdn_natgw', 'natgw_dataplane', '', '', '', '2023-06-02 14:54:14', '2023-06-02 14:54:14'),
('5.3.0', '虚拟网络', 'sdn_vpc', 'vpc_controlplane', 'iaas_controlplane', '', '', '2023-06-02 14:54:14', '2023-06-02 14:54:14'),
('5.3.0', '虚拟网络', 'sdn_vpc', 'vpc_controlplane_az', 'iaas_controlplane', '', '', '2023-06-02 14:54:14', '2023-06-02 14:54:14'),
('5.3.0', '虚拟网络', 'sdn_vpc', 'vpc_dataplane', '', '', '', '2023-06-02 14:54:14', '2023-06-02 14:54:14'),
('5.3.0', '虚拟网络', 'sdn_vpc', 'vpc_openapi', '', '', '', '2023-06-02 14:54:14', '2023-06-02 14:54:14'),
('5.3.0', '虚拟网络', 'vpn', 'vpn_controlplane', 'iaas_controlplane', '', '', '2023-06-02 14:54:14', '2023-06-02 14:54:14'),
('5.3.0', '网络监控平台', 'dnp', 'dnp_agent', '', '', '', '2023-06-02 14:54:14', '2023-06-02 14:54:14'),
('5.3.0', '网络监控平台', 'dnp', 'dnp_cassandra', '', '', '', '2023-06-02 14:54:14', '2023-06-02 14:54:14'),
('5.3.0', '网络监控平台', 'dnp', 'dnp_console', '', '', '', '2023-06-02 14:54:14', '2023-06-02 14:54:14'),
('5.3.0', '网络监控平台', 'dnp', 'dnp_middleware', '', '', '', '2023-06-02 14:54:14', '2023-06-02 14:54:14'),
('5.3.0', '网络监控平台', 'dnp', 'dnp_monitor', '', '', '', '2023-06-02 14:54:14', '2023-06-02 14:54:14'),
('5.3.0', '网络监控平台', 'dnp', 'dnp_openapi', '', '', '', '2023-06-02 14:54:14', '2023-06-02 14:54:14'),
('5.3.0', '网络监控平台', 'dnp', 'dnp_spark', '', '', '', '2023-06-02 14:54:14', '2023-06-02 14:54:14'),
('5.3.0', '网络互通', 'jdstack_sdn', 'pcd_inter_working', '', '', '', '2023-06-02 14:54:14', '2023-06-02 14:54:14'),
('5.3.0', '网络互通', 'jdstack_sdn', 'pcd_net_console', '', '', '', '2023-06-02 14:54:14', '2023-06-02 14:54:14'),
('5.3.0', '网络互通', 'jdstack_sdn', 'pcd_top_openapi', '', '', '', '2023-06-02 14:54:14', '2023-06-02 14:54:14'),
('5.3.0', '网络互通', 'jdstack_sdn', 'pcd_diagnosis_api', '', '', '', '2023-06-02 14:54:14', '2023-06-02 14:54:14'),
('5.3.0', '网络互通', 'jdstack_sdn', 'pcd_diagnosis_cloudhc', 'iaas_controlplane', '', '', '2023-06-02 14:54:14', '2023-06-02 14:54:14'),
('5.3.0', 'DevOps', 'logs', 'logs_console', '', '', '', '2023-06-02 14:54:14', '2023-06-02 14:54:14'),
('5.3.0', 'DevOps', 'logs', 'logs_controlplate', '', '', '', '2023-06-02 14:54:14', '2023-06-02 14:54:14'),
('5.3.0', 'DevOps', 'logs', 'logs_openapi', '', '', '', '2023-06-02 14:54:14', '2023-06-02 14:54:14'),
('5.3.0', 'DevOps', 'monitor', 'monitor_api', '', '', '', '2023-06-02 14:54:14', '2023-06-02 14:54:14'),
('5.3.0', 'DevOps', 'monitor', 'monitor_console', '', '', '', '2023-06-02 14:54:14', '2023-06-02 14:54:14'),
('5.3.0', 'DevOps', 'monitor', 'monitor_controlplane', '', '', '', '2023-06-02 14:54:14', '2023-06-02 14:54:14'),
('5.3.0', 'DevOps', 'monitor', 'monitor_dataplane', '', '', '', '2023-06-02 14:54:14', '2023-06-02 14:54:14'),
('5.3.0', 'DevOps', 'monitor', 'monitor_dataplane_init', 'jdock_runtime', '', '', '2023-06-02 14:54:14', '2023-06-02 14:54:14'),
('5.3.0', '基础服务', 'skywing', 'special_skywing', 'skywing', '', '', '2023-06-02 14:54:14', '2023-06-02 14:54:14'),
('5.3.0', '统一运维平台', 'jdstack_ops', 'storage_manager', '', '', '', '2023-06-09 16:02:19', '2023-06-09 16:03:44'),
('5.3.0', '统一运维平台', 'jdstack_ops', 'jdstack_dms_openapi', '', '', '', '2023-06-09 16:02:19', '2023-06-09 16:03:44'),
('5.3.0', '存储', 'zbs', 'zbs_snapshot', 'zbs_snapshot', '', '', '2023-06-09 23:19:06', '2023-06-09 23:22:34'),
('5.3.0', '存储', 'zbs', 'zbs_refresh_snapshot', 'zbs_snapshot', '', '', '2023-06-09 23:19:06', '2023-06-09 23:22:34'),
('5.3.0', 'iaas控制台', 'iaas_console', 'webterminal_console', ' ', '', '', '2023-06-10 15:01:36', '2023-06-10 15:09:53'),
('5.3.0', '存储', 'oss_data_service', 'oss_ds_master', 'oss_ds_master', '', '', '2023-06-10 23:41:03', '2023-06-11 00:00:16'),
('5.3.0', '存储', 'oss_data_service', 'oss_pd', 'oss_pd', '', '', '2023-06-10 23:41:58', '2023-06-11 00:00:22'),
('5.3.0', '存储', 'oss_data_service', 'oss_tikv', 'oss_tikv', '', '', '2023-06-10 23:42:32', '2023-06-10 23:43:56'),
('5.3.0', '存储', 'oss_data_service', 'oss_ec_master', 'oss_ec_master', '', '', '2023-06-10 23:44:29', '2023-06-10 23:44:29'),
('5.3.0', '存储', 'oss_data_service', 'oss_ec_datanode', 'oss_ec_datanode', '', '', '2023-06-10 23:44:29', '2023-06-10 23:44:29'),
('5.3.0', '存储', 'oss_data_service', 'oss_ec_node', 'oss_ec_node', '', '', '2023-06-10 23:44:29', '2023-06-10 23:44:29'),
('5.3.0', '存储', 'oss_data_service', 'oss_data_transfer', '', '', '', '2023-06-10 23:44:29', '2023-06-10 23:44:29'),
('5.3.0', '扁鹊', 'bianque', 'jdstack_push_gateway', 'jdstack_push_gateway', '', '', '2023-06-06 18:39:10', '2023-06-14 17:32:06'),
('5.3.0', '扁鹊', 'bianque', 'bianque_gateway', '', '', '', '2023-06-11 16:48:14', '2023-06-14 17:32:05'),
('5.3.0', '扁鹊', 'bianque', 'bianque_scheduler', '', '', '', '2023-06-11 16:48:39', '2023-06-14 17:32:05'),
('5.3.0', '扁鹊', 'bianque', 'bianque_probe', 'bianque_probe', '', '', '2023-06-12 10:26:08', '2023-06-14 17:32:04'),
('5.3.0', '扁鹊', 'bianque', 'bianque_postgres_exporter', 'bianque_postgres_exporter', '', '', '2023-06-14 17:32:01', '2023-06-17 23:16:24'),
('5.3.0', '扁鹊', 'bianque', 'bianque_mysql_exporter', 'bianque_mysql_exporter', '', '', '2023-06-14 17:32:37', '2023-06-14 17:32:37'),
('5.3.0', 'DevOps', 'monitor', 'monitor_ifrit', ' ', '', '', '2023-06-14 18:33:31', '2023-06-14 18:34:36'),
('5.3.0', '存储', 'cfs', 'cfs_ha', 'cfs_ha', 'role', 'master', '2023-06-02 14:54:14', '2023-06-28 17:52:02'),
('5.3.0', '存储', 'cfs', 'cfs_clt', 'cfs_clt', '', '', '2023-06-02 14:54:14', '2023-06-14 23:20:40'),
('5.3.0', '存储', 'cfs', 'cfs_cluster_api_master', 'cfs_cluster_api_master', '', '', '2023-06-02 14:54:14', '2023-06-14 23:20:40'),
('5.3.0', '存储', 'cfs', 'cfs_cluster_api_slave', 'cfs_cluster_api_slave', '', '', '2023-06-02 14:54:14', '2023-06-14 23:20:40'),
('5.3.0', '扁鹊', 'bianque', 'bianque_grafana', 'bianque_grafana', '', '', '2023-06-18 14:15:19', '2023-06-18 14:15:19'),
('5.3.0', '优化顾问', 'jdcloud_cost_optimization', 'advisor_console', '', '', '', '2023-06-18 23:40:16', '2023-07-14 13:51:29'),
('5.3.0', '弹性计算', 'jvirt_dh', 'dh_dataplane', 'dh_dataplane', 'machine', 'general_2', '2023-06-02 14:54:14', '2023-06-28 16:16:47'),
('5.3.0', '弹性计算', 'jvirt_dh', 'dh_dataplane', 'dh_dataplane', 'machine', 'gpu', '2023-06-02 14:54:14', '2023-06-02 14:54:14'),
('5.3.0', '弹性计算', 'jvirt_dh', 'dh_dataplane', 'dh_dataplane', 'machine', 'bare_metal', '2023-06-02 14:54:14', '2023-06-02 14:54:14'),
('5.3.0', '弹性计算', 'jvirt_dh', 'dh_dataplane', 'dh_dataplane', 'machine', 'paas_mysql', '2023-06-02 14:54:14', '2023-06-02 14:54:14'),
('5.3.0', '弹性计算', 'jvirt_dh', 'dh_dataplane', 'dh_dataplane', 'machine', 'paas_sqlserver', '2023-06-02 14:54:14', '2023-06-02 14:54:14'),
('5.3.0', '弹性计算', 'jvirt_dh', 'dh_dataplane', 'dh_dataplane', 'machine', 'storageoptimized', '2023-06-02 14:54:14', '2023-06-02 14:54:14'),
('5.3.0', '弹性计算', 'vm', 'vm_dataplane_kvm', 'jcs_dataplane', 'machine', 'general_2', '2023-06-02 14:54:14', '2023-06-19 19:07:33'),
('5.3.0', '弹性计算', 'vm', 'vm_dataplane_alb', 'jcs_dataplane', 'machine', 'iaas_alb', '2023-06-02 14:54:14', '2023-06-19 19:07:33'),
('5.3.0', '弹性计算', 'vm', 'jcs_dataplane', 'jcs_dataplane', 'machine', 'general_2', '2023-06-02 14:54:14', '2023-06-19 19:07:33'),
('5.3.0', '弹性计算', 'vm', 'jcs_dataplane', 'jcs_dataplane', 'machine', 'gpu', '2023-06-02 14:54:14', '2023-06-19 19:07:33'),
('5.3.0', '弹性计算', 'vm', 'jcs_dataplane', 'jcs_dataplane', 'machine', 'bare_metal', '2023-06-02 14:54:14', '2023-06-19 19:07:33'),
('5.3.0', '弹性计算', 'vm', 'jcs_dataplane', 'jcs_dataplane', 'machine', 'paas_mysql', '2023-06-02 14:54:14', '2023-06-19 19:07:33'),
('5.3.0', '弹性计算', 'vm', 'jcs_dataplane', 'jcs_dataplane', 'machine', 'paas_sqlserver', '2023-06-02 14:54:14', '2023-06-19 19:07:33'),
('5.3.0', '弹性计算', 'vm', 'jcs_dataplane', 'jcs_dataplane', 'machine', 'storageoptimized', '2023-06-02 14:54:14', '2023-06-19 19:07:33'),
('5.3.0', '弹性计算', 'vm', 'jcs_dataplane', 'jcs_dataplane', 'machine', 'g.n1', '2023-06-02 14:54:14', '2023-06-19 19:07:33'),
('5.3.0', '弹性计算', 'vm', 'jcs_dataplane', 'jcs_dataplane', 'machine', 'iaas_alb', '2023-06-02 14:54:14', '2023-06-19 19:07:33'),
('5.3.0', '弹性计算', 'vm', 'jcs_dataplane', 'jcs_dataplane', 'machine', 'general_2#dedicated_host', '2023-06-02 14:54:14', '2023-06-19 19:07:33'),
('5.3.0', '基础服务', 'base_mysql', 'base_mysql', 'base_mysql', 'role', 'slave', '2023-06-02 14:54:14', '2023-06-14 18:44:27'),
('5.3.0', '基础服务', 'base_mysql', 'base_mysql', 'base_mysql', 'role', 'backup', '2023-06-02 14:54:14', '2023-06-14 18:44:27'),
('5.3.0', '基础服务', 'base_pgsql', 'base_pgsql', 'base_pgsql', 'role', 'slave', '2023-06-02 14:54:14', '2023-06-14 18:44:29'),
('5.3.0', '基础服务', 'base_pgsql', 'base_pgsql', 'base_pgsql', 'role', 'backup', '2023-06-02 14:54:14', '2023-06-14 18:44:29'),
('5.3.0', '弹性计算', 'jvirt_dh', 'dh_dataplane', 'dh_dataplane', 'servicecode', 'normal', '2023-06-02 14:54:14', '2023-06-28 17:30:33'),
('5.3.0', '弹性计算', 'jvirt_dh', 'dh_dataplane', 'dh_dataplane', 'servicecode', 'dedicated_host', '2023-06-02 14:54:14', '2023-06-02 14:54:14'),
('5.3.0', '弹性计算', 'jvirt_dh', 'dh_dataplane', 'dh_dataplane', 'servicecode', 'iaas-alb', '2023-06-02 14:54:14', '2023-06-02 14:54:14'),
('5.3.0', '弹性计算', 'jvirt_dh', 'dh_dataplane', 'dh_dataplane', 'servicecode', 'paas-jvessel', '2023-06-02 14:54:14', '2023-06-02 14:54:14'),
('5.3.0', '弹性计算', 'jvirt_dh', 'dh_dataplane', 'dh_dataplane', 'servicecode', 'paas-mysql', '2023-06-02 14:54:14', '2023-06-02 14:54:14'),
('5.3.0', '弹性计算', 'jvirt_dh', 'dh_dataplane', 'dh_dataplane', 'servicecode', 'paas-sqlserver', '2023-06-02 14:54:14', '2023-06-02 14:54:14'),
('5.3.0', '弹性计算', 'vm', 'jcs_dataplane', 'jcs_dataplane', 'servicecode', 'normal', '2023-06-02 14:54:14', '2023-06-28 17:35:29'),
('5.3.0', '弹性计算', 'vm', 'jcs_dataplane', 'jcs_dataplane', 'servicecode', 'dedicated_host', '2023-06-02 14:54:14', '2023-06-19 19:07:33'),
('5.3.0', '弹性计算', 'vm', 'jcs_dataplane', 'jcs_dataplane', 'servicecode', 'iaas-alb', '2023-06-02 14:54:14', '2023-06-19 19:07:33'),
('5.3.0', '弹性计算', 'vm', 'jcs_dataplane', 'jcs_dataplane', 'servicecode', 'paas-jvessel', '2023-06-02 14:54:14', '2023-06-19 19:07:33'),
('5.3.0', '弹性计算', 'vm', 'jcs_dataplane', 'jcs_dataplane', 'servicecode', 'paas-mysql', '2023-06-02 14:54:14', '2023-06-19 19:07:33'),
('5.3.0', '弹性计算', 'vm', 'jcs_dataplane', 'jcs_dataplane', 'servicecode', 'paas-sqlserver', '2023-06-02 14:54:14', '2023-06-19 19:07:33'),
('5.3.0', '基础服务', 'base_dns', 'base_dns', 'base_dns', 'role', 'slave', '2023-06-02 14:54:14', '2023-06-14 18:44:17'),
('5.3.0', '基础服务', 'base_lb', 'base_lb', 'base_lb', 'role', 'slave', '2023-06-02 14:54:14', '2023-06-28 17:37:39'),
('5.3.0', '基础服务', 'base_hub', 'base_hub', 'base_hub', 'role', 'master', '2023-06-02 14:54:14', '2023-06-28 17:40:28'),
('5.3.0', '基础服务', 'base_hub', 'base_hub', 'base_hub', 'role', 'slave', '2023-06-02 14:54:14', '2023-06-28 17:39:37'),
('5.3.0', '基础服务', 'base_k8s', 'base_k8s', 'base_k8s', 'role', 'master', '2023-06-02 14:54:14', '2023-06-14 18:44:20'),
('5.3.0', '基础服务', 'base_k8s', 'base_k8s', 'base_k8s', 'role', 'node', '2023-06-02 14:54:14', '2023-06-14 18:44:20'),
('5.3.0', '存储', 'cfs', 'cfs_ha', 'cfs_ha', 'role', 'slave', '2023-06-02 14:54:14', '2023-06-15 00:28:47'),
('5.3.0', '弹性计算', 'web_terminal', 'web_terminal_console', '', '', '', '2023-06-02 14:54:14', '2023-06-02 14:54:14'),
('5.3.0', '网络互通', 'jdstack_sdn', 'pcd_nc_engine', 'iaas_controlplane', '', '', '2023-07-16 16:09:55', '2023-07-16 16:10:09'),
('5.3.0', '天基', 'jdock', 'jdock_runtime', 'jdock_runtime', '', '', '2023-07-16 16:09:55', '2023-07-16 16:10:09'),
('5.3.0', '存储', 'oss', 'oss_ec_datanode', 'oss_ec_datanode', '', '', '2023-06-02 20:27:39', '2023-06-02 20:27:39'),
('5.3.0', '天基TPaasPGSQL', 'base_middleware_pgsql', 'pgsql', '', '', '', '2023-06-02 20:27:39', '2023-06-02 20:27:39'),
('5.3.0', '基础服务', 'skywing_v2', 'skywing_v2', '', '', '', NOW(), NOW()),
('5.3.0', '基础服务', 'skywing_v2', 'skywing_captain_v2', '', '', '', NOW(), NOW()),
('5.3.0', '弹性计算', 'assistant', 'jvirt_assist_server', 'iaas_controlplane', '', '', '2023-06-02 20:27:39', '2023-06-02 20:27:39'),
('5.3.0', '云电脑', 'cloudpc', 'cloudpc_view', '', '', '', '2023-06-02 20:27:39', '2023-06-02 20:27:39'),
('5.3.0', '云电脑', 'cloudpc', 'cloudpc_controlplane_api', '', '', '', '2023-06-02 20:27:39', '2023-06-02 20:27:39'),
('5.3.0', '云电脑', 'cloudpc', 'cloudpc_controlplane_service', '', '', '', '2023-06-02 20:27:39', '2023-06-02 20:27:39'),
('5.3.0', '云电脑', 'cloudpc', 'cloudpc_monitor', '', '', '', '2023-06-02 20:27:39', '2023-06-02 20:27:39'),
('5.3.0', '运维管理', 'prism', 'prism_cmdb', '', '', '', '2023-06-02 20:27:39', '2023-06-02 20:27:39'),
('5.3.0', '虚拟网络', 'alb_autoscale', 'alb_autoscale', 'iaas_controlplane', '', '', '2023-09-12 20:49:25', '2023-09-14 03:51:27'),
('5.3.0', 'vr数据面', 'sdn_vpc', 'vpc_vr_dataplane', 'sdn_std_vr', NULL, NULL, '2024-05-09 15:39:27', '2024-05-20 21:57:55'),
('5.3.0', 'dr数据面', 'sdn_vpc', 'vpc_dr_dataplane', 'sdn_light_vr_dr_ipv4', NULL, NULL, '2024-05-09 15:39:27', '2024-05-20 22:04:53'),
('5.3.0', 'dr数据面', 'sdn_vpc', 'vpc_dr_dataplane', 'sdn_light_vr_dr_natgw', NULL, NULL, '2024-05-09 15:39:27', '2024-05-20 22:05:52'),
('5.3.0', 'natgw数据面', 'sdn_natgw', 'natgw_dataplane', 'sdn_std_natgw', NULL, NULL, '2024-05-09 15:39:27', '2024-05-20 22:08:43'),
('5.3.0', 'vr数据面', 'sdn_vpc', 'vpc_vr_dataplane', 'sdn_light_vr_dr_ipv4', NULL, NULL, '2024-05-09 15:39:27', '2024-05-20 21:58:33'),
('5.3.0', 'vr数据面', 'sdn_vpc', 'vpc_vr_dataplane', 'sdn_light_vr_dr_natgw', NULL, NULL, '2024-05-09 15:39:27', '2024-05-20 21:59:03'),
('5.3.0', 'dr数据面', 'sdn_vpc', 'vpc_dr_dataplane', 'sdn_light_vr_dr_natgw', NULL, NULL, '2024-05-09 15:39:27', '2024-05-20 21:59:03'),
('5.3.0', 'natgw数据面', 'sdn_natgw', 'natgw_dataplane', 'sdn_light_natgw', NULL, NULL, '2024-05-09 15:39:27', '2024-05-20 22:09:13'),
('5.3.0', 'natgw数据面', 'sdn_natgw', 'natgw_dataplane', 'sdn_light_vr_dr_natgw', NULL, NULL, '2024-05-09 15:39:27', '2024-05-20 22:09:43'),
('5.3.0', 'bgw数据面', 'bgw', 'bgw_dataplane', 'sdn_std_bgw', NULL, NULL, '2024-05-09 15:39:27', '2024-05-20 22:11:23'),
('5.3.0', 'bgw数据面', 'bgw', 'bgw_dataplane', 'sdn_light_bgw', NULL, NULL, '2024-05-09 15:39:27', '2024-05-20 22:11:42'),
('5.3.0', '云日志事件总线', 'eventbus', 'eventbus_controlplate', '', NULL, NULL, '2024-05-09 15:39:27', '2024-05-20 22:13:18'),
('5.3.0', '容灾平台', 'jdstack_drms', 'jdstack_drms_console', '', NULL, NULL, '2024-05-09 15:39:27', '2024-05-20 22:13:18'),
('5.3.0', '容灾平台', 'jdstack_drms', 'jdstack_drms', 'jdstack_drms', NULL, NULL, '2024-05-09 15:39:27', '2024-05-20 22:13:18'),
('5.3.0', '运维编排', 'ops_server', 'ops_server', 'iaas_controlplane', NULL, NULL, '2024-05-09 15:39:27', '2024-05-20 22:13:18'),
('5.3.0', '运维编排', 'ops_server', 'ops_back', '', NULL, NULL, '2024-05-09 15:39:27', '2024-05-20 22:13:18'),
('5.3.0', 'digger', 'jdstack_digger', 'machine', 'bianque_probe', NULL, NULL, '2024-05-09 15:39:27', '2024-05-20 22:13:18'),
('5.3.0', 'digger', 'jdstack_digger', 'network', '', NULL, NULL, '2024-05-09 15:39:27', '2024-05-20 22:13:18'),
('5.3.0', 'vpn数据面', 'vpn', 'vpn_dataplane', 'sdn_light_vpn', NULL, NULL, '2024-05-09 15:39:27', '2024-05-20 22:13:18'),
('5.3.0', 'vpn数据面', 'vpn', 'vpn_dataplane', 'sdn_std_vpn', NULL, NULL, '2024-05-09 15:39:27', '2024-05-20 22:13:00');

SET FOREIGN_KEY_CHECKS = 1;

CREATE TABLE IF NOT EXISTS `server_role_constants`
(
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `role` varchar(255)  NOT NULL,
    `metadata_key` varchar(255)  DEFAULT NULL,
    `metadata_values` varchar(1020) DEFAULT NULL,
    `device_type` varchar(64)  DEFAULT NULL COMMENT '所属设备类型',
    `category` varchar(64)  DEFAULT NULL COMMENT '角色分类',
    `plane` varchar(64)  DEFAULT NULL COMMENT 'control: 控制面; data: 数据面',
    `plane_category` varchar(128)  DEFAULT NULL COMMENT 'plane分类',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8;

BEGIN;
REPLACE INTO `server_role_constants` (`id`, `role`, `metadata_key`, `metadata_values`, `device_type`, `category`, `plane`, `plane_category`)

VALUES (1, 'base_mysql', 'role', 'master,slave', 'server', '基础服务', 'control', '基础数据库'),
       (3, 'base_ntp', '', NULL, 'server', '基础服务', 'control', '基础NTP'),
       (4, 'base_dns', 'role', 'master,slave', 'server', '基础服务', 'control', '基础DNS'),
       (5, 'base_lb', 'role', 'master,slave', 'server', '基础服务', 'control', '基础LB'),
       (6, 'base_loader', NULL, NULL, 'server', '基础服务', 'control', '基础LB'),
       (7, 'base_minio', NULL, NULL, 'server', '基础服务', 'control', '基础LB'),
       (8, 'skywing', NULL, NULL, 'server', '基础服务', 'control', '云翼服务器'),
       (9, 'base_hub', 'role', 'master,slave', 'server', '基础服务', 'control', '基础LB'),
       (10, 'base_k8s', 'role', 'master,node', 'server', '基础服务', 'control', '云翼服务器,天基服务器'),
       (11, 'base_yum', NULL, NULL, 'server', '基础服务', 'control', '基础LB'),
       (12, 'base_tpaas', NULL, NULL, 'server', '基础服务', 'control', '天基服务器'),
       (13, 'mate', NULL, NULL, 'server', '基础服务', NULL, NULL),
       (14, 'base_starDb', 'role', 'master,slave', 'server', '基础服务', 'control', '基础数据库'),
       (15, 'cross_az_deploy', NULL, NULL, 'server', '基础服务', NULL, NULL),
       (16, 'jdock_runtime', NULL, NULL, 'server', '天基', 'control', '天基服务器'),
       (17, 'base_pgsql', 'role', 'master,slave,backup', 'server', '基础服务', 'control', '基础数据库'),
       (20, 'zbs_controlplane', NULL, NULL, 'server', 'zbs', 'control', 'IaaS控制面'),
       (21, 'zbs_snapshot', NULL, NULL, 'server', 'zbs', 'control', 'IaaS控制面,ZBS快照缓存服务'),
       (22, 'zbs_pool', '', NULL, 'server', 'zbs', NULL, 'ZBS块存储'),
       (24, 'oss_controlplane', NULL, NULL, 'server', 'oss', NULL, NULL),
       (25, 'oss_ds_datanode', NULL, NULL, 'server', 'oss', NULL, 'OSS对象存储'),
       (26, 'oss_datacap', NULL, NULL, 'server', 'oss', NULL, 'OSS对象存储'),
       (27, 'oss_tikv', NULL, NULL, 'server', 'oss', NULL, 'OSS对象存储'),
       (28, 'oss_pd', NULL, NULL, 'server', 'oss', NULL, 'OSS对象存储'),
       (29, 'oss_ds_master', NULL, NULL, 'server', 'oss', NULL, 'OSS对象存储'),
       (31, 'nf1', NULL, NULL, 'server', '安全', NULL, NULL),
       (32, 'tp_clean', NULL, NULL, 'server', '安全', NULL, NULL),
       (33, 'tp_detect', NULL, NULL, 'server', '安全', NULL, NULL),
       (34, 'csa_detect', NULL, NULL, 'server', '安全', NULL, NULL),
       (35, 'csa_analyze', NULL, NULL, 'server', '安全', NULL, NULL),
       (36, 'light_pdns', NULL, NULL, 'server', '安全', NULL, NULL),
       (37, 'light_adns', NULL, NULL, 'server', '安全', NULL, NULL),
       (38, 'pdns', NULL, NULL, 'server', '安全', NULL, NULL),
       (39, 'adns', NULL, NULL, 'server', '安全', NULL, NULL),
       (40, 'jcs_image', NULL, NULL, 'server', 'jcs', 'control', '云主机镜像服务'),
       (41, 'jcs_controlplane', NULL, NULL, 'server', 'jcs', NULL, NULL),
       (42, 'jcs_dataplane', 'servicecode', 'normal,dedicated_host,iaas-alb,paas-jvessel,paas-mysql,paas-sqlserver', 'server', 'jcs', NULL, NULL),
       (45, 'jcs_dataplane', 'machine', 'general_2,gpu,bare_metal,paas_mysql,paas_sqlserver,storageoptimized,g.n1,iaas_alb,general_2#dedicated_host', 'server', 'jcs', NULL, NULL),
       (46, 'testcase', NULL, NULL, 'server', 'jcs', NULL, NULL),
       (47, 'chaos', NULL, NULL, 'server', 'jcs', NULL, NULL),
       (48, 'chaosman', NULL, NULL, 'server', 'jcs', NULL, NULL),
       (49, 'dh_dataplane', 'machine', 'general_2,gpu,bare_metal,paas_mysql,paas_sqlserver,storageoptimized', 'server', 'jcs', NULL, NULL),
       (50, 'dh_dataplane', 'servicecode', 'normal,dedicated_host,iaas-alb,paas-jvessel,paas-mysql,paas-sqlserver', 'server', 'jcs', NULL, NULL),
       (51, 'iaas_controlplane', NULL, NULL, 'server', 'jcs', NULL, NULL),
       (52, 'csa_ids', NULL, NULL, 'server', 'jcs', NULL, NULL),
       (54, 'interworking-ce', NULL, NULL, 'network', '网络设备', NULL, NULL),
       (55, 'interworking-dlsw', NULL, NULL, 'network', '网络设备', NULL, NULL),
       (56, 'interworking-dxr', NULL, NULL, 'network', '网络设备', NULL, NULL),
       (57, 'interworking-fw', NULL, NULL, 'network', '网络设备', NULL, NULL),
       (59, 'sdn_controlplane', NULL, NULL, 'server', 'sdn', NULL, NULL),
       (61, 'sdn_std_dr_ipv4', NULL, NULL, 'server', 'sdn', NULL, 'DR'),
       (62, 'sdn_std_dr_ipv6', NULL, NULL, 'server', 'sdn', NULL, 'DR'),
       (63, 'sdn_inat', NULL, NULL, 'server', 'sdn', NULL, NULL),
       (64, 'sdn_light_natgw', NULL, NULL, 'server', 'sdn', NULL, 'NATGW'),
       (65, 'sdn_std_natgw', NULL, NULL, 'server', 'sdn', NULL, 'NATGW'),
       (66, 'sdn_light_bgw', NULL, NULL, 'server', 'sdn', NULL, 'BGW'),
       (67, 'sdn_std_bgw', NULL, NULL, 'server', 'sdn', NULL, 'BGW'),
       (68, 'sdn_light_vpn', NULL, NULL, 'server', 'sdn', NULL, 'VPN'),
       (69, 'sdn_std_vpn', NULL, NULL, 'server', 'sdn', NULL, 'VPN'),
       (70, 'sdn_std_vr', NULL, NULL, 'server', 'sdn', NULL, 'VR'),
       (71, 'sdn_light_vr_dr_ipv4', NULL, NULL, 'server', 'sdn', NULL, 'VR'),
       (72, 'sdn_light_vr_dr_ipv6', NULL, NULL, 'server', 'sdn', NULL, 'VR'),
       (73, 'sdn_cloudroute', NULL, NULL, 'server', 'sdn', 'control', 'IaaS控制面'),
       (74, 'bianque_mysql_exporter', NULL, NULL, 'server', '扁鹊', NULL, NULL),
       (75, 'bianque_grafana', NULL, NULL, 'server', '扁鹊', NULL, NULL),
       (76, 'jdstack_push_gateway', NULL, NULL, 'server', '扁鹊', NULL, NULL),
       (77, 'bianque_default', NULL, NULL, 'server', '扁鹊', NULL, NULL),
       (78, 'bianque_probe', NULL, NULL, 'server', '扁鹊', NULL, NULL),
       (79, 'bianque_postgres_exporter', NULL, NULL, 'server', '扁鹊', NULL, NULL),
       (80, 'cfs_dataplane', NULL, NULL, 'server', 'cfs', NULL, NULL),
       (81, 'cfs_controlplane', NULL, NULL, 'server', 'cfs', 'control', 'IaaS控制面'),
       (82, 'cfs_clt', NULL, NULL, 'server', 'cfs', NULL, NULL),
       (83, 'cfs_global_api', NULL, NULL, 'server', 'cfs', 'control', 'IaaS控制面'),
       (84, 'cfs_ha', 'role', 'master,slave', 'server', 'cfs', 'control', 'IaaS控制面'),
       (85, 'cfs_cluster_api_master', NULL, NULL, 'server', 'cfs', 'control', 'IaaS控制面'),
       (86, 'cfs_cluster_api_slave', NULL, NULL, 'server', 'cfs', 'control', 'IaaS控制面'),
       (87, 'mate_daemon_agent', NULL, NULL, 'server', 'mate', 'control', '云主机镜像服务'),
       (88, 'vm_dataplane', NULL, NULL, 'server', 'vm', NULL, NULL),
       (91, 'sdn_light_vr_dr_natgw', NULL, NULL, 'server', 'sdn', NULL, NULL),
       (92, 'sdn_light_vr_dr_natgw_ipv6', NULL, NULL, 'server', 'sdn', NULL, NULL),
       (93, 'oss_ec_master', NULL, NULL, 'server', 'oss', NULL, 'OSS对象存储'),
       (94, 'oss_ec_datanode', NULL, NULL, 'server', 'oss', NULL, 'OSS对象存储'),
       (95, 'oss_ec_node', NULL, NULL, 'server', 'oss', NULL, 'OSS对象存储'),
       (96, 'sdn_mn_agent_sli', NULL, NULL, 'server', 'sdn', NULL, NULL),
       (97, 'jdstack_drms', NULL, NULL, 'server', '容灾', NULL, NULL);
COMMIT;


CREATE TABLE IF NOT EXISTS `device_group`
(
    `id`          int(11)     NOT NULL AUTO_INCREMENT COMMENT '自增主键',
    `group_id`    varchar(64) NOT NULL                COMMENT '组ID',
    `group_type`  varchar(64) NOT NULL                COMMENT '组类型',
    `device_type` varchar(64) NOT NULL                COMMENT '组内设备类型，server:服务器，network:网络设备',
    `region`      varchar(64) NOT NULL                COMMENT 'region',
    `description` text                                COMMENT '描述',
    `creator`     varchar(128)         DEFAULT ''     COMMENT 'erp/pin',
    `updater`     varchar(128)         DEFAULT '',
    `created_at`  datetime    NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at`  datetime    NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    `version`     bigint(20)  NOT NULL DEFAULT 1      COMMENT '用于进行版本控制',
    PRIMARY KEY (`id`)  USING BTREE,
    UNIQUE KEY `group_id` (`group_id`)
    ) COMMENT ='设备组表' CHARACTER SET = utf8mb4
    COLLATE = utf8mb4_general_ci
    ENGINE = InnoDB;

call alter_column("device_group","group_name","varchar",64,1,"设备组名称");

CREATE TABLE IF NOT EXISTS `device_group_rel`
(
    `id`          int(11)     NOT NULL AUTO_INCREMENT COMMENT '自增主键',
    `group_id`    varchar(64) NOT NULL                COMMENT '组ID',
    `device_id`   varchar(64) NOT NULL                COMMENT '设备ID,即服务器或者网络设备的UUID',
    `creator`     varchar(128)         DEFAULT ''     COMMENT 'erp/pin',
    `updater`     varchar(128)         DEFAULT '',
    `created_at`  datetime    NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at`  datetime    NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    `version`     bigint(20)  NOT NULL DEFAULT 1      COMMENT '用于进行版本控制',
    PRIMARY KEY (`id`)  USING BTREE,
    UNIQUE KEY `device_group_id` (`group_id`,`device_id`)
    ) COMMENT ='设备组与设备的关联关系表' CHARACTER SET = utf8mb4
    COLLATE = utf8mb4_general_ci
    ENGINE = InnoDB;

CREATE TABLE IF NOT EXISTS `phy_direct_connect`
(
    `id`              int(11)     NOT NULL AUTO_INCREMENT COMMENT '自增主键',
    `uuid`            varchar(64) NOT NULL                COMMENT 'uuid',
    `name`            varchar(64) NOT NULL                COMMENT '专线名称',
    `conn_state`      tinyint(1)  NOT NULL                COMMENT '连接状态，1:已接线，2:未接线，9:连线错误',
    `conn_type`       tinyint(1)  NOT NULL                COMMENT '连接类型，1:本地连接，2:区域互联',
    `deploy_type`     tinyint(1)  NOT NULL                COMMENT '部署类型，1:标准版，2:轻量版',
    `region`          varchar(64) NOT NULL                COMMENT 'region',
    `peer_region`     varchar(64)                         COMMENT '对端region，区域互联时为必填',
    `local_bgw_id`    varchar(64) NOT NULL                COMMENT '本地BGW组ID',
    `peer_bgw_id`     varchar(64)                         COMMENT '对端BGW组ID',
    `local_cr_id`     varchar(64) NOT NULL                COMMENT '本地CR组ID',
    `peer_cr_id`      varchar(64)                         COMMENT '对端CR组ID',
    `ce_id`           varchar(64)                         COMMENT '接入点的CE组ID',
    `ce_port`         varchar(64)                         COMMENT '接入点的CE组端口',
    `fw_strategy`     tinyint(1)           DEFAULT 1      COMMENT '防火墙策略，0:关闭，1:开启',
    `fw_share`        tinyint(1)           DEFAULT 0      COMMENT '防火墙共享，0:关闭，1:开启',
    `description`     text                                COMMENT '备注',
    `creator`         varchar(128)         DEFAULT ''     COMMENT 'erp/pin',
    `updater`         varchar(128)         DEFAULT '',
    `created_at`      datetime    NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at`      datetime    NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    `version`         bigint(20)  NOT NULL DEFAULT 1      COMMENT '用于进行版本控制',
    PRIMARY KEY (`id`)  USING BTREE,
    UNIQUE KEY `uuid` (`uuid`),
    UNIQUE KEY `name` (`name`)
    ) COMMENT ='物理专线表' CHARACTER SET = utf8mb4
    COLLATE = utf8mb4_general_ci
    ENGINE = InnoDB;

CREATE TABLE IF NOT EXISTS `phy_direct_connect_line`
(
    `id`                int(11)     NOT NULL AUTO_INCREMENT  COMMENT '自增主键',
    `pdc_id`            varchar(64) NOT NULL                 COMMENT '物理专线ID',
    `line_type`         tinyint(1)  NOT NULL                 COMMENT '连线类型，1:本端-本端，2:对端-对端，3:本端-对端',
    `source_group_id`   varchar(64) NOT NULL                 COMMENT '源设备组ID',
    `source_port`       varchar(64) NOT NULL                 COMMENT '源物理端口',
    `remote_group_id`   varchar(64) NOT NULL                 COMMENT '远端设备组ID',
    `remote_port`       varchar(64) NOT NULL                 COMMENT '远端物理端口',
    `creator`           varchar(128)         DEFAULT ''     COMMENT 'erp/pin',
    `updater`           varchar(128)         DEFAULT '',
    `created_at`        datetime    NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at`        datetime    NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    `version`           bigint(20)  NOT NULL DEFAULT 1      COMMENT '用于进行版本控制',
    PRIMARY KEY (`id`)  USING BTREE
    ) COMMENT ='物理专线连接配置表' CHARACTER SET = utf8mb4
    COLLATE = utf8mb4_general_ci
    ENGINE = InnoDB;

CREATE TABLE IF NOT EXISTS `phy_direct_connect_user_segment`
(
    `id`                int(11)     NOT NULL AUTO_INCREMENT  COMMENT '自增主键',
    `pdc_id`            varchar(64) NOT NULL                 COMMENT '物理专线ID',
    `user_pin`          varchar(64) NOT NULL                 COMMENT '用户PIN',
    `tenant_id`         varchar(64)                          COMMENT '租户ID',
    `local_segment`     varchar(64) NOT NULL                 COMMENT '本地网段',
    `vpc_segment`       varchar(64) NOT NULL                 COMMENT 'vpc网段',
    `creator`           varchar(128)         DEFAULT ''      COMMENT 'erp/pin',
    `updater`           varchar(128)         DEFAULT '',
    `created_at`        datetime    NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at`        datetime    NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    `version`           bigint(20)  NOT NULL DEFAULT 1      COMMENT '用于进行版本控制',
    PRIMARY KEY (`id`)  USING BTREE
    ) COMMENT ='物理专线网段配置表' CHARACTER SET = utf8mb4
    COLLATE = utf8mb4_general_ci
    ENGINE = InnoDB;

CREATE TABLE IF NOT EXISTS `classic_link_pool` (
    `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '自增主键',
    `pool_id` varchar(64) NOT NULL COMMENT '网络互通资源池ID',
    `cidr0` varchar(64) NOT NULL COMMENT '网络互通网段0',
    `cidr1` varchar(64) NOT NULL COMMENT '网络互通网段1',
    `bgp_asn_start` int(11) NOT NULL COMMENT 'BGPASN开始',
    `bgp_asn_end` int(11) NOT NULL COMMENT 'BGP ASN结束',
    `vlan_start` int(11) NOT NULL COMMENT 'VLAN开始',
    `vlan_end` int(11) NOT NULL COMMENT 'VLAN结束',
    `creator` varchar(128) DEFAULT '' COMMENT 'erp/pin',
    `updater` varchar(128) DEFAULT '',
    `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    `version` bigint(20) NOT NULL DEFAULT '1' COMMENT '用于进行版本控制',
    `is_deleted` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否删除 0未删除/1已删除',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE KEY `group_id` (`pool_id`)
    )  COMMENT ='网络互通资源池' CHARACTER SET = utf8mb4
    COLLATE = utf8mb4_general_ci
    ENGINE = InnoDB;


CREATE TABLE IF NOT EXISTS `network_plan_target`  (
    `id`              bigint(20)      NOT NULL AUTO_INCREMENT COMMENT '自增主键',
    `target`          varchar(64)     NOT NULL COMMENT '唯一的target',
    `name`            varchar(255)    NOT NULL COMMENT '对应的名称',
    `parent_target`   varchar(64)     DEFAULT NULL COMMENT '上级target，可以为空',
    `is_only`         bit(1)          DEFAULT NULL COMMENT '是否只能有一个',
    `is_must`         bit(1)          DEFAULT NULL COMMENT '是否必须录入，不录入影响流程',
    `rule_name`       varchar(64)     DEFAULT NULL COMMENT '验证规则名称',
    `created_at`      datetime(0)     NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at`      datetime(0)     NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP(0),
    `version`         bigint(20)      NOT NULL DEFAULT 1 COMMENT '版本',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE INDEX `target`(`target`) USING BTREE
    ) COMMENT ='网络规划target分类表' CHARACTER SET = utf8mb4
    COLLATE = utf8mb4_general_ci
    ENGINE = InnoDB;

BEGIN;
REPLACE INTO `network_plan_target` (`id`, `target`, `name`, `parent_target`, `is_only`, `is_must`, `rule_name`, `created_at`, `updated_at`, `version`) VALUES
	(1, 'dns', 'DNS', NULL, b'1', b'1', 'dns', '2024-07-10 14:07:31', '2024-07-10 14:07:31', 1),
	(2, 'lbs', '负载均衡', NULL, b'0', b'1', 'lbs', '2024-07-10 14:07:31', '2024-07-10 14:07:31', 1),
	(3, 'ispBGPs', 'EIP_IPv4', NULL, b'0', b'1', 'region_plan', '2024-07-10 14:07:31', '2024-07-10 14:07:31', 1),
	(4, 'jdIPv6s', 'EIP_IPv6', NULL, b'0', b'0', 'region_plan', '2024-07-10 14:07:31', '2024-07-10 14:07:31', 1),
	(6, 'interPriOverlaps', 'PaaS网段规划', NULL, b'0', b'1', 'region_plan', '2024-07-10 14:07:31', '2024-07-10 14:07:31', 1),
	(7, 'inats', 'INAT网段规划', NULL, b'0', b'1', 'region_plan', '2024-07-10 14:07:31', '2024-07-10 14:07:31', 1),
	(8, 'bgpAsNumber', 'BGP_AS号规划', NULL, b'0', b'1', 'bgpAsNumber', '2024-07-10 14:07:31', '2024-07-10 14:07:31', 1),
	(9, 'k8sCalico', 'k8s_CNI_calico网段', NULL, b'0', b'1', 'k8sCalico', '2024-07-10 14:07:31', '2024-07-10 14:07:31', 1),
	(10, 'k8sClusterIp', 'K8s_ClusterIp网段', NULL, b'0', b'1', 'k8sClusterIp', '2024-07-10 14:07:31', '2024-07-10 14:07:31', 1),
	(11, 'compute', 'IaaS资源节点路由配置-计算节点数据口', NULL, b'0', b'0', 'rack_region_plan', '2024-07-10 14:07:31', '2024-07-10 14:07:31', 1),
	(12, 'vm2vr', 'IaaS资源节点路由配置-VM到VR的目的地网段(VV-IPv4)', NULL, b'0', b'0', 'rack_region_plan', '2024-07-10 14:07:31', '2024-07-10 14:07:31', 1),
	(13, 'vm2vrIPv6', 'IaaS资源节点路由配置-VM到VR的目的地网段(VV-IPv6)', NULL, b'0', b'0', 'rack_region_plan', '2024-07-10 14:07:31', '2024-07-10 14:07:31', 1),
	(14, 'dr2vr', 'IaaS资源节点路由配置-DR到VR的目的地网段(DV-IPv4)', NULL, b'0', b'0', 'rack_region_plan', '2024-07-10 14:07:31', '2024-07-10 14:07:31', 1),
	(15, 'dr2vrIPv6', 'IaaS资源节点路由配置-DR到VR的目的地网段(DV-IPv6)', NULL, b'0', b'0', 'rack_region_plan', '2024-07-10 14:07:31', '2024-07-10 14:07:31', 1),
	(16, 'natgw2vm', 'IaaS资源节点路由配置-NATGW到VM的目的网段(VN)', NULL, b'0', b'0', 'rack_region_plan', '2024-07-10 14:07:31', '2024-07-10 14:07:31', 1),
	(17, 'vr2bgw', 'IaaS资源节点路由配置-VR到BGW的目的网段(BV)', NULL, b'0', b'0', 'rack_region_plan', '2024-07-10 14:07:31', '2024-07-10 14:07:31', 1),
	(20, 'storage', '存储数据网段规划', NULL, b'0', b'0', 'rack_plan', '2024-07-10 14:07:31', '2024-07-10 14:07:31', 1),
	(22, 'sdnDRVR', 'DR配置-DR和VR通信(DV-IPv4)', '', b'0', b'1', 'rack_plan', '2024-07-10 14:07:31', '2024-07-10 14:07:31', 1),
	(23, 'sdnIPv6DRVR', 'DR配置-DR和VR通信(DV-IPv6)', '', b'0', b'0', 'rack_plan', '2024-07-10 14:07:31', '2024-07-10 14:07:31', 1),
	(24, 'sdnVRDR', 'VR配置-VR和DR通信(DV-IPv4)', 'dr2vr', b'0', b'1', 'rack_plan', '2024-07-10 14:07:31', '2024-07-10 14:07:31', 1),
	(25, 'sdnIPv6VRDR', 'VR配置-VR和DR通信(DV-IPv6)', 'dr2vrIPv6', b'0', b'0', 'rack_plan', '2024-07-10 14:07:31', '2024-07-10 14:07:31', 1),
	(26, 'sdnVRVM', 'VR配置-VR和VM通信(VV-IPv4)', 'vm2vr', b'0', b'1', 'rack_plan', '2024-07-10 14:07:31', '2024-07-10 14:07:31', 1),
	(27, 'sdnIPv6VRVM', 'VR配置-VR和VM通信(VV-IPv6)', 'vm2vrIPv6', b'0', b'0', 'rack_plan', '2024-07-10 14:07:31', '2024-07-10 14:07:31', 1),
	(28, 'sdnVRBGW', 'VR配置-VR和BGW通信(BV)', 'vr2bgw', b'0', b'0', 'rack_plan', '2024-07-10 14:07:31', '2024-07-10 14:07:31', 1),
	(29, 'sdnVRNATGW', 'NATGW配置-NATGW和VR通信(VN)', 'natgw2vm', b'0', b'1', 'rack_plan', '2024-07-10 14:07:31', '2024-07-10 14:07:31', 1),
	(30, 'sdnDRNATGW', 'NATGW配置-NATGW和DR通信(DN)', NULL, b'0', b'1', 'rack_plan', '2024-07-10 14:07:31', '2024-07-10 14:07:31', 1),
	(31, 'sdnDRSW', 'DR配置-DR和交换机通信(DS-IPv4)', NULL, b'0', b'1', 'rack_plan', '2024-07-10 14:07:31', '2024-07-10 14:07:31', 1),
	(32, 'sdnIPv6DRSW', 'DR配置-DR和交换机通信(DS-IPv6)', NULL, b'0', b'0', 'rack_plan', '2024-07-10 14:07:31', '2024-07-10 14:07:31', 1),
	(33, 'sdnIPv6DRBGP', 'DR配置-DR和BGP通信(DBGP-IPv6)', NULL, b'0', b'0', 'rack_plan', '2024-07-10 14:07:31', '2024-07-10 14:07:31', 1),
	(34, 'sdnBGWVR', 'BGW配置-BGW和VR通信', NULL, b'0', b'0', 'rack_plan', '2024-07-10 14:07:31', '2024-07-10 14:07:31', 1),
	(35, 'sdnBGWDXR', 'BGW配置-BGW和DxRouter通信', NULL, b'0', b'0', 'rack_plan', '2024-07-10 14:07:31', '2024-07-10 14:07:31', 1),
	(41, 'ntpSource', 'NTP时钟源', NULL, b'1', b'0', 'ntp', '2024-07-10 14:07:31', '2024-07-10 14:07:31', 1),
	(42, 'ntpVip', 'NTP-VIP', NULL, b'1', b'0', 'ntp', '2024-07-10 14:07:31', '2024-07-10 14:07:31', 1),
	(43, 'stardbLocal', 'stardb物理机网段', NULL, b'0', b'1', 'region_plan', '2024-07-10 14:07:31', '2024-07-10 14:07:31', 1),
	(44, 'stardbVip', 'stardbVip网段', NULL, b'0', b'1', 'region_plan', '2024-07-10 14:07:31', '2024-07-10 14:07:31', 1),
	(45, 'bgwTunnel', 'BGW和DXRouter建隧道的overlay网关', NULL, b'0', b'0', 'rack_region_plan', '2024-07-10 14:07:31', '2024-07-10 14:07:31', 1),
	(46, 'bgwToDLR', 'DLR和BGW建隧道的underlay网段', NULL, b'0', b'0', 'rack_region_plan', '2024-07-10 14:07:31', '2024-07-10 14:07:31', 1),
	(48, 'sdnBGWSegment', 'bgw和dxr建隧道的underlay网段', NULL, b'0', b'0', 'rack_plan', '2024-07-10 14:07:31', '2024-07-10 14:07:31', 1),
	(49, 'sdnVpnDr', 'vpn的dp口网关', NULL, b'0', b'0', 'rack_plan', '2024-07-10 14:07:31', '2024-07-10 14:07:31', 1),
	(50, 'sdnVpnBgw', 'vpn的bp口网关', NULL, b'0', b'0', 'rack_plan', '2024-07-10 14:07:31', '2024-07-10 14:07:31', 1),
	(51, 'clrTunnel', 'clr和dxr建隧道的overlay网关', NULL, b'0', b'0', 'rack_region_plan', '2024-07-10 14:07:31', '2024-07-10 14:07:31', 1);
COMMIT;

CREATE TABLE IF NOT EXISTS `network_plan_rule`  (
    `id`          bigint(20)  NOT NULL AUTO_INCREMENT COMMENT '自增主键',
    `rule_name`   varchar(64) NOT NULL COMMENT '唯一主键',
    `description` varchar(128) NOT NULL COMMENT '描述',
    `created_at`  datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at`  datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP(0),
    `version`     bigint(20)  NOT NULL DEFAULT 1 COMMENT '版本',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE INDEX `rule_name`(`rule_name`) USING BTREE
    ) COMMENT ='网络规划验证规则表' CHARACTER SET = utf8mb4
    COLLATE = utf8mb4_general_ci
    ENGINE = InnoDB;

BEGIN;
REPLACE INTO `network_plan_rule` (`id`, `rule_name`,`description`)
VALUES  (1, 'dns','DNS专用的验证规则'),
        (2, 'lbs','负载均衡的验证规则'),
        (3, 'region_plan','region级别网络规划的通用验证规则'),
        (4, 'rack_region_plan','机柜的region级网络规划验证规则'),
        (5, 'rack_plan','机柜通用的验证规则'),
        (6, 'bgpAsNumber','bgp as号验证规则'),
        (7, 'k8sCalico','k8s calico 验证规则'),
        (8, 'k8sClusterIp','k8s cluster ip 验证规则'),
        (9, 'ntp','NTP专用的验证规则');

COMMIT;

CREATE TABLE IF NOT EXISTS `network_plan_rule_field`  (
    `id`              bigint(20)      NOT NULL AUTO_INCREMENT COMMENT '自增主键',
    `rule_name`       varchar(64)     NOT NULL COMMENT '所属target校验规则名称',
    `field`           varchar(255)    NOT NULL COMMENT '字段名称',
    `field_desc`      varchar(255)    DEFAULT NULL COMMENT '字段描述，用于导出的模板',
    `is_not_null`     bit(1)          DEFAULT NULL COMMENT '是否为非空',
    `is_global_only`  bit(1)          DEFAULT NULL COMMENT '是否必须全局唯一',
    `is_target_only`  bit(1)          DEFAULT NULL COMMENT '是否同一target下唯一',
    `value_type`      varchar(64)     DEFAULT 'string' COMMENT '值类型，string:字符,number:数字,cidr:网段,ip:IP地址',
    `is_foreign`      bit(1)          DEFAULT NULL COMMENT '是否需要关联外部查询',
    `foreign_table`   varchar(255)    DEFAULT NULL COMMENT '外部查询的表名',
    `created_at`      datetime(0)     NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at`      datetime(0)     NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP(0),
    `version`         bigint(20)      NOT NULL DEFAULT 1 COMMENT '版本',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE INDEX `target`(`rule_name`, `field`) USING BTREE
    ) COMMENT ='网络规划字段验证规则表' CHARACTER SET = utf8mb4
    COLLATE = utf8mb4_general_ci
    ENGINE = InnoDB;

BEGIN;
REPLACE INTO `network_plan_rule_field` (`id`, `rule_name`,`field`, `field_desc`,`is_not_null`, `is_global_only`, `is_target_only`, `value_type`)
values (1,'dns', 'ref_id', 'uuid', b'0', b'1', b'0', 'string'),
       (2,'dns', 'overlay_ip', 'dnsOverlayIp', b'1', b'1', b'0', 'cidr'),
       (3,'dns', 'underlay_ip', 'dnsUnderlayIp', b'1', b'1', b'0', 'cidr'),
       (4,'lbs', 'ref_id', 'uuid', b'0', b'1', b'0', 'string'),
       (5,'lbs', 'overlay_ip', 'overlayIp', b'1', b'1', b'0', 'cidr'),
       (6,'lbs', 'underlay_ip', 'underlayIp', b'1', b'1', b'0', 'cidr'),
       (7,'lbs', 'region', 'region', b'1', b'0', b'0', 'string'),
       (8,'region_plan', 'ref_id', 'uuid', b'0', b'1', b'0', 'string'),
       (9,'region_plan', 'name', '名称', b'0', b'1', b'0', 'string'),
       (10,'region_plan', 'subnet', '网段', b'1', b'1', b'0', 'cidr'),
       (11,'region_plan', 'region', 'region', b'1', b'0', b'0', 'string'),
       (12,'bgpAsNumber', 'ref_id', 'uuid', b'0', b'1', b'0', 'string'),
       (13,'bgpAsNumber', 'res_id', '设备', b'1', b'0', b'1', 'string'),
       (14,'bgpAsNumber', 'attribute_1', 'asNumber', b'1', b'0', b'0', 'number'),
       (15,'bgpAsNumber', 'res_type', '资源类型', b'1', b'0', b'0', 'string'),
       (16,'bgpAsNumber', 'description', '描述', b'1', b'0', b'0', 'string'),
       (17,'k8sCalico', 'ref_id', 'uuid', b'0', b'1', b'0', 'string'),
       (18,'k8sCalico', 'res_id', '设备', b'1', b'0', b'1', 'string'),
       (19,'k8sCalico', 'subnet', '网段', b'1', b'1', b'0', 'cidr'),
       (20,'k8sCalico', 'res_type', '资源类型', b'1', b'0', b'0', 'string'),
       (21,'k8sClusterIp', 'ref_id', 'uuid', b'0', b'1', b'0', 'string'),
       (22,'k8sClusterIp', 'subnet', '网段', b'1', b'1', b'0', 'cidr'),
       (23,'k8sClusterIp', 'attribute_1', 'AZ', b'1', b'0', b'0', 'string'),
       (24,'rack_region_plan', 'ref_id', 'uuid', b'0', b'1', b'0', 'string'),
       (25,'rack_region_plan', 'subnet', '网段', b'1', b'0', b'1', 'cidr'),
       (26,'rack_region_plan', 'region', 'region', b'1', b'0', b'1', 'string'),
       (27,'rack_plan', 'ref_id', 'uuid', b'0', b'1', b'0', 'string'),
       (28,'rack_plan', 'subnet', '网段', b'1', b'1', b'0', 'cidr'),
       (29,'rack_plan', 'res_id', '机柜', b'1', b'0', b'1', 'string'),
       (30,'rack_plan', 'gateway', '网关', b'1', b'0', b'0', 'ip'),
       (31,'rack_plan', 'vlan', 'vlan', b'1', b'0', b'0', 'number'),
       (32,'ntp', 'ref_id', 'uuid', b'0', b'1', b'0', 'string'),
       (33,'ntp', 'overlay_ip', 'firstSubnet', b'1', b'1', b'0', 'cidr'),
       (34,'ntp', 'underlay_ip', 'lastSubnet', b'1', b'1', b'0', 'cidr');

REPLACE INTO `network_plan_rule_field` (`id`, `rule_name`,`field`, `field_desc`,`is_not_null`, `is_global_only`, `is_target_only`, `value_type`, `is_foreign`, `foreign_table`)
    values (23,'k8sClusterIp', 'attribute_1', 'AZ', b'1', b'0', b'0', 'string', b'1','odin_availability_zone#uuid');
COMMIT;


CREATE TABLE IF NOT EXISTS `network_vlan`
(
    `id`                 bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '自增ID',
    `slot_id`            varchar(32)         NOT NULL COMMENT 'slot表唯一ID',
    `vlan_id`            bigint(20)          DEFAULT NULL COMMENT 'VLAN',
    `vlan_name`          varchar(32)         DEFAULT NULL COMMENT 'VLAN名称',
    `route_if_index`     bigint(20)          DEFAULT NULL COMMENT '路由端口index',
    `tagged_port_list`   varchar(128)        DEFAULT NULL COMMENT 'TaggedPortList',
    `ip_addr`            varchar(128)        DEFAULT NULL COMMENT 'ip',
    `created_at`         datetime            NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at`         datetime            NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `version`            bigint(20)          NOT NULL DEFAULT 1 COMMENT '版本',
    PRIMARY KEY (`id`) USING BTREE
    ) COMMENT ='网络设备VLAN表' CHARACTER SET = utf8mb4
    ENGINE = InnoDB
    ROW_FORMAT = DYNAMIC;

call alter_column("phy_direct_connect","status","tinyint",1,1,"专线是否启用，1：启用，2：未启用");

CREATE TABLE IF NOT EXISTS `network_config`  (
    `id`         bigint(20) NOT NULL AUTO_INCREMENT COMMENT '自增主键',
    `manage_ip`  varchar(128)   NOT NULL COMMENT '设备的IP',
    `config`     blob COMMENT '设备的配置',
    `created_at` datetime(0) NOT NULL COMMENT '创建时间',
    `version`    bigint(20) NOT NULL DEFAULT 1 COMMENT '版本',
    PRIMARY KEY (`id`) USING BTREE,
    INDEX `manage_ip`(`manage_ip`) USING BTREE,
    INDEX `created_at`(`created_at`) USING BTREE
    )  COMMENT ='网络设备配置表' CHARACTER SET = utf8mb4
    ENGINE = InnoDB
    ROW_FORMAT = DYNAMIC;

CREATE TABLE IF NOT EXISTS `network_plan_status` (
    `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
    `network_plan_id` varchar(64) COLLATE utf8_bin NOT NULL COMMENT 'network plan id',
    `status` varchar(8) COLLATE utf8_bin DEFAULT NULL COMMENT '状态',
    `status_info` varchar(128) COLLATE utf8_bin DEFAULT NULL COMMENT '状态详情',
    `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `network_plan_id` (`network_plan_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='网络规划状态';

CREATE TABLE IF NOT EXISTS `odin_region` (
  `uuid` varchar(64) NOT NULL COMMENT 'uuid',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `version` int unsigned NOT NULL DEFAULT '1' COMMENT '乐观锁',
  `name_cn` varchar(64) DEFAULT '' COMMENT '地域中文名称',
  `status` enum('building','available') DEFAULT 'building' COMMENT '状态',
  `is_default` enum('no','yes') DEFAULT 'no' COMMENT '是否为默认',
  `seq_no` tinyint unsigned DEFAULT '255' COMMENT '建设顺序,越小越靠前',
  `description` varchar(255) DEFAULT '' COMMENT '描述',
  PRIMARY KEY (`uuid`),
  UNIQUE KEY `region_name_cn` (`name_cn`),
  KEY `region_status` (`status`),
  KEY `region_is_default` (`is_default`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='地域管理';

CREATE TABLE IF NOT EXISTS `odin_availability_zone` (
  `uuid` varchar(64) NOT NULL COMMENT 'uuid',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `version` int unsigned NOT NULL DEFAULT '1' COMMENT '乐观锁',
  `name_cn` varchar(32) DEFAULT '' COMMENT '可用区中文名称',
  `status` enum('building','available') DEFAULT 'building' COMMENT '状态',
  `is_default` enum('no','yes') DEFAULT 'no' COMMENT '是否为默认',
  `description` varchar(255) DEFAULT '' COMMENT '描述',
  `region_uuid` varchar(64) DEFAULT NULL COMMENT '地域UUID',
  `is_visible` enum('no','yes') DEFAULT 'yes' COMMENT '是否为可见',
  `is_arbitration` enum('no','yes') DEFAULT 'no' COMMENT '是否为仲裁AZ',
  `seq_no` tinyint unsigned DEFAULT '255' COMMENT '建设顺序,越小越靠前',
  PRIMARY KEY (`uuid`),
  UNIQUE KEY `availabilityzone_name_cn` (`name_cn`),
  KEY `availabilityzone_status` (`status`),
  KEY `availabilityzone_region_uuid_is_default` (`region_uuid`,`is_default`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='可用区管理';

CREATE TABLE IF NOT EXISTS `odin_datacenter` (
  `uuid` varchar(64) NOT NULL COMMENT 'uuid',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `version` int unsigned NOT NULL DEFAULT '1' COMMENT '乐观锁',
  `name` varchar(64) DEFAULT '' COMMENT '机房名称',
  `address` varchar(128) DEFAULT '' COMMENT '机房地址',
  `level` varchar(64) DEFAULT '' COMMENT '等级',
  `provider` varchar(64) DEFAULT '' COMMENT '运营商',
  `province` varchar(64) DEFAULT '' COMMENT '省份',
  `city` varchar(64) DEFAULT '' COMMENT '地级市',
  `electrical_spec` varchar(64) DEFAULT '' COMMENT '机房的电力规格',
  `room_count` int unsigned DEFAULT NULL COMMENT '房间数量',
  PRIMARY KEY (`uuid`),
  UNIQUE KEY `datacenter_name` (`name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='机房管理';

CREATE TABLE IF NOT EXISTS `odin_rack` (
  `uuid` varchar(64) NOT NULL COMMENT 'uuid',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `version` int unsigned NOT NULL DEFAULT '1' COMMENT '乐观锁',
  `rack_number` varchar(64) DEFAULT '' COMMENT '机柜编号',
  `electrical_spec` varchar(64) DEFAULT NULL COMMENT '机柜的电力规格',
  `u_count` int unsigned DEFAULT '0' COMMENT '总u位数',
  `room_number` varchar(64) DEFAULT '' COMMENT '房间号',
  `network_address` varchar(64) DEFAULT '' COMMENT '网络地址',
  `subnet_mask` enum('unknown','*********','*********','*********','240.0.0.0','*********','*********','*********','*********','***********','***********','***********','***********','***********','***********','***********','***********','*************','*************','*************','*************','*************','*************','*************','*************','***************','***************','***************','***************','***************','***************','***************','***************') DEFAULT NULL COMMENT '子网掩码',
  `tor_ip` varchar(64) DEFAULT '' COMMENT '网关IP',
  `availability_zone_uuid` varchar(64) DEFAULT NULL COMMENT '可用区UUID',
  `datacenter_uuid` varchar(64) DEFAULT NULL COMMENT '机房UUID',
  `region_uuid` varchar(64) DEFAULT NULL COMMENT '地域UUID',
  PRIMARY KEY (`uuid`),
  UNIQUE KEY `rack_rack_number_datacenter_uuid` (`rack_number`,`datacenter_uuid`),
  KEY `rack_region_uuid` (`region_uuid`),
  KEY `rack_availability_zone_uuid` (`availability_zone_uuid`),
  KEY `rack_datacenter_uuid` (`datacenter_uuid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='机柜管理';

CREATE TABLE IF NOT EXISTS `odin_network_equipment` (
  `uuid` varchar(64) NOT NULL COMMENT 'uuid',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `version` int unsigned NOT NULL DEFAULT '1' COMMENT '乐观锁',
  `serial_number` varchar(64) DEFAULT '' COMMENT '序列号',
  `brand` varchar(64) DEFAULT '' COMMENT '品牌',
  `model` varchar(64) DEFAULT '' COMMENT '型号',
  `outofband_ip` varchar(64) DEFAULT NULL COMMENT '带外IP',
  `manage_ip` varchar(64) DEFAULT '' COMMENT '管理IP',
  `asset_state` enum('failure','maintenance','offline','online') DEFAULT 'offline' COMMENT '设备状态',
  `u_count` int unsigned DEFAULT '0' COMMENT '总u位数',
  `mc_name` varchar(64) DEFAULT NULL COMMENT 'Manufacturer Contact,厂商联系人姓名',
  `mc_phone` varchar(64) DEFAULT NULL COMMENT '厂商联系人电话',
  `mc_email` varchar(64) DEFAULT NULL COMMENT '厂商联系人邮件',
  `procurement_time` date DEFAULT NULL COMMENT '采购时间',
  `warranty_start` date DEFAULT NULL COMMENT '维保开始时间',
  `warranty_end` date DEFAULT NULL COMMENT '维保结束时间',
  `availability_zone_uuid` varchar(64) DEFAULT NULL COMMENT '可用区UUID',
  `datacenter_uuid` varchar(64) DEFAULT NULL COMMENT '机房UUID',
  `rack_uuid` varchar(64) DEFAULT NULL COMMENT '机柜UUID',
  `region_uuid` varchar(64) DEFAULT NULL COMMENT '地域UUID',
  PRIMARY KEY (`uuid`),
  UNIQUE KEY `networkequipment_serial_number` (`serial_number`),
  KEY `networkequipment_region_uuid` (`region_uuid`),
  KEY `networkequipment_availability_zone_uuid` (`availability_zone_uuid`),
  KEY `networkequipment_datacenter_uuid` (`datacenter_uuid`),
  KEY `networkequipment_rack_uuid` (`rack_uuid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='网络设备管理';

CREATE TABLE IF NOT EXISTS `odin_network_equipment_connection` (
  `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `version` int unsigned NOT NULL DEFAULT '1' COMMENT '乐观锁',
  `conn_type` enum('ssh','netconf') DEFAULT NULL COMMENT '连接方式',
  `port` int unsigned DEFAULT NULL COMMENT '连接端口',
  `username` varchar(255) DEFAULT NULL COMMENT '登录用户名',
  `ciphertext` varchar(255) DEFAULT NULL COMMENT '登录密码',
  `secret_key` varchar(255) DEFAULT NULL COMMENT '登录秘钥',
  `network_equipment_uuid` varchar(64) DEFAULT NULL COMMENT '网络设备UUID',
  PRIMARY KEY (`id`),
  KEY `networkequipmentconnection_network_equipment_uuid` (`network_equipment_uuid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='网络设备连接信息管理';

CREATE TABLE IF NOT EXISTS `odin_server` (
  `uuid` varchar(64) NOT NULL COMMENT 'uuid',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `version` int unsigned NOT NULL DEFAULT '1' COMMENT '乐观锁',
  `host_name` varchar(128) DEFAULT NULL COMMENT '主机名',
  `serial_number` varchar(64) DEFAULT '' COMMENT '机架编号',
  `brand` varchar(64) DEFAULT NULL COMMENT '品牌',
  `model` varchar(64) DEFAULT NULL COMMENT '型号',
  `system_ip` varchar(64) DEFAULT '' COMMENT '系统IP',
  `system_subnet_mask` enum('unknown','*********','*********','*********','240.0.0.0','*********','*********','*********','*********','***********','***********','***********','***********','***********','***********','***********','***********','*************','*************','*************','*************','*************','*************','*************','*************','***************','***************','***************','***************','***************','***************','***************','***************') DEFAULT NULL COMMENT '系统子网掩码',
  `system_mac` varchar(64) DEFAULT '' COMMENT '系统MAC',
  `manage_ip` varchar(64) DEFAULT '' COMMENT '管理IP',
  `data_ip` varchar(64) DEFAULT '' COMMENT '数据IP',
  `data_subnet_mask` enum('unknown','*********','*********','*********','240.0.0.0','*********','*********','*********','*********','***********','***********','***********','***********','***********','***********','***********','***********','*************','*************','*************','*************','*************','*************','*************','*************','***************','***************','***************','***************','***************','***************','***************','***************') DEFAULT NULL COMMENT '数据子网掩码',
  `smartnic_man_ip` varchar(64) DEFAULT '' COMMENT 'SmartNIC管理IP',
  `smartnic_man_ip_gateway` varchar(64) DEFAULT '' COMMENT 'SmartNIC管理IP网关',
  `smartnic_data_ip` varchar(64) DEFAULT '' COMMENT 'SmartNIC数据IP',
  `u_count` int unsigned DEFAULT '0' COMMENT '总u位数',
  `asset_state` enum('failure','maintenance','offline','online') DEFAULT 'offline' COMMENT '设备状态',
  `mc_name` varchar(64) DEFAULT NULL COMMENT 'Manufacturer Contact,厂商联系人姓名',
  `mc_phone` varchar(64) DEFAULT NULL COMMENT '厂商联系人电话',
  `mc_email` varchar(64) DEFAULT NULL COMMENT '厂商联系人邮件',
  `procurement_time` date DEFAULT NULL COMMENT '采购时间',
  `warranty_start` date DEFAULT NULL COMMENT '维保开始时间',
  `warranty_end` date DEFAULT NULL COMMENT '维保结束时间',
  `arbitration_az` enum('no','yes') DEFAULT 'no' COMMENT '服务器标记为逻辑仲裁区',
  `server_category` enum('inner','jdcloud') DEFAULT 'inner' COMMENT '服务器分类,若非内外部机器则一定是逻辑仲裁区的节点',
  `availability_zone_uuid` varchar(64) DEFAULT NULL COMMENT '可用区UUID',
  `real_availability_zone_uuid` varchar(64) DEFAULT NULL COMMENT '真实可用区UUID,将与idc-cm废弃',
  `datacenter_uuid` varchar(64) DEFAULT NULL COMMENT '机房UUID',
  `rack_uuid` varchar(64) DEFAULT NULL COMMENT '机架UUID',
  `region_uuid` varchar(64) DEFAULT NULL COMMENT '地域UUID',
  PRIMARY KEY (`uuid`),
  UNIQUE KEY `server_serial_number` (`serial_number`),
  KEY `server_region_uuid` (`region_uuid`),
  KEY `server_availability_zone_uuid` (`availability_zone_uuid`),
  KEY `server_datacenter_uuid` (`datacenter_uuid`),
  KEY `server_rack_uuid` (`rack_uuid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='服务器管理';

CREATE TABLE IF NOT EXISTS `odin_hardware_role_metadata` (
  `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `version` int unsigned NOT NULL DEFAULT '1' COMMENT '乐观锁',
  `role` varchar(128) DEFAULT '' COMMENT '角色名称',
  `metadata_key` varchar(64) DEFAULT '' COMMENT '标签键',
  `metadata_value` varchar(64) DEFAULT '' COMMENT '标签值',
  `kind` enum('server','network','centralizedStorage') DEFAULT NULL COMMENT '模块名',
  `serial_number` varchar(64) DEFAULT '' COMMENT '序列号,将与idc-cm废弃',
  `uuid` varchar(64) DEFAULT NULL COMMENT 'UUID',
  PRIMARY KEY (`id`),
  KEY `hardwarerolemetadata_uuid` (`uuid`),
  KEY `hardwarerolemetadata_kind` (`kind`),
  KEY `hardwarerolemetadata_serial_number` (`serial_number`),
  KEY `hardwarerolemetadata_role_metadata_key_metadata_value` (`role`,`metadata_key`,`metadata_value`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='硬件角色标签关联';

CREATE TABLE IF NOT EXISTS `odin_network_plan` (
  `uuid` varchar(64) NOT NULL COMMENT 'uuid',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `version` int unsigned NOT NULL DEFAULT '1' COMMENT '乐观锁',
  `name` varchar(64) DEFAULT NULL COMMENT '名称,可为空',
  `target_id` varchar(64) DEFAULT NULL COMMENT '规划的目标,比如ark、dns等',
  `subnet_cidr` varchar(64) DEFAULT NULL COMMENT 'IPv4子网CIDR/IPv6',
  `subnet_mask` enum('unknown','*********','*********','*********','240.0.0.0','*********','*********','*********','*********','***********','***********','***********','***********','***********','***********','***********','***********','*************','*************','*************','*************','*************','*************','*************','*************','***************','***************','***************','***************','***************','***************','***************','***************','800:00:00:00:00:00:00:00','c00:00:00:00:00:00:00:00','e00:00:00:00:00:00:00:00','f00:00:00:00:00:00:00:00','f80:00:00:00:00:00:00:00','fc0:00:00:00:00:00:00:00','fe0:00:00:00:00:00:00:00','ff0:00:00:00:00:00:00:00','ff80:00:00:00:00:00:00:00','ffc0:00:00:00:00:00:00:00','ffe0:00:00:00:00:00:00:00','fff0:00:00:00:00:00:00:00','fff8:00:00:00:00:00:00:00','fffc:00:00:00:00:00:00:00','fffe:00:00:00:00:00:00:00','ffff:00:00:00:00:00:00:00','ffff:800:00:00:00:00:00:00','ffff:c00:00:00:00:00:00:00','ffff:e00:00:00:00:00:00:00','ffff:f00:00:00:00:00:00:00','ffff:f80:00:00:00:00:00:00','ffff:fc0:00:00:00:00:00:00','ffff:fe0:00:00:00:00:00:00','ffff:ff0:00:00:00:00:00:00','ffff:ff80:00:00:00:00:00:00','ffff:ffc0:00:00:00:00:00:00','ffff:ffe0:00:00:00:00:00:00','ffff:fff0:00:00:00:00:00:00','ffff:fff8:00:00:00:00:00:00','ffff:fffc:00:00:00:00:00:00','ffff:fffe:00:00:00:00:00:00','ffff:ffff:00:00:00:00:00:00','ffff:ffff:800:00:00:00:00:00','ffff:ffff:c00:00:00:00:00:00','ffff:ffff:e00:00:00:00:00:00','ffff:ffff:f00:00:00:00:00:00','ffff:ffff:f80:00:00:00:00:00','ffff:ffff:fc0:00:00:00:00:00','ffff:ffff:fe0:00:00:00:00:00','ffff:ffff:ff0:00:00:00:00:00','ffff:ffff:ff80:00:00:00:00:00','ffff:ffff:ffc0:00:00:00:00:00','ffff:ffff:ffe0:00:00:00:00:00','ffff:ffff:fff0:00:00:00:00:00','ffff:ffff:fff8:00:00:00:00:00','ffff:ffff:fffc:00:00:00:00:00','ffff:ffff:fffe:00:00:00:00:00','ffff:ffff:ffff:00:00:00:00:00','ffff:ffff:ffff:800:00:00:00:00','ffff:ffff:ffff:c00:00:00:00:00','ffff:ffff:ffff:e00:00:00:00:00','ffff:ffff:ffff:f00:00:00:00:00','ffff:ffff:ffff:f80:00:00:00:00','ffff:ffff:ffff:fc0:00:00:00:00','ffff:ffff:ffff:fe0:00:00:00:00','ffff:ffff:ffff:ff0:00:00:00:00','ffff:ffff:ffff:ff80:00:00:00:00','ffff:ffff:ffff:ffc0:00:00:00:00','ffff:ffff:ffff:ffe0:00:00:00:00','ffff:ffff:ffff:fff0:00:00:00:00','ffff:ffff:ffff:fff8:00:00:00:00','ffff:ffff:ffff:fffc:00:00:00:00','ffff:ffff:ffff:fffe:00:00:00:00','ffff:ffff:ffff:ffff:00:00:00:00','ffff:ffff:ffff:ffff:800:00:00:00','ffff:ffff:ffff:ffff:c00:00:00:00','ffff:ffff:ffff:ffff:e00:00:00:00','ffff:ffff:ffff:ffff:f00:00:00:00','ffff:ffff:ffff:ffff:f80:00:00:00','ffff:ffff:ffff:ffff:fc0:00:00:00','ffff:ffff:ffff:ffff:fe0:00:00:00','ffff:ffff:ffff:ffff:ff0:00:00:00','ffff:ffff:ffff:ffff:ff80:00:00:00','ffff:ffff:ffff:ffff:ffc0:00:00:00','ffff:ffff:ffff:ffff:ffe0:00:00:00','ffff:ffff:ffff:ffff:fff0:00:00:00','ffff:ffff:ffff:ffff:fff8:00:00:00','ffff:ffff:ffff:ffff:fffc:00:00:00','ffff:ffff:ffff:ffff:fffe:00:00:00','ffff:ffff:ffff:ffff:ffff:00:00:00','ffff:ffff:ffff:ffff:ffff:800:00:00','ffff:ffff:ffff:ffff:ffff:c00:00:00','ffff:ffff:ffff:ffff:ffff:e00:00:00','ffff:ffff:ffff:ffff:ffff:f00:00:00','ffff:ffff:ffff:ffff:ffff:f80:00:00','ffff:ffff:ffff:ffff:ffff:fc0:00:00','ffff:ffff:ffff:ffff:ffff:fe0:00:00','ffff:ffff:ffff:ffff:ffff:ff0:00:00','ffff:ffff:ffff:ffff:ffff:ff80:00:00','ffff:ffff:ffff:ffff:ffff:ffc0:00:00','ffff:ffff:ffff:ffff:ffff:ffe0:00:00','ffff:ffff:ffff:ffff:ffff:fff0:00:00','ffff:ffff:ffff:ffff:ffff:fff8:00:00','ffff:ffff:ffff:ffff:ffff:fffc:00:00','ffff:ffff:ffff:ffff:ffff:fffe:00:00','ffff:ffff:ffff:ffff:ffff:ffff:00:00','ffff:ffff:ffff:ffff:ffff:ffff:800:00','ffff:ffff:ffff:ffff:ffff:ffff:c00:00','ffff:ffff:ffff:ffff:ffff:ffff:e00:00','ffff:ffff:ffff:ffff:ffff:ffff:f00:00','ffff:ffff:ffff:ffff:ffff:ffff:f80:00','ffff:ffff:ffff:ffff:ffff:ffff:fc0:00','ffff:ffff:ffff:ffff:ffff:ffff:fe0:00','ffff:ffff:ffff:ffff:ffff:ffff:ff0:00','ffff:ffff:ffff:ffff:ffff:ffff:ff80:00','ffff:ffff:ffff:ffff:ffff:ffff:ffc0:00','ffff:ffff:ffff:ffff:ffff:ffff:ffe0:00','ffff:ffff:ffff:ffff:ffff:ffff:fff0:00','ffff:ffff:ffff:ffff:ffff:ffff:fff8:00','ffff:ffff:ffff:ffff:ffff:ffff:fffc:00','ffff:ffff:ffff:ffff:ffff:ffff:fffe:00','ffff:ffff:ffff:ffff:ffff:ffff:ffff:00','ffff:ffff:ffff:ffff:ffff:ffff:ffff:800','ffff:ffff:ffff:ffff:ffff:ffff:ffff:c00','ffff:ffff:ffff:ffff:ffff:ffff:ffff:e00','ffff:ffff:ffff:ffff:ffff:ffff:ffff:f00','ffff:ffff:ffff:ffff:ffff:ffff:ffff:f80','ffff:ffff:ffff:ffff:ffff:ffff:ffff:fc0','ffff:ffff:ffff:ffff:ffff:ffff:ffff:fe0','ffff:ffff:ffff:ffff:ffff:ffff:ffff:ff0','ffff:ffff:ffff:ffff:ffff:ffff:ffff:ff80','ffff:ffff:ffff:ffff:ffff:ffff:ffff:ffc0','ffff:ffff:ffff:ffff:ffff:ffff:ffff:ffe0','ffff:ffff:ffff:ffff:ffff:ffff:ffff:fff0','ffff:ffff:ffff:ffff:ffff:ffff:ffff:fff8','ffff:ffff:ffff:ffff:ffff:ffff:ffff:fffc','ffff:ffff:ffff:ffff:ffff:ffff:ffff:fffe','ffff:ffff:ffff:ffff:ffff:ffff:ffff:ffff') DEFAULT NULL COMMENT '子网掩码IPv4/IPv6',
  `gateway` varchar(64) DEFAULT NULL COMMENT '网关',
  `vlan` int unsigned DEFAULT NULL COMMENT 'vlan',
  `res_kind` enum('rack','server','switch') DEFAULT NULL COMMENT '资源类型',
  `res_sn` varchar(64) DEFAULT NULL COMMENT '资源序列号,将与idc-cm废弃',
  `underlay_ip` varchar(64) DEFAULT NULL COMMENT 'underlay ip',
  `overlay_ip` varchar(64) DEFAULT NULL COMMENT 'overlay ip',
  `total_ip_count` int unsigned DEFAULT NULL COMMENT 'total_ip_count',
  `description` varchar(255) DEFAULT NULL COMMENT '描述',
  `attribute_1` varchar(255) DEFAULT NULL COMMENT '扩展属性1',
  `attribute_2` varchar(255) DEFAULT NULL COMMENT '扩展属性2',
  `attribute_3` varchar(255) DEFAULT NULL COMMENT '扩展属性3',
  `res_uuid` varchar(64) DEFAULT NULL COMMENT '如果是针对某个资源的网络规划，需要设置该字段，比如机架、服务器等',
  `region_uuid` varchar(64) DEFAULT NULL COMMENT '地域UUID',
  PRIMARY KEY (`uuid`),
  UNIQUE KEY `networkplan_subnet_cidr` (`subnet_cidr`),
  KEY `networkplan_res_uuid` (`res_uuid`),
  KEY `networkplan_res_kind` (`res_kind`),
  KEY `networkplan_res_sn` (`res_sn`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='网络规划管理';

CREATE TABLE IF NOT EXISTS `odin_eip_used_record` (
  `uuid` varchar(64) NOT NULL COMMENT 'uuid',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `version` int unsigned NOT NULL DEFAULT '1' COMMENT '乐观锁',
  `ip_address` varchar(64) DEFAULT NULL COMMENT 'IP占用地址',
  `start_ip_address` varchar(64) DEFAULT NULL COMMENT '占用起始地址',
  `count` int unsigned DEFAULT NULL COMMENT '占用数量',
  `state` enum('no','yes') DEFAULT 'yes' COMMENT '是否占用',
  `reason` varchar(64) DEFAULT NULL COMMENT '占用原因',
  `start_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '占用时间',
  `end_time` timestamp NULL DEFAULT NULL COMMENT '归还日期,进行‘归还’操作时,添加归还日期',
  `res_uuid` varchar(64) DEFAULT NULL COMMENT '如果是针对某个资源的网络规划EIP',
  `res_kind` varchar(64) DEFAULT NULL COMMENT '资源类型',
  `pin` varchar(64) DEFAULT NULL COMMENT 'PIN',
  `network_plan_uuid` varchar(64) DEFAULT NULL COMMENT '网络规划UUID',
  `region_uuid` varchar(64) DEFAULT NULL COMMENT '地域UUID',
  PRIMARY KEY (`uuid`),
  KEY `eipusedrecord_res_uuid` (`res_uuid`),
  KEY `eipusedrecord_res_kind` (`res_kind`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='弹性EIP使用记录';

TRUNCATE `odin_region`;
INSERT INTO `odin_region` (`uuid`, `created_at`, `version`, `name_cn`, `status`, `is_default`, `seq_no`, `description`)
SELECT `uuid`, `created_at`, `version`, `cn_name`, `status`,
  IF(`is_default` = '1', 'yes', 'no'), IF(`id`>255,255,`id`), `description`
FROM `region`;

TRUNCATE `odin_availability_zone`;
INSERT INTO `odin_availability_zone` (`uuid`, `created_at`, `version`, `name_cn`, `status`, `is_default`, `is_visible`, `is_arbitration`, `description`, `region_uuid`, `seq_no`)
SELECT `uuid`, `created_at`, `version`, `cn_name`, `status`,
  IF(`is_default` = '1', 'yes', 'no'), IF(RIGHT(`uuid`, 12) = '-arbitration', 'no', 'yes'), IF(RIGHT(`uuid`, 12) = '-arbitration', 'yes', 'no'), `description`, `region_uuid`, IF(`id`>255,255,`id`)
FROM `available_zone`;

TRUNCATE `odin_datacenter`;
INSERT INTO `odin_datacenter` (`uuid`, `created_at`, `version`, `name`, `address`, `level`, `provider`, `province`, `city`, `electrical_spec`, `room_count`)
SELECT `uuid`, `created_at`, `version`, `name`, `address`, `level`, `provider`, `province`, `city`, `electrical_spec`, `room_count`
FROM `datacenter`;

TRUNCATE `odin_rack`;
INSERT INTO `odin_rack` (`uuid`, `created_at`, `version`, `rack_number`, `electrical_spec`, `u_count`, `room_number`, `network_address`, `subnet_mask`, `tor_ip`, `availability_zone_uuid`, `datacenter_uuid`, `region_uuid`)
SELECT `uuid`, `created_at`, `version`, `rack_number`, `electrical_spec`, `u_count`, `room_number`, `network`, `network_mask`, `tor_ip`, `az_uuid`, `datacenter_uuid`, `region_uuid`
FROM `rack`;

TRUNCATE `odin_network_equipment`;
INSERT INTO `odin_network_equipment` (`uuid`, `created_at`, `version`, `serial_number`, `brand`, `model`, `outofband_ip`, `manage_ip`, `asset_state`, `u_count`, `mc_name`, `mc_phone`, `mc_email`, `procurement_time`, `warranty_start`, `warranty_end`, `availability_zone_uuid`, `datacenter_uuid`, `rack_uuid`, `region_uuid`)
SELECT `uuid`, `created_at`, `version`, `serial_number`, `brand`, `model`, `outofband_ip`, `manage_ip`,
  CASE asset_state WHEN 1 THEN 'failure' WHEN 2 THEN 'maintenance' WHEN 3 THEN 'offline' WHEN 4 THEN 'online' END,
  `u_count`, `mc_name`, `mc_phone`, `mc_email`,
  IF(`procurement_time` = '2006-01-02', '1970-01-01', `procurement_time`),
  IF(`warranty_start` = '2006-01-02', '1970-01-01', `warranty_start`),
  IF(`warranty_end` = '2006-01-02', '1970-01-01', `warranty_end`),
  `az_uuid`, `datacenter_uuid`, `rack_uuid`, `region_uuid`
FROM `network_equipment`;

TRUNCATE `odin_network_equipment_connection`;
INSERT INTO `odin_network_equipment_connection` (`created_at`, `version`, `conn_type`, `port`, `username`, `ciphertext`, `secret_key`, `network_equipment_uuid`)
SELECT `created_at`, `version`, `conn_type`, `port`, `user_name`, `password`, `secret_key`, `device_id`
FROM `device_conn`;

TRUNCATE `odin_server`;
INSERT INTO `odin_server` (`uuid`, `created_at`, `version`, `host_name`, `serial_number`, `brand`, `model`, `system_ip`, `system_subnet_mask`, `system_mac`, `manage_ip`, `data_ip`, `data_subnet_mask`, `smartnic_man_ip`, `smartnic_man_ip_gateway`, `smartnic_data_ip`, `u_count`, `asset_state`, `mc_name`, `mc_phone`, `mc_email`, `procurement_time`, `warranty_start`, `warranty_end`, `arbitration_az`, `server_category`, `availability_zone_uuid`, `real_availability_zone_uuid`, `datacenter_uuid`, `rack_uuid`, `region_uuid`)
SELECT `uuid`, `created_at`, `version`, `host_name`, `serial_number`, `brand`, `model`, `system_ip`, `system_ip_mask`, `system_ip_mac`, `manage_ip`, `data_ip`, `data_ip_mask`, `smartnic_man_ip`, `smartnic_man_ip_gateway`, `smartnic_data_ip`, `u_count`,
  CASE asset_state WHEN 1 THEN 'failure' WHEN 2 THEN 'maintenance' WHEN 3 THEN 'offline' WHEN 4 THEN 'online' END,
  `mc_name`, `mc_phone`, `mc_email`,
  IF(`procurement_time` = '2006-01-02', '1970-01-01', `procurement_time`),
  IF(`warranty_start` = '2006-01-02', '1970-01-01', `warranty_start`),
  IF(`warranty_end` = '2006-01-02', '1970-01-01', `warranty_end`),
  IF(`arbitration_az` = '0', 'no', 'yes'),
  IF(`server_category` = '1', 'inner', 'jdcloud'),
  IF(`arbitration_az` = '0', `az_uuid`, REPLACE(`region_uuid`, SUBSTRING_INDEX(`region_uuid`, '-', -1), 'arbitration')),
  `az_uuid`, `datacenter_uuid`, `rack_uuid`, `region_uuid`
FROM `server`;

TRUNCATE `odin_hardware_role_metadata`;
INSERT INTO `odin_hardware_role_metadata` (`uuid`, `role`, `metadata_key`, `metadata_value`, `kind`, `serial_number`)
SELECT
  IF(SUBSTRING(s.`role`, 1, 9) LIKE 'interwork', n.`uuid`, m.`uuid`),
  s.`role`, s.`metadata_key`, s.`metadata_value`,
  IF(SUBSTRING(s.`role`, 1, 9) LIKE 'interwork', 'network', 'server'),
  s.`uuid`
FROM `server_role_metadata` s
  LEFT JOIN network_equipment n ON n.serial_number = s.uuid
  LEFT JOIN server m ON m.serial_number = s.uuid;

TRUNCATE `odin_network_plan`;
INSERT INTO `odin_network_plan` (`uuid`, `created_at`, `version`, `name`, `target_id`, `subnet_cidr`, `subnet_mask`, `gateway`, `vlan`, `region_uuid`, `res_uuid`, `res_sn`, `res_kind`, `underlay_ip`, `overlay_ip`, `total_ip_count`, `description`, `attribute_1`, `attribute_2`, `attribute_3`)
SELECT `ref_id`, `created_at`, `version`, `name`, `target`, `subnet`, `mask`, `gateway`, `vlan`, `region`, `res_id`, `res_sn`, `res_type`, `underlay_ip`, `overlay_ip`, `total_ip_count`, `description`, `attribute_1`, `attribute_2`, `attribute_3`
FROM `network_plan`;

TRUNCATE `odin_eip_used_record`;
INSERT INTO `odin_eip_used_record` (`uuid`, `created_at`, `version`, `region_uuid`, `network_plan_uuid`, `ip_address`, `start_ip_address`, `count`, `state`, `reason`, `start_time`, `end_time`, `res_uuid`, `res_kind`, `pin`)
SELECT `uuid`, `created_at`, `version`, `region_id`, `eip_uuid`, `ip_address`, `start_ip_address`, `count`, IF(`state` = '0', 'no', 'yes'),  `reason`, `start_time`, `end_time`, `res_id`, `res_type`, `pin`
FROM `eip_used_record`;

