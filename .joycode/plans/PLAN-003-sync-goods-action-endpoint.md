# Task: Implement Goods Sync Action Endpoint

## Task Summary
This task involves creating a new API endpoint in the `zeus` service. This endpoint will synchronize the state of "goods" based on a provided list for a specific version. It will find all goods in the database for that version that are NOT in the provided list and update their `action` status to `offline`.

## Implementation Steps

### TODO: Define API and Generate Code
- [ ] Add the `SyncGoodsAction` RPC to `zeus/api/server/protobuf_spec/goods_stack.proto`.
- [ ] Define the `SyncGoodsActionReq` and `GoodsInfo` messages in the proto file.
- [ ] Run `mage gen` to generate the necessary gRPC and gateway code.

### TODO: Implement Data Layer
- [ ] Create a new method in the `goods` data repository to retrieve all goods for a given version.
- [ ] Create a new method to update the `action` status for a list of goods by their unique identifiers.

### TODO: Implement Business Logic (Biz Layer)
- [ ] Create the `SyncGoodsAction` use case in the `internal/server/biz/v1/goods_stack/` directory.
- [ ] Implement the core logic:
    - 1. Fetch all goods for the specified version from the database.
    - 2. Create a lookup map from the input list of active goods.
    - 3. Identify goods present in the database but absent from the input list.
    - 4. Call the data layer method to batch-update the identified goods' `action` to `offline`.

### TODO: Implement Service Layer
- [ ] Add the `SyncGoodsAction` method to the `GoodsStackService` in `internal/server/service/v1/jdstack_filecenter/jdstack_filecenter.go`.
- [ ] This service method will orchestrate the call to the business logic layer.

### TODO: Configure Dependency Injection
- [ ] Update the `wire.go` file in `zeus/cmd/server/` to ensure the new components (biz use case, data repository methods) are correctly injected.

## Documentation Requirements
- [ ] Add comments to all new public functions and methods, explaining their purpose, parameters, and return values.
- [ ] No changes to README.md are required for this task.