# 计划：为start.sh添加MySQL IP查询功能

## 任务摘要
修改`start.sh`脚本，添加在pod环境中执行时从MySQL数据库查询负载均衡器IP地址并更新配置文件的功能。当脚本在pod中执行时，需要查询MySQL数据库获取base_lb角色且为master的服务器IP地址，并将该IP地址更新到`config.jdock.yaml`文件中的`filecenter_jdstack_zeus_server`配置项，然后再启动服务。

## 实现步骤

### TODO: 创建目录检测函数
- [x] 添加函数检测脚本是否在pod环境中执行
- [x] 实现简单可靠的环境检测方法（如检查特定文件或环境变量）
- [x] 确保检测逻辑健壮且不会产生误判

### TODO: 实现MySQL查询功能
- [x] 添加函数从配置文件中提取MySQL连接信息
- [x] 实现MySQL查询逻辑，执行指定的SQL查询
- [x] 添加错误处理和重试机制，确保查询可靠性
- [x] 验证查询结果格式并进行必要的数据验证

### TODO: 实现配置文件更新功能
- [x] 添加函数用于创建临时配置文件（解决只读文件系统问题）
- [x] 实现安全的文件读写和替换逻辑
- [x] 添加验证机制，确保配置更新成功
- [x] 修改服务启动逻辑，使用临时配置文件

### TODO: 集成到现有启动流程
- [x] 在服务启动前添加新的逻辑流程
- [x] 确保新功能不影响现有功能
- [x] 添加适当的日志记录，便于问题排查
- [x] 优化错误处理，确保服务启动的可靠性

### TODO: 优化性能和资源使用
- [x] 确保只查询一次IP地址，避免重复查询
- [x] 使用全局变量存储查询结果，在服务重启时复用
- [x] 添加注释说明优化目的和实现方式

### TODO: 修复函数返回值处理问题
- [x] 修改`query_lb_ip`函数，确保只返回IP地址，不包含日志输出
- [x] 修改`create_temp_config`函数，确保只返回临时配置文件路径，不包含日志输出
- [x] 添加临时配置文件的存在性验证，避免使用无效的文件路径
- [x] 在`start_service`函数中添加文件存在性检查，确保只使用有效的配置文件

### TODO: 修复日志输出污染问题
- [x] 修改`log`函数，将日志输出重定向到标准错误输出（stderr）
- [x] 调整验证临时配置文件的顺序，先验证文件是否存在，再输出成功日志
- [x] 确保命令替换`$()`不会捕获日志输出
- [x] 添加注释说明日志输出重定向的目的

### TODO: 测试验证
- [ ] 在非pod环境中测试，确保原有功能正常
- [ ] 在pod环境中测试，验证新增功能正常
- [ ] 测试异常情况（如MySQL连接失败、查询结果为空等）
- [ ] 验证服务启动和监控功能正常

## 文档要求
- [x] 在脚本顶部添加功能说明注释
- [x] 为新增函数添加详细注释
- [ ] 更新README文件，说明新增功能和使用方法
- [ ] 记录测试结果和注意事项