# Zeus 基于 client-go 实现 Leader Election 的任务唯一调度方案

本任务规划目标是基于 Kubernetes client-go 的 Leader Election 能力，实现 Zeus 多 pod 场景下任务调度的全局唯一主控，确保任何时刻只有一个 pod 实际执行任务，其余 pod 处于 standby 备份状态。主节点切换时，自动转移任务调度权。

---

## TODO: 技术选型与设计
- [x] 技术选型：采用 K8s 官方 client-go/tools/leaderelection
- [x] 明确租约类型（Lease）、资源命名、租约时间、唯一身份标识方案
- [ ] 设计主从切换时的任务调度安全性与幂等性

## TODO: Leader Election 接入实现
- [ ] 集成 client-go/tools/leaderelection 组件，封装 leader 竞选与 isLeader 状态维护
- [ ] 在 Zeus 启动流程注册选主逻辑及回调（OnStartedLeading/OnStoppedLeading）
- [ ] 设计全局变量或 context 标识当前实例是否为 leader

## TODO: 任务调度主控化改造
- [ ] 将所有定时、拉取、消费任务的核心逻辑仅限于主节点（isLeader == true）时执行
- [ ] 其它 pod 处于 standby 状态，仅健康监控，不做任务调度

## TODO: 健康检查与主失效切换
- [ ] 实现主节点故障感知与自动重新选主（依赖 client-go 自动 failover 机制）
- [ ] 测试主节点下线、重启等场景的高可用切换

## TODO: 配置与部署验证
- [ ] 设计/编写 leader 选举 Lease 资源清单及所需 RBAC
- [ ] 本地和 k8s 多 pod 启动验证 leader 切换、任务唯一消费等功能

## TODO: 文档与代码注释
- [ ] 在 README 及关键代码处补充选主/主从机制说明
- [ ] 对关键接口、回调、主控判断加详细注释

---

**备注：**
- 建议优先在测试环境验证主备切换和任务唯一性。
- 任务调度入口示例：
  ```go
  if isLeader.Load() {
      // 执行任务调度
  }
  ```
- client-go 选主机制详见官方文档与 [知乎用法案例](https://zhuanlan.zhihu.com/p/427520194)，如需 Demo 可单独补充。