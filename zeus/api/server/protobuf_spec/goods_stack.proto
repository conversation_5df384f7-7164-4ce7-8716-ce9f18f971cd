syntax = "proto3";

package jdstack.zeus.v1.goodsStack;

option go_package = "coding.jd.com/fabric/zeusV2/zeus/api/server/pb/goods_stack/v1;pbGoodsStack";

import "google/api/annotations.proto";
import "google/api/visibility.proto";
import "protoc-gen-openapiv2/options/annotations.proto";
import "validate/validate.proto";
import "google/protobuf/empty.proto";
import "winapi/winapi.proto";
import "tagger/tagger.proto";
import "goods_delivery_rule.proto";
import "hardware_role.proto";
import "metadata.proto";
import "server_plane.proto";
import "version.proto";
import "unified_metadata.proto";

option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_swagger) = {
  host: "localhost:35711"
  base_path: "/v1"
  schemes: [HTTP]
  info: {
    title: "GoodsStack_标品管理"
    version: "v1"
  }
};

service GoodsStack {
  rpc Create (CreateReq) returns (DescribeReply) {
    option (google.api.http) = {post: "/version/{version}/goodsStack", body: "*"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {
      description: "创建标品信息"
    };
  }
  rpc Describe (DescribeReq) returns (DescribeReply) {
    option (google.api.http) = {get: "/version/{version}/goodsStack"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {
      description: "获取指定版本的标品信息"
    };
  }
  rpc Describes (DescribesReq) returns (DescribesReply) {
    option (google.api.http) = {get: "/goodsStack"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {
      description: "获取标品列表"
    };
  }

  rpc CreateFilecenter (CreateFilecenterReq) returns (DescribeFilecenterReply) {
    option (google.api.http) = {post: "/goodsStackFilecenter", body: "*"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {
      description: "创建标品记录文件或镜像信息"
    };
  }
  rpc DeleteFilecenter (DeleteFilecenterReq) returns (google.protobuf.Empty) {
    option (google.api.http) = {delete: "/goodsStackFilecenter/{id}"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {
      description: "删除标品记录文件或镜像信息"
    };
  }
  rpc DescribeFilecenter (DescribeFilecenterReq) returns (DescribeFilecenterReply) {
    option (google.api.http) = {get: "/goodsStackFilecenter/{id}"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {
      description: "获取标品记录文件或镜像信息"
    };
  }
  rpc DescribesFilecenter (DescribesFilecenterReq) returns (DescribesFilecenterReply) {
    option (google.api.http) = {get: "/goodsStackFilecenter"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {
      description: "获取标品记录文件列表"
    };
  }

  rpc CreateDelivery (CreateDeliveryReq) returns (DescribeDeliveryReply) {
    option (google.api.http) = {post: "/goodsStackDelivery", body: "*"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {
      description: "创建标品记录交付信息"
    };
  }
  rpc DeleteDelivery (DeleteDeliveryReq) returns (google.protobuf.Empty) {
    option (google.api.http) = {delete: "/goodsStackDelivery/{id}"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {
      description: "删除标品记录交付信息"
    };
  }
  rpc ModifyDelivery (ModifyDeliveryReq) returns (google.protobuf.Empty) {
    option (google.api.http) = {put: "/goodsStackDelivery/{id}", body: "*"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {
      description: "修改标品记录交付信息"
    };
  }
  rpc DescribeDelivery (DescribeDeliveryReq) returns (DescribeDeliveryReply) {
    option (google.api.http) = {get: "/goodsStackDelivery/{id}"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {
      description: "获取标品记录交付信息"
    };
  }
  rpc DescribesDelivery (DescribesDeliveryReq) returns (DescribesDeliveryReply) {
    option (google.api.http) = {get: "/goodsStackDelivery"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {
      description: "获取标品记录交付列表"
    };
  }

  rpc CreateDomain (CreateDomainReq) returns (DescribeDomainReply) {
    option (google.api.http) = {post: "/goodsStackDomain", body: "*"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {
      description: "创建标品记录域名信息"
    };
  }
  rpc DeleteDomain (DeleteDomainReq) returns (google.protobuf.Empty) {
    option (google.api.http) = {delete: "/goodsStackDomain/{id}"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {
      description: "删除标品记录域名信息"
    };
  }
  rpc ModifyDomain (ModifyDomainReq) returns (google.protobuf.Empty) {
    option (google.api.http) = {put: "/goodsStackDomain/{id}", body: "*"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {
      description: "修改标品记录域名信息"
    };
  }
  rpc DescribeDomain (DescribeDomainReq) returns (DescribeDomainReply) {
    option (google.api.http) = {get: "/goodsStackDomain/{id}"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {
      description: "获取标品记录域名信息"
    };
  }
  rpc DescribesDomain (DescribesDomainReq) returns (DescribesDomainReply) {
    option (google.api.http) = {get: "/goodsStackDomain"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {
      description: "获取标品记录域名列表"
    };
  }

  rpc CreateFlavor (CreateFlavorReq) returns (DescribeFlavorReply) {
    option (google.api.http) = {post: "/goodsStackFlavor", body: "*"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {
      description: "创建标品记录云翼配置信息"
    };
  }
  rpc DeleteFlavor (DeleteFlavorReq) returns (google.protobuf.Empty) {
    option (google.api.http) = {delete: "/goodsStackFlavor/{id}"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {
      description: "删除标品记录云翼配置信息"
    };
  }
  rpc ModifyFlavor (ModifyFlavorReq) returns (google.protobuf.Empty) {
    option (google.api.http) = {put: "/goodsStackFlavor/{id}", body: "*"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {
      description: "修改标品记录云翼配置信息"
    };
  }
  rpc DescribeFlavor (DescribeFlavorReq) returns (DescribeFlavorReply) {
    option (google.api.http) = {get: "/goodsStackFlavor/{id}"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {
      description: "获取标品记录云翼配置信息"
    };
  }
  rpc DescribesFlavor (DescribesFlavorReq) returns (DescribesFlavorReply) {
    option (google.api.http) = {get: "/goodsStackFlavor"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {
      description: "获取标品记录云翼配置列表"
    };
  }

  rpc CreateIac (CreateIacReq) returns (DescribeIacReply) {
    option (google.api.http) = {post: "/goodsStackIac", body: "*"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {
      description: "创建标品记录IAC描述信息"
    };
  }
  rpc DeleteIac (DeleteIacReq) returns (google.protobuf.Empty) {
    option (google.api.http) = {delete: "/goodsStackIac/{id}"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {
      description: "删除标品记录IAC描述信息"
    };
  }
  rpc ModifyIac (ModifyIacReq) returns (google.protobuf.Empty) {
    option (google.api.http) = {put: "/goodsStackIac/{id}", body: "*"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {
      description: "修改标品记录IAC描述信息"
    };
  }
  rpc DescribeIac (DescribeIacReq) returns (DescribeIacReply) {
    option (google.api.http) = {get: "/goodsStackIac/{id}"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {
      description: "获取标品记录IAC描述信息"
    };
  }
  rpc DescribesIac (DescribesIacReq) returns (DescribesIacReply) {
    option (google.api.http) = {get: "/goodsStackIac"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {
      description: "获取标品记录IAC描述列表"
    };
  }

  rpc CreateMiddleware (CreateMiddlewareReq) returns (DescribeMiddlewareReply) {
    option (google.api.http) = {post: "/goodsStackMiddleware", body: "*"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {
      description: "创建标品记录中间件信息 / 关联标品记录中间件信息"
    };
  }
  rpc DeleteMiddleware (DeleteMiddlewareReq) returns (google.protobuf.Empty) {
    option (google.api.http) = {delete: "/goodsStackMiddleware/{id}"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {
      description: "删除标品记录中间件信息"
    };
  }
  rpc ModifyMiddleware (ModifyMiddlewareReq) returns (google.protobuf.Empty) {
    option (google.api.http) = {put: "/goodsStackMiddleware/{id}", body: "*"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {
      description: "修改标品记录中间件信息"
    };
  }
  rpc DescribeMiddleware (DescribeMiddlewareReq) returns (DescribeMiddlewareReply) {
    option (google.api.http) = {get: "/goodsStackMiddleware/{id}"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {
      description: "获取标品记录中间件信息"
    };
  }
  rpc DescribesMiddleware (DescribesMiddlewareReq) returns (DescribesMiddlewareReply) {
    option (google.api.http) = {get: "/goodsStackMiddleware"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {
      description: "获取标品记录中间件列表"
    };
  }

  rpc CreateHardwareRoleUnion (CreateHardwareRoleUnionReq) returns (google.protobuf.Empty) {
    option (google.api.http) = {post: "/goodsStackHardwareRole", body: "*"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {
      description: "关联标品硬件角色和标签信息"
    };
  }

  rpc CreateHardwareRole (CreateHardwareRoleReq) returns (google.protobuf.Empty) {
    option (google.api.http) = {post: "/goodsStack/{goodsId}/hardwareRole/{hardwareRoleId}", body: "*"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {
      description: "关联标品硬件角色信息"
    };
  }
  rpc DeleteHardwareRole (DeleteHardwareRoleReq) returns (google.protobuf.Empty) {
    option (google.api.http) = {delete: "/goodsStack/{goodsId}/hardwareRole/{hardwareRoleId}"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {
      description: "删除关联标品硬件角色信息"
    };
  }
  rpc DescribeHardwareRole (DescribeHardwareRoleReq) returns (DescribeHardwareRoleReply) {
    option (google.api.http) = {get: "/goodsStackHardwareRole/{id}"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {
      description: "获取标品硬件角色信息"
    };
  }
  rpc DescribesHardwareRole (DescribesHardwareRoleReq) returns (DescribesHardwareRoleReply) {
    option (google.api.http) = {get: "/goodsStackHardwareRole"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {
      description: "获取标品硬件角色列表"
    };
  }

  rpc CreateHardwareRoleLabel (CreateHardwareRoleLabelReq) returns (google.protobuf.Empty) {
    option (google.api.http) = {post: "/goodsStack/{goodsId}/hardwareRoleLabel/{hardwareRoleLabelId}", body: "*"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {
      description: "关联标品硬件角色标签信息"
    };
  }
  rpc DeleteHardwareRoleLabel (DeleteHardwareRoleLabelReq) returns (google.protobuf.Empty) {
    option (google.api.http) = {delete: "/goodsStack/{goodsId}/hardwareRoleLabel/{hardwareRoleLabelId}"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {
      description: "删除关联标品硬件角色标签信息"
    };
  }
  rpc DescribeHardwareRoleLabel (DescribeHardwareRoleLabelReq) returns (DescribeHardwareRoleLabelReply) {
    option (google.api.http) = {get: "/goodsStackHardwareRoleLabel/{id}"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {
      description: "获取标品硬件角色标签信息"
    };
  }
  rpc DescribesHardwareRoleLabel (DescribesHardwareRoleLabelReq) returns (DescribesHardwareRoleLabelReply) {
    option (google.api.http) = {get: "/goodsStackHardwareRoleLabel"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {
      description: "获取标品硬件角色标签列表"
    };
  }

  rpc CreateChain (CreateChainReq) returns (DescribeChainReply) {
    option (google.api.http) = {post: "/goodsStackChain", body: "*"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {
      description: "创建标品链路(依赖)"
    };
  }
  rpc DescribeChain (DescribeChainReq) returns (DescribeChainReply) {
    option (google.api.http) = {get: "/goodsStackChain/{id}"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {
      description: "查询标品链路(依赖)"
    };
  }
  rpc DeleteChain (DeleteChainReq) returns (google.protobuf.Empty) {
    option (google.api.http) = {delete: "/goodsStackChain/{id}"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {
      description: "删除标品链路(依赖)"
    };
  }
  rpc DescribesChain (DescribesChainReq) returns (DescribesChainReply) {
    option (google.api.http) = {get: "/goodsStackChain"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {
      description: "查询标品链路(依赖)"
    };
  }

  rpc ModifySyncGoodsAction (SyncGoodsActionReq) returns (SyncGoodsActionReply) {
    option (google.api.http) = {post: "/version/{version}/goodsStack:syncAction", body: "*"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {
      description: "同步标品状态，将不在列表中的标品状态设置为offline"
    };
  }
}

message CreateReq {
  option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_schema) = {json_schema: {required: ["version", "category", "action"]}};
  string id = 1 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "ID,仅供导入"},
    (google.api.field_visibility).restriction = "INTERNAL",
    (tagger.tags) = 'excel:"ID"'
  ];
  string version = 2 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "JDStack版本"},
    (validate.rules).string = {min_bytes: 1, pattern: "^[0-9a-zA-Z.]+$"},
    (tagger.tags) = 'excel:"JDStack版本" required:"true"'
  ];
  repeated string policy = 3 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "策略:\noverride - 覆盖相同JDStack版本,应用\nforeignKeyDiscarded - 查出已存在的关联数据,本次请求没关联上就被淘汰,需指定FKD:{模块名}"}
  ];
  jdstack.zeus.v1.metadata.CreateReq metadata = 4 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "创建分类-产品-服务-应用,存在时会覆盖顺序1~4"}
  ];
  string category = 5 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "分类名称,顺序4(必填)"},
    (tagger.tags) = 'excel:"分类名称,顺序4(必填)" required:"true"'
  ];
  string service = 6 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "产品名称,顺序3(存在时4必填)"},
    (tagger.tags) = 'excel:"产品名称,顺序3(存在时4必填)"'
  ];
  string resource = 7 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "服务名称,顺序2(存在时3必填)"},
    (tagger.tags) = 'excel:"服务名称,顺序2(存在时3必填)"'
  ];
  string application = 8 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "应用名称,顺序1(存在时2必填)"},
    (tagger.tags) = 'excel:"应用名称,顺序1(存在时2必填)"'
  ];
  string action = 9 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "操作行为:\n[ debug online offline upgrade ]", default: "debug"},
    (validate.rules).string = {ignore_empty: true, in: "debug", in: "online", in: "offline", in: "upgrade"},
    (tagger.tags) = 'excel:"操作行为" excel-valid-enum:"debug,online,offline,upgrade"'
  ];
  string virtualGoods = 10 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "逻辑属性:\n[ no yes ]", default: "no"},
    (validate.rules).string = {ignore_empty: true, in: "no", in: "yes"},
    (tagger.tags) = 'excel:"逻辑属性" excel-valid-enum:"no,yes"'
  ];
  string cloudType = 11 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "云底座类别:\n[ jdstack cvessel ]", default: "jdstack"},
    (validate.rules).string = {ignore_empty: true, in: "jdstack", in: "cvessel"},
    (tagger.tags) = 'excel:"云底座类别" excel-valid-enum:"jdstack,cvessel"'
  ];
  // 关联部署依赖的标品信息
  reserved 12; // 关联故障链路依赖的标品信息
  repeated CreateFilecenterReq filecenter = 13 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "关联文件或镜像信息,policy[FDK:GoodsFilecenter]"}
  ];
  repeated CreateDeliveryReq delivery = 14 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "关联交付信息,policy[FDK:GoodsDelivery]"}
  ];
  repeated CreateDomainReq domain = 15 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "关联域名信息,policy[FDK:GoodsDomain]"}
  ];
  repeated CreateFlavorReq flavor = 16 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "关联云翼配置信息,policy[FDK:GoodsFlavor]"}
  ];
  repeated CreateIacReq iac = 17 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "关联IAC信息,policy[FDK:GoodsIac]"}
  ];
  repeated CreateMiddlewareReq middleware = 18 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "关联中间件信息,policy[FDK:GoodsMiddleware]"}
  ];
  repeated jdstack.zeus.v1.hardwareRole.DescribeReq hardwareRole = 19 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "关联硬件角色信息,policy[FDK:HardwareRole]"}
  ];
  repeated jdstack.zeus.v1.hardwareRole.CreateLabelReq hardwareRoleLabel = 20 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "关联硬件角色标签信息,policy[FDK:HardwareRoleLabel]"}
  ];
  CreatePdReq pd = 21 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "关联产品pd信息"}
  ];
  CreateMetadataReq businessMetadata = 22 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "产品关联的商业平台元数据信息"}
  ];
  repeated CreateAdlReq adl = 23 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "关联的adl文件内容"}
  ];
  repeated CreateConfigReq config = 24 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "关联的配置信息"}
  ];
  repeated CreateDeliveryRuleGroupReq deliveryRuleGroup = 25 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "关联交付规则组信息,policy[FDK:GoodsDeliveryRuleGroup]"}
  ];
}

message DescribeReq {
  option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_schema) = {json_schema: {required: ["version"]}};
  string id = 1 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "标品ID\n- 为空时按顺序查询字段：application、resource、service、category"}
  ];
  string version = 2 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "JDStack版本"},
    (validate.rules).string = {min_bytes: 1, pattern: "^[0-9a-zA-Z.]+$"},
    (tagger.tags) = 'excel:"JDStack版本" required:"true"'
  ];
  string category = 5 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "分类名称"},
    // 注意这边是给excel用的，规则与SDK有差异
    (tagger.tags) = 'excel:"分类名称,顺序4(必填)" required:"true"'
  ];
  string service = 6 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "产品名称\n- 为空时按顺序查询字段：category"},
    (tagger.tags) = 'excel:"产品名称,顺序3(存在时4必填)"'
  ];
  string resource = 7 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "服务名称\n- 为空时按顺序查询字段：service、category"},
    (tagger.tags) = 'excel:"服务名称,顺序2(存在时3必填)"'
  ];
  string application = 8 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "应用名称\n- 为空时按顺序查询字段：resource、service、category"},
    (tagger.tags) = 'excel:"应用名称,顺序1(存在时2必填)"'
  ];
  repeated wingo.api.Filter filters = 9 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "返回字段皆支持筛选，筛选扩展:\nWith:{模块名} - 返回关联表(此类过滤项无value，所以可将同类name合并name=.的values中)\nWith:{模块名}.{字段名} - 过滤并返回关联表"}
  ];
  string cloudType = 10 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "云底座类别:\n[ jdstack cvessel ]", default: "jdstack"},
    (validate.rules).string = {ignore_empty: true, in: "jdstack", in: "cvessel"},
    (tagger.tags) = 'excel:"云底座类别" excel-valid-enum:"jdstack,cvessel"'
  ];
}
message DescribeReply {
  string id = 1 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "标品ID"}
  ];
  jdstack.zeus.v1.version.DescribeReply version = 2 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "版本,filter[With:JdstackRelease]"}
  ];
  jdstack.zeus.v1.metadata.DescribeCategoryReply category = 5 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "分类,filter[With:MetadataCategory]"}
  ];
  jdstack.zeus.v1.metadata.DescribeServiceReply service = 6 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "产品,filter[With:MetadataService]"}
  ];
  jdstack.zeus.v1.metadata.DescribeResourceReply resource = 7 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "服务,filter[With:MetadataResource]"}
  ];
  jdstack.zeus.v1.metadata.DescribeApplicationReply application = 8 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "应用,filter[With:MetadataApplication]"}
  ];
  string action = 9 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "操作行为:\n[ debug online offline upgrade ]"}
  ];
  string virtualGoods = 10 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "逻辑属性:\n[ no yes ]"}
  ];
  string cloudType = 11 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "云底座类别:\n[ jdstack cvessel ]"}
  ];
  // 关联部署依赖的标品信息
  reserved 12; // 关联故障链路依赖的标品信息
  repeated FilecenterReply filecenter = 13 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "关联文件或镜像信息,filter[With:GoodsFilecenter]"}
  ];
  repeated DeliveryReply delivery = 14 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "关联交付信息,filter[With:GoodsDelivery]"}
  ];
  repeated DomainReply domain = 15 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "关联域名信息,filter[With:GoodsDomain]"}
  ];
  repeated FlavorReply flavor = 16 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "关联云翼配置信息,filter[With:GoodsFlavor]"}
  ];
  repeated IacReply iac = 17 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "关联IAC信息,filter[With:GoodsIac]"}
  ];
  repeated MiddlewareReply middleware = 18 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "关联中间件信息,filter[With:GoodsMiddleware]"}
  ];
  repeated jdstack.zeus.v1.hardwareRole.DescribeReply hardwareRole = 19 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "关联硬件角色信息,filter[With:HardwareRole]"}
  ];
  repeated jdstack.zeus.v1.hardwareRole.DescribeLabelReply hardwareRoleLabel = 20 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "关联硬件角色标签信息,filter[With:HardwareRoleLabel]"}
  ];
  repeated DescribeChainReply chain = 21 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "关联依赖信息,filter[With:GoodsChain]"}
  ];
  repeated DescribeChainReply chainFrom = 22 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "关联被依赖信息,filter[With:GoodsChain]"}
  ];
  repeated jdstack.zeus.v1.goodsDeliveryRule.DescribeDeliveryRuleGroupReply goodsDeliveryRuleGroup = 23 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "关联被,filter[With:GoodsDeliveryRuleGroup.With:GoodsDeliveryRule]"}
  ];
  DescribePdReply pd = 24 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "产品级别的product.yaml内容,filter[With:GoodsPd]"}
  ];
  DescribeUpgradeReply upgrade = 25 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "产品级别的升级描述内容,filter[With:GoodsUpgrade]"}
  ];
  repeated DescribeMetadataReply metadata = 26 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "产品级别元数据内容(商业平台/网关元数据),filter[With:GoodsMetadata]"}
  ];
  repeated DescribeConfigReply config = 27 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "关联配置,filter[With:GoodsConfig]"}
  ];
}
message Reply {
  string id = 1 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "标品ID"}
  ];
  jdstack.zeus.v1.version.DescribeReply version = 2 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "版本,filter[With:JdstackRelease]"}
  ];
  jdstack.zeus.v1.metadata.DescribeCategoryReply category = 5 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "分类,filter[With:MetadataCategory]"}
  ];
  jdstack.zeus.v1.metadata.DescribeServiceReply service = 6 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "产品,filter[With:MetadataService]"}
  ];
  jdstack.zeus.v1.metadata.DescribeResourceReply resource = 7 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "服务,filter[With:MetadataResource]"}
  ];
  jdstack.zeus.v1.metadata.DescribeApplicationReply application = 8 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "应用,filter[With:MetadataApplication]"}
  ];
  string action = 9 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "操作行为:\n[ debug online offline upgrade ]"}
  ];
  string virtualGoods = 10 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "逻辑属性:\n[ no yes ]"}
  ];
  string cloudType = 11 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "云底座类别:\n[ jdstack cvessel ]"}
  ];
  repeated jdstack.zeus.v1.hardwareRole.DescribeReply hardwareRole = 19 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "关联物理机标签信息,filter[With:HardwareRole]"}
  ];
  repeated jdstack.zeus.v1.hardwareRole.DescribeLabelReply hardwareRoleLabel = 20 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "关联物理机标签-高级属性信息,filter[With:HardwareRoleLabel]"}
  ];
  repeated DescribeChainReply chain = 21 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "关联依赖信息,filter[With:GoodsChain]"}
  ];
  repeated DescribeChainReply chainFrom = 22 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "关联被依赖信息,filter[With:GoodsChain]"}
  ];
}
message Less {
  string id = 1 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "标品ID"}
  ];
  jdstack.zeus.v1.version.DescribeReply version = 2 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "版本,filter[With:JdstackRelease]"}
  ];
  jdstack.zeus.v1.metadata.DescribeCategoryReply category = 5 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "分类,filter[With:MetadataCategory]"}
  ];
  jdstack.zeus.v1.metadata.DescribeServiceReply service = 6 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "产品,filter[With:MetadataService]"}
  ];
  jdstack.zeus.v1.metadata.DescribeResourceReply resource = 7 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "服务,filter[With:MetadataResource]"}
  ];
  jdstack.zeus.v1.metadata.DescribeApplicationReply application = 8 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "应用,filter[With:MetadataApplication]"}
  ];
  string action = 9 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "操作行为:\n[ debug online offline upgrade ]"}
  ];
  string virtualGoods = 10 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "逻辑属性:\n[ no yes ]"}
  ];
  string cloudType = 11 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "云底座类别:\n[ jdstack cvessel ]"}
  ];
}
message DescribesReq {
  option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_schema) = {json_schema: {required: ["pageNumber", "pageSize"]}};
  int64 pageNumber = 1 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "页码:\n-1 - 全部\n0 - 第1页\n1 - 第2页\n- 以此类推...", default: "0"},
    (validate.rules).int64 = {gte: -1, lte: 16777216}
  ];
  int64 pageSize = 2 [(validate.rules).int64 = {gte: 1, lte: 16777216}];
  repeated wingo.api.Filter filters = 3 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "返回字段皆支持筛选，筛选扩展:\nWith:{模块名} - 返回关联表(此类过滤项无value，所以可将同类name合并name=.的values中)\nWith:{模块名}.{字段名} - 过滤并返回关联表\nHas:{模块名}.{字段名} - 子查询(等值连接)"}
  ];
  repeated wingo.api.Sort sorts = 4 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "返回字段皆支持排序，排序扩展:\nWith:{模块名}.[ Min Max Count Sum ]:{字段名} - 按关联表字段排序"}
  ];
}
message DescribesReply {
  repeated DescribeReply list = 1;
  int64 totalCount = 2;
}

message ExcelFilecenterReq {
  option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_schema) = {json_schema: {required: ["goods", "arch", "hostOsType", "deployMode", "imageTags", "fileName", "md5"]}};
  int64 id = 1 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "ID,仅供导入"},
    (google.api.field_visibility).restriction = "INTERNAL",
    (tagger.tags) = 'excel:"ID"'
  ];
  DescribeReq goods = 4 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "关联标品信息"},
    (tagger.tags) = 'excel:"关联标品信息" required:"true"'
  ];
  string arch = 5 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "架构:\n[ x86_64 arm ]"},
    (validate.rules).string = {in: "x86_64", in: "arm"},
    (tagger.tags) = 'excel:"架构" required:"true" excel-valid-enum:"x86_64,arm"'
  ];
  string deployMode = 6 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "部署模式:\n[ skywing_container skywing_package mate_container shell helm rpm mate_bin mate_v2 iaas_image xingyun_container xingyun_package ]"},
    (validate.rules).string = {in: "skywing_container", in: "skywing_package", in: "mate_container", in: "shell", in: "helm", in: "rpm", in: "mate_bin", in: "mate_v2", in: "iaas_image", in: "xingyun_container", in: "xingyun_package"},
    (tagger.tags) = 'excel:"部署模式" required:"true" excel-valid-enum:"skywing_container,skywing_package,mate_container,shell,helm,rpm,mate_bin,mate_v2,iaas_image,xingyun_container,xingyun_package"'
  ];
  string fileIdentifier = 7 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "文件或镜像标识"},
    (validate.rules).string = {max_bytes: 64},
    (tagger.tags) = 'excel:"文件或镜像标识" excel-valid-len:"0,64"'
  ];
  string hostOsType = 8 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "操作系统发行版:\n[ centos openEuler kylin]"},
    (validate.rules).string = {in: "centos", in: "openEuler", in: "kylin"},
    (tagger.tags) = 'excel:"操作系统发行版" required:"true" excel-valid-enum:"centos,openEuler"'
  ];
  string fileName = 9 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "文件名称"},
    (validate.rules).string = {min_bytes: 1, max_bytes: 1000},
    (tagger.tags) = 'excel:"文件名称" required:"true" excel-valid-len:"1,1000"'
  ];
  string fileHash = 10 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "文件校验码"},
    (validate.rules).string = {max_bytes: 128},
    (tagger.tags) = 'excel:"文件校验码" excel-valid-len:"0,128"'
  ];
  int64 fileSize = 11 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "文件大小/字节"},
    (validate.rules).int64 = {gte: 0},
    (tagger.tags) = 'excel:"文件大小/字节"'
  ];
  // TODO ~~~
}

message FilecenterDeployDockerImageReq {
  option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_schema) = {json_schema: {required: ["tag", "registry"]}};
  string tag = 1 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "镜像版本"},
    (validate.rules).string = {min_bytes: 1, max_bytes: 1000},
    (tagger.tags) = 'excel:"镜像版本" required:"true" excel-valid-len:"1,1000"'
  ];
  string registry = 2 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "注册模块名"},
    (validate.rules).string = {min_bytes: 1, max_bytes: 64},
    (tagger.tags) = 'excel:"注册模块名" excel-valid-len:"1,64"'
  ];
}
message FilecenterDeploySkywingPackageReq {
  option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_schema) = {json_schema: {required: ["moduleName", "packageVersion"]}};
  string moduleName = 1 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "云翼模块名称"},
    (validate.rules).string = {min_bytes: 1, max_bytes: 64},
    (tagger.tags) = 'excel:"云翼模块名称" required:"true" excel-valid-len:"1,64"'
  ];
  string packageVersion = 2 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "包版本"},
    (validate.rules).string = {min_bytes: 1, max_bytes: 1000},
    (tagger.tags) = 'excel:"包版本" excel-valid-len:"1,1000"'
  ];
}
message FilecenterDeployShellReq {
  option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_schema) = {json_schema: {required: ["destPath", "storageLocation"]}};
  string destPath = 1 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "上传文件存放路径"},
    (validate.rules).string = {min_bytes: 1, max_bytes: 255},
    (tagger.tags) = 'excel:"上传文件存放路径" required:"true" excel-valid-len:"1,255"'
  ];
  string storageLocation = 2 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "文件存储位置:\n[ minio http ]"},
    (validate.rules).string = {in: "minio", in: "http"},
    (tagger.tags) = 'excel:"文件存储位置" required:"true" excel-valid-enum:"minio,http"'
  ];
  string tag = 3 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "文件特征,内部使用"},
    (google.api.field_visibility).restriction = "INTERNAL"
  ];
}
message FilecenterDeployRpmReq {
  option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_schema) = {json_schema: {required: ["repo"]}};
  string repo = 1 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "文件存储位置"},
    (validate.rules).string = {min_bytes: 1, max_bytes: 64},
    (tagger.tags) = 'excel:"文件存储位置" required:"true" excel-valid-len:"1,64"'
  ];
  string tag = 2 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "包版本,内部使用"},
    (google.api.field_visibility).restriction = "INTERNAL"
  ];
}
message FilecenterDeployMateBinReq {
  // TODO
}
message FilecenterDeployIaasImageReq {
  option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_schema) = {json_schema: {required: ["imageName", "imageId", "platform", "source"]}};
  string imageName = 1 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "镜像名称"},
    (validate.rules).string = {min_bytes: 1, max_bytes: 255},
    (tagger.tags) = 'excel:"镜像名称" required:"true" excel-valid-len:"1,255"'
  ];
  string imageId = 2 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "IaaS镜像ID"},
    (validate.rules).string = {min_bytes: 1, max_bytes: 64},
    (tagger.tags) = 'excel:"IaaS镜像ID" required:"true" excel-valid-len:"1,64"'
  ];
  string osType = 3 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "镜像操作系统类型"},
    (validate.rules).string = {max_bytes: 64},
    (tagger.tags) = 'excel:"镜像操作系统类型" excel-valid-len:"0,64"'
  ];
  string platform = 4 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "镜像所属平台:\n[ windows linux ]"},
    (validate.rules).string = {in: "windows", in: "linux"},
    (tagger.tags) = 'excel:"镜像所属平台" required:"true" excel-valid-enum:"windows,linux"'
  ];
  string containerFormat = 5 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "镜像用途:\n[ docker vm ]"},
    (validate.rules).string = {ignore_empty: true, in: "docker", in: "vm"},
    (tagger.tags) = 'excel:"镜像用途" excel-valid-enum:"docker,vm"'
  ];
  string isProtected = 6 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "是否为保护镜像:\n[ no yes ]", default: "no"},
    (validate.rules).string = {ignore_empty: true, in: "no", in: "yes"},
    (tagger.tags) = 'excel:"是否为保护镜像" excel-valid-enum:"no,yes"'
  ];
  string rootDeviceType = 7 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "镜像存储盘:\n[ local volume ]", default: "local"},
    (validate.rules).string = {ignore_empty: true, in: "local", in: "volume"},
    (tagger.tags) = 'excel:"镜像存储盘" excel-valid-enum:"local,volume"'
  ];
  string source = 8 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "镜像分类:\n[ jdcloud self ]"},
    (validate.rules).string = {in: "jdcloud", in: "self"},
    (tagger.tags) = 'excel:"镜像分类" required:"true" excel-valid-enum:"jdcloud,self"'
  ];
  string attributes = 9 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "镜像属性"},
    (validate.rules).string = {max_bytes: 512},
    (tagger.tags) = 'excel:"镜像属性" excel-valid-len:"0,512"'
  ];
  string guestAgent = 10 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "镜像agent类型"},
    (validate.rules).string = {max_bytes: 64},
    (tagger.tags) = 'excel:"镜像agent类型" excel-valid-len:"0,64"'
  ];
  string diskFormat = 11 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "镜像格式"},
    (validate.rules).string = {max_bytes: 64},
    (tagger.tags) = 'excel:"镜像格式" excel-valid-len:"0,64"'
  ];
}

message FilecenterDeployDockerImageReply {
  string tag = 1 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "镜像版本"}
  ];
  string registry = 2 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "注册模块名"}
  ];
}
message FilecenterDeploySkywingPackageReply {
  string moduleName = 1 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "云翼模块名称"}
  ];
  string packageVersion = 2 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "包版本"}
  ];
}
message FilecenterDeployShellReply {
  string destPath = 1 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "上传文件存放路径"}
  ];
  string storageLocation = 2 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "文件存储位置:\n[ minio http ]"}
  ];
  string tag = 3 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "文件特征"}
  ];
}
message FilecenterDeployRpmReply {
  string repo = 1 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "文件存储位置"}
  ];
  string tag = 2 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "包版本"}
  ];
}
message FilecenterDeployMateBinReply {
  // TODO
}
message FilecenterDeployIaasImageReply {
  string imageName = 1 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "镜像名称"}
  ];
  string imageId = 2 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "IaaS镜像ID"}
  ];
  string osType = 3 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "镜像操作系统类型"}
  ];
  string platform = 4 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "镜像所属平台:\n[ windows linux ]"}
  ];
  string containerFormat = 5 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "镜像用途:\n[ docker vm ]"}
  ];
  string isProtected = 6 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "是否为保护镜像:\n[ no yes ]", default: "no"}
  ];
  string rootDeviceType = 7 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "镜像存储盘:\n[ local volume ]", default: "local"}
  ];
  string source = 8 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "镜像分类:\n[ jdcloud self ]"}
  ];
  string attributes = 9 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "镜像属性"}
  ];
  string guestAgent = 10 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "镜像agent类型"}
  ];
  string diskFormat = 11 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "镜像格式"}
  ];
}

message CreatePdReq {
  option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_schema) = {json_schema: {required: ["content"]}};
  int64 id = 1 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "ID,仅供导入"},
    (google.api.field_visibility).restriction = "INTERNAL"
  ];

  repeated string policy = 2 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "策略:\noverride - 覆盖相同标品pd"}
  ];

  DescribeReq goods = 3 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "关联标品信息"}
  ];

  string content = 4 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "产品pd的内容"}
  ];
}

message CreateAdlReq {
  option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_schema) = {json_schema: {required: ["content"]}};
  int64 id = 1 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "ID,仅供导入"},
    (google.api.field_visibility).restriction = "INTERNAL"
  ];

  repeated string policy = 2 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "策略:\noverride - 覆盖相同标品adl"}
  ];

  DescribeReq goods = 3 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "关联标品信息"}
  ];

  AdlReq adl = 4 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "商业平台元数据内容"}
  ];
}

message AdlReq {
  option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_schema) = {json_schema: {required: ["filePath", 'content']}};
  string scope = 1 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "adl级别 : \n[app, product, serviceCode]", enum: ["app", "product", "serviceCode"]},
    (validate.rules).string = {min_bytes: 1}
  ];
  string filePath = 2 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "adl文件相对路径\n- 相对于adl项目的文件路径,包含文件名"},
    (validate.rules).string = {min_bytes: 1}
  ];
  string content = 3 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "adl文件内容"},
    (validate.rules).string = {min_bytes: 1}
  ];
}

message CreateMetadataReq {
  option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_schema) = {json_schema: {required: ["content"]}};
  int64 id = 1 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "ID,仅供导入"},
    (google.api.field_visibility).restriction = "INTERNAL"
  ];

  repeated string policy = 2 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "策略:\noverride - 覆盖相同标品pd"}
  ];

  DescribeReq goods = 3 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "关联标品信息"}
  ];

  unifiedMetadata.CreateReq unifiedMetadata = 4 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "商业平台元数据内容"}
  ];
}

message CreateFilecenterReq {
  option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_schema) = {json_schema: {required: ["arch", "deployMode", "hostOsType", "fileName", "hostOsType"]}};
  int64 id = 1 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "ID,仅供导入"},
    (google.api.field_visibility).restriction = "INTERNAL"
  ];

  repeated string policy = 2 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "策略:\noverride - 覆盖相同标品,架构,文件或镜像标识,操作系统发行版"}
  ];

  DescribeReq goods = 3 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "关联标品信息"}
  ];

  string arch = 5 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "架构类型"},
    (validate.rules).string = {in: ["x86_64", "arm"]}
  ];

  string hostOsType = 6 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "操作系统发行版"},
    (validate.rules).string = {in: ["centos", "openEuler", "kylin"]}
  ];

  string fileIdentifier = 7 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "文件或镜像标识"},
    (validate.rules).string = {max_len: 64}
  ];

  string directorySpec = 8 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "目录命名规范"},
    (validate.rules).string = {in: ["arch_fileType_serviceCode", "arch_serializeGoods"]}
  ];

  string fileName = 9 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "文件名称"},
    (validate.rules).string = {max_len: 1000}
  ];

  string fileHash = 10 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "文件校验码"},
    (validate.rules).string = {max_len: 128}
  ];

  int64 fileSize = 11 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "文件大小/字节"},
    (validate.rules).int64 = {gte: 1}
  ];

  string imageTags = 12 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "文件或镜像版本"},
    (validate.rules).string = {max_len: 1000}
  ];

  string fileVersion = 13 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "文件版本"},
    (validate.rules).string = {max_len: 255}
  ];

  string fileType = 14 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "文件类型"},
    (validate.rules).string = {max_len: 32}
  ];

  string filePath = 15 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "文件路径"},
    (validate.rules).string = {max_len: 1000}
  ];

  string param = 16 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "参数"}
  ];

  string remark = 17 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "备注"},
    (validate.rules).string = {max_len: 255}
  ];
}
message DeleteFilecenterReq {
  option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_schema) = {json_schema: {required: ["id"]}};
  int64 id = 1 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "ID"},
    (validate.rules).int64 = {gte: 1}
  ];
}
message DescribeFilecenterReq {
  option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_schema) = {json_schema: {required: ["id"]}};
  int64 id = 1 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "ID"},
    (validate.rules).int64 = {gte: 1}
  ];
  repeated wingo.api.Filter filters = 2 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "返回字段皆支持筛选，筛选扩展:\nWith:{模块名} - 返回关联表(此类过滤项无value，所以可将同类name合并name=.的values中)\nWith:{模块名}.{字段名} - 过滤并返回关联表"}
  ];
}
message DescribeFilecenterReply {
  int64 id = 1 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "ID"}
  ];
  string createdAt = 3 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "创建时间,UTC格式(毫秒级24位)"}
  ];
  DescribeReply goods = 4 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "关联标品,filter[With:Goods]"}
  ];
  string arch = 5 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "架构:\n[ x86 arm ]"}
  ];
  string deployMode = 6 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "部署模式:\n[ skywing_container skywing_package mate_container shell helm rpm mate_bin mate_v2 iaas_image xingyun_image xingyun_package ]"}
  ];
  string fileIdentifier = 7 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "文件或镜像标识"}
  ];
  string hostOsType = 8 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "操作系统发行版:\n[ centos openEuler kylin]"}
  ];
  string fileName = 9 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "文件名称"}
  ];
  string fileHash = 10 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "文件校验码"}
  ];
  int64 fileSize = 11 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "文件大小/字节"}
  ];
  FilecenterDeployDockerImageReply skywingContainer = 12 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "按部署模式 [ skywing_container ] 分组"}
  ];
  FilecenterDeploySkywingPackageReply skywingPackage = 13 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "按部署模式 [ skywing_package ] 分组"}
  ];
  FilecenterDeployDockerImageReply mateContainer = 14 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "按部署模式 [ mate_container ] 分组"}
  ];
  FilecenterDeployShellReply shell = 15 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "按部署模式 [ shell ] 分组"}
  ];
  FilecenterDeployDockerImageReply helm = 16 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "按部署模式 [ helm ] 分组"}
  ];
  FilecenterDeployRpmReply rpm = 17 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "按部署模式 [ rpm ] 分组"}
  ];
  FilecenterDeployMateBinReply mateBin = 18 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "按部署模式 [ mate_bin ] 分组"}
  ];
  FilecenterDeployRpmReply mateV2 = 19 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "按部署模式 [ mate_v2 ] 分组"}
  ];
  FilecenterDeployIaasImageReply iaasImage = 20 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "按部署模式 [ iaas_image ] 分组"}
  ];
}
message FilecenterReply {
  int64 id = 1 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "ID"}
  ];
  string createdAt = 3 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "创建时间,UTC格式(毫秒级24位)"}
  ];
  string goodsId = 4 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "关联标品ID,filter[With:Goods]"}
  ];
  string arch = 5 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "架构:\n[ x86_64 arm ]"}
  ];
  string deployMode = 6 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "部署模式:\n[ skywing_container skywing_package mate_container shell helm rpm mate_bin mate_v2 iaas_image xingyun_container xingyun_package ]"}
  ];
  string fileIdentifier = 7 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "文件或镜像标识"}
  ];
  string hostOsType = 8 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "操作系统发行版:\n[ centos openEuler kylin]"}
  ];
  string fileName = 9 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "文件名称"}
  ];
  string fileHash = 10 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "文件校验码"}
  ];
  int64 fileSize = 11 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "文件大小/字节"}
  ];
  FilecenterDeployDockerImageReply skywingContainer = 12 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "按部署模式 [ skywing_container ] 分组"}
  ];
  FilecenterDeploySkywingPackageReply skywingPackage = 13 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "按部署模式 [ skywing_package ] 分组"}
  ];
  FilecenterDeployDockerImageReply mateContainer = 14 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "按部署模式 [ mate_container ] 分组"}
  ];
  FilecenterDeployShellReply shell = 15 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "按部署模式 [ shell ] 分组"}
  ];
  FilecenterDeployDockerImageReply helm = 16 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "按部署模式 [ helm ] 分组"}
  ];
  FilecenterDeployRpmReply rpm = 17 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "按部署模式 [ rpm ] 分组"}
  ];
  FilecenterDeployMateBinReply mateBin = 18 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "按部署模式 [ mate_bin ] 分组"}
  ];
  FilecenterDeployRpmReply mateV2 = 19 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "按部署模式 [ mate_v2 ] 分组"}
  ];
  FilecenterDeployIaasImageReply iaasImage = 20 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "按部署模式 [ iaas_image ] 分组"}
  ];
}
message DescribesFilecenterReq {
  option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_schema) = {json_schema: {required: ["pageNumber", "pageSize"]}};
  int64 pageNumber = 1 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "页码:\n-1 - 全部\n0 - 第1页\n1 - 第2页\n- 以此类推...", default: "0"},
    (validate.rules).int64 = {gte: -1, lte: 16777216}
  ];
  int64 pageSize = 2 [(validate.rules).int64 = {gte: 1, lte: 16777216}];
  repeated wingo.api.Filter filters = 3 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "返回字段皆支持筛选，筛选扩展:\nWith:{模块名} - 返回关联表(此类过滤项无value，所以可将同类name合并name=.的values中)\nWith:{模块名}.{字段名} - 过滤并返回关联表\nHas:{模块名}.{字段名} - 子查询(等值连接)"}
  ];
  repeated wingo.api.Sort sorts = 4 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "返回字段皆支持排序，排序扩展:\nWith:{模块名}.[ Min Max Count Sum ]:{字段名} - 按关联表字段排序"}
  ];
}
message DescribesFilecenterReply {
  repeated DescribeFilecenterReply list = 1;
  int64 totalCount = 2;
}

message CreateDeliveryReq {
  option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_schema) = {json_schema: {required: ["must"]}};
  int64 id = 1 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "ID,仅供导入"},
    (google.api.field_visibility).restriction = "INTERNAL",
    (tagger.tags) = 'excel:"ID"'
  ];
  repeated string policy = 3 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "策略:\noverride - 覆盖相同标品"}
  ];
  DescribeReq goods = 4 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "关联标品信息"},
    (tagger.tags) = 'excel:"关联标品信息" required:"true"'
  ];
  string must = 5 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "是否必部署:\n[ no yes ]"},
    (validate.rules).string = {in: "no", in: "yes"},
    (tagger.tags) = 'excel:"是否必部署" required:"true" excel-valid-enum:"no,yes"'
  ];
}
message DeleteDeliveryReq {
  option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_schema) = {json_schema: {required: ["id"]}};
  int64 id = 1 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "ID"},
    (validate.rules).int64 = {gte: 1}
  ];
}
message ModifyDeliveryReq {
  option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_schema) = {json_schema: {required: ["id"]}};
  int64 id = 1 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "ID"},
    (validate.rules).int64 = {gte: 1}
  ];
  repeated string clearFields = 3 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "清空字段:\n"}
  ];
  string must = 5 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "是否必部署:\n[ no yes ]"},
    (validate.rules).string = {ignore_empty: true, in: "no", in: "yes"}
  ];
}
message DescribeDeliveryReq {
  option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_schema) = {json_schema: {required: ["id"]}};
  int64 id = 1 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "ID"},
    (validate.rules).int64 = {gte: 1}
  ];
  repeated wingo.api.Filter filters = 2 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "返回字段皆支持筛选，筛选扩展:\nWith:{模块名} - 返回关联表(此类过滤项无value，所以可将同类name合并name=.的values中)\nWith:{模块名}.{字段名} - 过滤并返回关联表"}
  ];
}
message DescribeDeliveryReply {
  int64 id = 1 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "ID"}
  ];
  string createdAt = 3 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "创建时间,UTC格式(毫秒级24位)"}
  ];
  DescribeReply goods = 4 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "关联标品,filter[With:Goods]"}
  ];
  string must = 5 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "是否必部署:\n[ no yes ]"}
  ];
}
message DeliveryReply {
  int64 id = 1 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "ID"}
  ];
  string createdAt = 3 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "创建时间,UTC格式(毫秒级24位)"}
  ];
  string goodsId = 4 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "关联标品ID,filter[With:Goods]"}
  ];
  string must = 5 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "是否必部署:\n[ no yes ]"}
  ];
}
message DescribesDeliveryReq {
  option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_schema) = {json_schema: {required: ["pageNumber", "pageSize"]}};
  int64 pageNumber = 1 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "页码:\n-1 - 全部\n0 - 第1页\n1 - 第2页\n- 以此类推...", default: "0"},
    (validate.rules).int64 = {gte: -1, lte: 16777216}
  ];
  int64 pageSize = 2 [(validate.rules).int64 = {gte: 1, lte: 16777216}];
  repeated wingo.api.Filter filters = 3 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "返回字段皆支持筛选，筛选扩展:\nWith:{模块名} - 返回关联表(此类过滤项无value，所以可将同类name合并name=.的values中)\nWith:{模块名}.{字段名} - 过滤并返回关联表\nHas:{模块名}.{字段名} - 子查询(等值连接)"}
  ];
  repeated wingo.api.Sort sorts = 4 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "返回字段皆支持排序，排序扩展:\nWith:{模块名}.[ Min Max Count Sum ]:{字段名} - 按关联表字段排序"}
  ];
}
message DescribesDeliveryReply {
  repeated DescribeDeliveryReply list = 1;
  int64 totalCount = 2;
}

message CreateDomainReq {
  option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_schema) = {json_schema: {required: ["template", "domainUse"]}};
  int64 id = 1 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "ID,仅供导入"},
    (google.api.field_visibility).restriction = "INTERNAL",
    (tagger.tags) = 'excel:"ID"'
  ];
  repeated string policy = 3 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "策略:\noverride - 覆盖相同标品,域名模板,域名端口"}
  ];
  DescribeReq goods = 4 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "关联标品信息"},
    (tagger.tags) = 'excel:"关联标品信息" required:"true"'
  ];
  string template = 5 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "域名模板"},
    (validate.rules).string = {min_bytes: 1, max_bytes: 255},
    (tagger.tags) = 'excel:"域名模板" required:"true" excel-valid-len:"1,255"'
  ];
  string domainUse = 6 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "域名类型:\n[ loadBalancer a ]"},
    (validate.rules).string = {in: "loadBalancer", in: "a"},
    (tagger.tags) = 'excel:"域名类型" required:"true" excel-valid-enum:"loadBalancer,a"'
  ];
  int32 targetPort = 7 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "服务端口号", default: "0"},
    (validate.rules).int32 = {gte: 0, lte: 16777216},
    (tagger.tags) = 'excel:"服务端口号" excel-valid-len:"0,16777216"'
  ];
  string protocol = 8 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "协议:\n[ http https tcp ]"},
    (validate.rules).string = {ignore_empty: true, in: "http", in: "https", in: "tcp"},
    (tagger.tags) = 'excel:"协议" required:"true" excel-valid-enum:"http,https,tcp"'
  ];
  repeated string confBlock = 9 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "配置块"}
  ];
  string description = 10 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "描述"},
    (validate.rules).string = {max_bytes: 255},
    (tagger.tags) = 'excel:"描述" excel-valid-len:"0,255"'
  ];
  int32 port = 11 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "域名端口号", default: "0"},
    (validate.rules).int32 = {gte: 0, lte: 16777216},
    (tagger.tags) = 'excel:"域名端口号" excel-valid-len:"0,16777216"'
  ];
}
message DeleteDomainReq {
  option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_schema) = {json_schema: {required: ["id"]}};
  int64 id = 1 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "ID"},
    (validate.rules).int64 = {gte: 1}
  ];
}
message ModifyDomainReq {
  option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_schema) = {json_schema: {required: ["id"]}};
  int64 id = 1 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "ID"},
    (validate.rules).int64 = {gte: 1}
  ];
  repeated string clearFields = 3 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "清空字段:\ndescription - 描述"}
  ];
  string template = 5 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "域名模板"},
    (validate.rules).string = {min_bytes: 0, max_bytes: 255}
  ];
  string domainUse = 6 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "域名类型:\n[ loadBalancer a ]"},
    (validate.rules).string = {ignore_empty: true, in: "loadBalancer", in: "a"}
  ];
  int32 targetPort = 7 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "服务端口号"},
    (validate.rules).int32 = {gte: 0, lte: 16777216}
  ];
  string protocol = 8 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "协议:\n[ http https tcp ]"},
    (validate.rules).string = {ignore_empty: true, in: "http", in: "https", in: "tcp"}
  ];
  repeated string confBlock = 9 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "配置块"}
  ];
  string description = 10 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "描述"},
    (validate.rules).string = {max_bytes: 255}
  ];
  int32 port = 11 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "域名端口号"}
  ];
}
message DescribeDomainReq {
  option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_schema) = {json_schema: {required: ["id"]}};
  int64 id = 1 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "ID"},
    (validate.rules).int64 = {gte: 1}
  ];
  repeated wingo.api.Filter filters = 2 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "返回字段皆支持筛选，筛选扩展:\nWith:{模块名} - 返回关联表(此类过滤项无value，所以可将同类name合并name=.的values中)\nWith:{模块名}.{字段名} - 过滤并返回关联表"}
  ];
}
message DescribeDomainReply {
  int64 id = 1 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "ID"}
  ];
  string createdAt = 3 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "创建时间,UTC格式(毫秒级24位)"}
  ];
  DescribeReply goods = 4 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "关联标品,filter[With:Goods]"}
  ];
  string template = 5 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "域名模板"}
  ];
  string domainUse = 6 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "域名类型:\n[ loadBalancer a ]"}
  ];
  int32 targetPort = 7 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "服务端口号"}
  ];
  string protocol = 8 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "协议:\n[ http https tcp ]"}
  ];
  repeated string confBlock = 9 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "配置块"}
  ];
  string description = 10 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "描述"}
  ];
  int32 port = 11 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "域名端口号"}
  ];
}
message DomainReply {
  int64 id = 1 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "ID"}
  ];
  string createdAt = 3 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "创建时间,UTC格式(毫秒级24位)"}
  ];
  string goodsId = 4 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "关联标品ID,filter[With:Goods]"}
  ];
  string template = 5 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "域名模板"}
  ];
  string domainUse = 6 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "域名类型:\n[ loadBalancer a ]"}
  ];
  int32 targetPort = 7 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "服务端口号"}
  ];
  string protocol = 8 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "协议:\n[ http https tcp ]"}
  ];
  repeated string confBlock = 9 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "配置块"}
  ];
  string description = 10 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "描述"}
  ];
  int32 port = 11 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "域名端口号"}
  ];
}
message DescribesDomainReq {
  option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_schema) = {json_schema: {required: ["pageNumber", "pageSize"]}};
  int64 pageNumber = 1 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "页码:\n-1 - 全部\n0 - 第1页\n1 - 第2页\n- 以此类推...", default: "0"},
    (validate.rules).int64 = {gte: -1, lte: 16777216}
  ];
  int64 pageSize = 2 [(validate.rules).int64 = {gte: 1, lte: 16777216}];
  repeated wingo.api.Filter filters = 3 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "返回字段皆支持筛选，筛选扩展:\nWith:{模块名} - 返回关联表(此类过滤项无value，所以可将同类name合并name=.的values中)\nWith:{模块名}.{字段名} - 过滤并返回关联表\nHas:{模块名}.{字段名} - 子查询(等值连接)"}
  ];
  repeated wingo.api.Sort sorts = 4 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "返回字段皆支持排序，排序扩展:\nWith:{模块名}.[ Min Max Count Sum ]:{字段名} - 按关联表字段排序"}
  ];
}
message DescribesDomainReply {
  repeated DescribeDomainReply list = 1;
  int64 totalCount = 2;
}

message CreateFlavorReq {
  option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_schema) = {json_schema: {required: ["clusterSize", "arch"]}};
  int64 id = 1 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "ID,仅供导入"},
    (google.api.field_visibility).restriction = "INTERNAL",
    (tagger.tags) = 'excel:"ID"'
  ];
  repeated string policy = 3 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "策略:\noverride - 覆盖相同标品,集群规模,架构类型"}
  ];
  DescribeReq goods = 4 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "关联标品信息"},
    (tagger.tags) = 'excel:"关联标品信息" required:"true"'
  ];
  string clusterSize = 5 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "集群规模:\n[ poc light small middle large ]"},
    (validate.rules).string = {in: "poc", in: "light", in: "small", in: "middle", in: "large"},
    (tagger.tags) = 'excel:"集群规模" required:"true" excel-valid-enum:"poc,light,small,middle,large"'
  ];
  int32 coreRequest = 6 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "CPU申请/‰核"},
    (validate.rules).int32 = {gte: 0, lte: 16777216},
    (tagger.tags) = 'excel:"CPU申请/‰核" excel-valid-len:"0,1677216"'
  ];
  int32 coreLimit = 7 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "CPU极限/‰核"},
    (validate.rules).int32 = {gte: 0, lte: 16777216},
    (tagger.tags) = 'excel:"CPU极限/‰核" excel-valid-len:"0,1677216"'
  ];
  int32 memoryRequest = 8 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "内存申请/Mi"},
    (validate.rules).int32 = {gte: 0, lte: 16777216},
    (tagger.tags) = 'excel:"内存申请/Mi" excel-valid-len:"0,1677216"'
  ];
  int32 memoryLimit = 9 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "内存极限/Mi"},
    (validate.rules).int32 = {gte: 0, lte: 16777216},
    (tagger.tags) = 'excel:"内存极限/Mi" excel-valid-len:"0,1677216"'
  ];
  int32 diskRequest = 10 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "硬盘申请/Gi"},
    (validate.rules).int32 = {gte: 0, lte: 16777216},
    (tagger.tags) = 'excel:"硬盘申请/Gi" excel-valid-len:"0,1677216"'
  ];
  int32 diskLimit = 11 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "硬盘极限/Gi"},
    (validate.rules).int32 = {gte: 0, lte: 16777216},
    (tagger.tags) = 'excel:"硬盘极限/Gi" excel-valid-len:"0,1677216"'
  ];
  int32 replicas = 12 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "副本数"},
    (validate.rules).int32 = {gte: 0, lte: 16777216},
    (tagger.tags) = 'excel:"副本数" excel-valid-len:"0,1677216"'
  ];
  string arch = 13 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "架构类型:\n[ x86_64 arm ]"},
    (validate.rules).string = {in: "x86_64", in: "arm"},
    (tagger.tags) = 'excel:"架构类型" required:"true" excel-valid-enum:"x86_64,arm"'
  ];
}
message DeleteFlavorReq {
  option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_schema) = {json_schema: {required: ["id"]}};
  int64 id = 1 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "ID"},
    (validate.rules).int64 = {gte: 1}
  ];
}
message ModifyFlavorReq {
  option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_schema) = {json_schema: {required: ["id"]}};
  int64 id = 1 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "ID"},
    (validate.rules).int64 = {gte: 1}
  ];
  repeated string clearFields = 3 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "清空字段:\n"}
  ];
  int32 coreRequest = 6 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "CPU申请/‰核"},
    (validate.rules).int32 = {lte: 16777216}
  ];
  int32 coreLimit = 7 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "CPU极限/‰核"},
    (validate.rules).int32 = {lte: 16777216}
  ];
  int32 memoryRequest = 8 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "内存申请/Mi"},
    (validate.rules).int32 = {lte: 16777216}
  ];
  int32 memoryLimit = 9 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "内存极限/Mi"},
    (validate.rules).int32 = {lte: 16777216}
  ];
  int32 diskRequest = 10 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "硬盘申请/Gi"},
    (validate.rules).int32 = {lte: 16777216}
  ];
  int32 diskLimit = 11 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "硬盘极限/Gi"},
    (validate.rules).int32 = {lte: 16777216}
  ];
  int32 replicas = 12 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "副本数"},
    (validate.rules).int32 = {lte: 16777216}
  ];
}
message DescribeFlavorReq {
  option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_schema) = {json_schema: {required: ["id"]}};
  int64 id = 1 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "ID"},
    (validate.rules).int64 = {gte: 1}
  ];
  repeated wingo.api.Filter filters = 2 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "返回字段皆支持筛选，筛选扩展:\nWith:{模块名} - 返回关联表(此类过滤项无value，所以可将同类name合并name=.的values中)\nWith:{模块名}.{字段名} - 过滤并返回关联表"}
  ];
}
message DescribeFlavorReply {
  int64 id = 1 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "ID"}
  ];
  string createdAt = 3 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "创建时间,UTC格式(毫秒级24位)"}
  ];
  DescribeReply goods = 4 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "关联标品,filter[With:Goods]"}
  ];
  string clusterSize = 5 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "集群规模:\n[ poc light small middle large ]"}
  ];
  int32 coreRequest = 6 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "CPU申请/‰核"}
  ];
  int32 coreLimit = 7 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "CPU极限/‰核"}
  ];
  int32 memoryRequest = 8 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "内存申请/Mi"}
  ];
  int32 memoryLimit = 9 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "内存极限/Mi"}
  ];
  int32 diskRequest = 10 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "硬盘申请/Gi"}
  ];
  int32 diskLimit = 11 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "硬盘极限/Gi"}
  ];
  int32 replicas = 12 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "副本数"}
  ];
  string arch = 13 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "架构类型:\n[ x86_64 arm ]"}
  ];
}
message FlavorReply {
  int64 id = 1 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "ID"}
  ];
  string createdAt = 3 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "创建时间,UTC格式(毫秒级24位)"}
  ];
  string goodsId = 4 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "关联标品ID,filter[With:Goods]"}
  ];
  string clusterSize = 5 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "集群规模:\n[ poc light small middle large ]"}
  ];
  int32 coreRequest = 6 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "CPU申请/‰核"}
  ];
  int32 coreLimit = 7 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "CPU极限/‰核"}
  ];
  int32 memoryRequest = 8 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "内存申请/Mi"}
  ];
  int32 memoryLimit = 9 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "内存极限/Mi"}
  ];
  int32 diskRequest = 10 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "硬盘申请/Gi"}
  ];
  int32 diskLimit = 11 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "硬盘极限/Gi"}
  ];
  int32 replicas = 12 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "副本数"}
  ];
  string arch = 13 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "架构类型:\n[ x86_64 arm ]"}
  ];
}
message DescribesFlavorReq {
  option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_schema) = {json_schema: {required: ["pageNumber", "pageSize"]}};
  int64 pageNumber = 1 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "页码:\n-1 - 全部\n0 - 第1页\n1 - 第2页\n- 以此类推...", default: "0"},
    (validate.rules).int64 = {gte: -1, lte: 16777216}
  ];
  int64 pageSize = 2 [(validate.rules).int64 = {gte: 1, lte: 16777216}];
  repeated wingo.api.Filter filters = 3 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "返回字段皆支持筛选，筛选扩展:\nWith:{模块名} - 返回关联表(此类过滤项无value，所以可将同类name合并name=.的values中)\nWith:{模块名}.{字段名} - 过滤并返回关联表\nHas:{模块名}.{字段名} - 子查询(等值连接)"}
  ];
  repeated wingo.api.Sort sorts = 4 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "返回字段皆支持排序，排序扩展:\nWith:{模块名}.[ Min Max Count Sum ]:{字段名} - 按关联表字段排序"}
  ];
}
message DescribesFlavorReply {
  repeated DescribeFlavorReply list = 1;
  int64 totalCount = 2;
}

message CreateIacReq {
  option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_schema) = {json_schema: {required: ["mode"]}};
  int64 id = 1 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "ID,仅供导入"},
    (google.api.field_visibility).restriction = "INTERNAL",
    (tagger.tags) = 'excel:"ID"'
  ];
  repeated string policy = 3 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "策略:\noverride - 覆盖相同标品,交付类型"}
  ];
  DescribeReq goods = 4 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "关联标品信息"},
    (tagger.tags) = 'excel:"关联标品信息" required:"true"'
  ];
  string mode = 5 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "交付类型:\n[ deploy upgrade ]"},
    (validate.rules).string = {in: "deploy", in: "upgrade"},
    (tagger.tags) = 'excel:"交付类型" required:"true" excel-valid-enum:"deploy,upgrade"'
  ];
}
message DeleteIacReq {
  option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_schema) = {json_schema: {required: ["id"]}};
  int64 id = 1 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "ID"},
    (validate.rules).int64 = {gte: 1}
  ];
}
message ModifyIacReq {
  option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_schema) = {json_schema: {required: ["id"]}};
  int64 id = 1 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "ID"},
    (validate.rules).int64 = {gte: 1}
  ];
  repeated string clearFields = 3 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "清空字段:\n"}
  ];
}
message DescribeIacReq {
  option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_schema) = {json_schema: {required: ["id"]}};
  int64 id = 1 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "ID"},
    (validate.rules).int64 = {gte: 1}
  ];
  repeated wingo.api.Filter filters = 2 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "返回字段皆支持筛选，筛选扩展:\nWith:{模块名} - 返回关联表(此类过滤项无value，所以可将同类name合并name=.的values中)\nWith:{模块名}.{字段名} - 过滤并返回关联表"}
  ];
}
message DescribeIacReply {
  int64 id = 1 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "ID"}
  ];
  string createdAt = 3 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "创建时间,UTC格式(毫秒级24位)"}
  ];
  DescribeReply goods = 4 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "关联标品,filter[With:Goods]"}
  ];
  string mode = 5 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "交付类型:\n[ deploy upgrade ]"}
  ];
}
message IacReply {
  int64 id = 1 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "ID"}
  ];
  string createdAt = 3 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "创建时间,UTC格式(毫秒级24位)"}
  ];
  string goodsId = 4 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "关联标品ID,filter[With:Goods]"}
  ];
  string mode = 5 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "交付类型:\n[ deploy upgrade ]"}
  ];
}
message DescribesIacReq {
  option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_schema) = {json_schema: {required: ["pageNumber", "pageSize"]}};
  int64 pageNumber = 1 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "页码:\n-1 - 全部\n0 - 第1页\n1 - 第2页\n- 以此类推...", default: "0"},
    (validate.rules).int64 = {gte: -1, lte: 16777216}
  ];
  int64 pageSize = 2 [(validate.rules).int64 = {gte: 1, lte: 16777216}];
  repeated wingo.api.Filter filters = 3 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "返回字段皆支持筛选，筛选扩展:\nWith:{模块名} - 返回关联表(此类过滤项无value，所以可将同类name合并name=.的values中)\nWith:{模块名}.{字段名} - 过滤并返回关联表\nHas:{模块名}.{字段名} - 子查询(等值连接)"}
  ];
  repeated wingo.api.Sort sorts = 4 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "返回字段皆支持排序，排序扩展:\nWith:{模块名}.[ Min Max Count Sum ]:{字段名} - 按关联表字段排序"}
  ];
}
message DescribesIacReply {
  repeated DescribeIacReply list = 1;
  int64 totalCount = 2;
}

message MiddlewareParams {
  string user = 1 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "用户名"},
    (tagger.tags) = 'excel:"用户名"'
  ];
  string password = 2 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "密码"},
    (tagger.tags) = 'excel:"密码"'
  ];
  string database = 3 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "数据库"},
    (tagger.tags) = 'excel:"数据库"'
  ];
}

message CreateMiddlewareReq {
  option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_schema) = {json_schema: {required: ["name", "kind", "domainUse"]}};
  int64 id = 1 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "ID,仅供导入"},
    (google.api.field_visibility).restriction = "INTERNAL",
    (tagger.tags) = 'excel:"ID"'
  ];
  repeated DescribeReq goods = 2 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "关联标品信息,中间件已存在时仍然可以继续新增标品关联,policy[FDK:Goods]"}
  ];
  repeated string policy = 3 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "策略:\noverride - 覆盖相同标品,中间件唯一标识,软件类型\nforeignKeyDiscarded - 查出已存在的关联数据,本次请求没关联上就被淘汰,需指定FKD:{模块名}"}
  ];
  string name = 4 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "中间件唯一标识"},
    (validate.rules).string = {min_bytes: 1},
    (tagger.tags) = 'excel:"唯一标识" required:"true"'
  ];
  string kind = 5 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "软件类型:\n[ mysql postgresql elasticsearch redis zookeeper etcd clickhouse kafka mongodb ]"},
    (validate.rules).string = {in: "mysql", in: "postgresql", in: "elasticsearch", in: "redis", in: "zookeeper", in: "etcd", in: "clickhouse", in: "kafka", in: "mongodb"},
    (tagger.tags) = 'excel:"软件类型" required:"true" excel-valid-enum:"mysql,postgresql,elasticsearch,redis,zookeeper,etcd,clickhouse,kafka,mongodb"'
  ];
  string urlTemplate = 6 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "URL"},
    (validate.rules).string = {max_bytes: 800},
    (tagger.tags) = 'excel:"URL" excel-valid-len:"0,800"'
  ];
  string protocol = 7 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "协议:\n[ http https tcp ]"},
    (validate.rules).string = {ignore_empty: true, in: "http", in: "https", in: "tcp"},
    (tagger.tags) = 'excel:"协议" excel-valid-enum:"http,https,tcp"'
  ];
  string domainUse = 8 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "域名类型:\n[ loadBalancer a ]"},
    (validate.rules).string = {in: "loadBalancer", in: "a"},
    (tagger.tags) = 'excel:"域名类型" required:"true" excel-valid-enum:"loadBalancer,a"'
  ];
  int32 targetPort = 9 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "服务端口号"},
    (validate.rules).int32 = {gte: 0, lte: 16777216},
    (tagger.tags) = 'excel:"服务端口号" excel-valid-len:"0,16777216"'
  ];
  MiddlewareParams params = 10 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "配置"},
    (tagger.tags) = 'excel:"配置"'
  ];
  int32 port = 11 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "域名端口号", default: "0"},
    (validate.rules).int32 = {gte: 0, lte: 16777216},
    (tagger.tags) = 'excel:"域名端口号" excel-valid-len:"0,16777216"'
  ];
  int32 coreRequest = 12 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "CPU申请/‰核", default: "0"},
    (validate.rules).int32 = {gte: 0, lte: 1000},
    (tagger.tags) = 'excel:"CPU申请/‰核(默认:0)" excel-valid-len:"0,1000"'
  ];
  int32 coreLimit = 13 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "CPU极限/‰核", default: "0"},
    (validate.rules).int32 = {gte: 0, lte: 1000},
    (tagger.tags) = 'excel:"CPU极限/‰核(默认:0)"'
  ];
  int32 memoryRequest = 14 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "内存申请/Mi", default: "0"},
    (validate.rules).int32 = {gte: 0},
    (tagger.tags) = 'excel:"内存申请/Mi(默认:0)"'
  ];
  int32 memoryLimit = 15 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "内存极限/Mi", default: "0"},
    (validate.rules).int32 = {gte: 0, lte: 1000},
    (tagger.tags) = 'excel:"内存极限/Mi(默认:0)"'
  ];
  int32 diskRequest = 16 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "硬盘申请/Gi", default: "0"},
    (validate.rules).int32 = {gte: 0, lte: 1000},
    (tagger.tags) = 'excel:"硬盘申请/Gi(默认:0)"'
  ];
  int32 diskLimit = 17 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "硬盘极限/Gi", default: "0"},
    (validate.rules).int32 = {gte: 0, lte: 1000},
    (tagger.tags) = 'excel:"硬盘极限/Gi(默认:0)"'
  ];
  string shareMode = 18 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "共享类型:\n[ no yes ]", default: "no"},
    (validate.rules).string = {ignore_empty: true, in: "no", in: "yes"},
    (tagger.tags) = 'excel:"共享类型" required:"true" excel-valid-enum:"no,yes"'
  ];
}
message DeleteMiddlewareReq {
  option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_schema) = {json_schema: {required: ["id"]}};
  int64 id = 1 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "ID\n- 为0时按顺序查询字段：name+kind", default: "0"}
  ];
  string goodsId = 2 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "标品ID\n- >0时删除关联\n- 为0时删除中间件数据"}
  ];
  string name = 4 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "中间件唯一标识"}
  ];
  string kind = 5 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "软件类型:\n[ mysql postgresql elasticsearch redis zookeeper etcd clickhouse kafka mongodb ]"}
  ];
}
message ModifyMiddlewareReq {
  option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_schema) = {json_schema: {required: ["id"]}};
  int64 id = 1 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "ID"},
    (validate.rules).int64 = {gte: 1}
  ];
  repeated string clearFields = 3 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "清空字段:\n"}
  ];
  string urlTemplate = 6 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "URL"},
    (validate.rules).string = {max_bytes: 800}
  ];
  string protocol = 7 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "协议:\n[ http https tcp ]"},
    (validate.rules).string = {ignore_empty: true, in: "http", in: "https", in: "tcp"}
  ];
  string domainUse = 8 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "域名类型:\n[ loadBalancer a ]"},
    (validate.rules).string = {ignore_empty: true, in: "loadBalancer", in: "a"}
  ];
  int32 targetPort = 9 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "服务端口号"},
    (validate.rules).int32 = {gte: 0, lte: 16777216}
  ];
  MiddlewareParams params = 10 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "配置"}
  ];
  int32 port = 11 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "域名端口号"}
  ];
  int32 coreRequest = 12 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "CPU申请/‰核"},
    (validate.rules).int32 = {gte: 0, lte: 1000}
  ];
  int32 coreLimit = 13 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "CPU极限/‰核"},
    (validate.rules).int32 = {gte: 0, lte: 1000}
  ];
  int32 memoryRequest = 14 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "内存申请/Mi"},
    (validate.rules).int32 = {gte: 0}
  ];
  int32 memoryLimit = 15 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "内存极限/Mi"},
    (validate.rules).int32 = {gte: 0, lte: 1000}
  ];
  int32 diskRequest = 16 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "硬盘申请/Gi"},
    (validate.rules).int32 = {gte: 0, lte: 1000}
  ];
  int32 diskLimit = 17 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "硬盘极限/Gi"},
    (validate.rules).int32 = {gte: 0, lte: 1000}
  ];
  string shareMode = 18 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "共享类型:\n[ no yes ]"},
    (validate.rules).string = {ignore_empty: true, in: "no", in: "yes"}
  ];
}
message DescribeMiddlewareReq {
  int64 id = 1 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "ID\n- 为0时按顺序查询字段：name+kind", default: "0"}
  ];
  repeated wingo.api.Filter filters = 2 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "返回字段皆支持筛选，筛选扩展:\nWith:{模块名} - 返回关联表(此类过滤项无value，所以可将同类name合并name=.的values中)\nWith:{模块名}.{字段名} - 过滤并返回关联表"}
  ];
  string name = 4 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "中间件唯一标识"}
  ];
  string kind = 5 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "软件类型:\n[ mysql postgresql elasticsearch redis zookeeper etcd clickhouse kafka mongodb ]"}
  ];
}
message DescribeMiddlewareReply {
  int64 id = 1 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "ID"}
  ];
  repeated DescribeReply goods = 2 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "关联标品,filter[With:Goods]"}
  ];
  string createdAt = 3 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "创建时间,UTC格式(毫秒级24位)"}
  ];
  string name = 4 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "中间件唯一标识"}
  ];
  string kind = 5 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "软件类型:\n[ mysql postgresql elasticsearch redis zookeeper etcd clickhouse kafka mongodb ]"}
  ];
  string urlTemplate = 6 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "URL"}
  ];
  string protocol = 7 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "协议:\n[ http https tcp ]"}
  ];
  string domainUse = 8 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "域名类型:\n[ loadBalancer a ]"}
  ];
  int32 targetPort = 9 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "服务端口号"}
  ];
  MiddlewareParams params = 10 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "配置"}
  ];
  int32 port = 11 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "域名端口号"}
  ];
  int32 coreRequest = 12 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "CPU申请/‰核"}
  ];
  int32 coreLimit = 13 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "CPU极限/‰核"}
  ];
  int32 memoryRequest = 14 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "内存申请/Mi"}
  ];
  int32 memoryLimit = 15 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "内存极限/Mi"}
  ];
  int32 diskRequest = 16 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "硬盘申请/Gi"}
  ];
  int32 diskLimit = 17 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "硬盘极限/Gi"}
  ];
  string shareMode = 18 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "共享类型:\n[ no yes ]"}
  ];
}
message MiddlewareReply {
  int64 id = 1 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "ID"}
  ];
  string createdAt = 3 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "创建时间,UTC格式(毫秒级24位)"}
  ];
  string name = 4 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "中间件唯一标识"}
  ];
  string kind = 5 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "软件类型:\n[ mysql postgresql elasticsearch redis zookeeper etcd clickhouse kafka mongodb ]"}
  ];
  string urlTemplate = 6 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "URL"}
  ];
  string protocol = 7 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "协议:\n[ http https tcp ]"}
  ];
  string domainUse = 8 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "域名类型:\n[ loadBalancer a ]"}
  ];
  int32 targetPort = 9 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "服务端口号"}
  ];
  MiddlewareParams params = 10 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "配置"}
  ];
  int32 port = 11 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "域名端口号"}
  ];
  int32 coreRequest = 12 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "CPU申请/‰核"}
  ];
  int32 coreLimit = 13 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "CPU极限/‰核"}
  ];
  int32 memoryRequest = 14 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "内存申请/Mi"}
  ];
  int32 memoryLimit = 15 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "内存极限/Mi"}
  ];
  int32 diskRequest = 16 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "硬盘申请/Gi"}
  ];
  int32 diskLimit = 17 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "硬盘极限/Gi"}
  ];
  string shareMode = 18 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "共享类型:\n[ no yes ]"}
  ];
}
message DescribesMiddlewareReq {
  option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_schema) = {json_schema: {required: ["pageNumber", "pageSize"]}};
  int64 pageNumber = 1 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "页码:\n-1 - 全部\n0 - 第1页\n1 - 第2页\n- 以此类推...", default: "0"},
    (validate.rules).int64 = {gte: -1, lte: 16777216}
  ];
  int64 pageSize = 2 [(validate.rules).int64 = {gte: 1, lte: 16777216}];
  repeated wingo.api.Filter filters = 3 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "返回字段皆支持筛选，筛选扩展:\nWith:{模块名} - 返回关联表(此类过滤项无value，所以可将同类name合并name=.的values中)\nWith:{模块名}.{字段名} - 过滤并返回关联表\nHas:{模块名}.{字段名} - 子查询(等值连接)"}
  ];
  repeated wingo.api.Sort sorts = 4 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "返回字段皆支持排序，排序扩展:\nWith:{模块名}.[ Min Max Count Sum ]:{字段名} - 按关联表字段排序"}
  ];
}
message DescribesMiddlewareReply {
  repeated DescribeMiddlewareReply list = 1;
  int64 totalCount = 2;
}

message CreateHardwareRoleUnionReq {
  option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_schema) = {json_schema: {required: ["goods"]}};
  DescribeReq goods = 1 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "关联标品信息,标品已存在时仍然可以继续新增标品关联"},
    (validate.rules).message = {required: true}
  ];
  repeated jdstack.zeus.v1.hardwareRole.DescribeReq hardwareRole = 2 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "关联硬件角色信息"}
  ];
  repeated jdstack.zeus.v1.hardwareRole.CreateLabelReq hardwareRoleLabel = 3 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "关联硬件角色标签信息"}
  ];
  repeated string policy = 4 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "策略:"}
  ];
}

message CreateHardwareRoleReq {
  option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_schema) = {json_schema: {required: ["goodsId", "hardwareRoleId"]}};
  string goodsId = 1 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "标品ID"},
    (validate.rules).string = {min_bytes: 1},
    (tagger.tags) = 'excel:"标品ID" required:"true"'
  ];
  int64 hardwareRoleId = 2 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "硬件角色ID"},
    (validate.rules).int64 = {gte: 1},
    (tagger.tags) = 'excel:"硬件角色ID" required:"true"'
  ];
  repeated string policy = 3 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "策略:"}
  ];
}
message DeleteHardwareRoleReq {
  option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_schema) = {json_schema: {required: ["goodsId", "hardwareRoleId"]}};
  string goodsId = 1 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "标品ID"},
    (validate.rules).string = {min_bytes: 1}
  ];
  int64 hardwareRoleId = 2 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "硬件角色ID"},
    (validate.rules).int64 = {gte: 1}
  ];
}
message DescribeHardwareRoleReq {
  int64 id = 1 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "ID\n- 为0时按顺序查询字段：name"},
    (validate.rules).int64 = {ignore_empty: true, gte: 0}
  ];
  string name = 2 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "角色名称"},
    (tagger.tags) = 'excel:"角色名称" required:"true"'
  ];
  repeated wingo.api.Filter filters = 3 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "返回字段皆支持筛选，筛选扩展:\nWith:{模块名} - 返回关联表(此类过滤项无value，所以可将同类name合并name=.的values中)\nWith:{模块名}.{字段名} - 过滤并返回关联表"}
  ];
}
message DescribeHardwareRoleReply {
  int64 id = 1 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "ID"}
  ];
  string name = 2 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "角色名称"}
  ];
  string createdAt = 3 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "创建时间,UTC格式(毫秒级24位)"}
  ];
  string hardware = 4 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "硬件类型:\n[ server network ]"}
  ];
  string description = 5 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "描述"}
  ];
  jdstack.zeus.v1.version.DescribeReply fromVersion = 6 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "始于JDStack版本信息,filter[With:FormJdstackRelease]"}
  ];
  repeated jdstack.zeus.v1.version.DescribeReply version = 7 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "关联JDStack版本信息,filter[With:JdstackRelease]"}
  ];
  repeated jdstack.zeus.v1.serverPlane.DescribeReply serverPlane = 8 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "关联服务器规划信息,filter[With:ServerPlane]"}
  ];
  repeated HardwareRoleLabelReply label = 9 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "关联硬件角色标签信息,filter[With:HardwareRoleLabel]"}
  ];
  repeated Reply goods = 10 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "关联标品,filter[With:Goods]"}
  ];
}
message HardwareRoleReply {
  int64 id = 1 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "ID"}
  ];
  string name = 2 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "角色名称"}
  ];
  string createdAt = 3 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "创建时间,UTC格式(毫秒级24位)"}
  ];
  string hardware = 4 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "硬件类型:\n[ server network ]"}
  ];
  string description = 5 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "描述"}
  ];
  jdstack.zeus.v1.version.DescribeReply fromVersion = 6 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "始于JDStack版本信息,filter[With:FormJdstackRelease]"}
  ];
  repeated jdstack.zeus.v1.version.DescribeReply version = 7 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "关联JDStack版本信息,filter[With:JdstackRelease]"}
  ];
  repeated jdstack.zeus.v1.serverPlane.DescribeReply serverPlane = 8 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "关联服务器规划信息,filter[With:ServerPlane]"}
  ];
  repeated HardwareRoleLabelReply label = 9 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "关联硬件角色标签信息,filter[With:HardwareRoleLabel]"}
  ];
}
message DescribesHardwareRoleReq {
  option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_schema) = {json_schema: {required: ["pageNumber", "pageSize"]}};
  int64 pageNumber = 1 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "页码:\n-1 - 全部\n0 - 第1页\n1 - 第2页\n- 以此类推...", default: "0"},
    (validate.rules).int64 = {gte: -1, lte: 16777216}
  ];
  int64 pageSize = 2 [(validate.rules).int64 = {gte: 1, lte: 16777216}];
  repeated wingo.api.Filter filters = 3 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "返回字段皆支持筛选，筛选扩展:\nWith:{模块名} - 返回关联表(此类过滤项无value，所以可将同类name合并name=.的values中)\nWith:{模块名}.{字段名} - 过滤并返回关联表\nHas:{模块名}.{字段名} - 子查询(等值连接)"}
  ];
  repeated wingo.api.Sort sorts = 4 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "返回字段皆支持排序，排序扩展:\nWith:{模块名}.[ Min Max Count Sum ]:{字段名} - 按关联表字段排序"}
  ];
}
message DescribesHardwareRoleReply {
  repeated DescribeHardwareRoleReply list = 1;
  int64 totalCount = 2;
}

message CreateHardwareRoleLabelReq {
  option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_schema) = {json_schema: {required: ["goodsId", "hardwareRoleLabelId"]}};
  string goodsId = 1 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "标品ID"},
    (validate.rules).string = {min_bytes: 1},
    (tagger.tags) = 'excel:"标品ID" required:"true"'
  ];
  int64 hardwareRoleLabelId = 2 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "硬件角色标签ID"},
    (validate.rules).int64 = {gte: 1},
    (tagger.tags) = 'excel:"硬件角色标签ID" required:"true"'
  ];
  repeated string policy = 3 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "策略:"}
  ];
}
message DeleteHardwareRoleLabelReq {
  option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_schema) = {json_schema: {required: ["goodsId", "hardwareRoleLabelId"]}};
  string goodsId = 1 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "标品ID"},
    (validate.rules).string = {min_bytes: 1}
  ];
  int64 hardwareRoleLabelId = 2 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "硬件角色标签ID"},
    (validate.rules).int64 = {gte: 1}
  ];
}
message DescribeHardwareRoleLabelReq {
  int64 id = 1 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "ID\n- 为0时按顺序查询字段：key、value"},
    (validate.rules).int64 = {ignore_empty: true, gte: 0}
  ];
  string key = 2 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "标签键"},
    (tagger.tags) = 'excel:"标签键" required:"true"'
  ];
  string value = 3 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "标签键"},
    (tagger.tags) = 'excel:"标签键" required:"true"'
  ];
  repeated wingo.api.Filter filters = 4 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "返回字段皆支持筛选，筛选扩展:\nWith:{模块名} - 返回关联表(此类过滤项无value，所以可将同类name合并name=.的values中)\nWith:{模块名}.{字段名} - 过滤并返回关联表"}
  ];
}
message DescribeHardwareRoleLabelReply {
  int64 id = 1 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "ID"}
  ];
  string createdAt = 3 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "创建时间,UTC格式(毫秒级24位)"}
  ];
  HardwareRoleReply role = 4 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "关联硬件角色信息,filter[With:HardwareRole]"}
  ];
  string key = 5 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "标签键"}
  ];
  string value = 6 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "标签值"}
  ];
  string description = 7 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "描述"}
  ];
  repeated Reply goods = 8 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "关联标品,filter[With:Goods]"}
  ];
}
message HardwareRoleLabelReply {
  int64 id = 1 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "ID"}
  ];
  string createdAt = 3 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "创建时间,UTC格式(毫秒级24位)"}
  ];
  int64 hardwareRoleId = 4 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "关联硬件角色ID,filter[With:HardwareRole]"}
  ];
  string key = 5 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "标签键"}
  ];
  string value = 6 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "标签值"}
  ];
  string description = 7 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "描述"}
  ];
}
message DescribesHardwareRoleLabelReq {
  option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_schema) = {json_schema: {required: ["pageNumber", "pageSize"]}};
  int64 pageNumber = 1 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "页码:\n-1 - 全部\n0 - 第1页\n1 - 第2页\n- 以此类推...", default: "0"},
    (validate.rules).int64 = {gte: -1, lte: 16777216}
  ];
  int64 pageSize = 2 [(validate.rules).int64 = {gte: 1, lte: 16777216}];
  repeated wingo.api.Filter filters = 3 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "返回字段皆支持筛选，筛选扩展:\nWith:{模块名} - 返回关联表(此类过滤项无value，所以可将同类name合并name=.的values中)\nWith:{模块名}.{字段名} - 过滤并返回关联表\nHas:{模块名}.{字段名} - 子查询(等值连接)"}
  ];
  repeated wingo.api.Sort sorts = 4 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "返回字段皆支持排序，排序扩展:\nWith:{模块名}.[ Min Max Count Sum ]:{字段名} - 按关联表字段排序"}
  ];
}
message DescribesHardwareRoleLabelReply {
  repeated DescribeHardwareRoleLabelReply list = 1;
  int64 totalCount = 2;
}

message CreateChainReq {
  option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_schema) = {json_schema: {required: ["toGoodsId", "fromGoodsId", "resKind"]}};
  int64 id = 1 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "ID,仅供导入"},
    (google.api.field_visibility).restriction = "INTERNAL",
    (tagger.tags) = 'excel:"ID"'
  ];
  repeated string policy = 3 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "策略\noverride - 覆盖相同本端标品信息,对端标品信息,关联类型,关联ID"}
  ];
  DescribeReq toGoods = 4 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "本端标品信息"}
  ];
  DescribeReq fromGoods = 5 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "对端标品信息"}
  ];
  string resKind = 6 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "关联类型:\n[ domain middleware delivery upgrade ]"},
    (validate.rules).string = {in: "domain", in: "middleware", in: "delivery", in: "upgrade"}
  ];
  int64 resId = 7 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "关联ID"}
  ];
}
message DescribeChainReq {
  int64 id = 1 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "ID"},
    (validate.rules).int64 = {ignore_empty: false, gte: 0}
  ];
  repeated wingo.api.Filter filters = 3 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "返回字段皆支持筛选，筛选扩展:\nWith:{模块名} - 返回关联表(此类过滤项无value，所以可将同类name合并name=.的values中)\nWith:{模块名}.{字段名} - 过滤并返回关联表\nbasic - 基础关联查询"}
  ];
}
message DescribeChainReply {
  int64 id = 1 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "ID"}
  ];
  string createdAt = 3 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "创建时间,UTC格式(毫秒级24位)"}
  ];
  Less toGoods = 4 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "关联本端标品信息"}
  ];
  Less fromGoods = 5 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "关联对端标品信息"}
  ];
  string resKind = 6 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "关联类型:\n[ domain middleware delivery upgrade ]"}
  ];
  int64 resId = 7 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "关联ID"}
  ];
  DomainReply domain = 8 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "关联类型Domain数据"}
  ];
  MiddlewareReply middleware = 9 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "关联类型Middleware数据"}
  ];
  string deployType = 10 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "交付类型,当前仅云舰版本使用,融合场景或标准交付场景:\n[ fusion standard ]"}
  ];
  string clusterType = 11 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "部署集群类型,当前仅限云舰版本使用:\n[ master slave ]"}
  ];
}
message DescribesChainReq {
  option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_schema) = {json_schema: {required: ["pageNumber", "pageSize"]}};
  int64 pageNumber = 1 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "页码:\n-1 - 全部\n0 - 第1页\n1 - 第2页\n- 以此类推...", default: "0"},
    (validate.rules).int64 = {gte: -1, lte: 16777216}
  ];
  int64 pageSize = 2 [(validate.rules).int64 = {gte: 1, lte: 16777216}];
  repeated wingo.api.Filter filters = 3 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "返回字段皆支持筛选，筛选扩展:\nWith:{模块名} - 返回关联表(此类过滤项无value，所以可将同类name合并name=.的values中)\nWith:{模块名}.{字段名} - 过滤并返回关联表\nHas:{模块名}.{字段名} - 子查询(等值连接)\bbasic - 基础关联查询"}
  ];
  repeated wingo.api.Sort sorts = 4 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "返回字段皆支持排序，排序扩展:\nWith:{模块名}.[ Min Max Count Sum ]:{字段名} - 按关联表字段排序"}
  ];
}
message DescribesChainReply {
  repeated DescribeChainReply list = 1;
  int64 totalCount = 2;
}

message DeleteChainReq {
  option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_schema) = {json_schema: {required: ["id"]}};
  int64 id = 1 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "标品链路(依赖)ID"},
    (validate.rules).int64 = {gte: 1}
  ];
}

message DescribePdReply {
  int64 id = 1 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "ID"}
  ];
  string createdAt = 2 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "创建时间,UTC格式(毫秒级24位)"}
  ];
  string updatedAt = 3 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "更新时间,UTC格式(毫秒级24位)"}
  ];
  string content = 4 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "pd内容"}
  ];
}
message DescribesPdReply {
  repeated DescribePdReply list = 1;
  int64 totalCount = 2;
}

message DescribeUpgradeReply {
  int64 id = 1 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "ID"}
  ];
  string createdAt = 2 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "创建时间,UTC格式(毫秒级24位)"}
  ];
  string updatedAt = 3 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "更新时间,UTC格式(毫秒级24位)"}
  ];
  string content = 4 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "升级描述内容"}
  ];
}
message DescribesUpgradeReply {
  repeated DescribeUpgradeReply list = 1;
  int64 totalCount = 2;
}

message DescribeMetadataReply {
  int64 id = 1 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "ID"}
  ];
  string createdAt = 2 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "创建时间,UTC格式(毫秒级24位)"}
  ];
  string updatedAt = 3 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "更新时间,UTC格式(毫秒级24位)"}
  ];
  string type = 4 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "元数据类型:\n[ business apigw ]"}
  ];
  string versionName = 5 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "元数据唯一标识"}
  ];
  string versionId = 6 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "元数据唯一标识Id"}
  ];
  int64 unifiedMetadataId = 7 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "存储元数据主键Id"}
  ];
}
message DescribesMetadataReply {
  repeated DescribeMetadataReply list = 1;
  int64 totalCount = 2;
}
message CreateConfigReq {
  option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_schema) = {json_schema: {required: ["content"]}};
  int64 id = 1 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "ID,仅供导入"},
    (google.api.field_visibility).restriction = "INTERNAL"
  ];
  DescribeReq goods = 2 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "关联标品信息"}
  ];
  repeated string policy = 3 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "策略:\noverride - 覆盖相同标品"}
  ];
  string kind = 4 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "配置类型:\n[ deployType ]"}
  ];

  string data = 5 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "配置数据"}
  ];
}
message DescribeConfigReply {
  int64 id = 1 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "ID"}
  ];
  string createdAt = 2 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "创建时间,UTC格式(毫秒级24位)"}
  ];
  string updatedAt = 3 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "更新时间,UTC格式(毫秒级24位)"}
  ];
  string kind = 4 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "配置类型:\n[ deployType ]"}
  ];
  string data = 5 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "配置数据"}
  ];
}
message CreateDeliveryRuleGroupReq {
  string name = 1 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "标品交付规则组名称"}
  ];
}

message GoodsInfo {
  string category = 1 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "分类"}];
  string service = 2 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "serviceCode"}];
  string resource = 3 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "产品code"}];
  string application = 4 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "应用code"}];
}

message SyncGoodsActionReq {
  option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_schema) = {json_schema: {required: ["version", "goodsList"]}};
  string version = 1 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "JDStack版本"},
    (validate.rules).string = {min_bytes: 1, pattern: "^[0-9a-zA-Z.]+$"}
  ];
  repeated GoodsInfo goodsList = 2 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "标品列表，不在此列表中的标品将被设置为offline状态"}];
}

message SyncGoodsActionReply {
  int32 offlineCount = 1 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "被设置为offline状态的标品数量"}];
}