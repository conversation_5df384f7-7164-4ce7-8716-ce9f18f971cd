swagger: "2.0"
info:
  title: GoodsStack_标品管理
  version: v1
paths:
  /goodsStack:
    get:
      description: 获取标品列表
      operationId: describeGoodsStacks
      responses:
        "200":
          x-jdcloud-tiered: false
          description: A successful response.
          schema:
            properties:
              error:
                properties:
                  errorObject:
                    $ref: ../../common/model/Err.yaml#/definitions/err
              requestId:
                type: string
              result:
                properties:
                  resultObject:
                    $ref: ../module/goods_stack.swagger.yaml#/definitions/GoodsStackGoodsStackDescribesReply
        default:
          description: An unexpected error response.
      parameters:
        - name: pageNumber
          description: |-
            页码:
            -1 - 全部
            0 - 第1页
            1 - 第2页
            - 以此类推...
          in: query
          required: true
          type: integer
          format: int64
          default: "0"
        - name: pageSize
          in: query
          required: true
          type: integer
          format: int64
        - name: filters
          description: |-
            返回字段皆支持筛选，筛选扩展:
            With:{模块名} - 返回关联表(此类过滤项无value，所以可将同类name合并name=.的values中)
            With:{模块名}.{字段名} - 过滤并返回关联表
            Has:{模块名}.{字段名} - 子查询(等值连接)
          in: query
          required: false
          type: array
          items:
            $ref: ../../common/model/Filter.yaml#/definitions/filter
          collectionFormat: multi
        - name: sorts
          description: |-
            返回字段皆支持排序，排序扩展:
            With:{模块名}.[ Min Max Count Sum ]:{字段名} - 按关联表字段排序
          in: query
          required: false
          type: array
          items:
            $ref: ../../common/model/Sort.yaml#/definitions/sort
          collectionFormat: multi
  /goodsStack/{goodsId}/hardwareRole/{hardwareRoleId}:
    delete:
      description: 删除关联标品硬件角色信息
      operationId: deleteGoodsStackHardwareRole
      responses:
        "200":
          x-jdcloud-tiered: false
          description: A successful response.
          schema:
            properties:
              error:
                properties:
                  errorObject:
                    $ref: ../../common/model/Err.yaml#/definitions/err
              requestId:
                type: string
        default:
          description: An unexpected error response.
      parameters:
        - name: goodsId
          description: 标品ID
          in: path
          required: true
          type: string
        - name: hardwareRoleId
          description: 硬件角色ID
          in: path
          required: true
          type: integer
          format: int64
    post:
      description: 关联标品硬件角色信息
      operationId: createGoodsStackHardwareRole
      responses:
        "200":
          x-jdcloud-tiered: false
          description: A successful response.
          schema:
            properties:
              error:
                properties:
                  errorObject:
                    $ref: ../../common/model/Err.yaml#/definitions/err
              requestId:
                type: string
        default:
          description: An unexpected error response.
      parameters:
        - name: goodsId
          description: 标品ID
          in: path
          required: true
          type: string
        - name: hardwareRoleId
          description: 硬件角色ID
          in: path
          required: true
          type: integer
          format: int64
        - name: body
          in: body
          required: true
          schema:
            type: object
            properties:
              policy:
                type: array
                items:
                  type: string
                description: '策略:'
  /goodsStack/{goodsId}/hardwareRoleLabel/{hardwareRoleLabelId}:
    delete:
      description: 删除关联标品硬件角色标签信息
      operationId: deleteGoodsStackHardwareRoleLabel
      responses:
        "200":
          x-jdcloud-tiered: false
          description: A successful response.
          schema:
            properties:
              error:
                properties:
                  errorObject:
                    $ref: ../../common/model/Err.yaml#/definitions/err
              requestId:
                type: string
        default:
          description: An unexpected error response.
      parameters:
        - name: goodsId
          description: 标品ID
          in: path
          required: true
          type: string
        - name: hardwareRoleLabelId
          description: 硬件角色标签ID
          in: path
          required: true
          type: integer
          format: int64
    post:
      description: 关联标品硬件角色标签信息
      operationId: createGoodsStackHardwareRoleLabel
      responses:
        "200":
          x-jdcloud-tiered: false
          description: A successful response.
          schema:
            properties:
              error:
                properties:
                  errorObject:
                    $ref: ../../common/model/Err.yaml#/definitions/err
              requestId:
                type: string
        default:
          description: An unexpected error response.
      parameters:
        - name: goodsId
          description: 标品ID
          in: path
          required: true
          type: string
        - name: hardwareRoleLabelId
          description: 硬件角色标签ID
          in: path
          required: true
          type: integer
          format: int64
        - name: body
          in: body
          required: true
          schema:
            type: object
            properties:
              policy:
                type: array
                items:
                  type: string
                description: '策略:'
  /goodsStackChain:
    get:
      description: 查询标品链路(依赖)
      operationId: describeGoodsStacksChain
      responses:
        "200":
          x-jdcloud-tiered: false
          description: A successful response.
          schema:
            properties:
              error:
                properties:
                  errorObject:
                    $ref: ../../common/model/Err.yaml#/definitions/err
              requestId:
                type: string
              result:
                properties:
                  resultObject:
                    $ref: ../module/goods_stack.swagger.yaml#/definitions/GoodsStackDescribesChainReply
        default:
          description: An unexpected error response.
      parameters:
        - name: pageNumber
          description: |-
            页码:
            -1 - 全部
            0 - 第1页
            1 - 第2页
            - 以此类推...
          in: query
          required: true
          type: integer
          format: int64
          default: "0"
        - name: pageSize
          in: query
          required: true
          type: integer
          format: int64
        - name: filters
          description: "返回字段皆支持筛选，筛选扩展:\nWith:{模块名} - 返回关联表(此类过滤项无value，所以可将同类name合并name=.的values中)\nWith:{模块名}.{字段名} - 过滤并返回关联表\nHas:{模块名}.{字段名} - 子查询(等值连接)\bbasic - 基础关联查询"
          in: query
          required: false
          type: array
          items:
            $ref: ../../common/model/Filter.yaml#/definitions/filter
          collectionFormat: multi
        - name: sorts
          description: |-
            返回字段皆支持排序，排序扩展:
            With:{模块名}.[ Min Max Count Sum ]:{字段名} - 按关联表字段排序
          in: query
          required: false
          type: array
          items:
            $ref: ../../common/model/Sort.yaml#/definitions/sort
          collectionFormat: multi
    post:
      description: 创建标品链路(依赖)
      operationId: createGoodsStackChain
      responses:
        "200":
          x-jdcloud-tiered: false
          description: A successful response.
          schema:
            properties:
              error:
                properties:
                  errorObject:
                    $ref: ../../common/model/Err.yaml#/definitions/err
              requestId:
                type: string
              result:
                properties:
                  resultObject:
                    $ref: ../module/goods_stack.swagger.yaml#/definitions/GoodsStackDescribeChainReply
        default:
          description: An unexpected error response.
      parameters:
        - x-jdcloud-tiered: false
          name: body
          in: body
          required: true
          schema:
            $ref: ../module/goods_stack.swagger.yaml#/definitions/GoodsStackCreateChainReq
  /goodsStackChain/{id}:
    get:
      description: 查询标品链路(依赖)
      operationId: describeGoodsStackChain
      responses:
        "200":
          x-jdcloud-tiered: false
          description: A successful response.
          schema:
            properties:
              error:
                properties:
                  errorObject:
                    $ref: ../../common/model/Err.yaml#/definitions/err
              requestId:
                type: string
              result:
                properties:
                  resultObject:
                    $ref: ../module/goods_stack.swagger.yaml#/definitions/GoodsStackDescribeChainReply
        default:
          description: An unexpected error response.
      parameters:
        - name: id
          description: ID
          in: path
          required: true
          type: integer
          format: int64
        - name: filters
          description: |-
            返回字段皆支持筛选，筛选扩展:
            With:{模块名} - 返回关联表(此类过滤项无value，所以可将同类name合并name=.的values中)
            With:{模块名}.{字段名} - 过滤并返回关联表
            basic - 基础关联查询
          in: query
          required: false
          type: array
          items:
            $ref: ../../common/model/Filter.yaml#/definitions/filter
          collectionFormat: multi
    delete:
      description: 删除标品链路(依赖)
      operationId: deleteGoodsStackChain
      responses:
        "200":
          x-jdcloud-tiered: false
          description: A successful response.
          schema:
            properties:
              error:
                properties:
                  errorObject:
                    $ref: ../../common/model/Err.yaml#/definitions/err
              requestId:
                type: string
        default:
          description: An unexpected error response.
      parameters:
        - name: id
          description: 标品链路(依赖)ID
          in: path
          required: true
          type: integer
          format: int64
  /goodsStackDelivery:
    get:
      description: 获取标品记录交付列表
      operationId: describeGoodsStacksDelivery
      responses:
        "200":
          x-jdcloud-tiered: false
          description: A successful response.
          schema:
            properties:
              error:
                properties:
                  errorObject:
                    $ref: ../../common/model/Err.yaml#/definitions/err
              requestId:
                type: string
              result:
                properties:
                  resultObject:
                    $ref: ../module/goods_stack.swagger.yaml#/definitions/GoodsStackDescribesDeliveryReply
        default:
          description: An unexpected error response.
      parameters:
        - name: pageNumber
          description: |-
            页码:
            -1 - 全部
            0 - 第1页
            1 - 第2页
            - 以此类推...
          in: query
          required: true
          type: integer
          format: int64
          default: "0"
        - name: pageSize
          in: query
          required: true
          type: integer
          format: int64
        - name: filters
          description: |-
            返回字段皆支持筛选，筛选扩展:
            With:{模块名} - 返回关联表(此类过滤项无value，所以可将同类name合并name=.的values中)
            With:{模块名}.{字段名} - 过滤并返回关联表
            Has:{模块名}.{字段名} - 子查询(等值连接)
          in: query
          required: false
          type: array
          items:
            $ref: ../../common/model/Filter.yaml#/definitions/filter
          collectionFormat: multi
        - name: sorts
          description: |-
            返回字段皆支持排序，排序扩展:
            With:{模块名}.[ Min Max Count Sum ]:{字段名} - 按关联表字段排序
          in: query
          required: false
          type: array
          items:
            $ref: ../../common/model/Sort.yaml#/definitions/sort
          collectionFormat: multi
    post:
      description: 创建标品记录交付信息
      operationId: createGoodsStackDelivery
      responses:
        "200":
          x-jdcloud-tiered: false
          description: A successful response.
          schema:
            properties:
              error:
                properties:
                  errorObject:
                    $ref: ../../common/model/Err.yaml#/definitions/err
              requestId:
                type: string
              result:
                properties:
                  resultObject:
                    $ref: ../module/goods_stack.swagger.yaml#/definitions/GoodsStackDescribeDeliveryReply
        default:
          description: An unexpected error response.
      parameters:
        - x-jdcloud-tiered: false
          name: body
          in: body
          required: true
          schema:
            $ref: ../module/goods_stack.swagger.yaml#/definitions/GoodsStackCreateDeliveryReq
  /goodsStackDelivery/{id}:
    get:
      description: 获取标品记录交付信息
      operationId: describeGoodsStackDelivery
      responses:
        "200":
          x-jdcloud-tiered: false
          description: A successful response.
          schema:
            properties:
              error:
                properties:
                  errorObject:
                    $ref: ../../common/model/Err.yaml#/definitions/err
              requestId:
                type: string
              result:
                properties:
                  resultObject:
                    $ref: ../module/goods_stack.swagger.yaml#/definitions/GoodsStackDescribeDeliveryReply
        default:
          description: An unexpected error response.
      parameters:
        - name: id
          description: ID
          in: path
          required: true
          type: integer
          format: int64
        - name: filters
          description: |-
            返回字段皆支持筛选，筛选扩展:
            With:{模块名} - 返回关联表(此类过滤项无value，所以可将同类name合并name=.的values中)
            With:{模块名}.{字段名} - 过滤并返回关联表
          in: query
          required: false
          type: array
          items:
            $ref: ../../common/model/Filter.yaml#/definitions/filter
          collectionFormat: multi
    delete:
      description: 删除标品记录交付信息
      operationId: deleteGoodsStackDelivery
      responses:
        "200":
          x-jdcloud-tiered: false
          description: A successful response.
          schema:
            properties:
              error:
                properties:
                  errorObject:
                    $ref: ../../common/model/Err.yaml#/definitions/err
              requestId:
                type: string
        default:
          description: An unexpected error response.
      parameters:
        - name: id
          description: ID
          in: path
          required: true
          type: integer
          format: int64
    put:
      description: 修改标品记录交付信息
      operationId: modifyGoodsStackDelivery
      responses:
        "200":
          x-jdcloud-tiered: false
          description: A successful response.
          schema:
            properties:
              error:
                properties:
                  errorObject:
                    $ref: ../../common/model/Err.yaml#/definitions/err
              requestId:
                type: string
        default:
          description: An unexpected error response.
      parameters:
        - name: id
          description: ID
          in: path
          required: true
          type: integer
          format: int64
        - name: body
          in: body
          required: true
          schema:
            type: object
            properties:
              clearFields:
                type: array
                items:
                  type: string
                description: |
                  清空字段:
              must:
                type: string
                description: |-
                  是否必部署:
                  [ no yes ]
  /goodsStackDomain:
    get:
      description: 获取标品记录域名列表
      operationId: describeGoodsStacksDomain
      responses:
        "200":
          x-jdcloud-tiered: false
          description: A successful response.
          schema:
            properties:
              error:
                properties:
                  errorObject:
                    $ref: ../../common/model/Err.yaml#/definitions/err
              requestId:
                type: string
              result:
                properties:
                  resultObject:
                    $ref: ../module/goods_stack.swagger.yaml#/definitions/GoodsStackDescribesDomainReply
        default:
          description: An unexpected error response.
      parameters:
        - name: pageNumber
          description: |-
            页码:
            -1 - 全部
            0 - 第1页
            1 - 第2页
            - 以此类推...
          in: query
          required: true
          type: integer
          format: int64
          default: "0"
        - name: pageSize
          in: query
          required: true
          type: integer
          format: int64
        - name: filters
          description: |-
            返回字段皆支持筛选，筛选扩展:
            With:{模块名} - 返回关联表(此类过滤项无value，所以可将同类name合并name=.的values中)
            With:{模块名}.{字段名} - 过滤并返回关联表
            Has:{模块名}.{字段名} - 子查询(等值连接)
          in: query
          required: false
          type: array
          items:
            $ref: ../../common/model/Filter.yaml#/definitions/filter
          collectionFormat: multi
        - name: sorts
          description: |-
            返回字段皆支持排序，排序扩展:
            With:{模块名}.[ Min Max Count Sum ]:{字段名} - 按关联表字段排序
          in: query
          required: false
          type: array
          items:
            $ref: ../../common/model/Sort.yaml#/definitions/sort
          collectionFormat: multi
    post:
      description: 创建标品记录域名信息
      operationId: createGoodsStackDomain
      responses:
        "200":
          x-jdcloud-tiered: false
          description: A successful response.
          schema:
            properties:
              error:
                properties:
                  errorObject:
                    $ref: ../../common/model/Err.yaml#/definitions/err
              requestId:
                type: string
              result:
                properties:
                  resultObject:
                    $ref: ../module/goods_stack.swagger.yaml#/definitions/GoodsStackDescribeDomainReply
        default:
          description: An unexpected error response.
      parameters:
        - x-jdcloud-tiered: false
          name: body
          in: body
          required: true
          schema:
            $ref: ../module/goods_stack.swagger.yaml#/definitions/GoodsStackCreateDomainReq
  /goodsStackDomain/{id}:
    get:
      description: 获取标品记录域名信息
      operationId: describeGoodsStackDomain
      responses:
        "200":
          x-jdcloud-tiered: false
          description: A successful response.
          schema:
            properties:
              error:
                properties:
                  errorObject:
                    $ref: ../../common/model/Err.yaml#/definitions/err
              requestId:
                type: string
              result:
                properties:
                  resultObject:
                    $ref: ../module/goods_stack.swagger.yaml#/definitions/GoodsStackDescribeDomainReply
        default:
          description: An unexpected error response.
      parameters:
        - name: id
          description: ID
          in: path
          required: true
          type: integer
          format: int64
        - name: filters
          description: |-
            返回字段皆支持筛选，筛选扩展:
            With:{模块名} - 返回关联表(此类过滤项无value，所以可将同类name合并name=.的values中)
            With:{模块名}.{字段名} - 过滤并返回关联表
          in: query
          required: false
          type: array
          items:
            $ref: ../../common/model/Filter.yaml#/definitions/filter
          collectionFormat: multi
    delete:
      description: 删除标品记录域名信息
      operationId: deleteGoodsStackDomain
      responses:
        "200":
          x-jdcloud-tiered: false
          description: A successful response.
          schema:
            properties:
              error:
                properties:
                  errorObject:
                    $ref: ../../common/model/Err.yaml#/definitions/err
              requestId:
                type: string
        default:
          description: An unexpected error response.
      parameters:
        - name: id
          description: ID
          in: path
          required: true
          type: integer
          format: int64
    put:
      description: 修改标品记录域名信息
      operationId: modifyGoodsStackDomain
      responses:
        "200":
          x-jdcloud-tiered: false
          description: A successful response.
          schema:
            properties:
              error:
                properties:
                  errorObject:
                    $ref: ../../common/model/Err.yaml#/definitions/err
              requestId:
                type: string
        default:
          description: An unexpected error response.
      parameters:
        - name: id
          description: ID
          in: path
          required: true
          type: integer
          format: int64
        - name: body
          in: body
          required: true
          schema:
            type: object
            properties:
              clearFields:
                type: array
                items:
                  type: string
                description: |-
                  清空字段:
                  description - 描述
              confBlock:
                type: array
                items:
                  type: string
                description: 配置块
              description:
                type: string
                description: 描述
              domainUse:
                type: string
                description: |-
                  域名类型:
                  [ loadBalancer a ]
              port:
                type: integer
                format: int32
                description: 域名端口号
              protocol:
                type: string
                description: |-
                  协议:
                  [ http https tcp ]
              targetPort:
                type: integer
                format: int32
                description: 服务端口号
              template:
                type: string
                description: 域名模板
  /goodsStackFilecenter:
    get:
      description: 获取标品记录文件列表
      operationId: describeGoodsStacksFilecenter
      responses:
        "200":
          x-jdcloud-tiered: false
          description: A successful response.
          schema:
            properties:
              error:
                properties:
                  errorObject:
                    $ref: ../../common/model/Err.yaml#/definitions/err
              requestId:
                type: string
              result:
                properties:
                  resultObject:
                    $ref: ../module/goods_stack.swagger.yaml#/definitions/GoodsStackDescribesFilecenterReply
        default:
          description: An unexpected error response.
      parameters:
        - name: pageNumber
          description: |-
            页码:
            -1 - 全部
            0 - 第1页
            1 - 第2页
            - 以此类推...
          in: query
          required: true
          type: integer
          format: int64
          default: "0"
        - name: pageSize
          in: query
          required: true
          type: integer
          format: int64
        - name: filters
          description: |-
            返回字段皆支持筛选，筛选扩展:
            With:{模块名} - 返回关联表(此类过滤项无value，所以可将同类name合并name=.的values中)
            With:{模块名}.{字段名} - 过滤并返回关联表
            Has:{模块名}.{字段名} - 子查询(等值连接)
          in: query
          required: false
          type: array
          items:
            $ref: ../../common/model/Filter.yaml#/definitions/filter
          collectionFormat: multi
        - name: sorts
          description: |-
            返回字段皆支持排序，排序扩展:
            With:{模块名}.[ Min Max Count Sum ]:{字段名} - 按关联表字段排序
          in: query
          required: false
          type: array
          items:
            $ref: ../../common/model/Sort.yaml#/definitions/sort
          collectionFormat: multi
    post:
      description: 创建标品记录文件或镜像信息
      operationId: createGoodsStackFilecenter
      responses:
        "200":
          x-jdcloud-tiered: false
          description: A successful response.
          schema:
            properties:
              error:
                properties:
                  errorObject:
                    $ref: ../../common/model/Err.yaml#/definitions/err
              requestId:
                type: string
              result:
                properties:
                  resultObject:
                    $ref: ../module/goods_stack.swagger.yaml#/definitions/GoodsStackDescribeFilecenterReply
        default:
          description: An unexpected error response.
      parameters:
        - x-jdcloud-tiered: false
          name: body
          in: body
          required: true
          schema:
            $ref: ../module/goods_stack.swagger.yaml#/definitions/GoodsStackCreateFilecenterReq
  /goodsStackFilecenter/{id}:
    get:
      description: 获取标品记录文件或镜像信息
      operationId: describeGoodsStackFilecenter
      responses:
        "200":
          x-jdcloud-tiered: false
          description: A successful response.
          schema:
            properties:
              error:
                properties:
                  errorObject:
                    $ref: ../../common/model/Err.yaml#/definitions/err
              requestId:
                type: string
              result:
                properties:
                  resultObject:
                    $ref: ../module/goods_stack.swagger.yaml#/definitions/GoodsStackDescribeFilecenterReply
        default:
          description: An unexpected error response.
      parameters:
        - name: id
          description: ID
          in: path
          required: true
          type: integer
          format: int64
        - name: filters
          description: |-
            返回字段皆支持筛选，筛选扩展:
            With:{模块名} - 返回关联表(此类过滤项无value，所以可将同类name合并name=.的values中)
            With:{模块名}.{字段名} - 过滤并返回关联表
          in: query
          required: false
          type: array
          items:
            $ref: ../../common/model/Filter.yaml#/definitions/filter
          collectionFormat: multi
    delete:
      description: 删除标品记录文件或镜像信息
      operationId: deleteGoodsStackFilecenter
      responses:
        "200":
          x-jdcloud-tiered: false
          description: A successful response.
          schema:
            properties:
              error:
                properties:
                  errorObject:
                    $ref: ../../common/model/Err.yaml#/definitions/err
              requestId:
                type: string
        default:
          description: An unexpected error response.
      parameters:
        - name: id
          description: ID
          in: path
          required: true
          type: integer
          format: int64
  /goodsStackFlavor:
    get:
      description: 获取标品记录云翼配置列表
      operationId: describeGoodsStacksFlavor
      responses:
        "200":
          x-jdcloud-tiered: false
          description: A successful response.
          schema:
            properties:
              error:
                properties:
                  errorObject:
                    $ref: ../../common/model/Err.yaml#/definitions/err
              requestId:
                type: string
              result:
                properties:
                  resultObject:
                    $ref: ../module/goods_stack.swagger.yaml#/definitions/GoodsStackDescribesFlavorReply
        default:
          description: An unexpected error response.
      parameters:
        - name: pageNumber
          description: |-
            页码:
            -1 - 全部
            0 - 第1页
            1 - 第2页
            - 以此类推...
          in: query
          required: true
          type: integer
          format: int64
          default: "0"
        - name: pageSize
          in: query
          required: true
          type: integer
          format: int64
        - name: filters
          description: |-
            返回字段皆支持筛选，筛选扩展:
            With:{模块名} - 返回关联表(此类过滤项无value，所以可将同类name合并name=.的values中)
            With:{模块名}.{字段名} - 过滤并返回关联表
            Has:{模块名}.{字段名} - 子查询(等值连接)
          in: query
          required: false
          type: array
          items:
            $ref: ../../common/model/Filter.yaml#/definitions/filter
          collectionFormat: multi
        - name: sorts
          description: |-
            返回字段皆支持排序，排序扩展:
            With:{模块名}.[ Min Max Count Sum ]:{字段名} - 按关联表字段排序
          in: query
          required: false
          type: array
          items:
            $ref: ../../common/model/Sort.yaml#/definitions/sort
          collectionFormat: multi
    post:
      description: 创建标品记录云翼配置信息
      operationId: createGoodsStackFlavor
      responses:
        "200":
          x-jdcloud-tiered: false
          description: A successful response.
          schema:
            properties:
              error:
                properties:
                  errorObject:
                    $ref: ../../common/model/Err.yaml#/definitions/err
              requestId:
                type: string
              result:
                properties:
                  resultObject:
                    $ref: ../module/goods_stack.swagger.yaml#/definitions/GoodsStackDescribeFlavorReply
        default:
          description: An unexpected error response.
      parameters:
        - x-jdcloud-tiered: false
          name: body
          in: body
          required: true
          schema:
            $ref: ../module/goods_stack.swagger.yaml#/definitions/GoodsStackCreateFlavorReq
  /goodsStackFlavor/{id}:
    get:
      description: 获取标品记录云翼配置信息
      operationId: describeGoodsStackFlavor
      responses:
        "200":
          x-jdcloud-tiered: false
          description: A successful response.
          schema:
            properties:
              error:
                properties:
                  errorObject:
                    $ref: ../../common/model/Err.yaml#/definitions/err
              requestId:
                type: string
              result:
                properties:
                  resultObject:
                    $ref: ../module/goods_stack.swagger.yaml#/definitions/GoodsStackDescribeFlavorReply
        default:
          description: An unexpected error response.
      parameters:
        - name: id
          description: ID
          in: path
          required: true
          type: integer
          format: int64
        - name: filters
          description: |-
            返回字段皆支持筛选，筛选扩展:
            With:{模块名} - 返回关联表(此类过滤项无value，所以可将同类name合并name=.的values中)
            With:{模块名}.{字段名} - 过滤并返回关联表
          in: query
          required: false
          type: array
          items:
            $ref: ../../common/model/Filter.yaml#/definitions/filter
          collectionFormat: multi
    delete:
      description: 删除标品记录云翼配置信息
      operationId: deleteGoodsStackFlavor
      responses:
        "200":
          x-jdcloud-tiered: false
          description: A successful response.
          schema:
            properties:
              error:
                properties:
                  errorObject:
                    $ref: ../../common/model/Err.yaml#/definitions/err
              requestId:
                type: string
        default:
          description: An unexpected error response.
      parameters:
        - name: id
          description: ID
          in: path
          required: true
          type: integer
          format: int64
    put:
      description: 修改标品记录云翼配置信息
      operationId: modifyGoodsStackFlavor
      responses:
        "200":
          x-jdcloud-tiered: false
          description: A successful response.
          schema:
            properties:
              error:
                properties:
                  errorObject:
                    $ref: ../../common/model/Err.yaml#/definitions/err
              requestId:
                type: string
        default:
          description: An unexpected error response.
      parameters:
        - name: id
          description: ID
          in: path
          required: true
          type: integer
          format: int64
        - name: body
          in: body
          required: true
          schema:
            type: object
            properties:
              clearFields:
                type: array
                items:
                  type: string
                description: |
                  清空字段:
              coreLimit:
                type: integer
                format: int32
                description: CPU极限/‰核
              coreRequest:
                type: integer
                format: int32
                description: CPU申请/‰核
              diskLimit:
                type: integer
                format: int32
                description: 硬盘极限/Gi
              diskRequest:
                type: integer
                format: int32
                description: 硬盘申请/Gi
              memoryLimit:
                type: integer
                format: int32
                description: 内存极限/Mi
              memoryRequest:
                type: integer
                format: int32
                description: 内存申请/Mi
              replicas:
                type: integer
                format: int32
                description: 副本数
  /goodsStackHardwareRole:
    get:
      description: 获取标品硬件角色列表
      operationId: describeGoodsStacksHardwareRole
      responses:
        "200":
          x-jdcloud-tiered: false
          description: A successful response.
          schema:
            properties:
              error:
                properties:
                  errorObject:
                    $ref: ../../common/model/Err.yaml#/definitions/err
              requestId:
                type: string
              result:
                properties:
                  resultObject:
                    $ref: ../module/goods_stack.swagger.yaml#/definitions/GoodsStackDescribesHardwareRoleReply
        default:
          description: An unexpected error response.
      parameters:
        - name: pageNumber
          description: |-
            页码:
            -1 - 全部
            0 - 第1页
            1 - 第2页
            - 以此类推...
          in: query
          required: true
          type: integer
          format: int64
          default: "0"
        - name: pageSize
          in: query
          required: true
          type: integer
          format: int64
        - name: filters
          description: |-
            返回字段皆支持筛选，筛选扩展:
            With:{模块名} - 返回关联表(此类过滤项无value，所以可将同类name合并name=.的values中)
            With:{模块名}.{字段名} - 过滤并返回关联表
            Has:{模块名}.{字段名} - 子查询(等值连接)
          in: query
          required: false
          type: array
          items:
            $ref: ../../common/model/Filter.yaml#/definitions/filter
          collectionFormat: multi
        - name: sorts
          description: |-
            返回字段皆支持排序，排序扩展:
            With:{模块名}.[ Min Max Count Sum ]:{字段名} - 按关联表字段排序
          in: query
          required: false
          type: array
          items:
            $ref: ../../common/model/Sort.yaml#/definitions/sort
          collectionFormat: multi
    post:
      description: 关联标品硬件角色和标签信息
      operationId: createGoodsStackHardwareRoleUnion
      responses:
        "200":
          x-jdcloud-tiered: false
          description: A successful response.
          schema:
            properties:
              error:
                properties:
                  errorObject:
                    $ref: ../../common/model/Err.yaml#/definitions/err
              requestId:
                type: string
        default:
          description: An unexpected error response.
      parameters:
        - x-jdcloud-tiered: false
          name: body
          in: body
          required: true
          schema:
            $ref: ../module/goods_stack.swagger.yaml#/definitions/GoodsStackCreateHardwareRoleUnionReq
  /goodsStackHardwareRole/{id}:
    get:
      description: 获取标品硬件角色信息
      operationId: describeGoodsStackHardwareRole
      responses:
        "200":
          x-jdcloud-tiered: false
          description: A successful response.
          schema:
            properties:
              error:
                properties:
                  errorObject:
                    $ref: ../../common/model/Err.yaml#/definitions/err
              requestId:
                type: string
              result:
                properties:
                  resultObject:
                    $ref: ../module/goods_stack.swagger.yaml#/definitions/GoodsStackDescribeHardwareRoleReply
        default:
          description: An unexpected error response.
      parameters:
        - name: id
          description: |-
            ID
            - 为0时按顺序查询字段：name
          in: path
          required: true
          type: integer
          format: int64
        - name: name
          description: 角色名称
          in: query
          required: false
          type: string
        - name: filters
          description: |-
            返回字段皆支持筛选，筛选扩展:
            With:{模块名} - 返回关联表(此类过滤项无value，所以可将同类name合并name=.的values中)
            With:{模块名}.{字段名} - 过滤并返回关联表
          in: query
          required: false
          type: array
          items:
            $ref: ../../common/model/Filter.yaml#/definitions/filter
          collectionFormat: multi
  /goodsStackHardwareRoleLabel:
    get:
      description: 获取标品硬件角色标签列表
      operationId: describeGoodsStacksHardwareRoleLabel
      responses:
        "200":
          x-jdcloud-tiered: false
          description: A successful response.
          schema:
            properties:
              error:
                properties:
                  errorObject:
                    $ref: ../../common/model/Err.yaml#/definitions/err
              requestId:
                type: string
              result:
                properties:
                  resultObject:
                    $ref: ../module/goods_stack.swagger.yaml#/definitions/GoodsStackDescribesHardwareRoleLabelReply
        default:
          description: An unexpected error response.
      parameters:
        - name: pageNumber
          description: |-
            页码:
            -1 - 全部
            0 - 第1页
            1 - 第2页
            - 以此类推...
          in: query
          required: true
          type: integer
          format: int64
          default: "0"
        - name: pageSize
          in: query
          required: true
          type: integer
          format: int64
        - name: filters
          description: |-
            返回字段皆支持筛选，筛选扩展:
            With:{模块名} - 返回关联表(此类过滤项无value，所以可将同类name合并name=.的values中)
            With:{模块名}.{字段名} - 过滤并返回关联表
            Has:{模块名}.{字段名} - 子查询(等值连接)
          in: query
          required: false
          type: array
          items:
            $ref: ../../common/model/Filter.yaml#/definitions/filter
          collectionFormat: multi
        - name: sorts
          description: |-
            返回字段皆支持排序，排序扩展:
            With:{模块名}.[ Min Max Count Sum ]:{字段名} - 按关联表字段排序
          in: query
          required: false
          type: array
          items:
            $ref: ../../common/model/Sort.yaml#/definitions/sort
          collectionFormat: multi
  /goodsStackHardwareRoleLabel/{id}:
    get:
      description: 获取标品硬件角色标签信息
      operationId: describeGoodsStackHardwareRoleLabel
      responses:
        "200":
          x-jdcloud-tiered: false
          description: A successful response.
          schema:
            properties:
              error:
                properties:
                  errorObject:
                    $ref: ../../common/model/Err.yaml#/definitions/err
              requestId:
                type: string
              result:
                properties:
                  resultObject:
                    $ref: ../module/goods_stack.swagger.yaml#/definitions/GoodsStackDescribeHardwareRoleLabelReply
        default:
          description: An unexpected error response.
      parameters:
        - name: id
          description: |-
            ID
            - 为0时按顺序查询字段：key、value
          in: path
          required: true
          type: integer
          format: int64
        - name: key
          description: 标签键
          in: query
          required: false
          type: string
        - name: value
          description: 标签键
          in: query
          required: false
          type: string
        - name: filters
          description: |-
            返回字段皆支持筛选，筛选扩展:
            With:{模块名} - 返回关联表(此类过滤项无value，所以可将同类name合并name=.的values中)
            With:{模块名}.{字段名} - 过滤并返回关联表
          in: query
          required: false
          type: array
          items:
            $ref: ../../common/model/Filter.yaml#/definitions/filter
          collectionFormat: multi
  /goodsStackIac:
    get:
      description: 获取标品记录IAC描述列表
      operationId: describeGoodsStacksIac
      responses:
        "200":
          x-jdcloud-tiered: false
          description: A successful response.
          schema:
            properties:
              error:
                properties:
                  errorObject:
                    $ref: ../../common/model/Err.yaml#/definitions/err
              requestId:
                type: string
              result:
                properties:
                  resultObject:
                    $ref: ../module/goods_stack.swagger.yaml#/definitions/GoodsStackDescribesIacReply
        default:
          description: An unexpected error response.
      parameters:
        - name: pageNumber
          description: |-
            页码:
            -1 - 全部
            0 - 第1页
            1 - 第2页
            - 以此类推...
          in: query
          required: true
          type: integer
          format: int64
          default: "0"
        - name: pageSize
          in: query
          required: true
          type: integer
          format: int64
        - name: filters
          description: |-
            返回字段皆支持筛选，筛选扩展:
            With:{模块名} - 返回关联表(此类过滤项无value，所以可将同类name合并name=.的values中)
            With:{模块名}.{字段名} - 过滤并返回关联表
            Has:{模块名}.{字段名} - 子查询(等值连接)
          in: query
          required: false
          type: array
          items:
            $ref: ../../common/model/Filter.yaml#/definitions/filter
          collectionFormat: multi
        - name: sorts
          description: |-
            返回字段皆支持排序，排序扩展:
            With:{模块名}.[ Min Max Count Sum ]:{字段名} - 按关联表字段排序
          in: query
          required: false
          type: array
          items:
            $ref: ../../common/model/Sort.yaml#/definitions/sort
          collectionFormat: multi
    post:
      description: 创建标品记录IAC描述信息
      operationId: createGoodsStackIac
      responses:
        "200":
          x-jdcloud-tiered: false
          description: A successful response.
          schema:
            properties:
              error:
                properties:
                  errorObject:
                    $ref: ../../common/model/Err.yaml#/definitions/err
              requestId:
                type: string
              result:
                properties:
                  resultObject:
                    $ref: ../module/goods_stack.swagger.yaml#/definitions/GoodsStackDescribeIacReply
        default:
          description: An unexpected error response.
      parameters:
        - x-jdcloud-tiered: false
          name: body
          in: body
          required: true
          schema:
            $ref: ../module/goods_stack.swagger.yaml#/definitions/GoodsStackCreateIacReq
  /goodsStackIac/{id}:
    get:
      description: 获取标品记录IAC描述信息
      operationId: describeGoodsStackIac
      responses:
        "200":
          x-jdcloud-tiered: false
          description: A successful response.
          schema:
            properties:
              error:
                properties:
                  errorObject:
                    $ref: ../../common/model/Err.yaml#/definitions/err
              requestId:
                type: string
              result:
                properties:
                  resultObject:
                    $ref: ../module/goods_stack.swagger.yaml#/definitions/GoodsStackDescribeIacReply
        default:
          description: An unexpected error response.
      parameters:
        - name: id
          description: ID
          in: path
          required: true
          type: integer
          format: int64
        - name: filters
          description: |-
            返回字段皆支持筛选，筛选扩展:
            With:{模块名} - 返回关联表(此类过滤项无value，所以可将同类name合并name=.的values中)
            With:{模块名}.{字段名} - 过滤并返回关联表
          in: query
          required: false
          type: array
          items:
            $ref: ../../common/model/Filter.yaml#/definitions/filter
          collectionFormat: multi
    delete:
      description: 删除标品记录IAC描述信息
      operationId: deleteGoodsStackIac
      responses:
        "200":
          x-jdcloud-tiered: false
          description: A successful response.
          schema:
            properties:
              error:
                properties:
                  errorObject:
                    $ref: ../../common/model/Err.yaml#/definitions/err
              requestId:
                type: string
        default:
          description: An unexpected error response.
      parameters:
        - name: id
          description: ID
          in: path
          required: true
          type: integer
          format: int64
    put:
      description: 修改标品记录IAC描述信息
      operationId: modifyGoodsStackIac
      responses:
        "200":
          x-jdcloud-tiered: false
          description: A successful response.
          schema:
            properties:
              error:
                properties:
                  errorObject:
                    $ref: ../../common/model/Err.yaml#/definitions/err
              requestId:
                type: string
        default:
          description: An unexpected error response.
      parameters:
        - name: id
          description: ID
          in: path
          required: true
          type: integer
          format: int64
        - name: body
          in: body
          required: true
          schema:
            type: object
            properties:
              clearFields:
                type: array
                items:
                  type: string
                description: |
                  清空字段:
  /goodsStackMiddleware:
    get:
      description: 获取标品记录中间件列表
      operationId: describeGoodsStacksMiddleware
      responses:
        "200":
          x-jdcloud-tiered: false
          description: A successful response.
          schema:
            properties:
              error:
                properties:
                  errorObject:
                    $ref: ../../common/model/Err.yaml#/definitions/err
              requestId:
                type: string
              result:
                properties:
                  resultObject:
                    $ref: ../module/goods_stack.swagger.yaml#/definitions/GoodsStackDescribesMiddlewareReply
        default:
          description: An unexpected error response.
      parameters:
        - name: pageNumber
          description: |-
            页码:
            -1 - 全部
            0 - 第1页
            1 - 第2页
            - 以此类推...
          in: query
          required: true
          type: integer
          format: int64
          default: "0"
        - name: pageSize
          in: query
          required: true
          type: integer
          format: int64
        - name: filters
          description: |-
            返回字段皆支持筛选，筛选扩展:
            With:{模块名} - 返回关联表(此类过滤项无value，所以可将同类name合并name=.的values中)
            With:{模块名}.{字段名} - 过滤并返回关联表
            Has:{模块名}.{字段名} - 子查询(等值连接)
          in: query
          required: false
          type: array
          items:
            $ref: ../../common/model/Filter.yaml#/definitions/filter
          collectionFormat: multi
        - name: sorts
          description: |-
            返回字段皆支持排序，排序扩展:
            With:{模块名}.[ Min Max Count Sum ]:{字段名} - 按关联表字段排序
          in: query
          required: false
          type: array
          items:
            $ref: ../../common/model/Sort.yaml#/definitions/sort
          collectionFormat: multi
    post:
      description: 创建标品记录中间件信息 / 关联标品记录中间件信息
      operationId: createGoodsStackMiddleware
      responses:
        "200":
          x-jdcloud-tiered: false
          description: A successful response.
          schema:
            properties:
              error:
                properties:
                  errorObject:
                    $ref: ../../common/model/Err.yaml#/definitions/err
              requestId:
                type: string
              result:
                properties:
                  resultObject:
                    $ref: ../module/goods_stack.swagger.yaml#/definitions/GoodsStackDescribeMiddlewareReply
        default:
          description: An unexpected error response.
      parameters:
        - x-jdcloud-tiered: false
          name: body
          in: body
          required: true
          schema:
            $ref: ../module/goods_stack.swagger.yaml#/definitions/GoodsStackCreateMiddlewareReq
  /goodsStackMiddleware/{id}:
    get:
      description: 获取标品记录中间件信息
      operationId: describeGoodsStackMiddleware
      responses:
        "200":
          x-jdcloud-tiered: false
          description: A successful response.
          schema:
            properties:
              error:
                properties:
                  errorObject:
                    $ref: ../../common/model/Err.yaml#/definitions/err
              requestId:
                type: string
              result:
                properties:
                  resultObject:
                    $ref: ../module/goods_stack.swagger.yaml#/definitions/GoodsStackDescribeMiddlewareReply
        default:
          description: An unexpected error response.
      parameters:
        - name: id
          description: |-
            ID
            - 为0时按顺序查询字段：name+kind
          in: path
          required: true
          type: integer
          format: int64
          default: "0"
        - name: filters
          description: |-
            返回字段皆支持筛选，筛选扩展:
            With:{模块名} - 返回关联表(此类过滤项无value，所以可将同类name合并name=.的values中)
            With:{模块名}.{字段名} - 过滤并返回关联表
          in: query
          required: false
          type: array
          items:
            $ref: ../../common/model/Filter.yaml#/definitions/filter
          collectionFormat: multi
        - name: name
          description: 中间件唯一标识
          in: query
          required: false
          type: string
        - name: kind
          description: |-
            软件类型:
            [ mysql postgresql elasticsearch redis zookeeper etcd clickhouse kafka mongodb ]
          in: query
          required: false
          type: string
    delete:
      description: 删除标品记录中间件信息
      operationId: deleteGoodsStackMiddleware
      responses:
        "200":
          x-jdcloud-tiered: false
          description: A successful response.
          schema:
            properties:
              error:
                properties:
                  errorObject:
                    $ref: ../../common/model/Err.yaml#/definitions/err
              requestId:
                type: string
        default:
          description: An unexpected error response.
      parameters:
        - name: id
          description: |-
            ID
            - 为0时按顺序查询字段：name+kind
          in: path
          required: true
          type: integer
          format: int64
          default: "0"
        - name: goodsId
          description: |-
            标品ID
            - >0时删除关联
            - 为0时删除中间件数据
          in: query
          required: false
          type: string
        - name: name
          description: 中间件唯一标识
          in: query
          required: false
          type: string
        - name: kind
          description: |-
            软件类型:
            [ mysql postgresql elasticsearch redis zookeeper etcd clickhouse kafka mongodb ]
          in: query
          required: false
          type: string
    put:
      description: 修改标品记录中间件信息
      operationId: modifyGoodsStackMiddleware
      responses:
        "200":
          x-jdcloud-tiered: false
          description: A successful response.
          schema:
            properties:
              error:
                properties:
                  errorObject:
                    $ref: ../../common/model/Err.yaml#/definitions/err
              requestId:
                type: string
        default:
          description: An unexpected error response.
      parameters:
        - name: id
          description: ID
          in: path
          required: true
          type: integer
          format: int64
        - name: body
          in: body
          required: true
          schema:
            type: object
            properties:
              clearFields:
                type: array
                items:
                  type: string
                description: |
                  清空字段:
              coreLimit:
                type: integer
                format: int32
                description: CPU极限/‰核
              coreRequest:
                type: integer
                format: int32
                description: CPU申请/‰核
              diskLimit:
                type: integer
                format: int32
                description: 硬盘极限/Gi
              diskRequest:
                type: integer
                format: int32
                description: 硬盘申请/Gi
              domainUse:
                type: string
                description: |-
                  域名类型:
                  [ loadBalancer a ]
              memoryLimit:
                type: integer
                format: int32
                description: 内存极限/Mi
              memoryRequest:
                type: integer
                format: int32
                description: 内存申请/Mi
              params:
                $ref: ../module/goods_stack.swagger.yaml#/definitions/GoodsStackMiddlewareParams
                description: 配置
              port:
                type: integer
                format: int32
                description: 域名端口号
              protocol:
                type: string
                description: |-
                  协议:
                  [ http https tcp ]
              shareMode:
                type: string
                description: |-
                  共享类型:
                  [ no yes ]
              targetPort:
                type: integer
                format: int32
                description: 服务端口号
              urlTemplate:
                type: string
                description: URL
  /version/{version}/goodsStack:
    get:
      description: 获取指定版本的标品信息
      operationId: describeGoodsStack
      responses:
        "200":
          x-jdcloud-tiered: false
          description: A successful response.
          schema:
            properties:
              error:
                properties:
                  errorObject:
                    $ref: ../../common/model/Err.yaml#/definitions/err
              requestId:
                type: string
              result:
                properties:
                  resultObject:
                    $ref: ../module/goods_stack.swagger.yaml#/definitions/GoodsStackGoodsStackDescribeReply
        default:
          description: An unexpected error response.
      parameters:
        - name: version
          description: JDStack版本
          in: path
          required: true
          type: string
        - name: id
          description: |-
            标品ID
            - 为空时按顺序查询字段：application、resource、service、category
          in: query
          required: false
          type: string
        - name: category
          description: 分类名称
          in: query
          required: false
          type: string
        - name: service
          description: |-
            产品名称
            - 为空时按顺序查询字段：category
          in: query
          required: false
          type: string
        - name: resource
          description: |-
            服务名称
            - 为空时按顺序查询字段：service、category
          in: query
          required: false
          type: string
        - name: application
          description: |-
            应用名称
            - 为空时按顺序查询字段：resource、service、category
          in: query
          required: false
          type: string
        - name: filters
          description: |-
            返回字段皆支持筛选，筛选扩展:
            With:{模块名} - 返回关联表(此类过滤项无value，所以可将同类name合并name=.的values中)
            With:{模块名}.{字段名} - 过滤并返回关联表
          in: query
          required: false
          type: array
          items:
            $ref: ../../common/model/Filter.yaml#/definitions/filter
          collectionFormat: multi
        - name: cloudType
          description: |-
            云底座类别:
            [ jdstack cvessel ]
          in: query
          required: false
          type: string
          default: jdstack
    post:
      description: 创建标品信息
      operationId: createGoodsStack
      responses:
        "200":
          x-jdcloud-tiered: false
          description: A successful response.
          schema:
            properties:
              error:
                properties:
                  errorObject:
                    $ref: ../../common/model/Err.yaml#/definitions/err
              requestId:
                type: string
              result:
                properties:
                  resultObject:
                    $ref: ../module/goods_stack.swagger.yaml#/definitions/GoodsStackGoodsStackDescribeReply
        default:
          description: An unexpected error response.
      parameters:
        - name: version
          description: JDStack版本
          in: path
          required: true
          type: string
        - name: body
          in: body
          required: true
          schema:
            type: object
            properties:
              action:
                type: string
                default: debug
                description: |-
                  操作行为:
                  [ debug online offline upgrade ]
              adl:
                type: array
                items:
                  $ref: ../module/goods_stack.swagger.yaml#/definitions/GoodsStackCreateAdlReq
                description: 关联的adl文件内容
              application:
                type: string
                description: 应用名称,顺序1(存在时2必填)
              businessMetadata:
                $ref: ../module/goods_stack.swagger.yaml#/definitions/GoodsStackCreateMetadataReq
                description: 产品关联的商业平台元数据信息
              category:
                type: string
                description: 分类名称,顺序4(必填)
              cloudType:
                type: string
                default: jdstack
                description: |-
                  云底座类别:
                  [ jdstack cvessel ]
              config:
                type: array
                items:
                  $ref: ../module/goods_stack.swagger.yaml#/definitions/GoodsStackCreateConfigReq
                description: 关联的配置信息
              delivery:
                type: array
                items:
                  $ref: ../module/goods_stack.swagger.yaml#/definitions/GoodsStackCreateDeliveryReq
                description: 关联交付信息,policy[FDK:GoodsDelivery]
              deliveryRuleGroup:
                type: array
                items:
                  $ref: ../module/goods_stack.swagger.yaml#/definitions/GoodsStackCreateDeliveryRuleGroupReq
                description: 关联交付规则组信息,policy[FDK:GoodsDeliveryRuleGroup]
              domain:
                type: array
                items:
                  $ref: ../module/goods_stack.swagger.yaml#/definitions/GoodsStackCreateDomainReq
                description: 关联域名信息,policy[FDK:GoodsDomain]
              filecenter:
                type: array
                items:
                  $ref: ../module/goods_stack.swagger.yaml#/definitions/GoodsStackCreateFilecenterReq
                description: 关联文件或镜像信息,policy[FDK:GoodsFilecenter]
              flavor:
                type: array
                items:
                  $ref: ../module/goods_stack.swagger.yaml#/definitions/GoodsStackCreateFlavorReq
                description: 关联云翼配置信息,policy[FDK:GoodsFlavor]
              hardwareRole:
                type: array
                items:
                  $ref: ../module/goods_stack.swagger.yaml#/definitions/GoodsStackHardwareRoleDescribeReq
                description: 关联硬件角色信息,policy[FDK:HardwareRole]
              hardwareRoleLabel:
                type: array
                items:
                  $ref: ../module/goods_stack.swagger.yaml#/definitions/GoodsStackCreateLabelReq
                description: 关联硬件角色标签信息,policy[FDK:HardwareRoleLabel]
              iac:
                type: array
                items:
                  $ref: ../module/goods_stack.swagger.yaml#/definitions/GoodsStackCreateIacReq
                description: 关联IAC信息,policy[FDK:GoodsIac]
              metadata:
                $ref: ../module/goods_stack.swagger.yaml#/definitions/GoodsStackMetadataCreateReq
                description: 创建分类-产品-服务-应用,存在时会覆盖顺序1~4
              middleware:
                type: array
                items:
                  $ref: ../module/goods_stack.swagger.yaml#/definitions/GoodsStackCreateMiddlewareReq
                description: 关联中间件信息,policy[FDK:GoodsMiddleware]
              pd:
                $ref: ../module/goods_stack.swagger.yaml#/definitions/GoodsStackCreatePdReq
                description: 关联产品pd信息
              policy:
                type: array
                items:
                  type: string
                description: |-
                  策略:
                  override - 覆盖相同JDStack版本,应用
                  foreignKeyDiscarded - 查出已存在的关联数据,本次请求没关联上就被淘汰,需指定FKD:{模块名}
              resource:
                type: string
                description: 服务名称,顺序2(存在时3必填)
              service:
                type: string
                description: 产品名称,顺序3(存在时4必填)
              virtualGoods:
                type: string
                default: "no"
                description: |-
                  逻辑属性:
                  [ no yes ]
            required:
              - category
              - action
  /version/{version}/goodsStack:syncAction:
    post:
      description: 同步标品状态，将不在列表中的标品状态设置为offline
      operationId: modifyGoodsStackSyncGoodsAction
      responses:
        "200":
          x-jdcloud-tiered: false
          description: A successful response.
          schema:
            properties:
              error:
                properties:
                  errorObject:
                    $ref: ../../common/model/Err.yaml#/definitions/err
              requestId:
                type: string
              result:
                properties:
                  resultObject:
                    $ref: ../module/goods_stack.swagger.yaml#/definitions/GoodsStackSyncGoodsActionReply
        default:
          description: An unexpected error response.
      parameters:
        - name: version
          description: JDStack版本
          in: path
          required: true
          type: string
        - name: body
          in: body
          required: true
          schema:
            type: object
            properties:
              goodsList:
                type: array
                items:
                  $ref: ../module/goods_stack.swagger.yaml#/definitions/GoodsStackGoodsInfo
                description: 标品列表，不在此列表中的标品将被设置为offline状态
            required:
              - goodsList
