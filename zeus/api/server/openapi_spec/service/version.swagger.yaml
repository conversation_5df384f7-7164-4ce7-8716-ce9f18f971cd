swagger: "2.0"
info:
  title: Version_JDStack版本信息
  version: v1
paths:
  /CvesselVersion:
    post:
      description: 创建云舰版本信息
      operationId: createVersionCvessel
      responses:
        "200":
          x-jdcloud-tiered: false
          description: A successful response.
          schema:
            properties:
              error:
                properties:
                  errorObject:
                    $ref: ../../common/model/Err.yaml#/definitions/err
              requestId:
                type: string
              result:
                properties:
                  resultObject:
                    $ref: ../module/version.swagger.yaml#/definitions/VersionDescribeCvesselReply
        default:
          description: An unexpected error response.
      parameters:
        - x-jdcloud-tiered: false
          name: body
          in: body
          required: true
          schema:
            $ref: ../module/version.swagger.yaml#/definitions/VersionCreateCvesselReq
  /CvesselVersion/{id}:
    put:
      description: 修改云舰版本信息
      operationId: modifyVersionCvessel
      responses:
        "200":
          x-jdcloud-tiered: false
          description: A successful response.
          schema:
            properties:
              error:
                properties:
                  errorObject:
                    $ref: ../../common/model/Err.yaml#/definitions/err
              requestId:
                type: string
        default:
          description: An unexpected error response.
      parameters:
        - name: id
          description: |-
            云舰版本ID
            - 为0时按顺序查询字段：version
          in: path
          required: true
          type: integer
          format: int32
        - name: body
          in: body
          required: true
          schema:
            type: object
            properties:
              clearFields:
                type: array
                items:
                  type: string
                description: |-
                  清空字段:
                  description - 描述
              description:
                type: string
                description: 描述
              version:
                type: string
                description: 云舰版本,只读
  /cvesselVersion:
    get:
      description: 获取云舰版本列表
      operationId: describeVersionsCvessel
      responses:
        "200":
          x-jdcloud-tiered: false
          description: A successful response.
          schema:
            properties:
              error:
                properties:
                  errorObject:
                    $ref: ../../common/model/Err.yaml#/definitions/err
              requestId:
                type: string
              result:
                properties:
                  resultObject:
                    $ref: ../module/version.swagger.yaml#/definitions/VersionDescribesCvesselReply
        default:
          description: An unexpected error response.
      parameters:
        - name: pageNumber
          description: |-
            页码:
            -1 - 全部
            0 - 第1页
            1 - 第2页
            - 以此类推...
          in: query
          required: true
          type: integer
          format: int64
          default: "0"
        - name: pageSize
          in: query
          required: true
          type: integer
          format: int64
        - name: filters
          description: |-
            返回字段皆支持筛选，筛选扩展:
            With:{模块名} - 返回关联表(此类过滤项无value，所以可将同类name合并name=.的values中)
            With:{模块名}.{字段名} - 过滤并返回关联表
            Has:{模块名}.{字段名} - 子查询(等值连接)
          in: query
          required: false
          type: array
          items:
            $ref: ../../common/model/Filter.yaml#/definitions/filter
          collectionFormat: multi
        - name: sorts
          description: |-
            返回字段皆支持排序，排序扩展:
            With:{模块名}.[ Min Max Count Sum ]:{字段名} - 按关联表字段排序
          in: query
          required: false
          type: array
          items:
            $ref: ../../common/model/Sort.yaml#/definitions/sort
          collectionFormat: multi
  /cvesselVersion/{id}:
    get:
      description: 获取云舰版本信息
      operationId: describeVersionCvessel
      responses:
        "200":
          x-jdcloud-tiered: false
          description: A successful response.
          schema:
            properties:
              error:
                properties:
                  errorObject:
                    $ref: ../../common/model/Err.yaml#/definitions/err
              requestId:
                type: string
              result:
                properties:
                  resultObject:
                    $ref: ../module/version.swagger.yaml#/definitions/VersionDescribeCvesselReply
        default:
          description: An unexpected error response.
      parameters:
        - name: id
          description: |-
            云舰版本ID
            - 为0时按顺序查询字段：version
          in: path
          required: true
          type: integer
          format: int32
        - name: version
          description: 云舰版本
          in: query
          required: false
          type: string
  /cvesselVersion/{version_1}:
    post:
      description: 创建/更新zeus库内天基云舰版本元数据,只保留一份,不存在则新建
      operationId: modifyVersionCvesselMetadata
      responses:
        "200":
          x-jdcloud-tiered: false
          description: A successful response.
          schema:
            properties:
              error:
                properties:
                  errorObject:
                    $ref: ../../common/model/Err.yaml#/definitions/err
              requestId:
                type: string
              result:
                properties:
                  resultObject:
                    $ref: ../module/version.swagger.yaml#/definitions/VersionModifyCvesselMetadataReply
        default:
          description: An unexpected error response.
      parameters:
        - name: version_1
          description: 命名方式:封版版本
          in: path
          required: true
          type: string
        - name: body
          in: body
          required: true
          schema:
            type: object
            properties:
              data:
                type: string
                format: byte
                description: 封版快照内容字节流
              jdstackVersion:
                type: string
                description: 此封版快照所关联的jdstack版本号,版本统一后如果是jdstack封版类型调用,此字段无效
              preVersion:
                type: string
                description: 预留字段,前序版本
              snapshotVersion:
                type: string
                description: 命名方式:云舰版本封版快照号
              versionType:
                type: string
                description: 命名方式:封版版本类型
  /cvesselVersion/{version}:
    delete:
      description: 删除zeus库内天基云舰版本元数据
      operationId: deleteVersionCvesselMetadata
      responses:
        "200":
          x-jdcloud-tiered: false
          description: A successful response.
          schema:
            properties:
              error:
                properties:
                  errorObject:
                    $ref: ../../common/model/Err.yaml#/definitions/err
              requestId:
                type: string
              result:
                properties:
                  resultObject:
                    $ref: ../module/version.swagger.yaml#/definitions/VersionDeleteCvesselMetadataReply
        default:
          description: An unexpected error response.
      parameters:
        - name: version
          description: 命名方式:云舰版本号
          in: path
          required: true
          type: string
        - name: versionType
          description: 命名方式:封版版本类型
          in: query
          required: false
          type: string
        - name: jdstackVersion
          description: 此封版快照所关联的jdstack版本号,版本统一后如果是jdstack封版类型调用,此字段无效
          in: query
          required: false
          type: string
    post:
      summary: '直接将快照导入zeus使用的接口 HACK: 这里有演进历程，当前的实际逻辑和命名可能不match，以实际逻辑为主'
      description: 接收天基云舰版本元数据包并导入zeus库
      operationId: createVersionCvesselMetadata
      responses:
        "200":
          x-jdcloud-tiered: false
          description: A successful response.
          schema:
            properties:
              error:
                properties:
                  errorObject:
                    $ref: ../../common/model/Err.yaml#/definitions/err
              requestId:
                type: string
              result:
                properties:
                  resultObject:
                    $ref: ../module/version.swagger.yaml#/definitions/VersionCreateCvesselMetadataReply
        default:
          description: An unexpected error response.
      parameters:
        - name: version
          description: 命名方式:封版版本
          in: path
          required: true
          type: string
        - name: body
          in: body
          required: true
          schema:
            type: object
            properties:
              data:
                type: string
                format: byte
                description: 封版快照内容字节流
              forceOverride:
                type: boolean
                default: "true"
                description: 是否强制覆盖版本数据,请谨慎操作
              jdstackVersion:
                type: string
                description: 此封版快照所关联的jdstack版本号,版本统一后如果是jdstack封版类型调用,此字段无效
              preVersion:
                type: string
                description: 预留字段,前序版本
              snapshotVersion:
                type: string
                description: 命名方式:云舰版本封版快照号
              versionType:
                type: string
                description: 命名方式:封版版本类型
  /describeDeployVersionInfo:
    get:
      description: 获取离线交付场景下,想要交付的版本信息,前提是离线数据库中仅包含单个版本的数据,或包含前序版本的数据
      operationId: describeVersionDeployVersionInfo
      responses:
        "200":
          x-jdcloud-tiered: false
          description: A successful response.
          schema:
            properties:
              error:
                properties:
                  errorObject:
                    $ref: ../../common/model/Err.yaml#/definitions/err
              requestId:
                type: string
              result:
                properties:
                  resultObject:
                    $ref: ../module/version.swagger.yaml#/definitions/VersionDescribeDeployVersionInfoReply
        default:
          description: An unexpected error response.
      parameters:
        - name: cloudType
          description: '云类型: jdstack | cvessel'
          in: query
          required: false
          type: string
  /releaseVersion/{version}/fileName/{fileName}:
    get:
      description: 获取zeus库内封版版本内制定配置文件,由于相对路径不同,相同名称可能是多份
      operationId: describeVersionReleaseConfigFile
      responses:
        "200":
          x-jdcloud-tiered: false
          description: A successful response.
          schema:
            properties:
              error:
                properties:
                  errorObject:
                    $ref: ../../common/model/Err.yaml#/definitions/err
              requestId:
                type: string
              result:
                properties:
                  resultObject:
                    $ref: ../module/version.swagger.yaml#/definitions/VersionDescribeReleaseConfigFilesReply
        default:
          description: An unexpected error response.
      parameters:
        - name: version
          in: path
          required: true
          type: string
        - name: fileName
          in: path
          required: true
          type: string
        - name: cloudType
          in: query
          required: false
          type: string
  /version:
    get:
      description: 获取JDStack版本列表
      operationId: describeVersions
      responses:
        "200":
          x-jdcloud-tiered: false
          description: A successful response.
          schema:
            properties:
              error:
                properties:
                  errorObject:
                    $ref: ../../common/model/Err.yaml#/definitions/err
              requestId:
                type: string
              result:
                properties:
                  resultObject:
                    $ref: ../module/version.swagger.yaml#/definitions/VersionDescribesReply
        default:
          description: An unexpected error response.
      parameters:
        - name: pageNumber
          description: |-
            页码:
            -1 - 全部
            0 - 第1页
            1 - 第2页
            - 以此类推...
          in: query
          required: true
          type: integer
          format: int64
          default: "0"
        - name: pageSize
          in: query
          required: true
          type: integer
          format: int64
        - name: filters
          description: |-
            返回字段皆支持筛选，筛选扩展:
            With:{模块名} - 返回关联表(此类过滤项无value，所以可将同类name合并name=.的values中)
            With:{模块名}.{字段名} - 过滤并返回关联表
            Has:{模块名}.{字段名} - 子查询(等值连接)
          in: query
          required: false
          type: array
          items:
            $ref: ../../common/model/Filter.yaml#/definitions/filter
          collectionFormat: multi
        - name: sorts
          description: |-
            返回字段皆支持排序，排序扩展:
            With:{模块名}.[ Min Max Count Sum ]:{字段名} - 按关联表字段排序
          in: query
          required: false
          type: array
          items:
            $ref: ../../common/model/Sort.yaml#/definitions/sort
          collectionFormat: multi
    post:
      description: 创建JDStack版本信息
      operationId: createVersion
      responses:
        "200":
          x-jdcloud-tiered: false
          description: A successful response.
          schema:
            properties:
              error:
                properties:
                  errorObject:
                    $ref: ../../common/model/Err.yaml#/definitions/err
              requestId:
                type: string
              result:
                properties:
                  resultObject:
                    $ref: ../module/version.swagger.yaml#/definitions/VersionDescribeReply
        default:
          description: An unexpected error response.
      parameters:
        - x-jdcloud-tiered: false
          name: body
          in: body
          required: true
          schema:
            $ref: ../module/version.swagger.yaml#/definitions/VersionCreateReq
  /version/{id}:
    get:
      description: 获取JDStack版本信息
      operationId: describeVersion
      responses:
        "200":
          x-jdcloud-tiered: false
          description: A successful response.
          schema:
            properties:
              error:
                properties:
                  errorObject:
                    $ref: ../../common/model/Err.yaml#/definitions/err
              requestId:
                type: string
              result:
                properties:
                  resultObject:
                    $ref: ../module/version.swagger.yaml#/definitions/VersionDescribeReply
        default:
          description: An unexpected error response.
      parameters:
        - name: id
          description: |-
            JDStack版本ID
            - 为0时按顺序查询字段：version
          in: path
          required: true
          type: integer
          format: int32
        - name: version
          description: JDStack版本
          in: query
          required: false
          type: string
    put:
      description: 修改JDStack版本信息
      operationId: modifyVersion
      responses:
        "200":
          x-jdcloud-tiered: false
          description: A successful response.
          schema:
            properties:
              error:
                properties:
                  errorObject:
                    $ref: ../../common/model/Err.yaml#/definitions/err
              requestId:
                type: string
        default:
          description: An unexpected error response.
      parameters:
        - name: id
          description: |-
            JDStack版本ID
            - 为0时按顺序查询字段：version
          in: path
          required: true
          type: integer
          format: int32
        - name: body
          in: body
          required: true
          schema:
            type: object
            properties:
              clearFields:
                type: array
                items:
                  type: string
                description: |-
                  清空字段:
                  description - 描述
              description:
                type: string
                description: 描述
              locked:
                type: string
                description: |-
                  是否锁定:
                  [ no yes ]
              version:
                type: string
                description: JDStack版本,只读
  /version/{version}/exportSql:
    post:
      description: 导出指定版本的SQL文件并上传至S3
      operationId: createVersionSqlExportToS3
      responses:
        "200":
          x-jdcloud-tiered: false
          description: A successful response.
          schema:
            properties:
              error:
                properties:
                  errorObject:
                    $ref: ../../common/model/Err.yaml#/definitions/err
              requestId:
                type: string
              result:
                properties:
                  resultObject:
                    $ref: ../module/version.swagger.yaml#/definitions/VersionCreateSqlExportToS3Reply
        default:
          description: An unexpected error response.
      parameters:
        - name: version
          description: Zeus版本号
          in: path
          required: true
          type: string
        - name: body
          in: body
          required: true
          schema:
            type: object
            properties:
              includePrevVersion:
                type: boolean
                default: "false"
                description: 是否包含前序版本数据
            title: CreateSqlExportToS3 相关消息
  /version/{version}/modifyDependencies:
    post:
      description: 全量更新版本内产品间部署依赖信息,仅限云舰版本且仅处理主集群,最终数据会注入goods_chain
      operationId: modifyVersionDependencies
      responses:
        "200":
          x-jdcloud-tiered: false
          description: A successful response.
          schema:
            properties:
              error:
                properties:
                  errorObject:
                    $ref: ../../common/model/Err.yaml#/definitions/err
              requestId:
                type: string
              result:
                properties:
                  resultObject:
                    $ref: ../module/version.swagger.yaml#/definitions/VersionModifyDependenciesReply
        default:
          description: An unexpected error response.
      parameters:
        - name: version
          description: 封版版本号,仅限更改
          in: path
          required: true
          type: string
        - name: body
          in: body
          required: true
          schema:
            type: object
            properties:
              products:
                type: array
                items:
                  $ref: ../module/version.swagger.yaml#/definitions/VersionProductInfo
                description: 产品列表
            title: ModifyDependencies related messages
