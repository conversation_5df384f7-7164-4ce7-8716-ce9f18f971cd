// Code generated by protoc-gen-go-http. DO NOT EDIT.
// versions:
// - protoc-gen-go-http v2.6.2.230413
// - protoc             v3.21.1
// source: version.proto

package pbVersion

import (
	context1 "coding.jd.com/pcd-application/win-go/context"
	http1 "coding.jd.com/pcd-application/win-go/transport/http"
	context "context"
	http "github.com/go-kratos/kratos/v2/transport/http"
	binding "github.com/go-kratos/kratos/v2/transport/http/binding"
	empty "github.com/golang/protobuf/ptypes/empty"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the kratos package it is being compiled against.
var _ = new(context.Context)
var _ = binding.EncodeURL
var _ = new(context1.Context)

const _ = http.SupportPackageIsVersion1
const _ = http1.SupportPackageIsVersion1

const OperationVersionCreate = "/jdstack.zeus.v1.version.Version/Create"
const OperationVersionCreateCvessel = "/jdstack.zeus.v1.version.Version/CreateCvessel"
const OperationVersionCreateCvesselMetadata = "/jdstack.zeus.v1.version.Version/CreateCvesselMetadata"
const OperationVersionCreateSqlExportToS3 = "/jdstack.zeus.v1.version.Version/CreateSqlExportToS3"
const OperationVersionDeleteCvesselMetadata = "/jdstack.zeus.v1.version.Version/DeleteCvesselMetadata"
const OperationVersionDescribe = "/jdstack.zeus.v1.version.Version/Describe"
const OperationVersionDescribeCvessel = "/jdstack.zeus.v1.version.Version/DescribeCvessel"
const OperationVersionDescribeDeployVersionInfo = "/jdstack.zeus.v1.version.Version/DescribeDeployVersionInfo"
const OperationVersionDescribeReleaseConfigFile = "/jdstack.zeus.v1.version.Version/DescribeReleaseConfigFile"
const OperationVersionDescribes = "/jdstack.zeus.v1.version.Version/Describes"
const OperationVersionDescribesCvessel = "/jdstack.zeus.v1.version.Version/DescribesCvessel"
const OperationVersionModify = "/jdstack.zeus.v1.version.Version/Modify"
const OperationVersionModifyCvessel = "/jdstack.zeus.v1.version.Version/ModifyCvessel"
const OperationVersionModifyCvesselMetadata = "/jdstack.zeus.v1.version.Version/ModifyCvesselMetadata"
const OperationVersionModifyDependencies = "/jdstack.zeus.v1.version.Version/ModifyDependencies"

type VersionHTTPServer interface {
	Create(context.Context, context1.Context, *CreateReq) (*DescribeReply, error)
	CreateCvessel(context.Context, context1.Context, *CreateCvesselReq) (*DescribeCvesselReply, error)
	// CreateCvesselMetadata 直接将快照导入zeus使用的接口 HACK: 这里有演进历程，当前的实际逻辑和命名可能不match，以实际逻辑为主
	CreateCvesselMetadata(context.Context, context1.Context, *CreateCvesselMetadataReq) (*CreateCvesselMetadataReply, error)
	CreateSqlExportToS3(context.Context, context1.Context, *CreateSqlExportToS3Req) (*CreateSqlExportToS3Reply, error)
	DeleteCvesselMetadata(context.Context, context1.Context, *DeleteCvesselMetadataReq) (*DeleteCvesselMetadataReply, error)
	Describe(context.Context, context1.Context, *DescribeReq) (*DescribeReply, error)
	DescribeCvessel(context.Context, context1.Context, *DescribeCvesselReq) (*DescribeCvesselReply, error)
	DescribeDeployVersionInfo(context.Context, context1.Context, *DescribeDeployVersionInfoReq) (*DescribeDeployVersionInfoReply, error)
	DescribeReleaseConfigFile(context.Context, context1.Context, *DescribeReleaseConfigFileReq) (*DescribeReleaseConfigFilesReply, error)
	Describes(context.Context, context1.Context, *DescribesReq) (*DescribesReply, error)
	DescribesCvessel(context.Context, context1.Context, *DescribesCvesselReq) (*DescribesCvesselReply, error)
	Modify(context.Context, context1.Context, *ModifyReq) (*empty.Empty, error)
	ModifyCvessel(context.Context, context1.Context, *ModifyCvesselReq) (*empty.Empty, error)
	ModifyCvesselMetadata(context.Context, context1.Context, *ModifyCvesselMetadataReq) (*ModifyCvesselMetadataReply, error)
	ModifyDependencies(context.Context, context1.Context, *ModifyDependenciesReq) (*ModifyDependenciesReply, error)
}

func RegisterVersionHTTPServer(r *http1.Router, srv VersionHTTPServer) {
	r.GET("/releaseVersion/{version}/fileName/{fileName}", _Version_DescribeReleaseConfigFile0_HTTP_Handler(srv))
	r.POST("/version/{version}/modifyDependencies", _Version_ModifyDependencies0_HTTP_Handler(srv))
	r.POST("/version/{version}/exportSql", _Version_CreateSqlExportToS30_HTTP_Handler(srv))
	r.GET("/describeDeployVersionInfo", _Version_DescribeDeployVersionInfo0_HTTP_Handler(srv))
	r.POST("/cvesselVersion/{version}", _Version_CreateCvesselMetadata0_HTTP_Handler(srv))
	r.POST("/cvesselVersion/{version}", _Version_ModifyCvesselMetadata0_HTTP_Handler(srv))
	r.DELETE("/cvesselVersion/{version}", _Version_DeleteCvesselMetadata0_HTTP_Handler(srv))
	r.PUT("/CvesselVersion/{id}", _Version_ModifyCvessel0_HTTP_Handler(srv))
	r.GET("/cvesselVersion/{id}", _Version_DescribeCvessel0_HTTP_Handler(srv))
	r.POST("/CvesselVersion", _Version_CreateCvessel0_HTTP_Handler(srv))
	r.GET("/cvesselVersion", _Version_DescribesCvessel0_HTTP_Handler(srv))
	r.PUT("/version/{id}", _Version_Modify0_HTTP_Handler(srv))
	r.GET("/version/{id}", _Version_Describe0_HTTP_Handler(srv))
	r.POST("/version", _Version_Create0_HTTP_Handler(srv))
	r.GET("/version", _Version_Describes0_HTTP_Handler(srv))
}

func _Version_DescribeReleaseConfigFile0_HTTP_Handler(srv VersionHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in DescribeReleaseConfigFileReq
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationVersionDescribeReleaseConfigFile)
		h := ctx.Middleware(func(ctx context.Context, req any) (any, error) {
			return srv.DescribeReleaseConfigFile(ctx, context1.ParseContext(ctx), req.(*DescribeReleaseConfigFileReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*DescribeReleaseConfigFilesReply)
		return ctx.Result(200, reply)
	}
}

func _Version_ModifyDependencies0_HTTP_Handler(srv VersionHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ModifyDependenciesReq
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationVersionModifyDependencies)
		h := ctx.Middleware(func(ctx context.Context, req any) (any, error) {
			return srv.ModifyDependencies(ctx, context1.ParseContext(ctx), req.(*ModifyDependenciesReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ModifyDependenciesReply)
		return ctx.Result(200, reply)
	}
}

func _Version_CreateSqlExportToS30_HTTP_Handler(srv VersionHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in CreateSqlExportToS3Req
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationVersionCreateSqlExportToS3)
		h := ctx.Middleware(func(ctx context.Context, req any) (any, error) {
			return srv.CreateSqlExportToS3(ctx, context1.ParseContext(ctx), req.(*CreateSqlExportToS3Req))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*CreateSqlExportToS3Reply)
		return ctx.Result(200, reply)
	}
}

func _Version_DescribeDeployVersionInfo0_HTTP_Handler(srv VersionHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in DescribeDeployVersionInfoReq
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationVersionDescribeDeployVersionInfo)
		h := ctx.Middleware(func(ctx context.Context, req any) (any, error) {
			return srv.DescribeDeployVersionInfo(ctx, context1.ParseContext(ctx), req.(*DescribeDeployVersionInfoReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*DescribeDeployVersionInfoReply)
		return ctx.Result(200, reply)
	}
}

func _Version_CreateCvesselMetadata0_HTTP_Handler(srv VersionHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in CreateCvesselMetadataReq
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationVersionCreateCvesselMetadata)
		h := ctx.Middleware(func(ctx context.Context, req any) (any, error) {
			return srv.CreateCvesselMetadata(ctx, context1.ParseContext(ctx), req.(*CreateCvesselMetadataReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*CreateCvesselMetadataReply)
		return ctx.Result(200, reply)
	}
}

func _Version_ModifyCvesselMetadata0_HTTP_Handler(srv VersionHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ModifyCvesselMetadataReq
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationVersionModifyCvesselMetadata)
		h := ctx.Middleware(func(ctx context.Context, req any) (any, error) {
			return srv.ModifyCvesselMetadata(ctx, context1.ParseContext(ctx), req.(*ModifyCvesselMetadataReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ModifyCvesselMetadataReply)
		return ctx.Result(200, reply)
	}
}

func _Version_DeleteCvesselMetadata0_HTTP_Handler(srv VersionHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in DeleteCvesselMetadataReq
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationVersionDeleteCvesselMetadata)
		h := ctx.Middleware(func(ctx context.Context, req any) (any, error) {
			return srv.DeleteCvesselMetadata(ctx, context1.ParseContext(ctx), req.(*DeleteCvesselMetadataReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*DeleteCvesselMetadataReply)
		return ctx.Result(200, reply)
	}
}

func _Version_ModifyCvessel0_HTTP_Handler(srv VersionHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ModifyCvesselReq
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationVersionModifyCvessel)
		h := ctx.Middleware(func(ctx context.Context, req any) (any, error) {
			return srv.ModifyCvessel(ctx, context1.ParseContext(ctx), req.(*ModifyCvesselReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*empty.Empty)
		return ctx.Result(200, reply)
	}
}

func _Version_DescribeCvessel0_HTTP_Handler(srv VersionHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in DescribeCvesselReq
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationVersionDescribeCvessel)
		h := ctx.Middleware(func(ctx context.Context, req any) (any, error) {
			return srv.DescribeCvessel(ctx, context1.ParseContext(ctx), req.(*DescribeCvesselReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*DescribeCvesselReply)
		return ctx.Result(200, reply)
	}
}

func _Version_CreateCvessel0_HTTP_Handler(srv VersionHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in CreateCvesselReq
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationVersionCreateCvessel)
		h := ctx.Middleware(func(ctx context.Context, req any) (any, error) {
			return srv.CreateCvessel(ctx, context1.ParseContext(ctx), req.(*CreateCvesselReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*DescribeCvesselReply)
		return ctx.Result(200, reply)
	}
}

func _Version_DescribesCvessel0_HTTP_Handler(srv VersionHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in DescribesCvesselReq
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationVersionDescribesCvessel)
		h := ctx.Middleware(func(ctx context.Context, req any) (any, error) {
			return srv.DescribesCvessel(ctx, context1.ParseContext(ctx), req.(*DescribesCvesselReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*DescribesCvesselReply)
		return ctx.Result(200, reply)
	}
}

func _Version_Modify0_HTTP_Handler(srv VersionHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ModifyReq
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationVersionModify)
		h := ctx.Middleware(func(ctx context.Context, req any) (any, error) {
			return srv.Modify(ctx, context1.ParseContext(ctx), req.(*ModifyReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*empty.Empty)
		return ctx.Result(200, reply)
	}
}

func _Version_Describe0_HTTP_Handler(srv VersionHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in DescribeReq
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationVersionDescribe)
		h := ctx.Middleware(func(ctx context.Context, req any) (any, error) {
			return srv.Describe(ctx, context1.ParseContext(ctx), req.(*DescribeReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*DescribeReply)
		return ctx.Result(200, reply)
	}
}

func _Version_Create0_HTTP_Handler(srv VersionHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in CreateReq
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationVersionCreate)
		h := ctx.Middleware(func(ctx context.Context, req any) (any, error) {
			return srv.Create(ctx, context1.ParseContext(ctx), req.(*CreateReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*DescribeReply)
		return ctx.Result(200, reply)
	}
}

func _Version_Describes0_HTTP_Handler(srv VersionHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in DescribesReq
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationVersionDescribes)
		h := ctx.Middleware(func(ctx context.Context, req any) (any, error) {
			return srv.Describes(ctx, context1.ParseContext(ctx), req.(*DescribesReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*DescribesReply)
		return ctx.Result(200, reply)
	}
}

type VersionHTTPClient interface {
	Create(ctx context.Context, req *CreateReq, opts ...http.CallOption) (rsp *DescribeReply, err error)
	CreateCvessel(ctx context.Context, req *CreateCvesselReq, opts ...http.CallOption) (rsp *DescribeCvesselReply, err error)
	CreateCvesselMetadata(ctx context.Context, req *CreateCvesselMetadataReq, opts ...http.CallOption) (rsp *CreateCvesselMetadataReply, err error)
	CreateSqlExportToS3(ctx context.Context, req *CreateSqlExportToS3Req, opts ...http.CallOption) (rsp *CreateSqlExportToS3Reply, err error)
	DeleteCvesselMetadata(ctx context.Context, req *DeleteCvesselMetadataReq, opts ...http.CallOption) (rsp *DeleteCvesselMetadataReply, err error)
	Describe(ctx context.Context, req *DescribeReq, opts ...http.CallOption) (rsp *DescribeReply, err error)
	DescribeCvessel(ctx context.Context, req *DescribeCvesselReq, opts ...http.CallOption) (rsp *DescribeCvesselReply, err error)
	DescribeDeployVersionInfo(ctx context.Context, req *DescribeDeployVersionInfoReq, opts ...http.CallOption) (rsp *DescribeDeployVersionInfoReply, err error)
	DescribeReleaseConfigFile(ctx context.Context, req *DescribeReleaseConfigFileReq, opts ...http.CallOption) (rsp *DescribeReleaseConfigFilesReply, err error)
	Describes(ctx context.Context, req *DescribesReq, opts ...http.CallOption) (rsp *DescribesReply, err error)
	DescribesCvessel(ctx context.Context, req *DescribesCvesselReq, opts ...http.CallOption) (rsp *DescribesCvesselReply, err error)
	Modify(ctx context.Context, req *ModifyReq, opts ...http.CallOption) (rsp *empty.Empty, err error)
	ModifyCvessel(ctx context.Context, req *ModifyCvesselReq, opts ...http.CallOption) (rsp *empty.Empty, err error)
	ModifyCvesselMetadata(ctx context.Context, req *ModifyCvesselMetadataReq, opts ...http.CallOption) (rsp *ModifyCvesselMetadataReply, err error)
	ModifyDependencies(ctx context.Context, req *ModifyDependenciesReq, opts ...http.CallOption) (rsp *ModifyDependenciesReply, err error)
}

type VersionHTTPClientImpl struct {
	cc *http.Client
}

func NewVersionHTTPClient(client *http.Client) VersionHTTPClient {
	return &VersionHTTPClientImpl{client}
}

func (c *VersionHTTPClientImpl) Create(ctx context.Context, in *CreateReq, opts ...http.CallOption) (*DescribeReply, error) {
	var out DescribeReply
	pattern := "/version"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationVersionCreate))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *VersionHTTPClientImpl) CreateCvessel(ctx context.Context, in *CreateCvesselReq, opts ...http.CallOption) (*DescribeCvesselReply, error) {
	var out DescribeCvesselReply
	pattern := "/CvesselVersion"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationVersionCreateCvessel))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *VersionHTTPClientImpl) CreateCvesselMetadata(ctx context.Context, in *CreateCvesselMetadataReq, opts ...http.CallOption) (*CreateCvesselMetadataReply, error) {
	var out CreateCvesselMetadataReply
	pattern := "/cvesselVersion/{version}"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationVersionCreateCvesselMetadata))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *VersionHTTPClientImpl) CreateSqlExportToS3(ctx context.Context, in *CreateSqlExportToS3Req, opts ...http.CallOption) (*CreateSqlExportToS3Reply, error) {
	var out CreateSqlExportToS3Reply
	pattern := "/version/{version}/exportSql"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationVersionCreateSqlExportToS3))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *VersionHTTPClientImpl) DeleteCvesselMetadata(ctx context.Context, in *DeleteCvesselMetadataReq, opts ...http.CallOption) (*DeleteCvesselMetadataReply, error) {
	var out DeleteCvesselMetadataReply
	pattern := "/cvesselVersion/{version}"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationVersionDeleteCvesselMetadata))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "DELETE", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *VersionHTTPClientImpl) Describe(ctx context.Context, in *DescribeReq, opts ...http.CallOption) (*DescribeReply, error) {
	var out DescribeReply
	pattern := "/version/{id}"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationVersionDescribe))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *VersionHTTPClientImpl) DescribeCvessel(ctx context.Context, in *DescribeCvesselReq, opts ...http.CallOption) (*DescribeCvesselReply, error) {
	var out DescribeCvesselReply
	pattern := "/cvesselVersion/{id}"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationVersionDescribeCvessel))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *VersionHTTPClientImpl) DescribeDeployVersionInfo(ctx context.Context, in *DescribeDeployVersionInfoReq, opts ...http.CallOption) (*DescribeDeployVersionInfoReply, error) {
	var out DescribeDeployVersionInfoReply
	pattern := "/describeDeployVersionInfo"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationVersionDescribeDeployVersionInfo))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *VersionHTTPClientImpl) DescribeReleaseConfigFile(ctx context.Context, in *DescribeReleaseConfigFileReq, opts ...http.CallOption) (*DescribeReleaseConfigFilesReply, error) {
	var out DescribeReleaseConfigFilesReply
	pattern := "/releaseVersion/{version}/fileName/{fileName}"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationVersionDescribeReleaseConfigFile))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *VersionHTTPClientImpl) Describes(ctx context.Context, in *DescribesReq, opts ...http.CallOption) (*DescribesReply, error) {
	var out DescribesReply
	pattern := "/version"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationVersionDescribes))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *VersionHTTPClientImpl) DescribesCvessel(ctx context.Context, in *DescribesCvesselReq, opts ...http.CallOption) (*DescribesCvesselReply, error) {
	var out DescribesCvesselReply
	pattern := "/cvesselVersion"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationVersionDescribesCvessel))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *VersionHTTPClientImpl) Modify(ctx context.Context, in *ModifyReq, opts ...http.CallOption) (*empty.Empty, error) {
	var out empty.Empty
	pattern := "/version/{id}"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationVersionModify))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "PUT", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *VersionHTTPClientImpl) ModifyCvessel(ctx context.Context, in *ModifyCvesselReq, opts ...http.CallOption) (*empty.Empty, error) {
	var out empty.Empty
	pattern := "/CvesselVersion/{id}"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationVersionModifyCvessel))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "PUT", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *VersionHTTPClientImpl) ModifyCvesselMetadata(ctx context.Context, in *ModifyCvesselMetadataReq, opts ...http.CallOption) (*ModifyCvesselMetadataReply, error) {
	var out ModifyCvesselMetadataReply
	pattern := "/cvesselVersion/{version}"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationVersionModifyCvesselMetadata))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *VersionHTTPClientImpl) ModifyDependencies(ctx context.Context, in *ModifyDependenciesReq, opts ...http.CallOption) (*ModifyDependenciesReply, error) {
	var out ModifyDependenciesReply
	pattern := "/version/{version}/modifyDependencies"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationVersionModifyDependencies))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}
