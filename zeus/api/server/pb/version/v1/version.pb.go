// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.30.0
// 	protoc        v3.21.1
// source: version.proto

package pbVersion

import (
	winapi "coding.jd.com/pcd-application/win-go/third_party/winapi"
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	empty "github.com/golang/protobuf/ptypes/empty"
	_ "github.com/grpc-ecosystem/grpc-gateway/v2/protoc-gen-openapiv2/options"
	_ "github.com/srikrsna/protoc-gen-gotag/tagger"
	_ "google.golang.org/genproto/googleapis/api/annotations"
	_ "google.golang.org/genproto/googleapis/api/visibility"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type CreateReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id           int32  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty" excel:"ID"`
	Version      string `protobuf:"bytes,2,opt,name=version,proto3" json:"version,omitempty" excel:"JDStack版本" required:"true"`
	Description  string `protobuf:"bytes,4,opt,name=description,proto3" json:"description,omitempty" excel:"描述"`
	PrevPlatform string `protobuf:"bytes,5,opt,name=prevPlatform,proto3" json:"prevPlatform,omitempty" excel:"前序版本" required:"true"`
}

func (x *CreateReq) Reset() {
	*x = CreateReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_version_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateReq) ProtoMessage() {}

func (x *CreateReq) ProtoReflect() protoreflect.Message {
	mi := &file_version_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateReq.ProtoReflect.Descriptor instead.
func (*CreateReq) Descriptor() ([]byte, []int) {
	return file_version_proto_rawDescGZIP(), []int{0}
}

func (x *CreateReq) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *CreateReq) GetVersion() string {
	if x != nil {
		return x.Version
	}
	return ""
}

func (x *CreateReq) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *CreateReq) GetPrevPlatform() string {
	if x != nil {
		return x.PrevPlatform
	}
	return ""
}

type ModifyReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id          int32    `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Version     string   `protobuf:"bytes,2,opt,name=version,proto3" json:"version,omitempty"`
	ClearFields []string `protobuf:"bytes,3,rep,name=clearFields,proto3" json:"clearFields,omitempty"`
	Description string   `protobuf:"bytes,4,opt,name=description,proto3" json:"description,omitempty"`
	Locked      string   `protobuf:"bytes,5,opt,name=locked,proto3" json:"locked,omitempty"`
}

func (x *ModifyReq) Reset() {
	*x = ModifyReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_version_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ModifyReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ModifyReq) ProtoMessage() {}

func (x *ModifyReq) ProtoReflect() protoreflect.Message {
	mi := &file_version_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ModifyReq.ProtoReflect.Descriptor instead.
func (*ModifyReq) Descriptor() ([]byte, []int) {
	return file_version_proto_rawDescGZIP(), []int{1}
}

func (x *ModifyReq) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *ModifyReq) GetVersion() string {
	if x != nil {
		return x.Version
	}
	return ""
}

func (x *ModifyReq) GetClearFields() []string {
	if x != nil {
		return x.ClearFields
	}
	return nil
}

func (x *ModifyReq) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *ModifyReq) GetLocked() string {
	if x != nil {
		return x.Locked
	}
	return ""
}

type DescribeReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id      int32  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Version string `protobuf:"bytes,2,opt,name=version,proto3" json:"version,omitempty" excel:"JDStack版本" required:"true"`
}

func (x *DescribeReq) Reset() {
	*x = DescribeReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_version_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DescribeReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DescribeReq) ProtoMessage() {}

func (x *DescribeReq) ProtoReflect() protoreflect.Message {
	mi := &file_version_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DescribeReq.ProtoReflect.Descriptor instead.
func (*DescribeReq) Descriptor() ([]byte, []int) {
	return file_version_proto_rawDescGZIP(), []int{2}
}

func (x *DescribeReq) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *DescribeReq) GetVersion() string {
	if x != nil {
		return x.Version
	}
	return ""
}

type DescribeReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id              int32  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Version         string `protobuf:"bytes,2,opt,name=version,proto3" json:"version,omitempty"`
	CreatedAt       string `protobuf:"bytes,3,opt,name=createdAt,proto3" json:"createdAt,omitempty"`
	Description     string `protobuf:"bytes,4,opt,name=description,proto3" json:"description,omitempty"`
	PrevPlatform    string `protobuf:"bytes,5,opt,name=prevPlatform,proto3" json:"prevPlatform,omitempty"`
	NextPlatform    string `protobuf:"bytes,6,opt,name=nextPlatform,proto3" json:"nextPlatform,omitempty"`
	Locked          string `protobuf:"bytes,7,opt,name=locked,proto3" json:"locked,omitempty"`
	CloudType       string `protobuf:"bytes,8,opt,name=cloudType,proto3" json:"cloudType,omitempty"`
	RelatedPlatform string `protobuf:"bytes,9,opt,name=relatedPlatform,proto3" json:"relatedPlatform,omitempty"`
}

func (x *DescribeReply) Reset() {
	*x = DescribeReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_version_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DescribeReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DescribeReply) ProtoMessage() {}

func (x *DescribeReply) ProtoReflect() protoreflect.Message {
	mi := &file_version_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DescribeReply.ProtoReflect.Descriptor instead.
func (*DescribeReply) Descriptor() ([]byte, []int) {
	return file_version_proto_rawDescGZIP(), []int{3}
}

func (x *DescribeReply) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *DescribeReply) GetVersion() string {
	if x != nil {
		return x.Version
	}
	return ""
}

func (x *DescribeReply) GetCreatedAt() string {
	if x != nil {
		return x.CreatedAt
	}
	return ""
}

func (x *DescribeReply) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *DescribeReply) GetPrevPlatform() string {
	if x != nil {
		return x.PrevPlatform
	}
	return ""
}

func (x *DescribeReply) GetNextPlatform() string {
	if x != nil {
		return x.NextPlatform
	}
	return ""
}

func (x *DescribeReply) GetLocked() string {
	if x != nil {
		return x.Locked
	}
	return ""
}

func (x *DescribeReply) GetCloudType() string {
	if x != nil {
		return x.CloudType
	}
	return ""
}

func (x *DescribeReply) GetRelatedPlatform() string {
	if x != nil {
		return x.RelatedPlatform
	}
	return ""
}

type DescribesReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PageNumber int64            `protobuf:"varint,1,opt,name=pageNumber,proto3" json:"pageNumber,omitempty"`
	PageSize   int64            `protobuf:"varint,2,opt,name=pageSize,proto3" json:"pageSize,omitempty"`
	Filters    []*winapi.Filter `protobuf:"bytes,3,rep,name=filters,proto3" json:"filters,omitempty"`
	Sorts      []*winapi.Sort   `protobuf:"bytes,4,rep,name=sorts,proto3" json:"sorts,omitempty"`
}

func (x *DescribesReq) Reset() {
	*x = DescribesReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_version_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DescribesReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DescribesReq) ProtoMessage() {}

func (x *DescribesReq) ProtoReflect() protoreflect.Message {
	mi := &file_version_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DescribesReq.ProtoReflect.Descriptor instead.
func (*DescribesReq) Descriptor() ([]byte, []int) {
	return file_version_proto_rawDescGZIP(), []int{4}
}

func (x *DescribesReq) GetPageNumber() int64 {
	if x != nil {
		return x.PageNumber
	}
	return 0
}

func (x *DescribesReq) GetPageSize() int64 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *DescribesReq) GetFilters() []*winapi.Filter {
	if x != nil {
		return x.Filters
	}
	return nil
}

func (x *DescribesReq) GetSorts() []*winapi.Sort {
	if x != nil {
		return x.Sorts
	}
	return nil
}

type DescribesReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	List       []*DescribeReply `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
	TotalCount int64            `protobuf:"varint,2,opt,name=totalCount,proto3" json:"totalCount,omitempty"`
}

func (x *DescribesReply) Reset() {
	*x = DescribesReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_version_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DescribesReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DescribesReply) ProtoMessage() {}

func (x *DescribesReply) ProtoReflect() protoreflect.Message {
	mi := &file_version_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DescribesReply.ProtoReflect.Descriptor instead.
func (*DescribesReply) Descriptor() ([]byte, []int) {
	return file_version_proto_rawDescGZIP(), []int{5}
}

func (x *DescribesReply) GetList() []*DescribeReply {
	if x != nil {
		return x.List
	}
	return nil
}

func (x *DescribesReply) GetTotalCount() int64 {
	if x != nil {
		return x.TotalCount
	}
	return 0
}

type CreateCvesselReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id             int32  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty" excel:"ID"`
	Version        string `protobuf:"bytes,2,opt,name=version,proto3" json:"version,omitempty" excel:"云舰版本" required:"true"`
	JdstackVersion string `protobuf:"bytes,4,opt,name=jdstackVersion,proto3" json:"jdstackVersion,omitempty" excel:"JDStack版本" required:"true"`
	Description    string `protobuf:"bytes,5,opt,name=description,proto3" json:"description,omitempty" excel:"描述"`
	PreVersion     string `protobuf:"bytes,6,opt,name=preVersion,proto3" json:"preVersion,omitempty" excel:"前序版本" required:"true"`
}

func (x *CreateCvesselReq) Reset() {
	*x = CreateCvesselReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_version_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateCvesselReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateCvesselReq) ProtoMessage() {}

func (x *CreateCvesselReq) ProtoReflect() protoreflect.Message {
	mi := &file_version_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateCvesselReq.ProtoReflect.Descriptor instead.
func (*CreateCvesselReq) Descriptor() ([]byte, []int) {
	return file_version_proto_rawDescGZIP(), []int{6}
}

func (x *CreateCvesselReq) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *CreateCvesselReq) GetVersion() string {
	if x != nil {
		return x.Version
	}
	return ""
}

func (x *CreateCvesselReq) GetJdstackVersion() string {
	if x != nil {
		return x.JdstackVersion
	}
	return ""
}

func (x *CreateCvesselReq) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *CreateCvesselReq) GetPreVersion() string {
	if x != nil {
		return x.PreVersion
	}
	return ""
}

type ModifyCvesselReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id          int32    `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Version     string   `protobuf:"bytes,2,opt,name=version,proto3" json:"version,omitempty"`
	ClearFields []string `protobuf:"bytes,3,rep,name=clearFields,proto3" json:"clearFields,omitempty"`
	Description string   `protobuf:"bytes,5,opt,name=description,proto3" json:"description,omitempty"`
}

func (x *ModifyCvesselReq) Reset() {
	*x = ModifyCvesselReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_version_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ModifyCvesselReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ModifyCvesselReq) ProtoMessage() {}

func (x *ModifyCvesselReq) ProtoReflect() protoreflect.Message {
	mi := &file_version_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ModifyCvesselReq.ProtoReflect.Descriptor instead.
func (*ModifyCvesselReq) Descriptor() ([]byte, []int) {
	return file_version_proto_rawDescGZIP(), []int{7}
}

func (x *ModifyCvesselReq) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *ModifyCvesselReq) GetVersion() string {
	if x != nil {
		return x.Version
	}
	return ""
}

func (x *ModifyCvesselReq) GetClearFields() []string {
	if x != nil {
		return x.ClearFields
	}
	return nil
}

func (x *ModifyCvesselReq) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

type DescribeCvesselReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id      int32  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Version string `protobuf:"bytes,2,opt,name=version,proto3" json:"version,omitempty" excel:"云舰版本" required:"true"`
}

func (x *DescribeCvesselReq) Reset() {
	*x = DescribeCvesselReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_version_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DescribeCvesselReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DescribeCvesselReq) ProtoMessage() {}

func (x *DescribeCvesselReq) ProtoReflect() protoreflect.Message {
	mi := &file_version_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DescribeCvesselReq.ProtoReflect.Descriptor instead.
func (*DescribeCvesselReq) Descriptor() ([]byte, []int) {
	return file_version_proto_rawDescGZIP(), []int{8}
}

func (x *DescribeCvesselReq) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *DescribeCvesselReq) GetVersion() string {
	if x != nil {
		return x.Version
	}
	return ""
}

type DescribeCvesselReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id             int32          `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Version        string         `protobuf:"bytes,2,opt,name=version,proto3" json:"version,omitempty"`
	CreatedAt      string         `protobuf:"bytes,3,opt,name=createdAt,proto3" json:"createdAt,omitempty"`
	JdstackVersion *DescribeReply `protobuf:"bytes,4,opt,name=jdstackVersion,proto3" json:"jdstackVersion,omitempty"`
	Description    string         `protobuf:"bytes,5,opt,name=description,proto3" json:"description,omitempty"`
	PreVersion     string         `protobuf:"bytes,6,opt,name=preVersion,proto3" json:"preVersion,omitempty"`
	NextVersions   []string       `protobuf:"bytes,7,rep,name=nextVersions,proto3" json:"nextVersions,omitempty"`
}

func (x *DescribeCvesselReply) Reset() {
	*x = DescribeCvesselReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_version_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DescribeCvesselReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DescribeCvesselReply) ProtoMessage() {}

func (x *DescribeCvesselReply) ProtoReflect() protoreflect.Message {
	mi := &file_version_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DescribeCvesselReply.ProtoReflect.Descriptor instead.
func (*DescribeCvesselReply) Descriptor() ([]byte, []int) {
	return file_version_proto_rawDescGZIP(), []int{9}
}

func (x *DescribeCvesselReply) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *DescribeCvesselReply) GetVersion() string {
	if x != nil {
		return x.Version
	}
	return ""
}

func (x *DescribeCvesselReply) GetCreatedAt() string {
	if x != nil {
		return x.CreatedAt
	}
	return ""
}

func (x *DescribeCvesselReply) GetJdstackVersion() *DescribeReply {
	if x != nil {
		return x.JdstackVersion
	}
	return nil
}

func (x *DescribeCvesselReply) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *DescribeCvesselReply) GetPreVersion() string {
	if x != nil {
		return x.PreVersion
	}
	return ""
}

func (x *DescribeCvesselReply) GetNextVersions() []string {
	if x != nil {
		return x.NextVersions
	}
	return nil
}

type DescribesCvesselReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PageNumber int64            `protobuf:"varint,1,opt,name=pageNumber,proto3" json:"pageNumber,omitempty"`
	PageSize   int64            `protobuf:"varint,2,opt,name=pageSize,proto3" json:"pageSize,omitempty"`
	Filters    []*winapi.Filter `protobuf:"bytes,3,rep,name=filters,proto3" json:"filters,omitempty"`
	Sorts      []*winapi.Sort   `protobuf:"bytes,4,rep,name=sorts,proto3" json:"sorts,omitempty"`
}

func (x *DescribesCvesselReq) Reset() {
	*x = DescribesCvesselReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_version_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DescribesCvesselReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DescribesCvesselReq) ProtoMessage() {}

func (x *DescribesCvesselReq) ProtoReflect() protoreflect.Message {
	mi := &file_version_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DescribesCvesselReq.ProtoReflect.Descriptor instead.
func (*DescribesCvesselReq) Descriptor() ([]byte, []int) {
	return file_version_proto_rawDescGZIP(), []int{10}
}

func (x *DescribesCvesselReq) GetPageNumber() int64 {
	if x != nil {
		return x.PageNumber
	}
	return 0
}

func (x *DescribesCvesselReq) GetPageSize() int64 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *DescribesCvesselReq) GetFilters() []*winapi.Filter {
	if x != nil {
		return x.Filters
	}
	return nil
}

func (x *DescribesCvesselReq) GetSorts() []*winapi.Sort {
	if x != nil {
		return x.Sorts
	}
	return nil
}

type DescribesCvesselReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	List       []*DescribeCvesselReply `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
	TotalCount int64                   `protobuf:"varint,2,opt,name=totalCount,proto3" json:"totalCount,omitempty"`
}

func (x *DescribesCvesselReply) Reset() {
	*x = DescribesCvesselReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_version_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DescribesCvesselReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DescribesCvesselReply) ProtoMessage() {}

func (x *DescribesCvesselReply) ProtoReflect() protoreflect.Message {
	mi := &file_version_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DescribesCvesselReply.ProtoReflect.Descriptor instead.
func (*DescribesCvesselReply) Descriptor() ([]byte, []int) {
	return file_version_proto_rawDescGZIP(), []int{11}
}

func (x *DescribesCvesselReply) GetList() []*DescribeCvesselReply {
	if x != nil {
		return x.List
	}
	return nil
}

func (x *DescribesCvesselReply) GetTotalCount() int64 {
	if x != nil {
		return x.TotalCount
	}
	return 0
}

type DescribeDeployVersionInfoReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CloudType string `protobuf:"bytes,1,opt,name=cloudType,proto3" json:"cloudType,omitempty"`
}

func (x *DescribeDeployVersionInfoReq) Reset() {
	*x = DescribeDeployVersionInfoReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_version_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DescribeDeployVersionInfoReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DescribeDeployVersionInfoReq) ProtoMessage() {}

func (x *DescribeDeployVersionInfoReq) ProtoReflect() protoreflect.Message {
	mi := &file_version_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DescribeDeployVersionInfoReq.ProtoReflect.Descriptor instead.
func (*DescribeDeployVersionInfoReq) Descriptor() ([]byte, []int) {
	return file_version_proto_rawDescGZIP(), []int{12}
}

func (x *DescribeDeployVersionInfoReq) GetCloudType() string {
	if x != nil {
		return x.CloudType
	}
	return ""
}

type DescribeDeployVersionInfoReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	DeployVersion     *DescribeReply `protobuf:"bytes,1,opt,name=deployVersion,proto3" json:"deployVersion,omitempty"`
	DeployVersionPrev *DescribeReply `protobuf:"bytes,2,opt,name=deployVersionPrev,proto3" json:"deployVersionPrev,omitempty"`
}

func (x *DescribeDeployVersionInfoReply) Reset() {
	*x = DescribeDeployVersionInfoReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_version_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DescribeDeployVersionInfoReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DescribeDeployVersionInfoReply) ProtoMessage() {}

func (x *DescribeDeployVersionInfoReply) ProtoReflect() protoreflect.Message {
	mi := &file_version_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DescribeDeployVersionInfoReply.ProtoReflect.Descriptor instead.
func (*DescribeDeployVersionInfoReply) Descriptor() ([]byte, []int) {
	return file_version_proto_rawDescGZIP(), []int{13}
}

func (x *DescribeDeployVersionInfoReply) GetDeployVersion() *DescribeReply {
	if x != nil {
		return x.DeployVersion
	}
	return nil
}

func (x *DescribeDeployVersionInfoReply) GetDeployVersionPrev() *DescribeReply {
	if x != nil {
		return x.DeployVersionPrev
	}
	return nil
}

type CreateCvesselMetadataReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Version         string `protobuf:"bytes,1,opt,name=version,proto3" json:"version,omitempty"`
	VersionType     string `protobuf:"bytes,2,opt,name=versionType,proto3" json:"versionType,omitempty"`
	SnapshotVersion string `protobuf:"bytes,3,opt,name=snapshotVersion,proto3" json:"snapshotVersion,omitempty"`
	PreVersion      string `protobuf:"bytes,4,opt,name=preVersion,proto3" json:"preVersion,omitempty"`
	JdstackVersion  string `protobuf:"bytes,5,opt,name=jdstackVersion,proto3" json:"jdstackVersion,omitempty"`
	Data            []byte `protobuf:"bytes,6,opt,name=data,proto3" json:"data,omitempty"`
	ForceOverride   bool   `protobuf:"varint,7,opt,name=forceOverride,proto3" json:"forceOverride,omitempty"`
}

func (x *CreateCvesselMetadataReq) Reset() {
	*x = CreateCvesselMetadataReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_version_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateCvesselMetadataReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateCvesselMetadataReq) ProtoMessage() {}

func (x *CreateCvesselMetadataReq) ProtoReflect() protoreflect.Message {
	mi := &file_version_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateCvesselMetadataReq.ProtoReflect.Descriptor instead.
func (*CreateCvesselMetadataReq) Descriptor() ([]byte, []int) {
	return file_version_proto_rawDescGZIP(), []int{14}
}

func (x *CreateCvesselMetadataReq) GetVersion() string {
	if x != nil {
		return x.Version
	}
	return ""
}

func (x *CreateCvesselMetadataReq) GetVersionType() string {
	if x != nil {
		return x.VersionType
	}
	return ""
}

func (x *CreateCvesselMetadataReq) GetSnapshotVersion() string {
	if x != nil {
		return x.SnapshotVersion
	}
	return ""
}

func (x *CreateCvesselMetadataReq) GetPreVersion() string {
	if x != nil {
		return x.PreVersion
	}
	return ""
}

func (x *CreateCvesselMetadataReq) GetJdstackVersion() string {
	if x != nil {
		return x.JdstackVersion
	}
	return ""
}

func (x *CreateCvesselMetadataReq) GetData() []byte {
	if x != nil {
		return x.Data
	}
	return nil
}

func (x *CreateCvesselMetadataReq) GetForceOverride() bool {
	if x != nil {
		return x.ForceOverride
	}
	return false
}

type ModifyCvesselMetadataReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Version         string `protobuf:"bytes,1,opt,name=version,proto3" json:"version,omitempty"`
	VersionType     string `protobuf:"bytes,2,opt,name=versionType,proto3" json:"versionType,omitempty"`
	SnapshotVersion string `protobuf:"bytes,3,opt,name=snapshotVersion,proto3" json:"snapshotVersion,omitempty"`
	PreVersion      string `protobuf:"bytes,4,opt,name=preVersion,proto3" json:"preVersion,omitempty"`
	JdstackVersion  string `protobuf:"bytes,5,opt,name=jdstackVersion,proto3" json:"jdstackVersion,omitempty"`
	Data            []byte `protobuf:"bytes,6,opt,name=data,proto3" json:"data,omitempty"`
}

func (x *ModifyCvesselMetadataReq) Reset() {
	*x = ModifyCvesselMetadataReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_version_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ModifyCvesselMetadataReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ModifyCvesselMetadataReq) ProtoMessage() {}

func (x *ModifyCvesselMetadataReq) ProtoReflect() protoreflect.Message {
	mi := &file_version_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ModifyCvesselMetadataReq.ProtoReflect.Descriptor instead.
func (*ModifyCvesselMetadataReq) Descriptor() ([]byte, []int) {
	return file_version_proto_rawDescGZIP(), []int{15}
}

func (x *ModifyCvesselMetadataReq) GetVersion() string {
	if x != nil {
		return x.Version
	}
	return ""
}

func (x *ModifyCvesselMetadataReq) GetVersionType() string {
	if x != nil {
		return x.VersionType
	}
	return ""
}

func (x *ModifyCvesselMetadataReq) GetSnapshotVersion() string {
	if x != nil {
		return x.SnapshotVersion
	}
	return ""
}

func (x *ModifyCvesselMetadataReq) GetPreVersion() string {
	if x != nil {
		return x.PreVersion
	}
	return ""
}

func (x *ModifyCvesselMetadataReq) GetJdstackVersion() string {
	if x != nil {
		return x.JdstackVersion
	}
	return ""
}

func (x *ModifyCvesselMetadataReq) GetData() []byte {
	if x != nil {
		return x.Data
	}
	return nil
}

type DeleteCvesselMetadataReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Version        string `protobuf:"bytes,1,opt,name=version,proto3" json:"version,omitempty"`
	VersionType    string `protobuf:"bytes,2,opt,name=versionType,proto3" json:"versionType,omitempty"`
	JdstackVersion string `protobuf:"bytes,4,opt,name=jdstackVersion,proto3" json:"jdstackVersion,omitempty"`
}

func (x *DeleteCvesselMetadataReq) Reset() {
	*x = DeleteCvesselMetadataReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_version_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteCvesselMetadataReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteCvesselMetadataReq) ProtoMessage() {}

func (x *DeleteCvesselMetadataReq) ProtoReflect() protoreflect.Message {
	mi := &file_version_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteCvesselMetadataReq.ProtoReflect.Descriptor instead.
func (*DeleteCvesselMetadataReq) Descriptor() ([]byte, []int) {
	return file_version_proto_rawDescGZIP(), []int{16}
}

func (x *DeleteCvesselMetadataReq) GetVersion() string {
	if x != nil {
		return x.Version
	}
	return ""
}

func (x *DeleteCvesselMetadataReq) GetVersionType() string {
	if x != nil {
		return x.VersionType
	}
	return ""
}

func (x *DeleteCvesselMetadataReq) GetJdstackVersion() string {
	if x != nil {
		return x.JdstackVersion
	}
	return ""
}

type CreateCvesselMetadataReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id     string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Status string `protobuf:"bytes,2,opt,name=status,proto3" json:"status,omitempty"`
}

func (x *CreateCvesselMetadataReply) Reset() {
	*x = CreateCvesselMetadataReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_version_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateCvesselMetadataReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateCvesselMetadataReply) ProtoMessage() {}

func (x *CreateCvesselMetadataReply) ProtoReflect() protoreflect.Message {
	mi := &file_version_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateCvesselMetadataReply.ProtoReflect.Descriptor instead.
func (*CreateCvesselMetadataReply) Descriptor() ([]byte, []int) {
	return file_version_proto_rawDescGZIP(), []int{17}
}

func (x *CreateCvesselMetadataReply) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *CreateCvesselMetadataReply) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

type ModifyCvesselMetadataReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id     string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Status string `protobuf:"bytes,2,opt,name=status,proto3" json:"status,omitempty"`
}

func (x *ModifyCvesselMetadataReply) Reset() {
	*x = ModifyCvesselMetadataReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_version_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ModifyCvesselMetadataReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ModifyCvesselMetadataReply) ProtoMessage() {}

func (x *ModifyCvesselMetadataReply) ProtoReflect() protoreflect.Message {
	mi := &file_version_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ModifyCvesselMetadataReply.ProtoReflect.Descriptor instead.
func (*ModifyCvesselMetadataReply) Descriptor() ([]byte, []int) {
	return file_version_proto_rawDescGZIP(), []int{18}
}

func (x *ModifyCvesselMetadataReply) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *ModifyCvesselMetadataReply) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

type DeleteCvesselMetadataReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id     string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Status string `protobuf:"bytes,2,opt,name=status,proto3" json:"status,omitempty"`
}

func (x *DeleteCvesselMetadataReply) Reset() {
	*x = DeleteCvesselMetadataReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_version_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteCvesselMetadataReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteCvesselMetadataReply) ProtoMessage() {}

func (x *DeleteCvesselMetadataReply) ProtoReflect() protoreflect.Message {
	mi := &file_version_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteCvesselMetadataReply.ProtoReflect.Descriptor instead.
func (*DeleteCvesselMetadataReply) Descriptor() ([]byte, []int) {
	return file_version_proto_rawDescGZIP(), []int{19}
}

func (x *DeleteCvesselMetadataReply) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *DeleteCvesselMetadataReply) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

type DescribeReleaseConfigFileReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Version   string `protobuf:"bytes,1,opt,name=version,proto3" json:"version,omitempty"`
	CloudType string `protobuf:"bytes,2,opt,name=cloudType,proto3" json:"cloudType,omitempty"`
	FileName  string `protobuf:"bytes,3,opt,name=fileName,proto3" json:"fileName,omitempty"`
}

func (x *DescribeReleaseConfigFileReq) Reset() {
	*x = DescribeReleaseConfigFileReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_version_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DescribeReleaseConfigFileReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DescribeReleaseConfigFileReq) ProtoMessage() {}

func (x *DescribeReleaseConfigFileReq) ProtoReflect() protoreflect.Message {
	mi := &file_version_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DescribeReleaseConfigFileReq.ProtoReflect.Descriptor instead.
func (*DescribeReleaseConfigFileReq) Descriptor() ([]byte, []int) {
	return file_version_proto_rawDescGZIP(), []int{20}
}

func (x *DescribeReleaseConfigFileReq) GetVersion() string {
	if x != nil {
		return x.Version
	}
	return ""
}

func (x *DescribeReleaseConfigFileReq) GetCloudType() string {
	if x != nil {
		return x.CloudType
	}
	return ""
}

func (x *DescribeReleaseConfigFileReq) GetFileName() string {
	if x != nil {
		return x.FileName
	}
	return ""
}

type DescribeReleaseConfigFileReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	FileName     string `protobuf:"bytes,1,opt,name=fileName,proto3" json:"fileName,omitempty"`
	RelativePath string `protobuf:"bytes,2,opt,name=relativePath,proto3" json:"relativePath,omitempty"`
	Content      string `protobuf:"bytes,3,opt,name=content,proto3" json:"content,omitempty"`
}

func (x *DescribeReleaseConfigFileReply) Reset() {
	*x = DescribeReleaseConfigFileReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_version_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DescribeReleaseConfigFileReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DescribeReleaseConfigFileReply) ProtoMessage() {}

func (x *DescribeReleaseConfigFileReply) ProtoReflect() protoreflect.Message {
	mi := &file_version_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DescribeReleaseConfigFileReply.ProtoReflect.Descriptor instead.
func (*DescribeReleaseConfigFileReply) Descriptor() ([]byte, []int) {
	return file_version_proto_rawDescGZIP(), []int{21}
}

func (x *DescribeReleaseConfigFileReply) GetFileName() string {
	if x != nil {
		return x.FileName
	}
	return ""
}

func (x *DescribeReleaseConfigFileReply) GetRelativePath() string {
	if x != nil {
		return x.RelativePath
	}
	return ""
}

func (x *DescribeReleaseConfigFileReply) GetContent() string {
	if x != nil {
		return x.Content
	}
	return ""
}

type DescribeReleaseConfigFilesReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	List       []*DescribeReleaseConfigFileReply `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
	TotalCount int64                             `protobuf:"varint,2,opt,name=totalCount,proto3" json:"totalCount,omitempty"`
}

func (x *DescribeReleaseConfigFilesReply) Reset() {
	*x = DescribeReleaseConfigFilesReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_version_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DescribeReleaseConfigFilesReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DescribeReleaseConfigFilesReply) ProtoMessage() {}

func (x *DescribeReleaseConfigFilesReply) ProtoReflect() protoreflect.Message {
	mi := &file_version_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DescribeReleaseConfigFilesReply.ProtoReflect.Descriptor instead.
func (*DescribeReleaseConfigFilesReply) Descriptor() ([]byte, []int) {
	return file_version_proto_rawDescGZIP(), []int{22}
}

func (x *DescribeReleaseConfigFilesReply) GetList() []*DescribeReleaseConfigFileReply {
	if x != nil {
		return x.List
	}
	return nil
}

func (x *DescribeReleaseConfigFilesReply) GetTotalCount() int64 {
	if x != nil {
		return x.TotalCount
	}
	return 0
}

// ModifyDependencies related messages
type ModifyDependenciesReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Version  string         `protobuf:"bytes,1,opt,name=version,proto3" json:"version,omitempty"`
	Products []*ProductInfo `protobuf:"bytes,3,rep,name=products,proto3" json:"products,omitempty"`
}

func (x *ModifyDependenciesReq) Reset() {
	*x = ModifyDependenciesReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_version_proto_msgTypes[23]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ModifyDependenciesReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ModifyDependenciesReq) ProtoMessage() {}

func (x *ModifyDependenciesReq) ProtoReflect() protoreflect.Message {
	mi := &file_version_proto_msgTypes[23]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ModifyDependenciesReq.ProtoReflect.Descriptor instead.
func (*ModifyDependenciesReq) Descriptor() ([]byte, []int) {
	return file_version_proto_rawDescGZIP(), []int{23}
}

func (x *ModifyDependenciesReq) GetVersion() string {
	if x != nil {
		return x.Version
	}
	return ""
}

func (x *ModifyDependenciesReq) GetProducts() []*ProductInfo {
	if x != nil {
		return x.Products
	}
	return nil
}

type ProductInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id           string   `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Dependencies []string `protobuf:"bytes,2,rep,name=dependencies,proto3" json:"dependencies,omitempty"`
	Required     bool     `protobuf:"varint,3,opt,name=required,proto3" json:"required,omitempty"`
}

func (x *ProductInfo) Reset() {
	*x = ProductInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_version_proto_msgTypes[24]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ProductInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProductInfo) ProtoMessage() {}

func (x *ProductInfo) ProtoReflect() protoreflect.Message {
	mi := &file_version_proto_msgTypes[24]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProductInfo.ProtoReflect.Descriptor instead.
func (*ProductInfo) Descriptor() ([]byte, []int) {
	return file_version_proto_rawDescGZIP(), []int{24}
}

func (x *ProductInfo) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *ProductInfo) GetDependencies() []string {
	if x != nil {
		return x.Dependencies
	}
	return nil
}

func (x *ProductInfo) GetRequired() bool {
	if x != nil {
		return x.Required
	}
	return false
}

type ModifyDependenciesReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status  string `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
}

func (x *ModifyDependenciesReply) Reset() {
	*x = ModifyDependenciesReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_version_proto_msgTypes[25]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ModifyDependenciesReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ModifyDependenciesReply) ProtoMessage() {}

func (x *ModifyDependenciesReply) ProtoReflect() protoreflect.Message {
	mi := &file_version_proto_msgTypes[25]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ModifyDependenciesReply.ProtoReflect.Descriptor instead.
func (*ModifyDependenciesReply) Descriptor() ([]byte, []int) {
	return file_version_proto_rawDescGZIP(), []int{25}
}

func (x *ModifyDependenciesReply) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

func (x *ModifyDependenciesReply) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

// CreateSqlExportToS3 相关消息
type CreateSqlExportToS3Req struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Version            string `protobuf:"bytes,1,opt,name=version,proto3" json:"version,omitempty"`
	IncludePrevVersion bool   `protobuf:"varint,2,opt,name=includePrevVersion,proto3" json:"includePrevVersion,omitempty"`
}

func (x *CreateSqlExportToS3Req) Reset() {
	*x = CreateSqlExportToS3Req{}
	if protoimpl.UnsafeEnabled {
		mi := &file_version_proto_msgTypes[26]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateSqlExportToS3Req) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateSqlExportToS3Req) ProtoMessage() {}

func (x *CreateSqlExportToS3Req) ProtoReflect() protoreflect.Message {
	mi := &file_version_proto_msgTypes[26]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateSqlExportToS3Req.ProtoReflect.Descriptor instead.
func (*CreateSqlExportToS3Req) Descriptor() ([]byte, []int) {
	return file_version_proto_rawDescGZIP(), []int{26}
}

func (x *CreateSqlExportToS3Req) GetVersion() string {
	if x != nil {
		return x.Version
	}
	return ""
}

func (x *CreateSqlExportToS3Req) GetIncludePrevVersion() bool {
	if x != nil {
		return x.IncludePrevVersion
	}
	return false
}

type CreateSqlExportToS3Reply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	S3Bucket    string `protobuf:"bytes,1,opt,name=s3Bucket,proto3" json:"s3Bucket,omitempty"`
	S3ObjectKey string `protobuf:"bytes,2,opt,name=s3ObjectKey,proto3" json:"s3ObjectKey,omitempty"`
	Status      string `protobuf:"bytes,3,opt,name=status,proto3" json:"status,omitempty"`
	Message     string `protobuf:"bytes,4,opt,name=message,proto3" json:"message,omitempty"`
}

func (x *CreateSqlExportToS3Reply) Reset() {
	*x = CreateSqlExportToS3Reply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_version_proto_msgTypes[27]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateSqlExportToS3Reply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateSqlExportToS3Reply) ProtoMessage() {}

func (x *CreateSqlExportToS3Reply) ProtoReflect() protoreflect.Message {
	mi := &file_version_proto_msgTypes[27]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateSqlExportToS3Reply.ProtoReflect.Descriptor instead.
func (*CreateSqlExportToS3Reply) Descriptor() ([]byte, []int) {
	return file_version_proto_rawDescGZIP(), []int{27}
}

func (x *CreateSqlExportToS3Reply) GetS3Bucket() string {
	if x != nil {
		return x.S3Bucket
	}
	return ""
}

func (x *CreateSqlExportToS3Reply) GetS3ObjectKey() string {
	if x != nil {
		return x.S3ObjectKey
	}
	return ""
}

func (x *CreateSqlExportToS3Reply) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

func (x *CreateSqlExportToS3Reply) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

var File_version_proto protoreflect.FileDescriptor

var file_version_proto_rawDesc = []byte{
	0x0a, 0x0d, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12,
	0x17, 0x6a, 0x64, 0x73, 0x74, 0x61, 0x63, 0x6b, 0x2e, 0x7a, 0x65, 0x75, 0x73, 0x2e, 0x76, 0x31,
	0x2e, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x1a, 0x1c, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2f, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1b, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61,
	0x70, 0x69, 0x2f, 0x76, 0x69, 0x73, 0x69, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x1a, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x2d, 0x67, 0x65, 0x6e, 0x2d,
	0x6f, 0x70, 0x65, 0x6e, 0x61, 0x70, 0x69, 0x76, 0x32, 0x2f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x73, 0x2f, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x76, 0x61,
	0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1b, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x65, 0x6d,
	0x70, 0x74, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x13, 0x77, 0x69, 0x6e, 0x61, 0x70,
	0x69, 0x2f, 0x77, 0x69, 0x6e, 0x61, 0x70, 0x69, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x13,
	0x74, 0x61, 0x67, 0x67, 0x65, 0x72, 0x2f, 0x74, 0x61, 0x67, 0x67, 0x65, 0x72, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x22, 0x96, 0x03, 0x0a, 0x09, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x52, 0x65,
	0x71, 0x12, 0x50, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x42, 0x40, 0x92,
	0x41, 0x1e, 0x32, 0x1c, 0x4a, 0x44, 0x53, 0x74, 0x61, 0x63, 0x6b, 0xe7, 0x89, 0x88, 0xe6, 0x9c,
	0xac, 0x49, 0x44, 0x2c, 0xe4, 0xbb, 0x85, 0xe4, 0xbe, 0x9b, 0xe5, 0xaf, 0xbc, 0xe5, 0x85, 0xa5,
	0x9a, 0x84, 0x9e, 0x03, 0x0a, 0x65, 0x78, 0x63, 0x65, 0x6c, 0x3a, 0x22, 0x49, 0x44, 0x22, 0xfa,
	0xd2, 0xe4, 0x93, 0x02, 0x0a, 0x12, 0x08, 0x49, 0x4e, 0x54, 0x45, 0x52, 0x4e, 0x41, 0x4c, 0x52,
	0x02, 0x69, 0x64, 0x12, 0x6e, 0x0a, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x42, 0x54, 0x92, 0x41, 0x0f, 0x32, 0x0d, 0x4a, 0x44, 0x53, 0x74, 0x61,
	0x63, 0x6b, 0xe7, 0x89, 0x88, 0xe6, 0x9c, 0xac, 0xfa, 0x42, 0x15, 0x72, 0x13, 0x20, 0x01, 0x32,
	0x0f, 0x5e, 0x5b, 0x30, 0x2d, 0x39, 0x61, 0x2d, 0x7a, 0x41, 0x2d, 0x5a, 0x2e, 0x5d, 0x2b, 0x24,
	0x9a, 0x84, 0x9e, 0x03, 0x25, 0x65, 0x78, 0x63, 0x65, 0x6c, 0x3a, 0x22, 0x4a, 0x44, 0x53, 0x74,
	0x61, 0x63, 0x6b, 0xe7, 0x89, 0x88, 0xe6, 0x9c, 0xac, 0x22, 0x20, 0x72, 0x65, 0x71, 0x75, 0x69,
	0x72, 0x65, 0x64, 0x3a, 0x22, 0x74, 0x72, 0x75, 0x65, 0x22, 0x52, 0x07, 0x76, 0x65, 0x72, 0x73,
	0x69, 0x6f, 0x6e, 0x12, 0x40, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x42, 0x1e, 0x92, 0x41, 0x08, 0x32, 0x06, 0xe6,
	0x8f, 0x8f, 0xe8, 0xbf, 0xb0, 0x9a, 0x84, 0x9e, 0x03, 0x0e, 0x65, 0x78, 0x63, 0x65, 0x6c, 0x3a,
	0x22, 0xe6, 0x8f, 0x8f, 0xe8, 0xbf, 0xb0, 0x22, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x65, 0x0a, 0x0c, 0x70, 0x72, 0x65, 0x76, 0x50, 0x6c, 0x61,
	0x74, 0x66, 0x6f, 0x72, 0x6d, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x42, 0x41, 0x92, 0x41, 0x0e,
	0x32, 0x0c, 0xe5, 0x89, 0x8d, 0xe5, 0xba, 0x8f, 0xe7, 0x89, 0x88, 0xe6, 0x9c, 0xac, 0xfa, 0x42,
	0x04, 0x72, 0x02, 0x20, 0x01, 0x9a, 0x84, 0x9e, 0x03, 0x24, 0x65, 0x78, 0x63, 0x65, 0x6c, 0x3a,
	0x22, 0xe5, 0x89, 0x8d, 0xe5, 0xba, 0x8f, 0xe7, 0x89, 0x88, 0xe6, 0x9c, 0xac, 0x22, 0x20, 0x72,
	0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x3a, 0x22, 0x74, 0x72, 0x75, 0x65, 0x22, 0x52, 0x0c,
	0x70, 0x72, 0x65, 0x76, 0x50, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x3a, 0x1e, 0x92, 0x41,
	0x1b, 0x0a, 0x19, 0xd2, 0x01, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0xd2, 0x01, 0x0c,
	0x70, 0x72, 0x65, 0x76, 0x50, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x22, 0x8a, 0x03, 0x0a,
	0x09, 0x4d, 0x6f, 0x64, 0x69, 0x66, 0x79, 0x52, 0x65, 0x71, 0x12, 0x56, 0x0a, 0x02, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x42, 0x46, 0x92, 0x41, 0x3a, 0x32, 0x38, 0x4a, 0x44, 0x53,
	0x74, 0x61, 0x63, 0x6b, 0xe7, 0x89, 0x88, 0xe6, 0x9c, 0xac, 0x49, 0x44, 0x0a, 0x2d, 0x20, 0xe4,
	0xb8, 0xba, 0x30, 0xe6, 0x97, 0xb6, 0xe6, 0x8c, 0x89, 0xe9, 0xa1, 0xba, 0xe5, 0xba, 0x8f, 0xe6,
	0x9f, 0xa5, 0xe8, 0xaf, 0xa2, 0xe5, 0xad, 0x97, 0xe6, 0xae, 0xb5, 0xef, 0xbc, 0x9a, 0x76, 0x65,
	0x72, 0x73, 0x69, 0x6f, 0x6e, 0xfa, 0x42, 0x06, 0x1a, 0x04, 0x28, 0x00, 0x40, 0x01, 0x52, 0x02,
	0x69, 0x64, 0x12, 0x4c, 0x0a, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x42, 0x32, 0x92, 0x41, 0x16, 0x32, 0x14, 0x4a, 0x44, 0x53, 0x74, 0x61, 0x63,
	0x6b, 0xe7, 0x89, 0x88, 0xe6, 0x9c, 0xac, 0x2c, 0xe5, 0x8f, 0xaa, 0xe8, 0xaf, 0xbb, 0xfa, 0x42,
	0x16, 0x72, 0x14, 0x32, 0x0f, 0x5e, 0x5b, 0x30, 0x2d, 0x39, 0x61, 0x2d, 0x7a, 0x41, 0x2d, 0x5a,
	0x2e, 0x5d, 0x2b, 0x24, 0xd0, 0x01, 0x01, 0x52, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e,
	0x12, 0x60, 0x0a, 0x0b, 0x63, 0x6c, 0x65, 0x61, 0x72, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x73, 0x18,
	0x03, 0x20, 0x03, 0x28, 0x09, 0x42, 0x3e, 0x92, 0x41, 0x24, 0x32, 0x22, 0xe6, 0xb8, 0x85, 0xe7,
	0xa9, 0xba, 0xe5, 0xad, 0x97, 0xe6, 0xae, 0xb5, 0x3a, 0x0a, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x20, 0x2d, 0x20, 0xe6, 0x8f, 0x8f, 0xe8, 0xbf, 0xb0, 0xfa, 0x42,
	0x14, 0x92, 0x01, 0x11, 0x22, 0x0f, 0x72, 0x0d, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x0b, 0x63, 0x6c, 0x65, 0x61, 0x72, 0x46, 0x69, 0x65, 0x6c,
	0x64, 0x73, 0x12, 0x2d, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0b, 0x92, 0x41, 0x08, 0x32, 0x06, 0xe6, 0x8f,
	0x8f, 0xe8, 0xbf, 0xb0, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x12, 0x46, 0x0a, 0x06, 0x6c, 0x6f, 0x63, 0x6b, 0x65, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x09, 0x42, 0x2e, 0x92, 0x41, 0x1a, 0x32, 0x18, 0xe6, 0x98, 0xaf, 0xe5, 0x90, 0xa6, 0xe9, 0x94,
	0x81, 0xe5, 0xae, 0x9a, 0x3a, 0x0a, 0x5b, 0x20, 0x6e, 0x6f, 0x20, 0x79, 0x65, 0x73, 0x20, 0x5d,
	0xfa, 0x42, 0x0e, 0x72, 0x0c, 0x52, 0x02, 0x6e, 0x6f, 0x52, 0x03, 0x79, 0x65, 0x73, 0xd0, 0x01,
	0x01, 0x52, 0x06, 0x6c, 0x6f, 0x63, 0x6b, 0x65, 0x64, 0x22, 0xd6, 0x01, 0x0a, 0x0b, 0x44, 0x65,
	0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x52, 0x65, 0x71, 0x12, 0x56, 0x0a, 0x02, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x05, 0x42, 0x46, 0x92, 0x41, 0x3a, 0x32, 0x38, 0x4a, 0x44, 0x53, 0x74,
	0x61, 0x63, 0x6b, 0xe7, 0x89, 0x88, 0xe6, 0x9c, 0xac, 0x49, 0x44, 0x0a, 0x2d, 0x20, 0xe4, 0xb8,
	0xba, 0x30, 0xe6, 0x97, 0xb6, 0xe6, 0x8c, 0x89, 0xe9, 0xa1, 0xba, 0xe5, 0xba, 0x8f, 0xe6, 0x9f,
	0xa5, 0xe8, 0xaf, 0xa2, 0xe5, 0xad, 0x97, 0xe6, 0xae, 0xb5, 0xef, 0xbc, 0x9a, 0x76, 0x65, 0x72,
	0x73, 0x69, 0x6f, 0x6e, 0xfa, 0x42, 0x06, 0x1a, 0x04, 0x28, 0x00, 0x40, 0x01, 0x52, 0x02, 0x69,
	0x64, 0x12, 0x6f, 0x0a, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x42, 0x55, 0x92, 0x41, 0x0f, 0x32, 0x0d, 0x4a, 0x44, 0x53, 0x74, 0x61, 0x63, 0x6b,
	0xe7, 0x89, 0x88, 0xe6, 0x9c, 0xac, 0xfa, 0x42, 0x16, 0x72, 0x14, 0x32, 0x0f, 0x5e, 0x5b, 0x30,
	0x2d, 0x39, 0x61, 0x2d, 0x7a, 0x41, 0x2d, 0x5a, 0x2e, 0x5d, 0x2b, 0x24, 0xd0, 0x01, 0x01, 0x9a,
	0x84, 0x9e, 0x03, 0x25, 0x65, 0x78, 0x63, 0x65, 0x6c, 0x3a, 0x22, 0x4a, 0x44, 0x53, 0x74, 0x61,
	0x63, 0x6b, 0xe7, 0x89, 0x88, 0xe6, 0x9c, 0xac, 0x22, 0x20, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72,
	0x65, 0x64, 0x3a, 0x22, 0x74, 0x72, 0x75, 0x65, 0x22, 0x52, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69,
	0x6f, 0x6e, 0x22, 0xeb, 0x04, 0x0a, 0x0d, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x52,
	0x65, 0x70, 0x6c, 0x79, 0x12, 0x3a, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05,
	0x42, 0x2a, 0x92, 0x41, 0x27, 0x32, 0x25, 0x4a, 0x44, 0x53, 0x74, 0x61, 0x63, 0x6b, 0xe7, 0x89,
	0x88, 0xe6, 0x9c, 0xac, 0x49, 0x44, 0x2c, 0xe7, 0x89, 0xb9, 0xe6, 0xae, 0x8a, 0xe6, 0x83, 0x85,
	0xe5, 0x86, 0xb5, 0xe4, 0xb8, 0x8b, 0xe4, 0xbd, 0xbf, 0xe7, 0x94, 0xa8, 0x52, 0x02, 0x69, 0x64,
	0x12, 0x2c, 0x0a, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x42, 0x12, 0x92, 0x41, 0x0f, 0x32, 0x0d, 0x4a, 0x44, 0x53, 0x74, 0x61, 0x63, 0x6b, 0xe7,
	0x89, 0x88, 0xe6, 0x9c, 0xac, 0x52, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x49,
	0x0a, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x42, 0x2b, 0x92, 0x41, 0x28, 0x32, 0x26, 0xe5, 0x88, 0x9b, 0xe5, 0xbb, 0xba, 0xe6, 0x97,
	0xb6, 0xe9, 0x97, 0xb4, 0x2c, 0x55, 0x54, 0x43, 0xe6, 0xa0, 0xbc, 0xe5, 0xbc, 0x8f, 0x28, 0xe6,
	0xaf, 0xab, 0xe7, 0xa7, 0x92, 0xe7, 0xba, 0xa7, 0x32, 0x34, 0xe4, 0xbd, 0x8d, 0x29, 0x52, 0x09,
	0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x2d, 0x0a, 0x0b, 0x64, 0x65, 0x73,
	0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0b,
	0x92, 0x41, 0x08, 0x32, 0x06, 0xe6, 0x8f, 0x8f, 0xe8, 0xbf, 0xb0, 0x52, 0x0b, 0x64, 0x65, 0x73,
	0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x47, 0x0a, 0x0c, 0x70, 0x72, 0x65, 0x76,
	0x50, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x42, 0x23,
	0x92, 0x41, 0x20, 0x32, 0x1e, 0xe5, 0x89, 0x8d, 0xe5, 0xba, 0x8f, 0xe7, 0x89, 0x88, 0xe6, 0x9c,
	0xac, 0x2c, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x5b, 0x57, 0x69, 0x74, 0x68, 0x3a, 0x50, 0x72,
	0x65, 0x76, 0x5d, 0x52, 0x0c, 0x70, 0x72, 0x65, 0x76, 0x50, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72,
	0x6d, 0x12, 0x47, 0x0a, 0x0c, 0x6e, 0x65, 0x78, 0x74, 0x50, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72,
	0x6d, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x42, 0x23, 0x92, 0x41, 0x20, 0x32, 0x1e, 0xe5, 0x90,
	0x8e, 0xe5, 0xba, 0x8f, 0xe7, 0x89, 0x88, 0xe6, 0x9c, 0xac, 0x2c, 0x66, 0x69, 0x6c, 0x74, 0x65,
	0x72, 0x5b, 0x57, 0x69, 0x74, 0x68, 0x3a, 0x4e, 0x65, 0x78, 0x74, 0x5d, 0x52, 0x0c, 0x6e, 0x65,
	0x78, 0x74, 0x50, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x12, 0x35, 0x0a, 0x06, 0x6c, 0x6f,
	0x63, 0x6b, 0x65, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x42, 0x1d, 0x92, 0x41, 0x1a, 0x32,
	0x18, 0xe6, 0x98, 0xaf, 0xe5, 0x90, 0xa6, 0xe9, 0x94, 0x81, 0xe5, 0xae, 0x9a, 0x3a, 0x0a, 0x5b,
	0x20, 0x6e, 0x6f, 0x20, 0x79, 0x65, 0x73, 0x20, 0x5d, 0x52, 0x06, 0x6c, 0x6f, 0x63, 0x6b, 0x65,
	0x64, 0x12, 0x44, 0x0a, 0x09, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x54, 0x79, 0x70, 0x65, 0x18, 0x08,
	0x20, 0x01, 0x28, 0x09, 0x42, 0x26, 0x92, 0x41, 0x23, 0x32, 0x21, 0xe7, 0x89, 0x88, 0xe6, 0x9c,
	0xac, 0xe7, 0xb1, 0xbb, 0xe5, 0x9e, 0x8b, 0x3a, 0x0a, 0x5b, 0x20, 0x6a, 0x64, 0x73, 0x74, 0x61,
	0x63, 0x6b, 0x20, 0x63, 0x76, 0x65, 0x73, 0x73, 0x65, 0x6c, 0x20, 0x5d, 0x52, 0x09, 0x63, 0x6c,
	0x6f, 0x75, 0x64, 0x54, 0x79, 0x70, 0x65, 0x12, 0x67, 0x0a, 0x0f, 0x72, 0x65, 0x6c, 0x61, 0x74,
	0x65, 0x64, 0x50, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09,
	0x42, 0x3d, 0x92, 0x41, 0x3a, 0x32, 0x38, 0xe5, 0x85, 0xb3, 0xe8, 0x81, 0x94, 0xe7, 0x89, 0x88,
	0xe6, 0x9c, 0xac, 0x2c, 0xe7, 0x94, 0xa8, 0xe4, 0xba, 0x8e, 0xe6, 0x8f, 0x8f, 0xe8, 0xbf, 0xb0,
	0x4a, 0x44, 0x53, 0x74, 0x61, 0x63, 0x6b, 0xe5, 0x85, 0xb3, 0xe8, 0x81, 0x94, 0xe7, 0x9a, 0x84,
	0xe4, 0xba, 0x91, 0xe8, 0x88, 0xb0, 0xe7, 0x89, 0x88, 0xe6, 0x9c, 0xac, 0xe5, 0x8f, 0xb7, 0x52,
	0x0f, 0x72, 0x65, 0x6c, 0x61, 0x74, 0x65, 0x64, 0x50, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d,
	0x22, 0xb9, 0x05, 0x0a, 0x0c, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x73, 0x52, 0x65,
	0x71, 0x12, 0x7a, 0x0a, 0x0a, 0x70, 0x61, 0x67, 0x65, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x5a, 0x92, 0x41, 0x42, 0x32, 0x3d, 0xe9, 0xa1, 0xb5, 0xe7,
	0xa0, 0x81, 0x3a, 0x0a, 0x2d, 0x31, 0x20, 0x2d, 0x20, 0xe5, 0x85, 0xa8, 0xe9, 0x83, 0xa8, 0x0a,
	0x30, 0x20, 0x2d, 0x20, 0xe7, 0xac, 0xac, 0x31, 0xe9, 0xa1, 0xb5, 0x0a, 0x31, 0x20, 0x2d, 0x20,
	0xe7, 0xac, 0xac, 0x32, 0xe9, 0xa1, 0xb5, 0x0a, 0x2d, 0x20, 0xe4, 0xbb, 0xa5, 0xe6, 0xad, 0xa4,
	0xe7, 0xb1, 0xbb, 0xe6, 0x8e, 0xa8, 0x2e, 0x2e, 0x2e, 0x3a, 0x01, 0x30, 0xfa, 0x42, 0x12, 0x22,
	0x10, 0x18, 0x80, 0x80, 0x80, 0x08, 0x28, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0x01, 0x52, 0x0a, 0x70, 0x61, 0x67, 0x65, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x28, 0x0a,
	0x08, 0x70, 0x61, 0x67, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x42,
	0x0c, 0xfa, 0x42, 0x09, 0x22, 0x07, 0x18, 0x80, 0x80, 0x80, 0x08, 0x28, 0x01, 0x52, 0x08, 0x70,
	0x61, 0x67, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x12, 0xbb, 0x02, 0x0a, 0x07, 0x66, 0x69, 0x6c, 0x74,
	0x65, 0x72, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x77, 0x69, 0x6e, 0x67,
	0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x42, 0x8d, 0x02, 0x92,
	0x41, 0x89, 0x02, 0x32, 0x86, 0x02, 0xe8, 0xbf, 0x94, 0xe5, 0x9b, 0x9e, 0xe5, 0xad, 0x97, 0xe6,
	0xae, 0xb5, 0xe7, 0x9a, 0x86, 0xe6, 0x94, 0xaf, 0xe6, 0x8c, 0x81, 0xe7, 0xad, 0x9b, 0xe9, 0x80,
	0x89, 0xef, 0xbc, 0x8c, 0xe7, 0xad, 0x9b, 0xe9, 0x80, 0x89, 0xe6, 0x89, 0xa9, 0xe5, 0xb1, 0x95,
	0x3a, 0x0a, 0x57, 0x69, 0x74, 0x68, 0x3a, 0x7b, 0xe6, 0xa8, 0xa1, 0xe5, 0x9d, 0x97, 0xe5, 0x90,
	0x8d, 0x7d, 0x20, 0x2d, 0x20, 0xe8, 0xbf, 0x94, 0xe5, 0x9b, 0x9e, 0xe5, 0x85, 0xb3, 0xe8, 0x81,
	0x94, 0xe8, 0xa1, 0xa8, 0x28, 0xe6, 0xad, 0xa4, 0xe7, 0xb1, 0xbb, 0xe8, 0xbf, 0x87, 0xe6, 0xbb,
	0xa4, 0xe9, 0xa1, 0xb9, 0xe6, 0x97, 0xa0, 0x76, 0x61, 0x6c, 0x75, 0x65, 0xef, 0xbc, 0x8c, 0xe6,
	0x89, 0x80, 0xe4, 0xbb, 0xa5, 0xe5, 0x8f, 0xaf, 0xe5, 0xb0, 0x86, 0xe5, 0x90, 0x8c, 0xe7, 0xb1,
	0xbb, 0x6e, 0x61, 0x6d, 0x65, 0xe5, 0x90, 0x88, 0xe5, 0xb9, 0xb6, 0x6e, 0x61, 0x6d, 0x65, 0x3d,
	0x2e, 0xe7, 0x9a, 0x84, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x73, 0xe4, 0xb8, 0xad, 0x29, 0x0a, 0x57,
	0x69, 0x74, 0x68, 0x3a, 0x7b, 0xe6, 0xa8, 0xa1, 0xe5, 0x9d, 0x97, 0xe5, 0x90, 0x8d, 0x7d, 0x2e,
	0x7b, 0xe5, 0xad, 0x97, 0xe6, 0xae, 0xb5, 0xe5, 0x90, 0x8d, 0x7d, 0x20, 0x2d, 0x20, 0xe8, 0xbf,
	0x87, 0xe6, 0xbb, 0xa4, 0xe5, 0xb9, 0xb6, 0xe8, 0xbf, 0x94, 0xe5, 0x9b, 0x9e, 0xe5, 0x85, 0xb3,
	0xe8, 0x81, 0x94, 0xe8, 0xa1, 0xa8, 0x0a, 0x48, 0x61, 0x73, 0x3a, 0x7b, 0xe6, 0xa8, 0xa1, 0xe5,
	0x9d, 0x97, 0xe5, 0x90, 0x8d, 0x7d, 0x2e, 0x7b, 0xe5, 0xad, 0x97, 0xe6, 0xae, 0xb5, 0xe5, 0x90,
	0x8d, 0x7d, 0x20, 0x2d, 0x20, 0xe5, 0xad, 0x90, 0xe6, 0x9f, 0xa5, 0xe8, 0xaf, 0xa2, 0x28, 0xe7,
	0xad, 0x89, 0xe5, 0x80, 0xbc, 0xe8, 0xbf, 0x9e, 0xe6, 0x8e, 0xa5, 0x29, 0x52, 0x07, 0x66, 0x69,
	0x6c, 0x74, 0x65, 0x72, 0x73, 0x12, 0xa5, 0x01, 0x0a, 0x05, 0x73, 0x6f, 0x72, 0x74, 0x73, 0x18,
	0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x0f, 0x2e, 0x77, 0x69, 0x6e, 0x67, 0x6f, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x53, 0x6f, 0x72, 0x74, 0x42, 0x7e, 0x92, 0x41, 0x7b, 0x32, 0x79, 0xe8, 0xbf, 0x94,
	0xe5, 0x9b, 0x9e, 0xe5, 0xad, 0x97, 0xe6, 0xae, 0xb5, 0xe7, 0x9a, 0x86, 0xe6, 0x94, 0xaf, 0xe6,
	0x8c, 0x81, 0xe6, 0x8e, 0x92, 0xe5, 0xba, 0x8f, 0xef, 0xbc, 0x8c, 0xe6, 0x8e, 0x92, 0xe5, 0xba,
	0x8f, 0xe6, 0x89, 0xa9, 0xe5, 0xb1, 0x95, 0x3a, 0x0a, 0x57, 0x69, 0x74, 0x68, 0x3a, 0x7b, 0xe6,
	0xa8, 0xa1, 0xe5, 0x9d, 0x97, 0xe5, 0x90, 0x8d, 0x7d, 0x2e, 0x5b, 0x20, 0x4d, 0x69, 0x6e, 0x20,
	0x4d, 0x61, 0x78, 0x20, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x20, 0x53, 0x75, 0x6d, 0x20, 0x5d, 0x3a,
	0x7b, 0xe5, 0xad, 0x97, 0xe6, 0xae, 0xb5, 0xe5, 0x90, 0x8d, 0x7d, 0x20, 0x2d, 0x20, 0xe6, 0x8c,
	0x89, 0xe5, 0x85, 0xb3, 0xe8, 0x81, 0x94, 0xe8, 0xa1, 0xa8, 0xe5, 0xad, 0x97, 0xe6, 0xae, 0xb5,
	0xe6, 0x8e, 0x92, 0xe5, 0xba, 0x8f, 0x52, 0x05, 0x73, 0x6f, 0x72, 0x74, 0x73, 0x3a, 0x1d, 0x92,
	0x41, 0x1a, 0x0a, 0x18, 0xd2, 0x01, 0x0a, 0x70, 0x61, 0x67, 0x65, 0x4e, 0x75, 0x6d, 0x62, 0x65,
	0x72, 0xd2, 0x01, 0x08, 0x70, 0x61, 0x67, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x22, 0x6c, 0x0a, 0x0e,
	0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x73, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x3a,
	0x0a, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x26, 0x2e, 0x6a,
	0x64, 0x73, 0x74, 0x61, 0x63, 0x6b, 0x2e, 0x7a, 0x65, 0x75, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x76,
	0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x2e, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x52,
	0x65, 0x70, 0x6c, 0x79, 0x52, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x12, 0x1e, 0x0a, 0x0a, 0x74, 0x6f,
	0x74, 0x61, 0x6c, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a,
	0x74, 0x6f, 0x74, 0x61, 0x6c, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x22, 0x8c, 0x04, 0x0a, 0x10, 0x43,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x43, 0x76, 0x65, 0x73, 0x73, 0x65, 0x6c, 0x52, 0x65, 0x71, 0x12,
	0x4f, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x42, 0x3f, 0x92, 0x41, 0x1d,
	0x32, 0x1b, 0xe4, 0xba, 0x91, 0xe8, 0x88, 0xb0, 0xe7, 0x89, 0x88, 0xe6, 0x9c, 0xac, 0x49, 0x44,
	0x2c, 0xe4, 0xbb, 0x85, 0xe4, 0xbe, 0x9b, 0xe5, 0xaf, 0xbc, 0xe5, 0x85, 0xa5, 0x9a, 0x84, 0x9e,
	0x03, 0x0a, 0x65, 0x78, 0x63, 0x65, 0x6c, 0x3a, 0x22, 0x49, 0x44, 0x22, 0xfa, 0xd2, 0xe4, 0x93,
	0x02, 0x0a, 0x12, 0x08, 0x49, 0x4e, 0x54, 0x45, 0x52, 0x4e, 0x41, 0x4c, 0x52, 0x02, 0x69, 0x64,
	0x12, 0x5b, 0x0a, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x42, 0x41, 0x92, 0x41, 0x0e, 0x32, 0x0c, 0xe4, 0xba, 0x91, 0xe8, 0x88, 0xb0, 0xe7, 0x89,
	0x88, 0xe6, 0x9c, 0xac, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x20, 0x01, 0x9a, 0x84, 0x9e, 0x03, 0x24,
	0x65, 0x78, 0x63, 0x65, 0x6c, 0x3a, 0x22, 0xe4, 0xba, 0x91, 0xe8, 0x88, 0xb0, 0xe7, 0x89, 0x88,
	0xe6, 0x9c, 0xac, 0x22, 0x20, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x3a, 0x22, 0x74,
	0x72, 0x75, 0x65, 0x22, 0x52, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x7d, 0x0a,
	0x0e, 0x6a, 0x64, 0x73, 0x74, 0x61, 0x63, 0x6b, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x09, 0x42, 0x55, 0x92, 0x41, 0x0f, 0x32, 0x0d, 0x4a, 0x44, 0x53, 0x74,
	0x61, 0x63, 0x6b, 0xe7, 0x89, 0x88, 0xe6, 0x9c, 0xac, 0xfa, 0x42, 0x16, 0x72, 0x14, 0x32, 0x0f,
	0x5e, 0x5b, 0x30, 0x2d, 0x39, 0x61, 0x2d, 0x7a, 0x41, 0x2d, 0x5a, 0x2e, 0x5d, 0x2b, 0x24, 0xd0,
	0x01, 0x01, 0x9a, 0x84, 0x9e, 0x03, 0x25, 0x65, 0x78, 0x63, 0x65, 0x6c, 0x3a, 0x22, 0x4a, 0x44,
	0x53, 0x74, 0x61, 0x63, 0x6b, 0xe7, 0x89, 0x88, 0xe6, 0x9c, 0xac, 0x22, 0x20, 0x72, 0x65, 0x71,
	0x75, 0x69, 0x72, 0x65, 0x64, 0x3a, 0x22, 0x74, 0x72, 0x75, 0x65, 0x22, 0x52, 0x0e, 0x6a, 0x64,
	0x73, 0x74, 0x61, 0x63, 0x6b, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x40, 0x0a, 0x0b,
	0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x09, 0x42, 0x1e, 0x92, 0x41, 0x08, 0x32, 0x06, 0xe6, 0x8f, 0x8f, 0xe8, 0xbf, 0xb0, 0x9a, 0x84,
	0x9e, 0x03, 0x0e, 0x65, 0x78, 0x63, 0x65, 0x6c, 0x3a, 0x22, 0xe6, 0x8f, 0x8f, 0xe8, 0xbf, 0xb0,
	0x22, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x62,
	0x0a, 0x0a, 0x70, 0x72, 0x65, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x06, 0x20, 0x01,
	0x28, 0x09, 0x42, 0x42, 0x92, 0x41, 0x0e, 0x32, 0x0c, 0xe5, 0x89, 0x8d, 0xe5, 0xba, 0x8f, 0xe7,
	0x89, 0x88, 0xe6, 0x9c, 0xac, 0xfa, 0x42, 0x05, 0x72, 0x03, 0xd0, 0x01, 0x01, 0x9a, 0x84, 0x9e,
	0x03, 0x24, 0x65, 0x78, 0x63, 0x65, 0x6c, 0x3a, 0x22, 0xe5, 0x89, 0x8d, 0xe5, 0xba, 0x8f, 0xe7,
	0x89, 0x88, 0xe6, 0x9c, 0xac, 0x22, 0x20, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x3a,
	0x22, 0x74, 0x72, 0x75, 0x65, 0x22, 0x52, 0x0a, 0x70, 0x72, 0x65, 0x56, 0x65, 0x72, 0x73, 0x69,
	0x6f, 0x6e, 0x3a, 0x25, 0x92, 0x41, 0x22, 0x0a, 0x20, 0xd2, 0x01, 0x04, 0x6e, 0x61, 0x6d, 0x65,
	0xd2, 0x01, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0xd2, 0x01, 0x0c, 0x70, 0x72, 0x65,
	0x76, 0x50, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x22, 0xc7, 0x02, 0x0a, 0x10, 0x4d, 0x6f,
	0x64, 0x69, 0x66, 0x79, 0x43, 0x76, 0x65, 0x73, 0x73, 0x65, 0x6c, 0x52, 0x65, 0x71, 0x12, 0x55,
	0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x42, 0x45, 0x92, 0x41, 0x39, 0x32,
	0x37, 0xe4, 0xba, 0x91, 0xe8, 0x88, 0xb0, 0xe7, 0x89, 0x88, 0xe6, 0x9c, 0xac, 0x49, 0x44, 0x0a,
	0x2d, 0x20, 0xe4, 0xb8, 0xba, 0x30, 0xe6, 0x97, 0xb6, 0xe6, 0x8c, 0x89, 0xe9, 0xa1, 0xba, 0xe5,
	0xba, 0x8f, 0xe6, 0x9f, 0xa5, 0xe8, 0xaf, 0xa2, 0xe5, 0xad, 0x97, 0xe6, 0xae, 0xb5, 0xef, 0xbc,
	0x9a, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0xfa, 0x42, 0x06, 0x1a, 0x04, 0x28, 0x00, 0x40,
	0x01, 0x52, 0x02, 0x69, 0x64, 0x12, 0x4b, 0x0a, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x31, 0x92, 0x41, 0x15, 0x32, 0x13, 0xe4, 0xba, 0x91,
	0xe8, 0x88, 0xb0, 0xe7, 0x89, 0x88, 0xe6, 0x9c, 0xac, 0x2c, 0xe5, 0x8f, 0xaa, 0xe8, 0xaf, 0xbb,
	0xfa, 0x42, 0x16, 0x72, 0x14, 0x32, 0x0f, 0x5e, 0x5b, 0x30, 0x2d, 0x39, 0x61, 0x2d, 0x7a, 0x41,
	0x2d, 0x5a, 0x2e, 0x5d, 0x2b, 0x24, 0xd0, 0x01, 0x01, 0x52, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69,
	0x6f, 0x6e, 0x12, 0x60, 0x0a, 0x0b, 0x63, 0x6c, 0x65, 0x61, 0x72, 0x46, 0x69, 0x65, 0x6c, 0x64,
	0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x09, 0x42, 0x3e, 0x92, 0x41, 0x24, 0x32, 0x22, 0xe6, 0xb8,
	0x85, 0xe7, 0xa9, 0xba, 0xe5, 0xad, 0x97, 0xe6, 0xae, 0xb5, 0x3a, 0x0a, 0x64, 0x65, 0x73, 0x63,
	0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x20, 0x2d, 0x20, 0xe6, 0x8f, 0x8f, 0xe8, 0xbf, 0xb0,
	0xfa, 0x42, 0x14, 0x92, 0x01, 0x11, 0x22, 0x0f, 0x72, 0x0d, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63,
	0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x0b, 0x63, 0x6c, 0x65, 0x61, 0x72, 0x46, 0x69,
	0x65, 0x6c, 0x64, 0x73, 0x12, 0x2d, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0b, 0x92, 0x41, 0x08, 0x32, 0x06,
	0xe6, 0x8f, 0x8f, 0xe8, 0xbf, 0xb0, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x22, 0xda, 0x01, 0x0a, 0x12, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65,
	0x43, 0x76, 0x65, 0x73, 0x73, 0x65, 0x6c, 0x52, 0x65, 0x71, 0x12, 0x55, 0x0a, 0x02, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x42, 0x45, 0x92, 0x41, 0x39, 0x32, 0x37, 0xe4, 0xba, 0x91,
	0xe8, 0x88, 0xb0, 0xe7, 0x89, 0x88, 0xe6, 0x9c, 0xac, 0x49, 0x44, 0x0a, 0x2d, 0x20, 0xe4, 0xb8,
	0xba, 0x30, 0xe6, 0x97, 0xb6, 0xe6, 0x8c, 0x89, 0xe9, 0xa1, 0xba, 0xe5, 0xba, 0x8f, 0xe6, 0x9f,
	0xa5, 0xe8, 0xaf, 0xa2, 0xe5, 0xad, 0x97, 0xe6, 0xae, 0xb5, 0xef, 0xbc, 0x9a, 0x76, 0x65, 0x72,
	0x73, 0x69, 0x6f, 0x6e, 0xfa, 0x42, 0x06, 0x1a, 0x04, 0x28, 0x00, 0x40, 0x01, 0x52, 0x02, 0x69,
	0x64, 0x12, 0x6d, 0x0a, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x42, 0x53, 0x92, 0x41, 0x0e, 0x32, 0x0c, 0xe4, 0xba, 0x91, 0xe8, 0x88, 0xb0, 0xe7,
	0x89, 0x88, 0xe6, 0x9c, 0xac, 0xfa, 0x42, 0x16, 0x72, 0x14, 0x32, 0x0f, 0x5e, 0x5b, 0x30, 0x2d,
	0x39, 0x61, 0x2d, 0x7a, 0x41, 0x2d, 0x5a, 0x2e, 0x5d, 0x2b, 0x24, 0xd0, 0x01, 0x01, 0x9a, 0x84,
	0x9e, 0x03, 0x24, 0x65, 0x78, 0x63, 0x65, 0x6c, 0x3a, 0x22, 0xe4, 0xba, 0x91, 0xe8, 0x88, 0xb0,
	0xe7, 0x89, 0x88, 0xe6, 0x9c, 0xac, 0x22, 0x20, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64,
	0x3a, 0x22, 0x74, 0x72, 0x75, 0x65, 0x22, 0x52, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e,
	0x22, 0x90, 0x04, 0x0a, 0x14, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x43, 0x76, 0x65,
	0x73, 0x73, 0x65, 0x6c, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x39, 0x0a, 0x02, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x05, 0x42, 0x29, 0x92, 0x41, 0x26, 0x32, 0x24, 0xe4, 0xba, 0x91, 0xe8,
	0x88, 0xb0, 0xe7, 0x89, 0x88, 0xe6, 0x9c, 0xac, 0x49, 0x44, 0x2c, 0xe7, 0x89, 0xb9, 0xe6, 0xae,
	0x8a, 0xe6, 0x83, 0x85, 0xe5, 0x86, 0xb5, 0xe4, 0xb8, 0x8b, 0xe4, 0xbd, 0xbf, 0xe7, 0x94, 0xa8,
	0x52, 0x02, 0x69, 0x64, 0x12, 0x2b, 0x0a, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x32, 0x0c, 0xe4, 0xba, 0x91, 0xe8,
	0x88, 0xb0, 0xe7, 0x89, 0x88, 0xe6, 0x9c, 0xac, 0x52, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f,
	0x6e, 0x12, 0x49, 0x0a, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x42, 0x2b, 0x92, 0x41, 0x28, 0x32, 0x26, 0xe5, 0x88, 0x9b, 0xe5, 0xbb,
	0xba, 0xe6, 0x97, 0xb6, 0xe9, 0x97, 0xb4, 0x2c, 0x55, 0x54, 0x43, 0xe6, 0xa0, 0xbc, 0xe5, 0xbc,
	0x8f, 0x28, 0xe6, 0xaf, 0xab, 0xe7, 0xa7, 0x92, 0xe7, 0xba, 0xa7, 0x32, 0x34, 0xe4, 0xbd, 0x8d,
	0x29, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x87, 0x01, 0x0a,
	0x0e, 0x6a, 0x64, 0x73, 0x74, 0x61, 0x63, 0x6b, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x26, 0x2e, 0x6a, 0x64, 0x73, 0x74, 0x61, 0x63, 0x6b, 0x2e,
	0x7a, 0x65, 0x75, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x2e,
	0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x42, 0x37, 0x92,
	0x41, 0x34, 0x32, 0x32, 0xe5, 0x85, 0xb3, 0xe8, 0x81, 0x94, 0xe7, 0x9a, 0x84, 0x6a, 0x64, 0x73,
	0x74, 0x61, 0x63, 0x6b, 0xe7, 0x89, 0x88, 0xe6, 0x9c, 0xac, 0x2c, 0x66, 0x69, 0x6c, 0x74, 0x65,
	0x72, 0x5b, 0x57, 0x69, 0x74, 0x68, 0x3a, 0x52, 0x65, 0x6c, 0x61, 0x74, 0x65, 0x64, 0x52, 0x65,
	0x6c, 0x65, 0x61, 0x73, 0x65, 0x5d, 0x52, 0x0e, 0x6a, 0x64, 0x73, 0x74, 0x61, 0x63, 0x6b, 0x56,
	0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x2d, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0b, 0x92, 0x41, 0x08,
	0x32, 0x06, 0xe6, 0x8f, 0x8f, 0xe8, 0xbf, 0xb0, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x43, 0x0a, 0x0a, 0x70, 0x72, 0x65, 0x56, 0x65, 0x72, 0x73,
	0x69, 0x6f, 0x6e, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x42, 0x23, 0x92, 0x41, 0x20, 0x32, 0x1e,
	0xe5, 0x89, 0x8d, 0xe5, 0xba, 0x8f, 0xe7, 0x89, 0x88, 0xe6, 0x9c, 0xac, 0x2c, 0x66, 0x69, 0x6c,
	0x74, 0x65, 0x72, 0x5b, 0x57, 0x69, 0x74, 0x68, 0x3a, 0x50, 0x72, 0x65, 0x76, 0x5d, 0x52, 0x0a,
	0x70, 0x72, 0x65, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x47, 0x0a, 0x0c, 0x6e, 0x65,
	0x78, 0x74, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x07, 0x20, 0x03, 0x28, 0x09,
	0x42, 0x23, 0x92, 0x41, 0x20, 0x32, 0x1e, 0xe5, 0x90, 0x8e, 0xe5, 0xba, 0x8f, 0xe7, 0x89, 0x88,
	0xe6, 0x9c, 0xac, 0x2c, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x5b, 0x57, 0x69, 0x74, 0x68, 0x3a,
	0x4e, 0x65, 0x78, 0x74, 0x5d, 0x52, 0x0c, 0x6e, 0x65, 0x78, 0x74, 0x56, 0x65, 0x72, 0x73, 0x69,
	0x6f, 0x6e, 0x73, 0x22, 0xc0, 0x05, 0x0a, 0x13, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65,
	0x73, 0x43, 0x76, 0x65, 0x73, 0x73, 0x65, 0x6c, 0x52, 0x65, 0x71, 0x12, 0x7a, 0x0a, 0x0a, 0x70,
	0x61, 0x67, 0x65, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42,
	0x5a, 0x92, 0x41, 0x42, 0x32, 0x3d, 0xe9, 0xa1, 0xb5, 0xe7, 0xa0, 0x81, 0x3a, 0x0a, 0x2d, 0x31,
	0x20, 0x2d, 0x20, 0xe5, 0x85, 0xa8, 0xe9, 0x83, 0xa8, 0x0a, 0x30, 0x20, 0x2d, 0x20, 0xe7, 0xac,
	0xac, 0x31, 0xe9, 0xa1, 0xb5, 0x0a, 0x31, 0x20, 0x2d, 0x20, 0xe7, 0xac, 0xac, 0x32, 0xe9, 0xa1,
	0xb5, 0x0a, 0x2d, 0x20, 0xe4, 0xbb, 0xa5, 0xe6, 0xad, 0xa4, 0xe7, 0xb1, 0xbb, 0xe6, 0x8e, 0xa8,
	0x2e, 0x2e, 0x2e, 0x3a, 0x01, 0x30, 0xfa, 0x42, 0x12, 0x22, 0x10, 0x18, 0x80, 0x80, 0x80, 0x08,
	0x28, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x01, 0x52, 0x0a, 0x70, 0x61, 0x67,
	0x65, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x28, 0x0a, 0x08, 0x70, 0x61, 0x67, 0x65, 0x53,
	0x69, 0x7a, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x42, 0x0c, 0xfa, 0x42, 0x09, 0x22, 0x07,
	0x18, 0x80, 0x80, 0x80, 0x08, 0x28, 0x01, 0x52, 0x08, 0x70, 0x61, 0x67, 0x65, 0x53, 0x69, 0x7a,
	0x65, 0x12, 0xbb, 0x02, 0x0a, 0x07, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x73, 0x18, 0x03, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x77, 0x69, 0x6e, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x42, 0x8d, 0x02, 0x92, 0x41, 0x89, 0x02, 0x32, 0x86, 0x02,
	0xe8, 0xbf, 0x94, 0xe5, 0x9b, 0x9e, 0xe5, 0xad, 0x97, 0xe6, 0xae, 0xb5, 0xe7, 0x9a, 0x86, 0xe6,
	0x94, 0xaf, 0xe6, 0x8c, 0x81, 0xe7, 0xad, 0x9b, 0xe9, 0x80, 0x89, 0xef, 0xbc, 0x8c, 0xe7, 0xad,
	0x9b, 0xe9, 0x80, 0x89, 0xe6, 0x89, 0xa9, 0xe5, 0xb1, 0x95, 0x3a, 0x0a, 0x57, 0x69, 0x74, 0x68,
	0x3a, 0x7b, 0xe6, 0xa8, 0xa1, 0xe5, 0x9d, 0x97, 0xe5, 0x90, 0x8d, 0x7d, 0x20, 0x2d, 0x20, 0xe8,
	0xbf, 0x94, 0xe5, 0x9b, 0x9e, 0xe5, 0x85, 0xb3, 0xe8, 0x81, 0x94, 0xe8, 0xa1, 0xa8, 0x28, 0xe6,
	0xad, 0xa4, 0xe7, 0xb1, 0xbb, 0xe8, 0xbf, 0x87, 0xe6, 0xbb, 0xa4, 0xe9, 0xa1, 0xb9, 0xe6, 0x97,
	0xa0, 0x76, 0x61, 0x6c, 0x75, 0x65, 0xef, 0xbc, 0x8c, 0xe6, 0x89, 0x80, 0xe4, 0xbb, 0xa5, 0xe5,
	0x8f, 0xaf, 0xe5, 0xb0, 0x86, 0xe5, 0x90, 0x8c, 0xe7, 0xb1, 0xbb, 0x6e, 0x61, 0x6d, 0x65, 0xe5,
	0x90, 0x88, 0xe5, 0xb9, 0xb6, 0x6e, 0x61, 0x6d, 0x65, 0x3d, 0x2e, 0xe7, 0x9a, 0x84, 0x76, 0x61,
	0x6c, 0x75, 0x65, 0x73, 0xe4, 0xb8, 0xad, 0x29, 0x0a, 0x57, 0x69, 0x74, 0x68, 0x3a, 0x7b, 0xe6,
	0xa8, 0xa1, 0xe5, 0x9d, 0x97, 0xe5, 0x90, 0x8d, 0x7d, 0x2e, 0x7b, 0xe5, 0xad, 0x97, 0xe6, 0xae,
	0xb5, 0xe5, 0x90, 0x8d, 0x7d, 0x20, 0x2d, 0x20, 0xe8, 0xbf, 0x87, 0xe6, 0xbb, 0xa4, 0xe5, 0xb9,
	0xb6, 0xe8, 0xbf, 0x94, 0xe5, 0x9b, 0x9e, 0xe5, 0x85, 0xb3, 0xe8, 0x81, 0x94, 0xe8, 0xa1, 0xa8,
	0x0a, 0x48, 0x61, 0x73, 0x3a, 0x7b, 0xe6, 0xa8, 0xa1, 0xe5, 0x9d, 0x97, 0xe5, 0x90, 0x8d, 0x7d,
	0x2e, 0x7b, 0xe5, 0xad, 0x97, 0xe6, 0xae, 0xb5, 0xe5, 0x90, 0x8d, 0x7d, 0x20, 0x2d, 0x20, 0xe5,
	0xad, 0x90, 0xe6, 0x9f, 0xa5, 0xe8, 0xaf, 0xa2, 0x28, 0xe7, 0xad, 0x89, 0xe5, 0x80, 0xbc, 0xe8,
	0xbf, 0x9e, 0xe6, 0x8e, 0xa5, 0x29, 0x52, 0x07, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x73, 0x12,
	0xa5, 0x01, 0x0a, 0x05, 0x73, 0x6f, 0x72, 0x74, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x0f, 0x2e, 0x77, 0x69, 0x6e, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x53, 0x6f, 0x72, 0x74,
	0x42, 0x7e, 0x92, 0x41, 0x7b, 0x32, 0x79, 0xe8, 0xbf, 0x94, 0xe5, 0x9b, 0x9e, 0xe5, 0xad, 0x97,
	0xe6, 0xae, 0xb5, 0xe7, 0x9a, 0x86, 0xe6, 0x94, 0xaf, 0xe6, 0x8c, 0x81, 0xe6, 0x8e, 0x92, 0xe5,
	0xba, 0x8f, 0xef, 0xbc, 0x8c, 0xe6, 0x8e, 0x92, 0xe5, 0xba, 0x8f, 0xe6, 0x89, 0xa9, 0xe5, 0xb1,
	0x95, 0x3a, 0x0a, 0x57, 0x69, 0x74, 0x68, 0x3a, 0x7b, 0xe6, 0xa8, 0xa1, 0xe5, 0x9d, 0x97, 0xe5,
	0x90, 0x8d, 0x7d, 0x2e, 0x5b, 0x20, 0x4d, 0x69, 0x6e, 0x20, 0x4d, 0x61, 0x78, 0x20, 0x43, 0x6f,
	0x75, 0x6e, 0x74, 0x20, 0x53, 0x75, 0x6d, 0x20, 0x5d, 0x3a, 0x7b, 0xe5, 0xad, 0x97, 0xe6, 0xae,
	0xb5, 0xe5, 0x90, 0x8d, 0x7d, 0x20, 0x2d, 0x20, 0xe6, 0x8c, 0x89, 0xe5, 0x85, 0xb3, 0xe8, 0x81,
	0x94, 0xe8, 0xa1, 0xa8, 0xe5, 0xad, 0x97, 0xe6, 0xae, 0xb5, 0xe6, 0x8e, 0x92, 0xe5, 0xba, 0x8f,
	0x52, 0x05, 0x73, 0x6f, 0x72, 0x74, 0x73, 0x3a, 0x1d, 0x92, 0x41, 0x1a, 0x0a, 0x18, 0xd2, 0x01,
	0x0a, 0x70, 0x61, 0x67, 0x65, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0xd2, 0x01, 0x08, 0x70, 0x61,
	0x67, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x22, 0x7a, 0x0a, 0x15, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69,
	0x62, 0x65, 0x73, 0x43, 0x76, 0x65, 0x73, 0x73, 0x65, 0x6c, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12,
	0x41, 0x0a, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2d, 0x2e,
	0x6a, 0x64, 0x73, 0x74, 0x61, 0x63, 0x6b, 0x2e, 0x7a, 0x65, 0x75, 0x73, 0x2e, 0x76, 0x31, 0x2e,
	0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x2e, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65,
	0x43, 0x76, 0x65, 0x73, 0x73, 0x65, 0x6c, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x52, 0x04, 0x6c, 0x69,
	0x73, 0x74, 0x12, 0x1e, 0x0a, 0x0a, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x43, 0x6f, 0x75, 0x6e, 0x74,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x43, 0x6f, 0x75,
	0x6e, 0x74, 0x22, 0x76, 0x0a, 0x1c, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x44, 0x65,
	0x70, 0x6c, 0x6f, 0x79, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x52,
	0x65, 0x71, 0x12, 0x56, 0x0a, 0x09, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x54, 0x79, 0x70, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x38, 0x92, 0x41, 0x1e, 0x32, 0x1c, 0xe4, 0xba, 0x91, 0xe7,
	0xb1, 0xbb, 0xe5, 0x9e, 0x8b, 0x3a, 0x20, 0x6a, 0x64, 0x73, 0x74, 0x61, 0x63, 0x6b, 0x20, 0x7c,
	0x20, 0x63, 0x76, 0x65, 0x73, 0x73, 0x65, 0x6c, 0xfa, 0x42, 0x14, 0x72, 0x12, 0x52, 0x07, 0x6a,
	0x64, 0x73, 0x74, 0x61, 0x63, 0x6b, 0x52, 0x07, 0x63, 0x76, 0x65, 0x73, 0x73, 0x65, 0x6c, 0x52,
	0x09, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x54, 0x79, 0x70, 0x65, 0x22, 0xdc, 0x02, 0x0a, 0x1e, 0x44,
	0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x44, 0x65, 0x70, 0x6c, 0x6f, 0x79, 0x56, 0x65, 0x72,
	0x73, 0x69, 0x6f, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x83, 0x01,
	0x0a, 0x0d, 0x64, 0x65, 0x70, 0x6c, 0x6f, 0x79, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x26, 0x2e, 0x6a, 0x64, 0x73, 0x74, 0x61, 0x63, 0x6b, 0x2e,
	0x7a, 0x65, 0x75, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x2e,
	0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x42, 0x35, 0x92,
	0x41, 0x32, 0x32, 0x30, 0xe5, 0xbd, 0x93, 0xe5, 0x89, 0x8d, 0xe6, 0x95, 0xb0, 0xe6, 0x8d, 0xae,
	0xe5, 0xba, 0x93, 0xe4, 0xb8, 0xad, 0xe5, 0x8c, 0x85, 0xe5, 0x90, 0xab, 0xe7, 0x9a, 0x84, 0xe5,
	0x8d, 0xb3, 0xe5, 0xb0, 0x86, 0xe4, 0xba, 0xa4, 0xe4, 0xbb, 0x98, 0xe7, 0x9a, 0x84, 0xe7, 0x89,
	0x88, 0xe6, 0x9c, 0xac, 0x52, 0x0d, 0x64, 0x65, 0x70, 0x6c, 0x6f, 0x79, 0x56, 0x65, 0x72, 0x73,
	0x69, 0x6f, 0x6e, 0x12, 0xb3, 0x01, 0x0a, 0x11, 0x64, 0x65, 0x70, 0x6c, 0x6f, 0x79, 0x56, 0x65,
	0x72, 0x73, 0x69, 0x6f, 0x6e, 0x50, 0x72, 0x65, 0x76, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x26, 0x2e, 0x6a, 0x64, 0x73, 0x74, 0x61, 0x63, 0x6b, 0x2e, 0x7a, 0x65, 0x75, 0x73, 0x2e, 0x76,
	0x31, 0x2e, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x2e, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69,
	0x62, 0x65, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x42, 0x5d, 0x92, 0x41, 0x5a, 0x32, 0x58, 0xe5, 0xbd,
	0x93, 0xe5, 0x89, 0x8d, 0xe6, 0x95, 0xb0, 0xe6, 0x8d, 0xae, 0xe5, 0xba, 0x93, 0xe4, 0xb8, 0xad,
	0xe5, 0x8c, 0x85, 0xe5, 0x90, 0xab, 0xe7, 0x9a, 0x84, 0xe5, 0x8d, 0xb3, 0xe5, 0xb0, 0x86, 0xe4,
	0xba, 0xa4, 0xe4, 0xbb, 0x98, 0xe7, 0x9a, 0x84, 0xe7, 0x89, 0x88, 0xe6, 0x9c, 0xac, 0xe7, 0x9a,
	0x84, 0xe5, 0x89, 0x8d, 0xe5, 0xba, 0x8f, 0xe7, 0x89, 0x88, 0xe6, 0x9c, 0xac, 0x2c, 0xe5, 0xa6,
	0x82, 0xe6, 0x9e, 0x9c, 0xe4, 0xb8, 0x8d, 0xe5, 0xad, 0x98, 0xe5, 0x9c, 0xa8, 0xe5, 0x88, 0x99,
	0xe4, 0xb8, 0xba, 0xe7, 0xa9, 0xba, 0x52, 0x11, 0x64, 0x65, 0x70, 0x6c, 0x6f, 0x79, 0x56, 0x65,
	0x72, 0x73, 0x69, 0x6f, 0x6e, 0x50, 0x72, 0x65, 0x76, 0x22, 0xde, 0x05, 0x0a, 0x18, 0x43, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x43, 0x76, 0x65, 0x73, 0x73, 0x65, 0x6c, 0x4d, 0x65, 0x74, 0x61, 0x64,
	0x61, 0x74, 0x61, 0x52, 0x65, 0x71, 0x12, 0x53, 0x0a, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f,
	0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x39, 0x92, 0x41, 0x1b, 0x32, 0x19, 0xe5, 0x91,
	0xbd, 0xe5, 0x90, 0x8d, 0xe6, 0x96, 0xb9, 0xe5, 0xbc, 0x8f, 0x3a, 0xe5, 0xb0, 0x81, 0xe7, 0x89,
	0x88, 0xe7, 0x89, 0x88, 0xe6, 0x9c, 0xac, 0xfa, 0x42, 0x18, 0x72, 0x16, 0x32, 0x11, 0x5e, 0x5b,
	0x30, 0x2d, 0x39, 0x61, 0x2d, 0x7a, 0x41, 0x2d, 0x5a, 0x2e, 0x5f, 0x2d, 0x5d, 0x2b, 0x24, 0xd0,
	0x01, 0x01, 0x52, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x4e, 0x0a, 0x0b, 0x76,
	0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x42, 0x2c, 0x92, 0x41, 0x21, 0x32, 0x1f, 0xe5, 0x91, 0xbd, 0xe5, 0x90, 0x8d, 0xe6, 0x96, 0xb9,
	0xe5, 0xbc, 0x8f, 0x3a, 0xe5, 0xb0, 0x81, 0xe7, 0x89, 0x88, 0xe7, 0x89, 0x88, 0xe6, 0x9c, 0xac,
	0xe7, 0xb1, 0xbb, 0xe5, 0x9e, 0x8b, 0xfa, 0x42, 0x05, 0x72, 0x03, 0xd0, 0x01, 0x01, 0x52, 0x0b,
	0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x12, 0x72, 0x0a, 0x0f, 0x73,
	0x6e, 0x61, 0x70, 0x73, 0x68, 0x6f, 0x74, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x42, 0x48, 0x92, 0x41, 0x2a, 0x32, 0x28, 0xe5, 0x91, 0xbd, 0xe5, 0x90,
	0x8d, 0xe6, 0x96, 0xb9, 0xe5, 0xbc, 0x8f, 0x3a, 0xe4, 0xba, 0x91, 0xe8, 0x88, 0xb0, 0xe7, 0x89,
	0x88, 0xe6, 0x9c, 0xac, 0xe5, 0xb0, 0x81, 0xe7, 0x89, 0x88, 0xe5, 0xbf, 0xab, 0xe7, 0x85, 0xa7,
	0xe5, 0x8f, 0xb7, 0xfa, 0x42, 0x18, 0x72, 0x16, 0x32, 0x11, 0x5e, 0x5b, 0x30, 0x2d, 0x39, 0x61,
	0x2d, 0x7a, 0x41, 0x2d, 0x5a, 0x2e, 0x5f, 0x2d, 0x5d, 0x2b, 0x24, 0xd0, 0x01, 0x01, 0x52, 0x0f,
	0x73, 0x6e, 0x61, 0x70, 0x73, 0x68, 0x6f, 0x74, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12,
	0x59, 0x0a, 0x0a, 0x70, 0x72, 0x65, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x09, 0x42, 0x39, 0x92, 0x41, 0x1b, 0x32, 0x19, 0xe9, 0xa2, 0x84, 0xe7, 0x95, 0x99,
	0xe5, 0xad, 0x97, 0xe6, 0xae, 0xb5, 0x2c, 0xe5, 0x89, 0x8d, 0xe5, 0xba, 0x8f, 0xe7, 0x89, 0x88,
	0xe6, 0x9c, 0xac, 0xfa, 0x42, 0x18, 0x72, 0x16, 0x32, 0x11, 0x5e, 0x5b, 0x30, 0x2d, 0x39, 0x61,
	0x2d, 0x7a, 0x41, 0x2d, 0x5a, 0x2e, 0x5f, 0x2d, 0x5d, 0x2b, 0x24, 0xd0, 0x01, 0x01, 0x52, 0x0a,
	0x70, 0x72, 0x65, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0xb6, 0x01, 0x0a, 0x0e, 0x6a,
	0x64, 0x73, 0x74, 0x61, 0x63, 0x6b, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x09, 0x42, 0x8d, 0x01, 0x92, 0x41, 0x6f, 0x32, 0x6d, 0xe6, 0xad, 0xa4, 0xe5, 0xb0,
	0x81, 0xe7, 0x89, 0x88, 0xe5, 0xbf, 0xab, 0xe7, 0x85, 0xa7, 0xe6, 0x89, 0x80, 0xe5, 0x85, 0xb3,
	0xe8, 0x81, 0x94, 0xe7, 0x9a, 0x84, 0x6a, 0x64, 0x73, 0x74, 0x61, 0x63, 0x6b, 0xe7, 0x89, 0x88,
	0xe6, 0x9c, 0xac, 0xe5, 0x8f, 0xb7, 0x2c, 0xe7, 0x89, 0x88, 0xe6, 0x9c, 0xac, 0xe7, 0xbb, 0x9f,
	0xe4, 0xb8, 0x80, 0xe5, 0x90, 0x8e, 0xe5, 0xa6, 0x82, 0xe6, 0x9e, 0x9c, 0xe6, 0x98, 0xaf, 0x6a,
	0x64, 0x73, 0x74, 0x61, 0x63, 0x6b, 0xe5, 0xb0, 0x81, 0xe7, 0x89, 0x88, 0xe7, 0xb1, 0xbb, 0xe5,
	0x9e, 0x8b, 0xe8, 0xb0, 0x83, 0xe7, 0x94, 0xa8, 0x2c, 0xe6, 0xad, 0xa4, 0xe5, 0xad, 0x97, 0xe6,
	0xae, 0xb5, 0xe6, 0x97, 0xa0, 0xe6, 0x95, 0x88, 0xfa, 0x42, 0x18, 0x72, 0x16, 0x32, 0x11, 0x5e,
	0x5b, 0x30, 0x2d, 0x39, 0x61, 0x2d, 0x7a, 0x41, 0x2d, 0x5a, 0x2e, 0x5f, 0x2d, 0x5d, 0x2b, 0x24,
	0xd0, 0x01, 0x01, 0x52, 0x0e, 0x6a, 0x64, 0x73, 0x74, 0x61, 0x63, 0x6b, 0x56, 0x65, 0x72, 0x73,
	0x69, 0x6f, 0x6e, 0x12, 0x34, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x06, 0x20, 0x01, 0x28,
	0x0c, 0x42, 0x20, 0x92, 0x41, 0x1d, 0x32, 0x1b, 0xe5, 0xb0, 0x81, 0xe7, 0x89, 0x88, 0xe5, 0xbf,
	0xab, 0xe7, 0x85, 0xa7, 0xe5, 0x86, 0x85, 0xe5, 0xae, 0xb9, 0xe5, 0xad, 0x97, 0xe8, 0x8a, 0x82,
	0xe6, 0xb5, 0x81, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x12, 0x5f, 0x0a, 0x0d, 0x66, 0x6f, 0x72,
	0x63, 0x65, 0x4f, 0x76, 0x65, 0x72, 0x72, 0x69, 0x64, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x08,
	0x42, 0x39, 0x92, 0x41, 0x36, 0x32, 0x2e, 0xe6, 0x98, 0xaf, 0xe5, 0x90, 0xa6, 0xe5, 0xbc, 0xba,
	0xe5, 0x88, 0xb6, 0xe8, 0xa6, 0x86, 0xe7, 0x9b, 0x96, 0xe7, 0x89, 0x88, 0xe6, 0x9c, 0xac, 0xe6,
	0x95, 0xb0, 0xe6, 0x8d, 0xae, 0x2c, 0xe8, 0xaf, 0xb7, 0xe8, 0xb0, 0xa8, 0xe6, 0x85, 0x8e, 0xe6,
	0x93, 0x8d, 0xe4, 0xbd, 0x9c, 0x3a, 0x04, 0x74, 0x72, 0x75, 0x65, 0x52, 0x0d, 0x66, 0x6f, 0x72,
	0x63, 0x65, 0x4f, 0x76, 0x65, 0x72, 0x72, 0x69, 0x64, 0x65, 0x22, 0xfd, 0x04, 0x0a, 0x18, 0x4d,
	0x6f, 0x64, 0x69, 0x66, 0x79, 0x43, 0x76, 0x65, 0x73, 0x73, 0x65, 0x6c, 0x4d, 0x65, 0x74, 0x61,
	0x64, 0x61, 0x74, 0x61, 0x52, 0x65, 0x71, 0x12, 0x53, 0x0a, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69,
	0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x39, 0x92, 0x41, 0x1b, 0x32, 0x19, 0xe5,
	0x91, 0xbd, 0xe5, 0x90, 0x8d, 0xe6, 0x96, 0xb9, 0xe5, 0xbc, 0x8f, 0x3a, 0xe5, 0xb0, 0x81, 0xe7,
	0x89, 0x88, 0xe7, 0x89, 0x88, 0xe6, 0x9c, 0xac, 0xfa, 0x42, 0x18, 0x72, 0x16, 0x32, 0x11, 0x5e,
	0x5b, 0x30, 0x2d, 0x39, 0x61, 0x2d, 0x7a, 0x41, 0x2d, 0x5a, 0x2e, 0x5f, 0x2d, 0x5d, 0x2b, 0x24,
	0xd0, 0x01, 0x01, 0x52, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x4e, 0x0a, 0x0b,
	0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x42, 0x2c, 0x92, 0x41, 0x21, 0x32, 0x1f, 0xe5, 0x91, 0xbd, 0xe5, 0x90, 0x8d, 0xe6, 0x96,
	0xb9, 0xe5, 0xbc, 0x8f, 0x3a, 0xe5, 0xb0, 0x81, 0xe7, 0x89, 0x88, 0xe7, 0x89, 0x88, 0xe6, 0x9c,
	0xac, 0xe7, 0xb1, 0xbb, 0xe5, 0x9e, 0x8b, 0xfa, 0x42, 0x05, 0x72, 0x03, 0xd0, 0x01, 0x01, 0x52,
	0x0b, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x12, 0x72, 0x0a, 0x0f,
	0x73, 0x6e, 0x61, 0x70, 0x73, 0x68, 0x6f, 0x74, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x48, 0x92, 0x41, 0x2a, 0x32, 0x28, 0xe5, 0x91, 0xbd, 0xe5,
	0x90, 0x8d, 0xe6, 0x96, 0xb9, 0xe5, 0xbc, 0x8f, 0x3a, 0xe4, 0xba, 0x91, 0xe8, 0x88, 0xb0, 0xe7,
	0x89, 0x88, 0xe6, 0x9c, 0xac, 0xe5, 0xb0, 0x81, 0xe7, 0x89, 0x88, 0xe5, 0xbf, 0xab, 0xe7, 0x85,
	0xa7, 0xe5, 0x8f, 0xb7, 0xfa, 0x42, 0x18, 0x72, 0x16, 0x32, 0x11, 0x5e, 0x5b, 0x30, 0x2d, 0x39,
	0x61, 0x2d, 0x7a, 0x41, 0x2d, 0x5a, 0x2e, 0x5f, 0x2d, 0x5d, 0x2b, 0x24, 0xd0, 0x01, 0x01, 0x52,
	0x0f, 0x73, 0x6e, 0x61, 0x70, 0x73, 0x68, 0x6f, 0x74, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e,
	0x12, 0x59, 0x0a, 0x0a, 0x70, 0x72, 0x65, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x09, 0x42, 0x39, 0x92, 0x41, 0x1b, 0x32, 0x19, 0xe9, 0xa2, 0x84, 0xe7, 0x95,
	0x99, 0xe5, 0xad, 0x97, 0xe6, 0xae, 0xb5, 0x2c, 0xe5, 0x89, 0x8d, 0xe5, 0xba, 0x8f, 0xe7, 0x89,
	0x88, 0xe6, 0x9c, 0xac, 0xfa, 0x42, 0x18, 0x72, 0x16, 0x32, 0x11, 0x5e, 0x5b, 0x30, 0x2d, 0x39,
	0x61, 0x2d, 0x7a, 0x41, 0x2d, 0x5a, 0x2e, 0x5f, 0x2d, 0x5d, 0x2b, 0x24, 0xd0, 0x01, 0x01, 0x52,
	0x0a, 0x70, 0x72, 0x65, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0xb6, 0x01, 0x0a, 0x0e,
	0x6a, 0x64, 0x73, 0x74, 0x61, 0x63, 0x6b, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x09, 0x42, 0x8d, 0x01, 0x92, 0x41, 0x6f, 0x32, 0x6d, 0xe6, 0xad, 0xa4, 0xe5,
	0xb0, 0x81, 0xe7, 0x89, 0x88, 0xe5, 0xbf, 0xab, 0xe7, 0x85, 0xa7, 0xe6, 0x89, 0x80, 0xe5, 0x85,
	0xb3, 0xe8, 0x81, 0x94, 0xe7, 0x9a, 0x84, 0x6a, 0x64, 0x73, 0x74, 0x61, 0x63, 0x6b, 0xe7, 0x89,
	0x88, 0xe6, 0x9c, 0xac, 0xe5, 0x8f, 0xb7, 0x2c, 0xe7, 0x89, 0x88, 0xe6, 0x9c, 0xac, 0xe7, 0xbb,
	0x9f, 0xe4, 0xb8, 0x80, 0xe5, 0x90, 0x8e, 0xe5, 0xa6, 0x82, 0xe6, 0x9e, 0x9c, 0xe6, 0x98, 0xaf,
	0x6a, 0x64, 0x73, 0x74, 0x61, 0x63, 0x6b, 0xe5, 0xb0, 0x81, 0xe7, 0x89, 0x88, 0xe7, 0xb1, 0xbb,
	0xe5, 0x9e, 0x8b, 0xe8, 0xb0, 0x83, 0xe7, 0x94, 0xa8, 0x2c, 0xe6, 0xad, 0xa4, 0xe5, 0xad, 0x97,
	0xe6, 0xae, 0xb5, 0xe6, 0x97, 0xa0, 0xe6, 0x95, 0x88, 0xfa, 0x42, 0x18, 0x72, 0x16, 0x32, 0x11,
	0x5e, 0x5b, 0x30, 0x2d, 0x39, 0x61, 0x2d, 0x7a, 0x41, 0x2d, 0x5a, 0x2e, 0x5f, 0x2d, 0x5d, 0x2b,
	0x24, 0xd0, 0x01, 0x01, 0x52, 0x0e, 0x6a, 0x64, 0x73, 0x74, 0x61, 0x63, 0x6b, 0x56, 0x65, 0x72,
	0x73, 0x69, 0x6f, 0x6e, 0x12, 0x34, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x06, 0x20, 0x01,
	0x28, 0x0c, 0x42, 0x20, 0x92, 0x41, 0x1d, 0x32, 0x1b, 0xe5, 0xb0, 0x81, 0xe7, 0x89, 0x88, 0xe5,
	0xbf, 0xab, 0xe7, 0x85, 0xa7, 0xe5, 0x86, 0x85, 0xe5, 0xae, 0xb9, 0xe5, 0xad, 0x97, 0xe8, 0x8a,
	0x82, 0xe6, 0xb5, 0x81, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x22, 0xfb, 0x02, 0x0a, 0x18, 0x44,
	0x65, 0x6c, 0x65, 0x74, 0x65, 0x43, 0x76, 0x65, 0x73, 0x73, 0x65, 0x6c, 0x4d, 0x65, 0x74, 0x61,
	0x64, 0x61, 0x74, 0x61, 0x52, 0x65, 0x71, 0x12, 0x56, 0x0a, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69,
	0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x3c, 0x92, 0x41, 0x1e, 0x32, 0x1c, 0xe5,
	0x91, 0xbd, 0xe5, 0x90, 0x8d, 0xe6, 0x96, 0xb9, 0xe5, 0xbc, 0x8f, 0x3a, 0xe4, 0xba, 0x91, 0xe8,
	0x88, 0xb0, 0xe7, 0x89, 0x88, 0xe6, 0x9c, 0xac, 0xe5, 0x8f, 0xb7, 0xfa, 0x42, 0x18, 0x72, 0x16,
	0x32, 0x11, 0x5e, 0x5b, 0x30, 0x2d, 0x39, 0x61, 0x2d, 0x7a, 0x41, 0x2d, 0x5a, 0x2e, 0x5f, 0x2d,
	0x5d, 0x2b, 0x24, 0xd0, 0x01, 0x01, 0x52, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12,
	0x4e, 0x0a, 0x0b, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x42, 0x2c, 0x92, 0x41, 0x21, 0x32, 0x1f, 0xe5, 0x91, 0xbd, 0xe5, 0x90,
	0x8d, 0xe6, 0x96, 0xb9, 0xe5, 0xbc, 0x8f, 0x3a, 0xe5, 0xb0, 0x81, 0xe7, 0x89, 0x88, 0xe7, 0x89,
	0x88, 0xe6, 0x9c, 0xac, 0xe7, 0xb1, 0xbb, 0xe5, 0x9e, 0x8b, 0xfa, 0x42, 0x05, 0x72, 0x03, 0xd0,
	0x01, 0x01, 0x52, 0x0b, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x12,
	0xb6, 0x01, 0x0a, 0x0e, 0x6a, 0x64, 0x73, 0x74, 0x61, 0x63, 0x6b, 0x56, 0x65, 0x72, 0x73, 0x69,
	0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x42, 0x8d, 0x01, 0x92, 0x41, 0x6f, 0x32, 0x6d,
	0xe6, 0xad, 0xa4, 0xe5, 0xb0, 0x81, 0xe7, 0x89, 0x88, 0xe5, 0xbf, 0xab, 0xe7, 0x85, 0xa7, 0xe6,
	0x89, 0x80, 0xe5, 0x85, 0xb3, 0xe8, 0x81, 0x94, 0xe7, 0x9a, 0x84, 0x6a, 0x64, 0x73, 0x74, 0x61,
	0x63, 0x6b, 0xe7, 0x89, 0x88, 0xe6, 0x9c, 0xac, 0xe5, 0x8f, 0xb7, 0x2c, 0xe7, 0x89, 0x88, 0xe6,
	0x9c, 0xac, 0xe7, 0xbb, 0x9f, 0xe4, 0xb8, 0x80, 0xe5, 0x90, 0x8e, 0xe5, 0xa6, 0x82, 0xe6, 0x9e,
	0x9c, 0xe6, 0x98, 0xaf, 0x6a, 0x64, 0x73, 0x74, 0x61, 0x63, 0x6b, 0xe5, 0xb0, 0x81, 0xe7, 0x89,
	0x88, 0xe7, 0xb1, 0xbb, 0xe5, 0x9e, 0x8b, 0xe8, 0xb0, 0x83, 0xe7, 0x94, 0xa8, 0x2c, 0xe6, 0xad,
	0xa4, 0xe5, 0xad, 0x97, 0xe6, 0xae, 0xb5, 0xe6, 0x97, 0xa0, 0xe6, 0x95, 0x88, 0xfa, 0x42, 0x18,
	0x72, 0x16, 0x32, 0x11, 0x5e, 0x5b, 0x30, 0x2d, 0x39, 0x61, 0x2d, 0x7a, 0x41, 0x2d, 0x5a, 0x2e,
	0x5f, 0x2d, 0x5d, 0x2b, 0x24, 0xd0, 0x01, 0x01, 0x52, 0x0e, 0x6a, 0x64, 0x73, 0x74, 0x61, 0x63,
	0x6b, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x22, 0x44, 0x0a, 0x1a, 0x43, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x43, 0x76, 0x65, 0x73, 0x73, 0x65, 0x6c, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74,
	0x61, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0x44,
	0x0a, 0x1a, 0x4d, 0x6f, 0x64, 0x69, 0x66, 0x79, 0x43, 0x76, 0x65, 0x73, 0x73, 0x65, 0x6c, 0x4d,
	0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x0e, 0x0a, 0x02,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x16, 0x0a, 0x06,
	0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x22, 0x44, 0x0a, 0x1a, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x43, 0x76,
	0x65, 0x73, 0x73, 0x65, 0x6c, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x52, 0x65, 0x70,
	0x6c, 0x79, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02,
	0x69, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0x72, 0x0a, 0x1c, 0x44, 0x65,
	0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x52, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x43, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x46, 0x69, 0x6c, 0x65, 0x52, 0x65, 0x71, 0x12, 0x18, 0x0a, 0x07, 0x76, 0x65,
	0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x76, 0x65, 0x72,
	0x73, 0x69, 0x6f, 0x6e, 0x12, 0x1c, 0x0a, 0x09, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x54, 0x79, 0x70,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x54, 0x79,
	0x70, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x66, 0x69, 0x6c, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x66, 0x69, 0x6c, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x22, 0x7a,
	0x0a, 0x1e, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x52, 0x65, 0x6c, 0x65, 0x61, 0x73,
	0x65, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x46, 0x69, 0x6c, 0x65, 0x52, 0x65, 0x70, 0x6c, 0x79,
	0x12, 0x1a, 0x0a, 0x08, 0x66, 0x69, 0x6c, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x08, 0x66, 0x69, 0x6c, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x22, 0x0a, 0x0c,
	0x72, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x76, 0x65, 0x50, 0x61, 0x74, 0x68, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0c, 0x72, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x76, 0x65, 0x50, 0x61, 0x74, 0x68,
	0x12, 0x18, 0x0a, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x22, 0x8e, 0x01, 0x0a, 0x1f, 0x44,
	0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x52, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x43, 0x6f,
	0x6e, 0x66, 0x69, 0x67, 0x46, 0x69, 0x6c, 0x65, 0x73, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x4b,
	0x0a, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x37, 0x2e, 0x6a,
	0x64, 0x73, 0x74, 0x61, 0x63, 0x6b, 0x2e, 0x7a, 0x65, 0x75, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x76,
	0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x2e, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x52,
	0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x46, 0x69, 0x6c, 0x65,
	0x52, 0x65, 0x70, 0x6c, 0x79, 0x52, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x12, 0x1e, 0x0a, 0x0a, 0x74,
	0x6f, 0x74, 0x61, 0x6c, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x0a, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x22, 0xd4, 0x01, 0x0a, 0x15,
	0x4d, 0x6f, 0x64, 0x69, 0x66, 0x79, 0x44, 0x65, 0x70, 0x65, 0x6e, 0x64, 0x65, 0x6e, 0x63, 0x69,
	0x65, 0x73, 0x52, 0x65, 0x71, 0x12, 0x55, 0x0a, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x3b, 0x92, 0x41, 0x1e, 0x32, 0x1c, 0xe5, 0xb0, 0x81,
	0xe7, 0x89, 0x88, 0xe7, 0x89, 0x88, 0xe6, 0x9c, 0xac, 0xe5, 0x8f, 0xb7, 0x2c, 0xe4, 0xbb, 0x85,
	0xe9, 0x99, 0x90, 0xe6, 0x9b, 0xb4, 0xe6, 0x94, 0xb9, 0xfa, 0x42, 0x17, 0x72, 0x15, 0x20, 0x01,
	0x32, 0x11, 0x5e, 0x5b, 0x30, 0x2d, 0x39, 0x61, 0x2d, 0x7a, 0x41, 0x2d, 0x5a, 0x2e, 0x5f, 0x2d,
	0x5d, 0x2b, 0x24, 0x52, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x53, 0x0a, 0x08,
	0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x24,
	0x2e, 0x6a, 0x64, 0x73, 0x74, 0x61, 0x63, 0x6b, 0x2e, 0x7a, 0x65, 0x75, 0x73, 0x2e, 0x76, 0x31,
	0x2e, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x2e, 0x50, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74,
	0x49, 0x6e, 0x66, 0x6f, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x32, 0x0c, 0xe4, 0xba, 0xa7, 0xe5, 0x93,
	0x81, 0xe5, 0x88, 0x97, 0xe8, 0xa1, 0xa8, 0x52, 0x08, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74,
	0x73, 0x3a, 0x0f, 0x92, 0x41, 0x0c, 0x0a, 0x0a, 0xd2, 0x01, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69,
	0x6f, 0x6e, 0x22, 0x9c, 0x01, 0x0a, 0x0b, 0x50, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x49, 0x6e,
	0x66, 0x6f, 0x12, 0x27, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x17,
	0x92, 0x41, 0x14, 0x32, 0x12, 0xe4, 0xba, 0xa7, 0xe5, 0x93, 0x81, 0xe5, 0x94, 0xaf, 0xe4, 0xb8,
	0x80, 0xe6, 0xa0, 0x87, 0xe8, 0xaf, 0x86, 0x52, 0x02, 0x69, 0x64, 0x12, 0x35, 0x0a, 0x0c, 0x64,
	0x65, 0x70, 0x65, 0x6e, 0x64, 0x65, 0x6e, 0x63, 0x69, 0x65, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28,
	0x09, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x32, 0x0c, 0xe4, 0xbe, 0x9d, 0xe8, 0xb5, 0x96, 0xe5, 0x88,
	0x97, 0xe8, 0xa1, 0xa8, 0x52, 0x0c, 0x64, 0x65, 0x70, 0x65, 0x6e, 0x64, 0x65, 0x6e, 0x63, 0x69,
	0x65, 0x73, 0x12, 0x2d, 0x0a, 0x08, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x08, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x32, 0x0c, 0xe6, 0x98, 0xaf, 0xe5, 0x90,
	0xa6, 0xe5, 0xbf, 0x85, 0xe9, 0x9c, 0x80, 0x52, 0x08, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65,
	0x64, 0x22, 0x71, 0x0a, 0x17, 0x4d, 0x6f, 0x64, 0x69, 0x66, 0x79, 0x44, 0x65, 0x70, 0x65, 0x6e,
	0x64, 0x65, 0x6e, 0x63, 0x69, 0x65, 0x73, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x29, 0x0a, 0x06,
	0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x11, 0x92, 0x41,
	0x0e, 0x32, 0x0c, 0xe6, 0x9b, 0xb4, 0xe6, 0x96, 0xb0, 0xe7, 0x8a, 0xb6, 0xe6, 0x80, 0x81, 0x52,
	0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x2b, 0x0a, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61,
	0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x32, 0x0c, 0xe8,
	0xbf, 0x94, 0xe5, 0x9b, 0x9e, 0xe6, 0xb6, 0x88, 0xe6, 0x81, 0xaf, 0x52, 0x07, 0x6d, 0x65, 0x73,
	0x73, 0x61, 0x67, 0x65, 0x22, 0xbc, 0x01, 0x0a, 0x16, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x53,
	0x71, 0x6c, 0x45, 0x78, 0x70, 0x6f, 0x72, 0x74, 0x54, 0x6f, 0x53, 0x33, 0x52, 0x65, 0x71, 0x12,
	0x46, 0x0a, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x42, 0x2c, 0x92, 0x41, 0x0f, 0x32, 0x0d, 0x5a, 0x65, 0x75, 0x73, 0xe7, 0x89, 0x88, 0xe6, 0x9c,
	0xac, 0xe5, 0x8f, 0xb7, 0xfa, 0x42, 0x17, 0x72, 0x15, 0x20, 0x01, 0x32, 0x11, 0x5e, 0x5b, 0x30,
	0x2d, 0x39, 0x61, 0x2d, 0x7a, 0x41, 0x2d, 0x5a, 0x2e, 0x5f, 0x2d, 0x5d, 0x2b, 0x24, 0x52, 0x07,
	0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x5a, 0x0a, 0x12, 0x69, 0x6e, 0x63, 0x6c, 0x75,
	0x64, 0x65, 0x50, 0x72, 0x65, 0x76, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x08, 0x42, 0x2a, 0x92, 0x41, 0x27, 0x32, 0x1e, 0xe6, 0x98, 0xaf, 0xe5, 0x90, 0xa6,
	0xe5, 0x8c, 0x85, 0xe5, 0x90, 0xab, 0xe5, 0x89, 0x8d, 0xe5, 0xba, 0x8f, 0xe7, 0x89, 0x88, 0xe6,
	0x9c, 0xac, 0xe6, 0x95, 0xb0, 0xe6, 0x8d, 0xae, 0x3a, 0x05, 0x66, 0x61, 0x6c, 0x73, 0x65, 0x52,
	0x12, 0x69, 0x6e, 0x63, 0x6c, 0x75, 0x64, 0x65, 0x50, 0x72, 0x65, 0x76, 0x56, 0x65, 0x72, 0x73,
	0x69, 0x6f, 0x6e, 0x22, 0x81, 0x02, 0x0a, 0x18, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x53, 0x71,
	0x6c, 0x45, 0x78, 0x70, 0x6f, 0x72, 0x74, 0x54, 0x6f, 0x53, 0x33, 0x52, 0x65, 0x70, 0x6c, 0x79,
	0x12, 0x41, 0x0a, 0x08, 0x73, 0x33, 0x42, 0x75, 0x63, 0x6b, 0x65, 0x74, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x42, 0x25, 0x92, 0x41, 0x22, 0x32, 0x20, 0xe5, 0xaf, 0xbc, 0xe5, 0x87, 0xba, 0x53,
	0x51, 0x4c, 0xe6, 0x96, 0x87, 0xe4, 0xbb, 0xb6, 0xe5, 0x9c, 0xa8, 0x53, 0x33, 0xe4, 0xb8, 0x8a,
	0xe7, 0x9a, 0x84, 0x42, 0x75, 0x63, 0x6b, 0x65, 0x74, 0x52, 0x08, 0x73, 0x33, 0x42, 0x75, 0x63,
	0x6b, 0x65, 0x74, 0x12, 0x4a, 0x0a, 0x0b, 0x73, 0x33, 0x4f, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x4b,
	0x65, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x28, 0x92, 0x41, 0x25, 0x32, 0x23, 0xe5,
	0xaf, 0xbc, 0xe5, 0x87, 0xba, 0x53, 0x51, 0x4c, 0xe6, 0x96, 0x87, 0xe4, 0xbb, 0xb6, 0xe5, 0x9c,
	0xa8, 0x53, 0x33, 0xe4, 0xb8, 0x8a, 0xe7, 0x9a, 0x84, 0x4f, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x4b,
	0x65, 0x79, 0x52, 0x0b, 0x73, 0x33, 0x4f, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x4b, 0x65, 0x79, 0x12,
	0x29, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42,
	0x11, 0x92, 0x41, 0x0e, 0x32, 0x0c, 0xe5, 0xaf, 0xbc, 0xe5, 0x87, 0xba, 0xe7, 0x8a, 0xb6, 0xe6,
	0x80, 0x81, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x2b, 0x0a, 0x07, 0x6d, 0x65,
	0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x42, 0x11, 0x92, 0x41, 0x0e,
	0x32, 0x0c, 0xe8, 0xbf, 0x94, 0xe5, 0x9b, 0x9e, 0xe6, 0xb6, 0x88, 0xe6, 0x81, 0xaf, 0x52, 0x07,
	0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x32, 0xef, 0x17, 0x0a, 0x07, 0x56, 0x65, 0x72, 0x73,
	0x69, 0x6f, 0x6e, 0x12, 0x87, 0x01, 0x0a, 0x06, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x12, 0x22,
	0x2e, 0x6a, 0x64, 0x73, 0x74, 0x61, 0x63, 0x6b, 0x2e, 0x7a, 0x65, 0x75, 0x73, 0x2e, 0x76, 0x31,
	0x2e, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x52,
	0x65, 0x71, 0x1a, 0x26, 0x2e, 0x6a, 0x64, 0x73, 0x74, 0x61, 0x63, 0x6b, 0x2e, 0x7a, 0x65, 0x75,
	0x73, 0x2e, 0x76, 0x31, 0x2e, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x2e, 0x44, 0x65, 0x73,
	0x63, 0x72, 0x69, 0x62, 0x65, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x31, 0x92, 0x41, 0x1b, 0x1a,
	0x19, 0xe5, 0x88, 0x9b, 0xe5, 0xbb, 0xba, 0x4a, 0x44, 0x53, 0x74, 0x61, 0x63, 0x6b, 0xe7, 0x89,
	0x88, 0xe6, 0x9c, 0xac, 0xe4, 0xbf, 0xa1, 0xe6, 0x81, 0xaf, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x0d,
	0x3a, 0x01, 0x2a, 0x22, 0x08, 0x2f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x7c, 0x0a,
	0x06, 0x4d, 0x6f, 0x64, 0x69, 0x66, 0x79, 0x12, 0x22, 0x2e, 0x6a, 0x64, 0x73, 0x74, 0x61, 0x63,
	0x6b, 0x2e, 0x7a, 0x65, 0x75, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f,
	0x6e, 0x2e, 0x4d, 0x6f, 0x64, 0x69, 0x66, 0x79, 0x52, 0x65, 0x71, 0x1a, 0x16, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d,
	0x70, 0x74, 0x79, 0x22, 0x36, 0x92, 0x41, 0x1b, 0x1a, 0x19, 0xe4, 0xbf, 0xae, 0xe6, 0x94, 0xb9,
	0x4a, 0x44, 0x53, 0x74, 0x61, 0x63, 0x6b, 0xe7, 0x89, 0x88, 0xe6, 0x9c, 0xac, 0xe4, 0xbf, 0xa1,
	0xe6, 0x81, 0xaf, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x12, 0x3a, 0x01, 0x2a, 0x1a, 0x0d, 0x2f, 0x76,
	0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x2f, 0x7b, 0x69, 0x64, 0x7d, 0x12, 0x8d, 0x01, 0x0a, 0x08,
	0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x12, 0x24, 0x2e, 0x6a, 0x64, 0x73, 0x74, 0x61,
	0x63, 0x6b, 0x2e, 0x7a, 0x65, 0x75, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x76, 0x65, 0x72, 0x73, 0x69,
	0x6f, 0x6e, 0x2e, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x52, 0x65, 0x71, 0x1a, 0x26,
	0x2e, 0x6a, 0x64, 0x73, 0x74, 0x61, 0x63, 0x6b, 0x2e, 0x7a, 0x65, 0x75, 0x73, 0x2e, 0x76, 0x31,
	0x2e, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x2e, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62,
	0x65, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x33, 0x92, 0x41, 0x1b, 0x1a, 0x19, 0xe8, 0x8e, 0xb7,
	0xe5, 0x8f, 0x96, 0x4a, 0x44, 0x53, 0x74, 0x61, 0x63, 0x6b, 0xe7, 0x89, 0x88, 0xe6, 0x9c, 0xac,
	0xe4, 0xbf, 0xa1, 0xe6, 0x81, 0xaf, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x0f, 0x12, 0x0d, 0x2f, 0x76,
	0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x2f, 0x7b, 0x69, 0x64, 0x7d, 0x12, 0x8b, 0x01, 0x0a, 0x09,
	0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x73, 0x12, 0x25, 0x2e, 0x6a, 0x64, 0x73, 0x74,
	0x61, 0x63, 0x6b, 0x2e, 0x7a, 0x65, 0x75, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x76, 0x65, 0x72, 0x73,
	0x69, 0x6f, 0x6e, 0x2e, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x73, 0x52, 0x65, 0x71,
	0x1a, 0x27, 0x2e, 0x6a, 0x64, 0x73, 0x74, 0x61, 0x63, 0x6b, 0x2e, 0x7a, 0x65, 0x75, 0x73, 0x2e,
	0x76, 0x31, 0x2e, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x2e, 0x44, 0x65, 0x73, 0x63, 0x72,
	0x69, 0x62, 0x65, 0x73, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x2e, 0x92, 0x41, 0x1b, 0x1a, 0x19,
	0xe8, 0x8e, 0xb7, 0xe5, 0x8f, 0x96, 0x4a, 0x44, 0x53, 0x74, 0x61, 0x63, 0x6b, 0xe7, 0x89, 0x88,
	0xe6, 0x9c, 0xac, 0xe5, 0x88, 0x97, 0xe8, 0xa1, 0xa8, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x0a, 0x12,
	0x08, 0x2f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0xa2, 0x01, 0x0a, 0x0d, 0x43, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x43, 0x76, 0x65, 0x73, 0x73, 0x65, 0x6c, 0x12, 0x29, 0x2e, 0x6a, 0x64,
	0x73, 0x74, 0x61, 0x63, 0x6b, 0x2e, 0x7a, 0x65, 0x75, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x76, 0x65,
	0x72, 0x73, 0x69, 0x6f, 0x6e, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x43, 0x76, 0x65, 0x73,
	0x73, 0x65, 0x6c, 0x52, 0x65, 0x71, 0x1a, 0x2d, 0x2e, 0x6a, 0x64, 0x73, 0x74, 0x61, 0x63, 0x6b,
	0x2e, 0x7a, 0x65, 0x75, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e,
	0x2e, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x43, 0x76, 0x65, 0x73, 0x73, 0x65, 0x6c,
	0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x37, 0x92, 0x41, 0x1a, 0x1a, 0x18, 0xe5, 0x88, 0x9b, 0xe5,
	0xbb, 0xba, 0xe4, 0xba, 0x91, 0xe8, 0x88, 0xb0, 0xe7, 0x89, 0x88, 0xe6, 0x9c, 0xac, 0xe4, 0xbf,
	0xa1, 0xe6, 0x81, 0xaf, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x14, 0x3a, 0x01, 0x2a, 0x22, 0x0f, 0x2f,
	0x43, 0x76, 0x65, 0x73, 0x73, 0x65, 0x6c, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x90,
	0x01, 0x0a, 0x0d, 0x4d, 0x6f, 0x64, 0x69, 0x66, 0x79, 0x43, 0x76, 0x65, 0x73, 0x73, 0x65, 0x6c,
	0x12, 0x29, 0x2e, 0x6a, 0x64, 0x73, 0x74, 0x61, 0x63, 0x6b, 0x2e, 0x7a, 0x65, 0x75, 0x73, 0x2e,
	0x76, 0x31, 0x2e, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x2e, 0x4d, 0x6f, 0x64, 0x69, 0x66,
	0x79, 0x43, 0x76, 0x65, 0x73, 0x73, 0x65, 0x6c, 0x52, 0x65, 0x71, 0x1a, 0x16, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d,
	0x70, 0x74, 0x79, 0x22, 0x3c, 0x92, 0x41, 0x1a, 0x1a, 0x18, 0xe4, 0xbf, 0xae, 0xe6, 0x94, 0xb9,
	0xe4, 0xba, 0x91, 0xe8, 0x88, 0xb0, 0xe7, 0x89, 0x88, 0xe6, 0x9c, 0xac, 0xe4, 0xbf, 0xa1, 0xe6,
	0x81, 0xaf, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x19, 0x3a, 0x01, 0x2a, 0x1a, 0x14, 0x2f, 0x43, 0x76,
	0x65, 0x73, 0x73, 0x65, 0x6c, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x2f, 0x7b, 0x69, 0x64,
	0x7d, 0x12, 0xa8, 0x01, 0x0a, 0x0f, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x43, 0x76,
	0x65, 0x73, 0x73, 0x65, 0x6c, 0x12, 0x2b, 0x2e, 0x6a, 0x64, 0x73, 0x74, 0x61, 0x63, 0x6b, 0x2e,
	0x7a, 0x65, 0x75, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x2e,
	0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x43, 0x76, 0x65, 0x73, 0x73, 0x65, 0x6c, 0x52,
	0x65, 0x71, 0x1a, 0x2d, 0x2e, 0x6a, 0x64, 0x73, 0x74, 0x61, 0x63, 0x6b, 0x2e, 0x7a, 0x65, 0x75,
	0x73, 0x2e, 0x76, 0x31, 0x2e, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x2e, 0x44, 0x65, 0x73,
	0x63, 0x72, 0x69, 0x62, 0x65, 0x43, 0x76, 0x65, 0x73, 0x73, 0x65, 0x6c, 0x52, 0x65, 0x70, 0x6c,
	0x79, 0x22, 0x39, 0x92, 0x41, 0x1a, 0x1a, 0x18, 0xe8, 0x8e, 0xb7, 0xe5, 0x8f, 0x96, 0xe4, 0xba,
	0x91, 0xe8, 0x88, 0xb0, 0xe7, 0x89, 0x88, 0xe6, 0x9c, 0xac, 0xe4, 0xbf, 0xa1, 0xe6, 0x81, 0xaf,
	0x82, 0xd3, 0xe4, 0x93, 0x02, 0x16, 0x12, 0x14, 0x2f, 0x63, 0x76, 0x65, 0x73, 0x73, 0x65, 0x6c,
	0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x2f, 0x7b, 0x69, 0x64, 0x7d, 0x12, 0xa6, 0x01, 0x0a,
	0x10, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x73, 0x43, 0x76, 0x65, 0x73, 0x73, 0x65,
	0x6c, 0x12, 0x2c, 0x2e, 0x6a, 0x64, 0x73, 0x74, 0x61, 0x63, 0x6b, 0x2e, 0x7a, 0x65, 0x75, 0x73,
	0x2e, 0x76, 0x31, 0x2e, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x2e, 0x44, 0x65, 0x73, 0x63,
	0x72, 0x69, 0x62, 0x65, 0x73, 0x43, 0x76, 0x65, 0x73, 0x73, 0x65, 0x6c, 0x52, 0x65, 0x71, 0x1a,
	0x2e, 0x2e, 0x6a, 0x64, 0x73, 0x74, 0x61, 0x63, 0x6b, 0x2e, 0x7a, 0x65, 0x75, 0x73, 0x2e, 0x76,
	0x31, 0x2e, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x2e, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69,
	0x62, 0x65, 0x73, 0x43, 0x76, 0x65, 0x73, 0x73, 0x65, 0x6c, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22,
	0x34, 0x92, 0x41, 0x1a, 0x1a, 0x18, 0xe8, 0x8e, 0xb7, 0xe5, 0x8f, 0x96, 0xe4, 0xba, 0x91, 0xe8,
	0x88, 0xb0, 0xe7, 0x89, 0x88, 0xe6, 0x9c, 0xac, 0xe5, 0x88, 0x97, 0xe8, 0xa1, 0xa8, 0x82, 0xd3,
	0xe4, 0x93, 0x02, 0x11, 0x12, 0x0f, 0x2f, 0x63, 0x76, 0x65, 0x73, 0x73, 0x65, 0x6c, 0x56, 0x65,
	0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0xde, 0x01, 0x0a, 0x15, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x43, 0x76, 0x65, 0x73, 0x73, 0x65, 0x6c, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x12,
	0x31, 0x2e, 0x6a, 0x64, 0x73, 0x74, 0x61, 0x63, 0x6b, 0x2e, 0x7a, 0x65, 0x75, 0x73, 0x2e, 0x76,
	0x31, 0x2e, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x43, 0x76, 0x65, 0x73, 0x73, 0x65, 0x6c, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x52,
	0x65, 0x71, 0x1a, 0x33, 0x2e, 0x6a, 0x64, 0x73, 0x74, 0x61, 0x63, 0x6b, 0x2e, 0x7a, 0x65, 0x75,
	0x73, 0x2e, 0x76, 0x31, 0x2e, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x2e, 0x43, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x43, 0x76, 0x65, 0x73, 0x73, 0x65, 0x6c, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61,
	0x74, 0x61, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x5d, 0x92, 0x41, 0x36, 0x1a, 0x34, 0xe6, 0x8e,
	0xa5, 0xe6, 0x94, 0xb6, 0xe5, 0xa4, 0xa9, 0xe5, 0x9f, 0xba, 0xe4, 0xba, 0x91, 0xe8, 0x88, 0xb0,
	0xe7, 0x89, 0x88, 0xe6, 0x9c, 0xac, 0xe5, 0x85, 0x83, 0xe6, 0x95, 0xb0, 0xe6, 0x8d, 0xae, 0xe5,
	0x8c, 0x85, 0xe5, 0xb9, 0xb6, 0xe5, 0xaf, 0xbc, 0xe5, 0x85, 0xa5, 0x7a, 0x65, 0x75, 0x73, 0xe5,
	0xba, 0x93, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x1e, 0x3a, 0x01, 0x2a, 0x22, 0x19, 0x2f, 0x63, 0x76,
	0x65, 0x73, 0x73, 0x65, 0x6c, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x2f, 0x7b, 0x76, 0x65,
	0x72, 0x73, 0x69, 0x6f, 0x6e, 0x7d, 0x12, 0xff, 0x01, 0x0a, 0x15, 0x4d, 0x6f, 0x64, 0x69, 0x66,
	0x79, 0x43, 0x76, 0x65, 0x73, 0x73, 0x65, 0x6c, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61,
	0x12, 0x31, 0x2e, 0x6a, 0x64, 0x73, 0x74, 0x61, 0x63, 0x6b, 0x2e, 0x7a, 0x65, 0x75, 0x73, 0x2e,
	0x76, 0x31, 0x2e, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x2e, 0x4d, 0x6f, 0x64, 0x69, 0x66,
	0x79, 0x43, 0x76, 0x65, 0x73, 0x73, 0x65, 0x6c, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61,
	0x52, 0x65, 0x71, 0x1a, 0x33, 0x2e, 0x6a, 0x64, 0x73, 0x74, 0x61, 0x63, 0x6b, 0x2e, 0x7a, 0x65,
	0x75, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x2e, 0x4d, 0x6f,
	0x64, 0x69, 0x66, 0x79, 0x43, 0x76, 0x65, 0x73, 0x73, 0x65, 0x6c, 0x4d, 0x65, 0x74, 0x61, 0x64,
	0x61, 0x74, 0x61, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x7e, 0x92, 0x41, 0x57, 0x1a, 0x55, 0xe5,
	0x88, 0x9b, 0xe5, 0xbb, 0xba, 0x2f, 0xe6, 0x9b, 0xb4, 0xe6, 0x96, 0xb0, 0x7a, 0x65, 0x75, 0x73,
	0xe5, 0xba, 0x93, 0xe5, 0x86, 0x85, 0xe5, 0xa4, 0xa9, 0xe5, 0x9f, 0xba, 0xe4, 0xba, 0x91, 0xe8,
	0x88, 0xb0, 0xe7, 0x89, 0x88, 0xe6, 0x9c, 0xac, 0xe5, 0x85, 0x83, 0xe6, 0x95, 0xb0, 0xe6, 0x8d,
	0xae, 0x2c, 0xe5, 0x8f, 0xaa, 0xe4, 0xbf, 0x9d, 0xe7, 0x95, 0x99, 0xe4, 0xb8, 0x80, 0xe4, 0xbb,
	0xbd, 0x2c, 0xe4, 0xb8, 0x8d, 0xe5, 0xad, 0x98, 0xe5, 0x9c, 0xa8, 0xe5, 0x88, 0x99, 0xe6, 0x96,
	0xb0, 0xe5, 0xbb, 0xba, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x1e, 0x3a, 0x01, 0x2a, 0x22, 0x19, 0x2f,
	0x63, 0x76, 0x65, 0x73, 0x73, 0x65, 0x6c, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x2f, 0x7b,
	0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x7d, 0x12, 0xd2, 0x01, 0x0a, 0x15, 0x44, 0x65, 0x6c,
	0x65, 0x74, 0x65, 0x43, 0x76, 0x65, 0x73, 0x73, 0x65, 0x6c, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61,
	0x74, 0x61, 0x12, 0x31, 0x2e, 0x6a, 0x64, 0x73, 0x74, 0x61, 0x63, 0x6b, 0x2e, 0x7a, 0x65, 0x75,
	0x73, 0x2e, 0x76, 0x31, 0x2e, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x2e, 0x44, 0x65, 0x6c,
	0x65, 0x74, 0x65, 0x43, 0x76, 0x65, 0x73, 0x73, 0x65, 0x6c, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61,
	0x74, 0x61, 0x52, 0x65, 0x71, 0x1a, 0x33, 0x2e, 0x6a, 0x64, 0x73, 0x74, 0x61, 0x63, 0x6b, 0x2e,
	0x7a, 0x65, 0x75, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x2e,
	0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x43, 0x76, 0x65, 0x73, 0x73, 0x65, 0x6c, 0x4d, 0x65, 0x74,
	0x61, 0x64, 0x61, 0x74, 0x61, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x51, 0x92, 0x41, 0x2d, 0x1a,
	0x2b, 0xe5, 0x88, 0xa0, 0xe9, 0x99, 0xa4, 0x7a, 0x65, 0x75, 0x73, 0xe5, 0xba, 0x93, 0xe5, 0x86,
	0x85, 0xe5, 0xa4, 0xa9, 0xe5, 0x9f, 0xba, 0xe4, 0xba, 0x91, 0xe8, 0x88, 0xb0, 0xe7, 0x89, 0x88,
	0xe6, 0x9c, 0xac, 0xe5, 0x85, 0x83, 0xe6, 0x95, 0xb0, 0xe6, 0x8d, 0xae, 0x82, 0xd3, 0xe4, 0x93,
	0x02, 0x1b, 0x2a, 0x19, 0x2f, 0x63, 0x76, 0x65, 0x73, 0x73, 0x65, 0x6c, 0x56, 0x65, 0x72, 0x73,
	0x69, 0x6f, 0x6e, 0x2f, 0x7b, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x7d, 0x12, 0xaf, 0x02,
	0x0a, 0x19, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x52, 0x65, 0x6c, 0x65, 0x61, 0x73,
	0x65, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x46, 0x69, 0x6c, 0x65, 0x12, 0x35, 0x2e, 0x6a, 0x64,
	0x73, 0x74, 0x61, 0x63, 0x6b, 0x2e, 0x7a, 0x65, 0x75, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x76, 0x65,
	0x72, 0x73, 0x69, 0x6f, 0x6e, 0x2e, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x52, 0x65,
	0x6c, 0x65, 0x61, 0x73, 0x65, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x46, 0x69, 0x6c, 0x65, 0x52,
	0x65, 0x71, 0x1a, 0x38, 0x2e, 0x6a, 0x64, 0x73, 0x74, 0x61, 0x63, 0x6b, 0x2e, 0x7a, 0x65, 0x75,
	0x73, 0x2e, 0x76, 0x31, 0x2e, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x2e, 0x44, 0x65, 0x73,
	0x63, 0x72, 0x69, 0x62, 0x65, 0x52, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x43, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x46, 0x69, 0x6c, 0x65, 0x73, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0xa0, 0x01, 0x92,
	0x41, 0x68, 0x1a, 0x66, 0xe8, 0x8e, 0xb7, 0xe5, 0x8f, 0x96, 0x7a, 0x65, 0x75, 0x73, 0xe5, 0xba,
	0x93, 0xe5, 0x86, 0x85, 0xe5, 0xb0, 0x81, 0xe7, 0x89, 0x88, 0xe7, 0x89, 0x88, 0xe6, 0x9c, 0xac,
	0xe5, 0x86, 0x85, 0xe5, 0x88, 0xb6, 0xe5, 0xae, 0x9a, 0xe9, 0x85, 0x8d, 0xe7, 0xbd, 0xae, 0xe6,
	0x96, 0x87, 0xe4, 0xbb, 0xb6, 0x2c, 0xe7, 0x94, 0xb1, 0xe4, 0xba, 0x8e, 0xe7, 0x9b, 0xb8, 0xe5,
	0xaf, 0xb9, 0xe8, 0xb7, 0xaf, 0xe5, 0xbe, 0x84, 0xe4, 0xb8, 0x8d, 0xe5, 0x90, 0x8c, 0x2c, 0xe7,
	0x9b, 0xb8, 0xe5, 0x90, 0x8c, 0xe5, 0x90, 0x8d, 0xe7, 0xa7, 0xb0, 0xe5, 0x8f, 0xaf, 0xe8, 0x83,
	0xbd, 0xe6, 0x98, 0xaf, 0xe5, 0xa4, 0x9a, 0xe4, 0xbb, 0xbd, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x2f,
	0x12, 0x2d, 0x2f, 0x72, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f,
	0x6e, 0x2f, 0x7b, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x7d, 0x2f, 0x66, 0x69, 0x6c, 0x65,
	0x4e, 0x61, 0x6d, 0x65, 0x2f, 0x7b, 0x66, 0x69, 0x6c, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x7d, 0x12,
	0xa7, 0x02, 0x0a, 0x12, 0x4d, 0x6f, 0x64, 0x69, 0x66, 0x79, 0x44, 0x65, 0x70, 0x65, 0x6e, 0x64,
	0x65, 0x6e, 0x63, 0x69, 0x65, 0x73, 0x12, 0x2e, 0x2e, 0x6a, 0x64, 0x73, 0x74, 0x61, 0x63, 0x6b,
	0x2e, 0x7a, 0x65, 0x75, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e,
	0x2e, 0x4d, 0x6f, 0x64, 0x69, 0x66, 0x79, 0x44, 0x65, 0x70, 0x65, 0x6e, 0x64, 0x65, 0x6e, 0x63,
	0x69, 0x65, 0x73, 0x52, 0x65, 0x71, 0x1a, 0x30, 0x2e, 0x6a, 0x64, 0x73, 0x74, 0x61, 0x63, 0x6b,
	0x2e, 0x7a, 0x65, 0x75, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e,
	0x2e, 0x4d, 0x6f, 0x64, 0x69, 0x66, 0x79, 0x44, 0x65, 0x70, 0x65, 0x6e, 0x64, 0x65, 0x6e, 0x63,
	0x69, 0x65, 0x73, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0xae, 0x01, 0x92, 0x41, 0x7b, 0x1a, 0x79,
	0xe5, 0x85, 0xa8, 0xe9, 0x87, 0x8f, 0xe6, 0x9b, 0xb4, 0xe6, 0x96, 0xb0, 0xe7, 0x89, 0x88, 0xe6,
	0x9c, 0xac, 0xe5, 0x86, 0x85, 0xe4, 0xba, 0xa7, 0xe5, 0x93, 0x81, 0xe9, 0x97, 0xb4, 0xe9, 0x83,
	0xa8, 0xe7, 0xbd, 0xb2, 0xe4, 0xbe, 0x9d, 0xe8, 0xb5, 0x96, 0xe4, 0xbf, 0xa1, 0xe6, 0x81, 0xaf,
	0x2c, 0xe4, 0xbb, 0x85, 0xe9, 0x99, 0x90, 0xe4, 0xba, 0x91, 0xe8, 0x88, 0xb0, 0xe7, 0x89, 0x88,
	0xe6, 0x9c, 0xac, 0xe4, 0xb8, 0x94, 0xe4, 0xbb, 0x85, 0xe5, 0xa4, 0x84, 0xe7, 0x90, 0x86, 0xe4,
	0xb8, 0xbb, 0xe9, 0x9b, 0x86, 0xe7, 0xbe, 0xa4, 0x2c, 0xe6, 0x9c, 0x80, 0xe7, 0xbb, 0x88, 0xe6,
	0x95, 0xb0, 0xe6, 0x8d, 0xae, 0xe4, 0xbc, 0x9a, 0xe6, 0xb3, 0xa8, 0xe5, 0x85, 0xa5, 0x67, 0x6f,
	0x6f, 0x64, 0x73, 0x5f, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x2a, 0x3a,
	0x01, 0x2a, 0x22, 0x25, 0x2f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x2f, 0x7b, 0x76, 0x65,
	0x72, 0x73, 0x69, 0x6f, 0x6e, 0x7d, 0x2f, 0x6d, 0x6f, 0x64, 0x69, 0x66, 0x79, 0x44, 0x65, 0x70,
	0x65, 0x6e, 0x64, 0x65, 0x6e, 0x63, 0x69, 0x65, 0x73, 0x12, 0xd3, 0x01, 0x0a, 0x13, 0x43, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x53, 0x71, 0x6c, 0x45, 0x78, 0x70, 0x6f, 0x72, 0x74, 0x54, 0x6f, 0x53,
	0x33, 0x12, 0x2f, 0x2e, 0x6a, 0x64, 0x73, 0x74, 0x61, 0x63, 0x6b, 0x2e, 0x7a, 0x65, 0x75, 0x73,
	0x2e, 0x76, 0x31, 0x2e, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x2e, 0x43, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x53, 0x71, 0x6c, 0x45, 0x78, 0x70, 0x6f, 0x72, 0x74, 0x54, 0x6f, 0x53, 0x33, 0x52,
	0x65, 0x71, 0x1a, 0x31, 0x2e, 0x6a, 0x64, 0x73, 0x74, 0x61, 0x63, 0x6b, 0x2e, 0x7a, 0x65, 0x75,
	0x73, 0x2e, 0x76, 0x31, 0x2e, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x2e, 0x43, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x53, 0x71, 0x6c, 0x45, 0x78, 0x70, 0x6f, 0x72, 0x74, 0x54, 0x6f, 0x53, 0x33,
	0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x58, 0x92, 0x41, 0x2e, 0x1a, 0x2c, 0xe5, 0xaf, 0xbc, 0xe5,
	0x87, 0xba, 0xe6, 0x8c, 0x87, 0xe5, 0xae, 0x9a, 0xe7, 0x89, 0x88, 0xe6, 0x9c, 0xac, 0xe7, 0x9a,
	0x84, 0x53, 0x51, 0x4c, 0xe6, 0x96, 0x87, 0xe4, 0xbb, 0xb6, 0xe5, 0xb9, 0xb6, 0xe4, 0xb8, 0x8a,
	0xe4, 0xbc, 0xa0, 0xe8, 0x87, 0xb3, 0x53, 0x33, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x21, 0x3a, 0x01,
	0x2a, 0x22, 0x1c, 0x2f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x2f, 0x7b, 0x76, 0x65, 0x72,
	0x73, 0x69, 0x6f, 0x6e, 0x7d, 0x2f, 0x65, 0x78, 0x70, 0x6f, 0x72, 0x74, 0x53, 0x71, 0x6c, 0x12,
	0xc7, 0x02, 0x0a, 0x19, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x44, 0x65, 0x70, 0x6c,
	0x6f, 0x79, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x35, 0x2e,
	0x6a, 0x64, 0x73, 0x74, 0x61, 0x63, 0x6b, 0x2e, 0x7a, 0x65, 0x75, 0x73, 0x2e, 0x76, 0x31, 0x2e,
	0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x2e, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65,
	0x44, 0x65, 0x70, 0x6c, 0x6f, 0x79, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x49, 0x6e, 0x66,
	0x6f, 0x52, 0x65, 0x71, 0x1a, 0x37, 0x2e, 0x6a, 0x64, 0x73, 0x74, 0x61, 0x63, 0x6b, 0x2e, 0x7a,
	0x65, 0x75, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x2e, 0x44,
	0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x44, 0x65, 0x70, 0x6c, 0x6f, 0x79, 0x56, 0x65, 0x72,
	0x73, 0x69, 0x6f, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0xb9, 0x01,
	0x92, 0x41, 0x93, 0x01, 0x1a, 0x90, 0x01, 0xe8, 0x8e, 0xb7, 0xe5, 0x8f, 0x96, 0xe7, 0xa6, 0xbb,
	0xe7, 0xba, 0xbf, 0xe4, 0xba, 0xa4, 0xe4, 0xbb, 0x98, 0xe5, 0x9c, 0xba, 0xe6, 0x99, 0xaf, 0xe4,
	0xb8, 0x8b, 0x2c, 0xe6, 0x83, 0xb3, 0xe8, 0xa6, 0x81, 0xe4, 0xba, 0xa4, 0xe4, 0xbb, 0x98, 0xe7,
	0x9a, 0x84, 0xe7, 0x89, 0x88, 0xe6, 0x9c, 0xac, 0xe4, 0xbf, 0xa1, 0xe6, 0x81, 0xaf, 0x2c, 0xe5,
	0x89, 0x8d, 0xe6, 0x8f, 0x90, 0xe6, 0x98, 0xaf, 0xe7, 0xa6, 0xbb, 0xe7, 0xba, 0xbf, 0xe6, 0x95,
	0xb0, 0xe6, 0x8d, 0xae, 0xe5, 0xba, 0x93, 0xe4, 0xb8, 0xad, 0xe4, 0xbb, 0x85, 0xe5, 0x8c, 0x85,
	0xe5, 0x90, 0xab, 0xe5, 0x8d, 0x95, 0xe4, 0xb8, 0xaa, 0xe7, 0x89, 0x88, 0xe6, 0x9c, 0xac, 0xe7,
	0x9a, 0x84, 0xe6, 0x95, 0xb0, 0xe6, 0x8d, 0xae, 0x2c, 0xe6, 0x88, 0x96, 0xe5, 0x8c, 0x85, 0xe5,
	0x90, 0xab, 0xe5, 0x89, 0x8d, 0xe5, 0xba, 0x8f, 0xe7, 0x89, 0x88, 0xe6, 0x9c, 0xac, 0xe7, 0x9a,
	0x84, 0xe6, 0x95, 0xb0, 0xe6, 0x8d, 0xae, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x1c, 0x12, 0x1a, 0x2f,
	0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x44, 0x65, 0x70, 0x6c, 0x6f, 0x79, 0x56, 0x65,
	0x72, 0x73, 0x69, 0x6f, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x42, 0x84, 0x01, 0x92, 0x41, 0x3c, 0x12,
	0x21, 0x0a, 0x1b, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x4a, 0x44, 0x53, 0x74, 0x61,
	0x63, 0x6b, 0xe7, 0x89, 0x88, 0xe6, 0x9c, 0xac, 0xe4, 0xbf, 0xa1, 0xe6, 0x81, 0xaf, 0x32, 0x02,
	0x76, 0x31, 0x1a, 0x0f, 0x6c, 0x6f, 0x63, 0x61, 0x6c, 0x68, 0x6f, 0x73, 0x74, 0x3a, 0x33, 0x35,
	0x37, 0x31, 0x31, 0x22, 0x03, 0x2f, 0x76, 0x31, 0x2a, 0x01, 0x01, 0x5a, 0x43, 0x63, 0x6f, 0x64,
	0x69, 0x6e, 0x67, 0x2e, 0x6a, 0x64, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x66, 0x61, 0x62, 0x72, 0x69,
	0x63, 0x2f, 0x7a, 0x65, 0x75, 0x73, 0x56, 0x32, 0x2f, 0x7a, 0x65, 0x75, 0x73, 0x2f, 0x61, 0x70,
	0x69, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2f, 0x70, 0x62, 0x2f, 0x76, 0x65, 0x72, 0x73,
	0x69, 0x6f, 0x6e, 0x2f, 0x76, 0x31, 0x3b, 0x70, 0x62, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e,
	0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_version_proto_rawDescOnce sync.Once
	file_version_proto_rawDescData = file_version_proto_rawDesc
)

func file_version_proto_rawDescGZIP() []byte {
	file_version_proto_rawDescOnce.Do(func() {
		file_version_proto_rawDescData = protoimpl.X.CompressGZIP(file_version_proto_rawDescData)
	})
	return file_version_proto_rawDescData
}

var file_version_proto_msgTypes = make([]protoimpl.MessageInfo, 28)
var file_version_proto_goTypes = []interface{}{
	(*CreateReq)(nil),                       // 0: jdstack.zeus.v1.version.CreateReq
	(*ModifyReq)(nil),                       // 1: jdstack.zeus.v1.version.ModifyReq
	(*DescribeReq)(nil),                     // 2: jdstack.zeus.v1.version.DescribeReq
	(*DescribeReply)(nil),                   // 3: jdstack.zeus.v1.version.DescribeReply
	(*DescribesReq)(nil),                    // 4: jdstack.zeus.v1.version.DescribesReq
	(*DescribesReply)(nil),                  // 5: jdstack.zeus.v1.version.DescribesReply
	(*CreateCvesselReq)(nil),                // 6: jdstack.zeus.v1.version.CreateCvesselReq
	(*ModifyCvesselReq)(nil),                // 7: jdstack.zeus.v1.version.ModifyCvesselReq
	(*DescribeCvesselReq)(nil),              // 8: jdstack.zeus.v1.version.DescribeCvesselReq
	(*DescribeCvesselReply)(nil),            // 9: jdstack.zeus.v1.version.DescribeCvesselReply
	(*DescribesCvesselReq)(nil),             // 10: jdstack.zeus.v1.version.DescribesCvesselReq
	(*DescribesCvesselReply)(nil),           // 11: jdstack.zeus.v1.version.DescribesCvesselReply
	(*DescribeDeployVersionInfoReq)(nil),    // 12: jdstack.zeus.v1.version.DescribeDeployVersionInfoReq
	(*DescribeDeployVersionInfoReply)(nil),  // 13: jdstack.zeus.v1.version.DescribeDeployVersionInfoReply
	(*CreateCvesselMetadataReq)(nil),        // 14: jdstack.zeus.v1.version.CreateCvesselMetadataReq
	(*ModifyCvesselMetadataReq)(nil),        // 15: jdstack.zeus.v1.version.ModifyCvesselMetadataReq
	(*DeleteCvesselMetadataReq)(nil),        // 16: jdstack.zeus.v1.version.DeleteCvesselMetadataReq
	(*CreateCvesselMetadataReply)(nil),      // 17: jdstack.zeus.v1.version.CreateCvesselMetadataReply
	(*ModifyCvesselMetadataReply)(nil),      // 18: jdstack.zeus.v1.version.ModifyCvesselMetadataReply
	(*DeleteCvesselMetadataReply)(nil),      // 19: jdstack.zeus.v1.version.DeleteCvesselMetadataReply
	(*DescribeReleaseConfigFileReq)(nil),    // 20: jdstack.zeus.v1.version.DescribeReleaseConfigFileReq
	(*DescribeReleaseConfigFileReply)(nil),  // 21: jdstack.zeus.v1.version.DescribeReleaseConfigFileReply
	(*DescribeReleaseConfigFilesReply)(nil), // 22: jdstack.zeus.v1.version.DescribeReleaseConfigFilesReply
	(*ModifyDependenciesReq)(nil),           // 23: jdstack.zeus.v1.version.ModifyDependenciesReq
	(*ProductInfo)(nil),                     // 24: jdstack.zeus.v1.version.ProductInfo
	(*ModifyDependenciesReply)(nil),         // 25: jdstack.zeus.v1.version.ModifyDependenciesReply
	(*CreateSqlExportToS3Req)(nil),          // 26: jdstack.zeus.v1.version.CreateSqlExportToS3Req
	(*CreateSqlExportToS3Reply)(nil),        // 27: jdstack.zeus.v1.version.CreateSqlExportToS3Reply
	(*winapi.Filter)(nil),                   // 28: wingo.api.Filter
	(*winapi.Sort)(nil),                     // 29: wingo.api.Sort
	(*empty.Empty)(nil),                     // 30: google.protobuf.Empty
}
var file_version_proto_depIdxs = []int32{
	28, // 0: jdstack.zeus.v1.version.DescribesReq.filters:type_name -> wingo.api.Filter
	29, // 1: jdstack.zeus.v1.version.DescribesReq.sorts:type_name -> wingo.api.Sort
	3,  // 2: jdstack.zeus.v1.version.DescribesReply.list:type_name -> jdstack.zeus.v1.version.DescribeReply
	3,  // 3: jdstack.zeus.v1.version.DescribeCvesselReply.jdstackVersion:type_name -> jdstack.zeus.v1.version.DescribeReply
	28, // 4: jdstack.zeus.v1.version.DescribesCvesselReq.filters:type_name -> wingo.api.Filter
	29, // 5: jdstack.zeus.v1.version.DescribesCvesselReq.sorts:type_name -> wingo.api.Sort
	9,  // 6: jdstack.zeus.v1.version.DescribesCvesselReply.list:type_name -> jdstack.zeus.v1.version.DescribeCvesselReply
	3,  // 7: jdstack.zeus.v1.version.DescribeDeployVersionInfoReply.deployVersion:type_name -> jdstack.zeus.v1.version.DescribeReply
	3,  // 8: jdstack.zeus.v1.version.DescribeDeployVersionInfoReply.deployVersionPrev:type_name -> jdstack.zeus.v1.version.DescribeReply
	21, // 9: jdstack.zeus.v1.version.DescribeReleaseConfigFilesReply.list:type_name -> jdstack.zeus.v1.version.DescribeReleaseConfigFileReply
	24, // 10: jdstack.zeus.v1.version.ModifyDependenciesReq.products:type_name -> jdstack.zeus.v1.version.ProductInfo
	0,  // 11: jdstack.zeus.v1.version.Version.Create:input_type -> jdstack.zeus.v1.version.CreateReq
	1,  // 12: jdstack.zeus.v1.version.Version.Modify:input_type -> jdstack.zeus.v1.version.ModifyReq
	2,  // 13: jdstack.zeus.v1.version.Version.Describe:input_type -> jdstack.zeus.v1.version.DescribeReq
	4,  // 14: jdstack.zeus.v1.version.Version.Describes:input_type -> jdstack.zeus.v1.version.DescribesReq
	6,  // 15: jdstack.zeus.v1.version.Version.CreateCvessel:input_type -> jdstack.zeus.v1.version.CreateCvesselReq
	7,  // 16: jdstack.zeus.v1.version.Version.ModifyCvessel:input_type -> jdstack.zeus.v1.version.ModifyCvesselReq
	8,  // 17: jdstack.zeus.v1.version.Version.DescribeCvessel:input_type -> jdstack.zeus.v1.version.DescribeCvesselReq
	10, // 18: jdstack.zeus.v1.version.Version.DescribesCvessel:input_type -> jdstack.zeus.v1.version.DescribesCvesselReq
	14, // 19: jdstack.zeus.v1.version.Version.CreateCvesselMetadata:input_type -> jdstack.zeus.v1.version.CreateCvesselMetadataReq
	15, // 20: jdstack.zeus.v1.version.Version.ModifyCvesselMetadata:input_type -> jdstack.zeus.v1.version.ModifyCvesselMetadataReq
	16, // 21: jdstack.zeus.v1.version.Version.DeleteCvesselMetadata:input_type -> jdstack.zeus.v1.version.DeleteCvesselMetadataReq
	20, // 22: jdstack.zeus.v1.version.Version.DescribeReleaseConfigFile:input_type -> jdstack.zeus.v1.version.DescribeReleaseConfigFileReq
	23, // 23: jdstack.zeus.v1.version.Version.ModifyDependencies:input_type -> jdstack.zeus.v1.version.ModifyDependenciesReq
	26, // 24: jdstack.zeus.v1.version.Version.CreateSqlExportToS3:input_type -> jdstack.zeus.v1.version.CreateSqlExportToS3Req
	12, // 25: jdstack.zeus.v1.version.Version.DescribeDeployVersionInfo:input_type -> jdstack.zeus.v1.version.DescribeDeployVersionInfoReq
	3,  // 26: jdstack.zeus.v1.version.Version.Create:output_type -> jdstack.zeus.v1.version.DescribeReply
	30, // 27: jdstack.zeus.v1.version.Version.Modify:output_type -> google.protobuf.Empty
	3,  // 28: jdstack.zeus.v1.version.Version.Describe:output_type -> jdstack.zeus.v1.version.DescribeReply
	5,  // 29: jdstack.zeus.v1.version.Version.Describes:output_type -> jdstack.zeus.v1.version.DescribesReply
	9,  // 30: jdstack.zeus.v1.version.Version.CreateCvessel:output_type -> jdstack.zeus.v1.version.DescribeCvesselReply
	30, // 31: jdstack.zeus.v1.version.Version.ModifyCvessel:output_type -> google.protobuf.Empty
	9,  // 32: jdstack.zeus.v1.version.Version.DescribeCvessel:output_type -> jdstack.zeus.v1.version.DescribeCvesselReply
	11, // 33: jdstack.zeus.v1.version.Version.DescribesCvessel:output_type -> jdstack.zeus.v1.version.DescribesCvesselReply
	17, // 34: jdstack.zeus.v1.version.Version.CreateCvesselMetadata:output_type -> jdstack.zeus.v1.version.CreateCvesselMetadataReply
	18, // 35: jdstack.zeus.v1.version.Version.ModifyCvesselMetadata:output_type -> jdstack.zeus.v1.version.ModifyCvesselMetadataReply
	19, // 36: jdstack.zeus.v1.version.Version.DeleteCvesselMetadata:output_type -> jdstack.zeus.v1.version.DeleteCvesselMetadataReply
	22, // 37: jdstack.zeus.v1.version.Version.DescribeReleaseConfigFile:output_type -> jdstack.zeus.v1.version.DescribeReleaseConfigFilesReply
	25, // 38: jdstack.zeus.v1.version.Version.ModifyDependencies:output_type -> jdstack.zeus.v1.version.ModifyDependenciesReply
	27, // 39: jdstack.zeus.v1.version.Version.CreateSqlExportToS3:output_type -> jdstack.zeus.v1.version.CreateSqlExportToS3Reply
	13, // 40: jdstack.zeus.v1.version.Version.DescribeDeployVersionInfo:output_type -> jdstack.zeus.v1.version.DescribeDeployVersionInfoReply
	26, // [26:41] is the sub-list for method output_type
	11, // [11:26] is the sub-list for method input_type
	11, // [11:11] is the sub-list for extension type_name
	11, // [11:11] is the sub-list for extension extendee
	0,  // [0:11] is the sub-list for field type_name
}

func init() { file_version_proto_init() }
func file_version_proto_init() {
	if File_version_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_version_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_version_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ModifyReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_version_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DescribeReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_version_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DescribeReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_version_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DescribesReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_version_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DescribesReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_version_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateCvesselReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_version_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ModifyCvesselReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_version_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DescribeCvesselReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_version_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DescribeCvesselReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_version_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DescribesCvesselReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_version_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DescribesCvesselReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_version_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DescribeDeployVersionInfoReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_version_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DescribeDeployVersionInfoReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_version_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateCvesselMetadataReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_version_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ModifyCvesselMetadataReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_version_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteCvesselMetadataReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_version_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateCvesselMetadataReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_version_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ModifyCvesselMetadataReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_version_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteCvesselMetadataReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_version_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DescribeReleaseConfigFileReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_version_proto_msgTypes[21].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DescribeReleaseConfigFileReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_version_proto_msgTypes[22].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DescribeReleaseConfigFilesReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_version_proto_msgTypes[23].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ModifyDependenciesReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_version_proto_msgTypes[24].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ProductInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_version_proto_msgTypes[25].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ModifyDependenciesReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_version_proto_msgTypes[26].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateSqlExportToS3Req); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_version_proto_msgTypes[27].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateSqlExportToS3Reply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_version_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   28,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_version_proto_goTypes,
		DependencyIndexes: file_version_proto_depIdxs,
		MessageInfos:      file_version_proto_msgTypes,
	}.Build()
	File_version_proto = out.File
	file_version_proto_rawDesc = nil
	file_version_proto_goTypes = nil
	file_version_proto_depIdxs = nil
}
