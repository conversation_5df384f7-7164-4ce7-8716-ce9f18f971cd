// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: goods_stack.proto

package pbGoodsStack

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on CreateReq with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *CreateReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateReq with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in CreateReqMultiError, or nil
// if none found.
func (m *CreateReq) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	if len(m.GetVersion()) < 1 {
		err := CreateReqValidationError{
			field:  "Version",
			reason: "value length must be at least 1 bytes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if !_CreateReq_Version_Pattern.MatchString(m.GetVersion()) {
		err := CreateReqValidationError{
			field:  "Version",
			reason: "value does not match regex pattern \"^[0-9a-zA-Z.]+$\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetMetadata()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateReqValidationError{
					field:  "Metadata",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateReqValidationError{
					field:  "Metadata",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetMetadata()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateReqValidationError{
				field:  "Metadata",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Category

	// no validation rules for Service

	// no validation rules for Resource

	// no validation rules for Application

	if m.GetAction() != "" {

		if _, ok := _CreateReq_Action_InLookup[m.GetAction()]; !ok {
			err := CreateReqValidationError{
				field:  "Action",
				reason: "value must be in list [debug online offline upgrade]",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

	}

	if m.GetVirtualGoods() != "" {

		if _, ok := _CreateReq_VirtualGoods_InLookup[m.GetVirtualGoods()]; !ok {
			err := CreateReqValidationError{
				field:  "VirtualGoods",
				reason: "value must be in list [no yes]",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

	}

	if m.GetCloudType() != "" {

		if _, ok := _CreateReq_CloudType_InLookup[m.GetCloudType()]; !ok {
			err := CreateReqValidationError{
				field:  "CloudType",
				reason: "value must be in list [jdstack cvessel]",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

	}

	for idx, item := range m.GetFilecenter() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, CreateReqValidationError{
						field:  fmt.Sprintf("Filecenter[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, CreateReqValidationError{
						field:  fmt.Sprintf("Filecenter[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return CreateReqValidationError{
					field:  fmt.Sprintf("Filecenter[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	for idx, item := range m.GetDelivery() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, CreateReqValidationError{
						field:  fmt.Sprintf("Delivery[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, CreateReqValidationError{
						field:  fmt.Sprintf("Delivery[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return CreateReqValidationError{
					field:  fmt.Sprintf("Delivery[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	for idx, item := range m.GetDomain() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, CreateReqValidationError{
						field:  fmt.Sprintf("Domain[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, CreateReqValidationError{
						field:  fmt.Sprintf("Domain[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return CreateReqValidationError{
					field:  fmt.Sprintf("Domain[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	for idx, item := range m.GetFlavor() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, CreateReqValidationError{
						field:  fmt.Sprintf("Flavor[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, CreateReqValidationError{
						field:  fmt.Sprintf("Flavor[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return CreateReqValidationError{
					field:  fmt.Sprintf("Flavor[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	for idx, item := range m.GetIac() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, CreateReqValidationError{
						field:  fmt.Sprintf("Iac[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, CreateReqValidationError{
						field:  fmt.Sprintf("Iac[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return CreateReqValidationError{
					field:  fmt.Sprintf("Iac[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	for idx, item := range m.GetMiddleware() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, CreateReqValidationError{
						field:  fmt.Sprintf("Middleware[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, CreateReqValidationError{
						field:  fmt.Sprintf("Middleware[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return CreateReqValidationError{
					field:  fmt.Sprintf("Middleware[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	for idx, item := range m.GetHardwareRole() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, CreateReqValidationError{
						field:  fmt.Sprintf("HardwareRole[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, CreateReqValidationError{
						field:  fmt.Sprintf("HardwareRole[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return CreateReqValidationError{
					field:  fmt.Sprintf("HardwareRole[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	for idx, item := range m.GetHardwareRoleLabel() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, CreateReqValidationError{
						field:  fmt.Sprintf("HardwareRoleLabel[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, CreateReqValidationError{
						field:  fmt.Sprintf("HardwareRoleLabel[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return CreateReqValidationError{
					field:  fmt.Sprintf("HardwareRoleLabel[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if all {
		switch v := interface{}(m.GetPd()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateReqValidationError{
					field:  "Pd",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateReqValidationError{
					field:  "Pd",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPd()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateReqValidationError{
				field:  "Pd",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetBusinessMetadata()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateReqValidationError{
					field:  "BusinessMetadata",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateReqValidationError{
					field:  "BusinessMetadata",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBusinessMetadata()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateReqValidationError{
				field:  "BusinessMetadata",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetAdl() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, CreateReqValidationError{
						field:  fmt.Sprintf("Adl[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, CreateReqValidationError{
						field:  fmt.Sprintf("Adl[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return CreateReqValidationError{
					field:  fmt.Sprintf("Adl[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	for idx, item := range m.GetConfig() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, CreateReqValidationError{
						field:  fmt.Sprintf("Config[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, CreateReqValidationError{
						field:  fmt.Sprintf("Config[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return CreateReqValidationError{
					field:  fmt.Sprintf("Config[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	for idx, item := range m.GetDeliveryRuleGroup() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, CreateReqValidationError{
						field:  fmt.Sprintf("DeliveryRuleGroup[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, CreateReqValidationError{
						field:  fmt.Sprintf("DeliveryRuleGroup[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return CreateReqValidationError{
					field:  fmt.Sprintf("DeliveryRuleGroup[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return CreateReqMultiError(errors)
	}

	return nil
}

// CreateReqMultiError is an error wrapping multiple validation errors returned
// by CreateReq.ValidateAll() if the designated constraints aren't met.
type CreateReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateReqMultiError) AllErrors() []error { return m }

// CreateReqValidationError is the validation error returned by
// CreateReq.Validate if the designated constraints aren't met.
type CreateReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateReqValidationError) ErrorName() string { return "CreateReqValidationError" }

// Error satisfies the builtin error interface
func (e CreateReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateReqValidationError{}

var _CreateReq_Version_Pattern = regexp.MustCompile("^[0-9a-zA-Z.]+$")

var _CreateReq_Action_InLookup = map[string]struct{}{
	"debug":   {},
	"online":  {},
	"offline": {},
	"upgrade": {},
}

var _CreateReq_VirtualGoods_InLookup = map[string]struct{}{
	"no":  {},
	"yes": {},
}

var _CreateReq_CloudType_InLookup = map[string]struct{}{
	"jdstack": {},
	"cvessel": {},
}

// Validate checks the field values on DescribeReq with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *DescribeReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DescribeReq with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in DescribeReqMultiError, or
// nil if none found.
func (m *DescribeReq) ValidateAll() error {
	return m.validate(true)
}

func (m *DescribeReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	if len(m.GetVersion()) < 1 {
		err := DescribeReqValidationError{
			field:  "Version",
			reason: "value length must be at least 1 bytes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if !_DescribeReq_Version_Pattern.MatchString(m.GetVersion()) {
		err := DescribeReqValidationError{
			field:  "Version",
			reason: "value does not match regex pattern \"^[0-9a-zA-Z.]+$\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for Category

	// no validation rules for Service

	// no validation rules for Resource

	// no validation rules for Application

	for idx, item := range m.GetFilters() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, DescribeReqValidationError{
						field:  fmt.Sprintf("Filters[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, DescribeReqValidationError{
						field:  fmt.Sprintf("Filters[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return DescribeReqValidationError{
					field:  fmt.Sprintf("Filters[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if m.GetCloudType() != "" {

		if _, ok := _DescribeReq_CloudType_InLookup[m.GetCloudType()]; !ok {
			err := DescribeReqValidationError{
				field:  "CloudType",
				reason: "value must be in list [jdstack cvessel]",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

	}

	if len(errors) > 0 {
		return DescribeReqMultiError(errors)
	}

	return nil
}

// DescribeReqMultiError is an error wrapping multiple validation errors
// returned by DescribeReq.ValidateAll() if the designated constraints aren't met.
type DescribeReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DescribeReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DescribeReqMultiError) AllErrors() []error { return m }

// DescribeReqValidationError is the validation error returned by
// DescribeReq.Validate if the designated constraints aren't met.
type DescribeReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DescribeReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DescribeReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DescribeReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DescribeReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DescribeReqValidationError) ErrorName() string { return "DescribeReqValidationError" }

// Error satisfies the builtin error interface
func (e DescribeReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDescribeReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DescribeReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DescribeReqValidationError{}

var _DescribeReq_Version_Pattern = regexp.MustCompile("^[0-9a-zA-Z.]+$")

var _DescribeReq_CloudType_InLookup = map[string]struct{}{
	"jdstack": {},
	"cvessel": {},
}

// Validate checks the field values on DescribeReply with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *DescribeReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DescribeReply with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in DescribeReplyMultiError, or
// nil if none found.
func (m *DescribeReply) ValidateAll() error {
	return m.validate(true)
}

func (m *DescribeReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	if all {
		switch v := interface{}(m.GetVersion()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DescribeReplyValidationError{
					field:  "Version",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DescribeReplyValidationError{
					field:  "Version",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetVersion()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DescribeReplyValidationError{
				field:  "Version",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetCategory()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DescribeReplyValidationError{
					field:  "Category",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DescribeReplyValidationError{
					field:  "Category",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCategory()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DescribeReplyValidationError{
				field:  "Category",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetService()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DescribeReplyValidationError{
					field:  "Service",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DescribeReplyValidationError{
					field:  "Service",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetService()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DescribeReplyValidationError{
				field:  "Service",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetResource()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DescribeReplyValidationError{
					field:  "Resource",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DescribeReplyValidationError{
					field:  "Resource",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetResource()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DescribeReplyValidationError{
				field:  "Resource",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetApplication()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DescribeReplyValidationError{
					field:  "Application",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DescribeReplyValidationError{
					field:  "Application",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetApplication()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DescribeReplyValidationError{
				field:  "Application",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Action

	// no validation rules for VirtualGoods

	// no validation rules for CloudType

	for idx, item := range m.GetFilecenter() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, DescribeReplyValidationError{
						field:  fmt.Sprintf("Filecenter[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, DescribeReplyValidationError{
						field:  fmt.Sprintf("Filecenter[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return DescribeReplyValidationError{
					field:  fmt.Sprintf("Filecenter[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	for idx, item := range m.GetDelivery() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, DescribeReplyValidationError{
						field:  fmt.Sprintf("Delivery[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, DescribeReplyValidationError{
						field:  fmt.Sprintf("Delivery[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return DescribeReplyValidationError{
					field:  fmt.Sprintf("Delivery[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	for idx, item := range m.GetDomain() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, DescribeReplyValidationError{
						field:  fmt.Sprintf("Domain[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, DescribeReplyValidationError{
						field:  fmt.Sprintf("Domain[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return DescribeReplyValidationError{
					field:  fmt.Sprintf("Domain[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	for idx, item := range m.GetFlavor() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, DescribeReplyValidationError{
						field:  fmt.Sprintf("Flavor[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, DescribeReplyValidationError{
						field:  fmt.Sprintf("Flavor[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return DescribeReplyValidationError{
					field:  fmt.Sprintf("Flavor[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	for idx, item := range m.GetIac() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, DescribeReplyValidationError{
						field:  fmt.Sprintf("Iac[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, DescribeReplyValidationError{
						field:  fmt.Sprintf("Iac[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return DescribeReplyValidationError{
					field:  fmt.Sprintf("Iac[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	for idx, item := range m.GetMiddleware() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, DescribeReplyValidationError{
						field:  fmt.Sprintf("Middleware[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, DescribeReplyValidationError{
						field:  fmt.Sprintf("Middleware[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return DescribeReplyValidationError{
					field:  fmt.Sprintf("Middleware[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	for idx, item := range m.GetHardwareRole() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, DescribeReplyValidationError{
						field:  fmt.Sprintf("HardwareRole[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, DescribeReplyValidationError{
						field:  fmt.Sprintf("HardwareRole[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return DescribeReplyValidationError{
					field:  fmt.Sprintf("HardwareRole[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	for idx, item := range m.GetHardwareRoleLabel() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, DescribeReplyValidationError{
						field:  fmt.Sprintf("HardwareRoleLabel[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, DescribeReplyValidationError{
						field:  fmt.Sprintf("HardwareRoleLabel[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return DescribeReplyValidationError{
					field:  fmt.Sprintf("HardwareRoleLabel[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	for idx, item := range m.GetChain() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, DescribeReplyValidationError{
						field:  fmt.Sprintf("Chain[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, DescribeReplyValidationError{
						field:  fmt.Sprintf("Chain[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return DescribeReplyValidationError{
					field:  fmt.Sprintf("Chain[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	for idx, item := range m.GetChainFrom() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, DescribeReplyValidationError{
						field:  fmt.Sprintf("ChainFrom[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, DescribeReplyValidationError{
						field:  fmt.Sprintf("ChainFrom[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return DescribeReplyValidationError{
					field:  fmt.Sprintf("ChainFrom[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	for idx, item := range m.GetGoodsDeliveryRuleGroup() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, DescribeReplyValidationError{
						field:  fmt.Sprintf("GoodsDeliveryRuleGroup[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, DescribeReplyValidationError{
						field:  fmt.Sprintf("GoodsDeliveryRuleGroup[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return DescribeReplyValidationError{
					field:  fmt.Sprintf("GoodsDeliveryRuleGroup[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if all {
		switch v := interface{}(m.GetPd()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DescribeReplyValidationError{
					field:  "Pd",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DescribeReplyValidationError{
					field:  "Pd",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPd()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DescribeReplyValidationError{
				field:  "Pd",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetUpgrade()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DescribeReplyValidationError{
					field:  "Upgrade",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DescribeReplyValidationError{
					field:  "Upgrade",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUpgrade()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DescribeReplyValidationError{
				field:  "Upgrade",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetMetadata() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, DescribeReplyValidationError{
						field:  fmt.Sprintf("Metadata[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, DescribeReplyValidationError{
						field:  fmt.Sprintf("Metadata[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return DescribeReplyValidationError{
					field:  fmt.Sprintf("Metadata[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	for idx, item := range m.GetConfig() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, DescribeReplyValidationError{
						field:  fmt.Sprintf("Config[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, DescribeReplyValidationError{
						field:  fmt.Sprintf("Config[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return DescribeReplyValidationError{
					field:  fmt.Sprintf("Config[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return DescribeReplyMultiError(errors)
	}

	return nil
}

// DescribeReplyMultiError is an error wrapping multiple validation errors
// returned by DescribeReply.ValidateAll() if the designated constraints
// aren't met.
type DescribeReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DescribeReplyMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DescribeReplyMultiError) AllErrors() []error { return m }

// DescribeReplyValidationError is the validation error returned by
// DescribeReply.Validate if the designated constraints aren't met.
type DescribeReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DescribeReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DescribeReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DescribeReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DescribeReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DescribeReplyValidationError) ErrorName() string { return "DescribeReplyValidationError" }

// Error satisfies the builtin error interface
func (e DescribeReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDescribeReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DescribeReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DescribeReplyValidationError{}

// Validate checks the field values on Reply with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *Reply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Reply with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in ReplyMultiError, or nil if none found.
func (m *Reply) ValidateAll() error {
	return m.validate(true)
}

func (m *Reply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	if all {
		switch v := interface{}(m.GetVersion()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ReplyValidationError{
					field:  "Version",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ReplyValidationError{
					field:  "Version",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetVersion()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ReplyValidationError{
				field:  "Version",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetCategory()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ReplyValidationError{
					field:  "Category",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ReplyValidationError{
					field:  "Category",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCategory()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ReplyValidationError{
				field:  "Category",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetService()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ReplyValidationError{
					field:  "Service",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ReplyValidationError{
					field:  "Service",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetService()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ReplyValidationError{
				field:  "Service",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetResource()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ReplyValidationError{
					field:  "Resource",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ReplyValidationError{
					field:  "Resource",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetResource()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ReplyValidationError{
				field:  "Resource",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetApplication()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ReplyValidationError{
					field:  "Application",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ReplyValidationError{
					field:  "Application",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetApplication()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ReplyValidationError{
				field:  "Application",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Action

	// no validation rules for VirtualGoods

	// no validation rules for CloudType

	for idx, item := range m.GetHardwareRole() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ReplyValidationError{
						field:  fmt.Sprintf("HardwareRole[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ReplyValidationError{
						field:  fmt.Sprintf("HardwareRole[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ReplyValidationError{
					field:  fmt.Sprintf("HardwareRole[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	for idx, item := range m.GetHardwareRoleLabel() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ReplyValidationError{
						field:  fmt.Sprintf("HardwareRoleLabel[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ReplyValidationError{
						field:  fmt.Sprintf("HardwareRoleLabel[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ReplyValidationError{
					field:  fmt.Sprintf("HardwareRoleLabel[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	for idx, item := range m.GetChain() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ReplyValidationError{
						field:  fmt.Sprintf("Chain[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ReplyValidationError{
						field:  fmt.Sprintf("Chain[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ReplyValidationError{
					field:  fmt.Sprintf("Chain[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	for idx, item := range m.GetChainFrom() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ReplyValidationError{
						field:  fmt.Sprintf("ChainFrom[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ReplyValidationError{
						field:  fmt.Sprintf("ChainFrom[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ReplyValidationError{
					field:  fmt.Sprintf("ChainFrom[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return ReplyMultiError(errors)
	}

	return nil
}

// ReplyMultiError is an error wrapping multiple validation errors returned by
// Reply.ValidateAll() if the designated constraints aren't met.
type ReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ReplyMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ReplyMultiError) AllErrors() []error { return m }

// ReplyValidationError is the validation error returned by Reply.Validate if
// the designated constraints aren't met.
type ReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ReplyValidationError) ErrorName() string { return "ReplyValidationError" }

// Error satisfies the builtin error interface
func (e ReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ReplyValidationError{}

// Validate checks the field values on Less with the rules defined in the proto
// definition for this message. If any rules are violated, the first error
// encountered is returned, or nil if there are no violations.
func (m *Less) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Less with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in LessMultiError, or nil if none found.
func (m *Less) ValidateAll() error {
	return m.validate(true)
}

func (m *Less) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	if all {
		switch v := interface{}(m.GetVersion()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LessValidationError{
					field:  "Version",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LessValidationError{
					field:  "Version",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetVersion()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LessValidationError{
				field:  "Version",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetCategory()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LessValidationError{
					field:  "Category",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LessValidationError{
					field:  "Category",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCategory()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LessValidationError{
				field:  "Category",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetService()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LessValidationError{
					field:  "Service",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LessValidationError{
					field:  "Service",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetService()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LessValidationError{
				field:  "Service",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetResource()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LessValidationError{
					field:  "Resource",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LessValidationError{
					field:  "Resource",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetResource()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LessValidationError{
				field:  "Resource",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetApplication()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LessValidationError{
					field:  "Application",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LessValidationError{
					field:  "Application",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetApplication()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LessValidationError{
				field:  "Application",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Action

	// no validation rules for VirtualGoods

	// no validation rules for CloudType

	if len(errors) > 0 {
		return LessMultiError(errors)
	}

	return nil
}

// LessMultiError is an error wrapping multiple validation errors returned by
// Less.ValidateAll() if the designated constraints aren't met.
type LessMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m LessMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m LessMultiError) AllErrors() []error { return m }

// LessValidationError is the validation error returned by Less.Validate if the
// designated constraints aren't met.
type LessValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e LessValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e LessValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e LessValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e LessValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e LessValidationError) ErrorName() string { return "LessValidationError" }

// Error satisfies the builtin error interface
func (e LessValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sLess.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = LessValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = LessValidationError{}

// Validate checks the field values on DescribesReq with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *DescribesReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DescribesReq with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in DescribesReqMultiError, or
// nil if none found.
func (m *DescribesReq) ValidateAll() error {
	return m.validate(true)
}

func (m *DescribesReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if val := m.GetPageNumber(); val < -1 || val > 16777216 {
		err := DescribesReqValidationError{
			field:  "PageNumber",
			reason: "value must be inside range [-1, 16777216]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if val := m.GetPageSize(); val < 1 || val > 16777216 {
		err := DescribesReqValidationError{
			field:  "PageSize",
			reason: "value must be inside range [1, 16777216]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	for idx, item := range m.GetFilters() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, DescribesReqValidationError{
						field:  fmt.Sprintf("Filters[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, DescribesReqValidationError{
						field:  fmt.Sprintf("Filters[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return DescribesReqValidationError{
					field:  fmt.Sprintf("Filters[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	for idx, item := range m.GetSorts() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, DescribesReqValidationError{
						field:  fmt.Sprintf("Sorts[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, DescribesReqValidationError{
						field:  fmt.Sprintf("Sorts[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return DescribesReqValidationError{
					field:  fmt.Sprintf("Sorts[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return DescribesReqMultiError(errors)
	}

	return nil
}

// DescribesReqMultiError is an error wrapping multiple validation errors
// returned by DescribesReq.ValidateAll() if the designated constraints aren't met.
type DescribesReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DescribesReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DescribesReqMultiError) AllErrors() []error { return m }

// DescribesReqValidationError is the validation error returned by
// DescribesReq.Validate if the designated constraints aren't met.
type DescribesReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DescribesReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DescribesReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DescribesReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DescribesReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DescribesReqValidationError) ErrorName() string { return "DescribesReqValidationError" }

// Error satisfies the builtin error interface
func (e DescribesReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDescribesReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DescribesReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DescribesReqValidationError{}

// Validate checks the field values on DescribesReply with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *DescribesReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DescribesReply with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in DescribesReplyMultiError,
// or nil if none found.
func (m *DescribesReply) ValidateAll() error {
	return m.validate(true)
}

func (m *DescribesReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetList() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, DescribesReplyValidationError{
						field:  fmt.Sprintf("List[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, DescribesReplyValidationError{
						field:  fmt.Sprintf("List[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return DescribesReplyValidationError{
					field:  fmt.Sprintf("List[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for TotalCount

	if len(errors) > 0 {
		return DescribesReplyMultiError(errors)
	}

	return nil
}

// DescribesReplyMultiError is an error wrapping multiple validation errors
// returned by DescribesReply.ValidateAll() if the designated constraints
// aren't met.
type DescribesReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DescribesReplyMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DescribesReplyMultiError) AllErrors() []error { return m }

// DescribesReplyValidationError is the validation error returned by
// DescribesReply.Validate if the designated constraints aren't met.
type DescribesReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DescribesReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DescribesReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DescribesReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DescribesReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DescribesReplyValidationError) ErrorName() string { return "DescribesReplyValidationError" }

// Error satisfies the builtin error interface
func (e DescribesReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDescribesReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DescribesReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DescribesReplyValidationError{}

// Validate checks the field values on ExcelFilecenterReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ExcelFilecenterReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ExcelFilecenterReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ExcelFilecenterReqMultiError, or nil if none found.
func (m *ExcelFilecenterReq) ValidateAll() error {
	return m.validate(true)
}

func (m *ExcelFilecenterReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	if all {
		switch v := interface{}(m.GetGoods()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ExcelFilecenterReqValidationError{
					field:  "Goods",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ExcelFilecenterReqValidationError{
					field:  "Goods",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetGoods()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ExcelFilecenterReqValidationError{
				field:  "Goods",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if _, ok := _ExcelFilecenterReq_Arch_InLookup[m.GetArch()]; !ok {
		err := ExcelFilecenterReqValidationError{
			field:  "Arch",
			reason: "value must be in list [x86_64 arm]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if _, ok := _ExcelFilecenterReq_DeployMode_InLookup[m.GetDeployMode()]; !ok {
		err := ExcelFilecenterReqValidationError{
			field:  "DeployMode",
			reason: "value must be in list [skywing_container skywing_package mate_container shell helm rpm mate_bin mate_v2 iaas_image xingyun_container xingyun_package]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(m.GetFileIdentifier()) > 64 {
		err := ExcelFilecenterReqValidationError{
			field:  "FileIdentifier",
			reason: "value length must be at most 64 bytes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if _, ok := _ExcelFilecenterReq_HostOsType_InLookup[m.GetHostOsType()]; !ok {
		err := ExcelFilecenterReqValidationError{
			field:  "HostOsType",
			reason: "value must be in list [centos openEuler kylin]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if l := len(m.GetFileName()); l < 1 || l > 1000 {
		err := ExcelFilecenterReqValidationError{
			field:  "FileName",
			reason: "value length must be between 1 and 1000 bytes, inclusive",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(m.GetFileHash()) > 128 {
		err := ExcelFilecenterReqValidationError{
			field:  "FileHash",
			reason: "value length must be at most 128 bytes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetFileSize() < 0 {
		err := ExcelFilecenterReqValidationError{
			field:  "FileSize",
			reason: "value must be greater than or equal to 0",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return ExcelFilecenterReqMultiError(errors)
	}

	return nil
}

// ExcelFilecenterReqMultiError is an error wrapping multiple validation errors
// returned by ExcelFilecenterReq.ValidateAll() if the designated constraints
// aren't met.
type ExcelFilecenterReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ExcelFilecenterReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ExcelFilecenterReqMultiError) AllErrors() []error { return m }

// ExcelFilecenterReqValidationError is the validation error returned by
// ExcelFilecenterReq.Validate if the designated constraints aren't met.
type ExcelFilecenterReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ExcelFilecenterReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ExcelFilecenterReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ExcelFilecenterReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ExcelFilecenterReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ExcelFilecenterReqValidationError) ErrorName() string {
	return "ExcelFilecenterReqValidationError"
}

// Error satisfies the builtin error interface
func (e ExcelFilecenterReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sExcelFilecenterReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ExcelFilecenterReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ExcelFilecenterReqValidationError{}

var _ExcelFilecenterReq_Arch_InLookup = map[string]struct{}{
	"x86_64": {},
	"arm":    {},
}

var _ExcelFilecenterReq_DeployMode_InLookup = map[string]struct{}{
	"skywing_container": {},
	"skywing_package":   {},
	"mate_container":    {},
	"shell":             {},
	"helm":              {},
	"rpm":               {},
	"mate_bin":          {},
	"mate_v2":           {},
	"iaas_image":        {},
	"xingyun_container": {},
	"xingyun_package":   {},
}

var _ExcelFilecenterReq_HostOsType_InLookup = map[string]struct{}{
	"centos":    {},
	"openEuler": {},
	"kylin":     {},
}

// Validate checks the field values on FilecenterDeployDockerImageReq with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *FilecenterDeployDockerImageReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on FilecenterDeployDockerImageReq with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// FilecenterDeployDockerImageReqMultiError, or nil if none found.
func (m *FilecenterDeployDockerImageReq) ValidateAll() error {
	return m.validate(true)
}

func (m *FilecenterDeployDockerImageReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if l := len(m.GetTag()); l < 1 || l > 1000 {
		err := FilecenterDeployDockerImageReqValidationError{
			field:  "Tag",
			reason: "value length must be between 1 and 1000 bytes, inclusive",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if l := len(m.GetRegistry()); l < 1 || l > 64 {
		err := FilecenterDeployDockerImageReqValidationError{
			field:  "Registry",
			reason: "value length must be between 1 and 64 bytes, inclusive",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return FilecenterDeployDockerImageReqMultiError(errors)
	}

	return nil
}

// FilecenterDeployDockerImageReqMultiError is an error wrapping multiple
// validation errors returned by FilecenterDeployDockerImageReq.ValidateAll()
// if the designated constraints aren't met.
type FilecenterDeployDockerImageReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FilecenterDeployDockerImageReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FilecenterDeployDockerImageReqMultiError) AllErrors() []error { return m }

// FilecenterDeployDockerImageReqValidationError is the validation error
// returned by FilecenterDeployDockerImageReq.Validate if the designated
// constraints aren't met.
type FilecenterDeployDockerImageReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FilecenterDeployDockerImageReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FilecenterDeployDockerImageReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FilecenterDeployDockerImageReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FilecenterDeployDockerImageReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FilecenterDeployDockerImageReqValidationError) ErrorName() string {
	return "FilecenterDeployDockerImageReqValidationError"
}

// Error satisfies the builtin error interface
func (e FilecenterDeployDockerImageReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFilecenterDeployDockerImageReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FilecenterDeployDockerImageReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FilecenterDeployDockerImageReqValidationError{}

// Validate checks the field values on FilecenterDeploySkywingPackageReq with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *FilecenterDeploySkywingPackageReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on FilecenterDeploySkywingPackageReq
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// FilecenterDeploySkywingPackageReqMultiError, or nil if none found.
func (m *FilecenterDeploySkywingPackageReq) ValidateAll() error {
	return m.validate(true)
}

func (m *FilecenterDeploySkywingPackageReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if l := len(m.GetModuleName()); l < 1 || l > 64 {
		err := FilecenterDeploySkywingPackageReqValidationError{
			field:  "ModuleName",
			reason: "value length must be between 1 and 64 bytes, inclusive",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if l := len(m.GetPackageVersion()); l < 1 || l > 1000 {
		err := FilecenterDeploySkywingPackageReqValidationError{
			field:  "PackageVersion",
			reason: "value length must be between 1 and 1000 bytes, inclusive",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return FilecenterDeploySkywingPackageReqMultiError(errors)
	}

	return nil
}

// FilecenterDeploySkywingPackageReqMultiError is an error wrapping multiple
// validation errors returned by
// FilecenterDeploySkywingPackageReq.ValidateAll() if the designated
// constraints aren't met.
type FilecenterDeploySkywingPackageReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FilecenterDeploySkywingPackageReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FilecenterDeploySkywingPackageReqMultiError) AllErrors() []error { return m }

// FilecenterDeploySkywingPackageReqValidationError is the validation error
// returned by FilecenterDeploySkywingPackageReq.Validate if the designated
// constraints aren't met.
type FilecenterDeploySkywingPackageReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FilecenterDeploySkywingPackageReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FilecenterDeploySkywingPackageReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FilecenterDeploySkywingPackageReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FilecenterDeploySkywingPackageReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FilecenterDeploySkywingPackageReqValidationError) ErrorName() string {
	return "FilecenterDeploySkywingPackageReqValidationError"
}

// Error satisfies the builtin error interface
func (e FilecenterDeploySkywingPackageReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFilecenterDeploySkywingPackageReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FilecenterDeploySkywingPackageReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FilecenterDeploySkywingPackageReqValidationError{}

// Validate checks the field values on FilecenterDeployShellReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *FilecenterDeployShellReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on FilecenterDeployShellReq with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// FilecenterDeployShellReqMultiError, or nil if none found.
func (m *FilecenterDeployShellReq) ValidateAll() error {
	return m.validate(true)
}

func (m *FilecenterDeployShellReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if l := len(m.GetDestPath()); l < 1 || l > 255 {
		err := FilecenterDeployShellReqValidationError{
			field:  "DestPath",
			reason: "value length must be between 1 and 255 bytes, inclusive",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if _, ok := _FilecenterDeployShellReq_StorageLocation_InLookup[m.GetStorageLocation()]; !ok {
		err := FilecenterDeployShellReqValidationError{
			field:  "StorageLocation",
			reason: "value must be in list [minio http]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for Tag

	if len(errors) > 0 {
		return FilecenterDeployShellReqMultiError(errors)
	}

	return nil
}

// FilecenterDeployShellReqMultiError is an error wrapping multiple validation
// errors returned by FilecenterDeployShellReq.ValidateAll() if the designated
// constraints aren't met.
type FilecenterDeployShellReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FilecenterDeployShellReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FilecenterDeployShellReqMultiError) AllErrors() []error { return m }

// FilecenterDeployShellReqValidationError is the validation error returned by
// FilecenterDeployShellReq.Validate if the designated constraints aren't met.
type FilecenterDeployShellReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FilecenterDeployShellReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FilecenterDeployShellReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FilecenterDeployShellReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FilecenterDeployShellReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FilecenterDeployShellReqValidationError) ErrorName() string {
	return "FilecenterDeployShellReqValidationError"
}

// Error satisfies the builtin error interface
func (e FilecenterDeployShellReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFilecenterDeployShellReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FilecenterDeployShellReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FilecenterDeployShellReqValidationError{}

var _FilecenterDeployShellReq_StorageLocation_InLookup = map[string]struct{}{
	"minio": {},
	"http":  {},
}

// Validate checks the field values on FilecenterDeployRpmReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *FilecenterDeployRpmReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on FilecenterDeployRpmReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// FilecenterDeployRpmReqMultiError, or nil if none found.
func (m *FilecenterDeployRpmReq) ValidateAll() error {
	return m.validate(true)
}

func (m *FilecenterDeployRpmReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if l := len(m.GetRepo()); l < 1 || l > 64 {
		err := FilecenterDeployRpmReqValidationError{
			field:  "Repo",
			reason: "value length must be between 1 and 64 bytes, inclusive",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for Tag

	if len(errors) > 0 {
		return FilecenterDeployRpmReqMultiError(errors)
	}

	return nil
}

// FilecenterDeployRpmReqMultiError is an error wrapping multiple validation
// errors returned by FilecenterDeployRpmReq.ValidateAll() if the designated
// constraints aren't met.
type FilecenterDeployRpmReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FilecenterDeployRpmReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FilecenterDeployRpmReqMultiError) AllErrors() []error { return m }

// FilecenterDeployRpmReqValidationError is the validation error returned by
// FilecenterDeployRpmReq.Validate if the designated constraints aren't met.
type FilecenterDeployRpmReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FilecenterDeployRpmReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FilecenterDeployRpmReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FilecenterDeployRpmReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FilecenterDeployRpmReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FilecenterDeployRpmReqValidationError) ErrorName() string {
	return "FilecenterDeployRpmReqValidationError"
}

// Error satisfies the builtin error interface
func (e FilecenterDeployRpmReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFilecenterDeployRpmReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FilecenterDeployRpmReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FilecenterDeployRpmReqValidationError{}

// Validate checks the field values on FilecenterDeployMateBinReq with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *FilecenterDeployMateBinReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on FilecenterDeployMateBinReq with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// FilecenterDeployMateBinReqMultiError, or nil if none found.
func (m *FilecenterDeployMateBinReq) ValidateAll() error {
	return m.validate(true)
}

func (m *FilecenterDeployMateBinReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return FilecenterDeployMateBinReqMultiError(errors)
	}

	return nil
}

// FilecenterDeployMateBinReqMultiError is an error wrapping multiple
// validation errors returned by FilecenterDeployMateBinReq.ValidateAll() if
// the designated constraints aren't met.
type FilecenterDeployMateBinReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FilecenterDeployMateBinReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FilecenterDeployMateBinReqMultiError) AllErrors() []error { return m }

// FilecenterDeployMateBinReqValidationError is the validation error returned
// by FilecenterDeployMateBinReq.Validate if the designated constraints aren't met.
type FilecenterDeployMateBinReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FilecenterDeployMateBinReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FilecenterDeployMateBinReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FilecenterDeployMateBinReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FilecenterDeployMateBinReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FilecenterDeployMateBinReqValidationError) ErrorName() string {
	return "FilecenterDeployMateBinReqValidationError"
}

// Error satisfies the builtin error interface
func (e FilecenterDeployMateBinReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFilecenterDeployMateBinReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FilecenterDeployMateBinReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FilecenterDeployMateBinReqValidationError{}

// Validate checks the field values on FilecenterDeployIaasImageReq with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *FilecenterDeployIaasImageReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on FilecenterDeployIaasImageReq with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// FilecenterDeployIaasImageReqMultiError, or nil if none found.
func (m *FilecenterDeployIaasImageReq) ValidateAll() error {
	return m.validate(true)
}

func (m *FilecenterDeployIaasImageReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if l := len(m.GetImageName()); l < 1 || l > 255 {
		err := FilecenterDeployIaasImageReqValidationError{
			field:  "ImageName",
			reason: "value length must be between 1 and 255 bytes, inclusive",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if l := len(m.GetImageId()); l < 1 || l > 64 {
		err := FilecenterDeployIaasImageReqValidationError{
			field:  "ImageId",
			reason: "value length must be between 1 and 64 bytes, inclusive",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(m.GetOsType()) > 64 {
		err := FilecenterDeployIaasImageReqValidationError{
			field:  "OsType",
			reason: "value length must be at most 64 bytes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if _, ok := _FilecenterDeployIaasImageReq_Platform_InLookup[m.GetPlatform()]; !ok {
		err := FilecenterDeployIaasImageReqValidationError{
			field:  "Platform",
			reason: "value must be in list [windows linux]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetContainerFormat() != "" {

		if _, ok := _FilecenterDeployIaasImageReq_ContainerFormat_InLookup[m.GetContainerFormat()]; !ok {
			err := FilecenterDeployIaasImageReqValidationError{
				field:  "ContainerFormat",
				reason: "value must be in list [docker vm]",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

	}

	if m.GetIsProtected() != "" {

		if _, ok := _FilecenterDeployIaasImageReq_IsProtected_InLookup[m.GetIsProtected()]; !ok {
			err := FilecenterDeployIaasImageReqValidationError{
				field:  "IsProtected",
				reason: "value must be in list [no yes]",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

	}

	if m.GetRootDeviceType() != "" {

		if _, ok := _FilecenterDeployIaasImageReq_RootDeviceType_InLookup[m.GetRootDeviceType()]; !ok {
			err := FilecenterDeployIaasImageReqValidationError{
				field:  "RootDeviceType",
				reason: "value must be in list [local volume]",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

	}

	if _, ok := _FilecenterDeployIaasImageReq_Source_InLookup[m.GetSource()]; !ok {
		err := FilecenterDeployIaasImageReqValidationError{
			field:  "Source",
			reason: "value must be in list [jdcloud self]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(m.GetAttributes()) > 512 {
		err := FilecenterDeployIaasImageReqValidationError{
			field:  "Attributes",
			reason: "value length must be at most 512 bytes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(m.GetGuestAgent()) > 64 {
		err := FilecenterDeployIaasImageReqValidationError{
			field:  "GuestAgent",
			reason: "value length must be at most 64 bytes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(m.GetDiskFormat()) > 64 {
		err := FilecenterDeployIaasImageReqValidationError{
			field:  "DiskFormat",
			reason: "value length must be at most 64 bytes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return FilecenterDeployIaasImageReqMultiError(errors)
	}

	return nil
}

// FilecenterDeployIaasImageReqMultiError is an error wrapping multiple
// validation errors returned by FilecenterDeployIaasImageReq.ValidateAll() if
// the designated constraints aren't met.
type FilecenterDeployIaasImageReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FilecenterDeployIaasImageReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FilecenterDeployIaasImageReqMultiError) AllErrors() []error { return m }

// FilecenterDeployIaasImageReqValidationError is the validation error returned
// by FilecenterDeployIaasImageReq.Validate if the designated constraints
// aren't met.
type FilecenterDeployIaasImageReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FilecenterDeployIaasImageReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FilecenterDeployIaasImageReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FilecenterDeployIaasImageReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FilecenterDeployIaasImageReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FilecenterDeployIaasImageReqValidationError) ErrorName() string {
	return "FilecenterDeployIaasImageReqValidationError"
}

// Error satisfies the builtin error interface
func (e FilecenterDeployIaasImageReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFilecenterDeployIaasImageReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FilecenterDeployIaasImageReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FilecenterDeployIaasImageReqValidationError{}

var _FilecenterDeployIaasImageReq_Platform_InLookup = map[string]struct{}{
	"windows": {},
	"linux":   {},
}

var _FilecenterDeployIaasImageReq_ContainerFormat_InLookup = map[string]struct{}{
	"docker": {},
	"vm":     {},
}

var _FilecenterDeployIaasImageReq_IsProtected_InLookup = map[string]struct{}{
	"no":  {},
	"yes": {},
}

var _FilecenterDeployIaasImageReq_RootDeviceType_InLookup = map[string]struct{}{
	"local":  {},
	"volume": {},
}

var _FilecenterDeployIaasImageReq_Source_InLookup = map[string]struct{}{
	"jdcloud": {},
	"self":    {},
}

// Validate checks the field values on FilecenterDeployDockerImageReply with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *FilecenterDeployDockerImageReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on FilecenterDeployDockerImageReply with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// FilecenterDeployDockerImageReplyMultiError, or nil if none found.
func (m *FilecenterDeployDockerImageReply) ValidateAll() error {
	return m.validate(true)
}

func (m *FilecenterDeployDockerImageReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Tag

	// no validation rules for Registry

	if len(errors) > 0 {
		return FilecenterDeployDockerImageReplyMultiError(errors)
	}

	return nil
}

// FilecenterDeployDockerImageReplyMultiError is an error wrapping multiple
// validation errors returned by
// FilecenterDeployDockerImageReply.ValidateAll() if the designated
// constraints aren't met.
type FilecenterDeployDockerImageReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FilecenterDeployDockerImageReplyMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FilecenterDeployDockerImageReplyMultiError) AllErrors() []error { return m }

// FilecenterDeployDockerImageReplyValidationError is the validation error
// returned by FilecenterDeployDockerImageReply.Validate if the designated
// constraints aren't met.
type FilecenterDeployDockerImageReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FilecenterDeployDockerImageReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FilecenterDeployDockerImageReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FilecenterDeployDockerImageReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FilecenterDeployDockerImageReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FilecenterDeployDockerImageReplyValidationError) ErrorName() string {
	return "FilecenterDeployDockerImageReplyValidationError"
}

// Error satisfies the builtin error interface
func (e FilecenterDeployDockerImageReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFilecenterDeployDockerImageReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FilecenterDeployDockerImageReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FilecenterDeployDockerImageReplyValidationError{}

// Validate checks the field values on FilecenterDeploySkywingPackageReply with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *FilecenterDeploySkywingPackageReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on FilecenterDeploySkywingPackageReply
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// FilecenterDeploySkywingPackageReplyMultiError, or nil if none found.
func (m *FilecenterDeploySkywingPackageReply) ValidateAll() error {
	return m.validate(true)
}

func (m *FilecenterDeploySkywingPackageReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ModuleName

	// no validation rules for PackageVersion

	if len(errors) > 0 {
		return FilecenterDeploySkywingPackageReplyMultiError(errors)
	}

	return nil
}

// FilecenterDeploySkywingPackageReplyMultiError is an error wrapping multiple
// validation errors returned by
// FilecenterDeploySkywingPackageReply.ValidateAll() if the designated
// constraints aren't met.
type FilecenterDeploySkywingPackageReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FilecenterDeploySkywingPackageReplyMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FilecenterDeploySkywingPackageReplyMultiError) AllErrors() []error { return m }

// FilecenterDeploySkywingPackageReplyValidationError is the validation error
// returned by FilecenterDeploySkywingPackageReply.Validate if the designated
// constraints aren't met.
type FilecenterDeploySkywingPackageReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FilecenterDeploySkywingPackageReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FilecenterDeploySkywingPackageReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FilecenterDeploySkywingPackageReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FilecenterDeploySkywingPackageReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FilecenterDeploySkywingPackageReplyValidationError) ErrorName() string {
	return "FilecenterDeploySkywingPackageReplyValidationError"
}

// Error satisfies the builtin error interface
func (e FilecenterDeploySkywingPackageReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFilecenterDeploySkywingPackageReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FilecenterDeploySkywingPackageReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FilecenterDeploySkywingPackageReplyValidationError{}

// Validate checks the field values on FilecenterDeployShellReply with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *FilecenterDeployShellReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on FilecenterDeployShellReply with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// FilecenterDeployShellReplyMultiError, or nil if none found.
func (m *FilecenterDeployShellReply) ValidateAll() error {
	return m.validate(true)
}

func (m *FilecenterDeployShellReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for DestPath

	// no validation rules for StorageLocation

	// no validation rules for Tag

	if len(errors) > 0 {
		return FilecenterDeployShellReplyMultiError(errors)
	}

	return nil
}

// FilecenterDeployShellReplyMultiError is an error wrapping multiple
// validation errors returned by FilecenterDeployShellReply.ValidateAll() if
// the designated constraints aren't met.
type FilecenterDeployShellReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FilecenterDeployShellReplyMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FilecenterDeployShellReplyMultiError) AllErrors() []error { return m }

// FilecenterDeployShellReplyValidationError is the validation error returned
// by FilecenterDeployShellReply.Validate if the designated constraints aren't met.
type FilecenterDeployShellReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FilecenterDeployShellReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FilecenterDeployShellReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FilecenterDeployShellReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FilecenterDeployShellReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FilecenterDeployShellReplyValidationError) ErrorName() string {
	return "FilecenterDeployShellReplyValidationError"
}

// Error satisfies the builtin error interface
func (e FilecenterDeployShellReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFilecenterDeployShellReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FilecenterDeployShellReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FilecenterDeployShellReplyValidationError{}

// Validate checks the field values on FilecenterDeployRpmReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *FilecenterDeployRpmReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on FilecenterDeployRpmReply with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// FilecenterDeployRpmReplyMultiError, or nil if none found.
func (m *FilecenterDeployRpmReply) ValidateAll() error {
	return m.validate(true)
}

func (m *FilecenterDeployRpmReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Repo

	// no validation rules for Tag

	if len(errors) > 0 {
		return FilecenterDeployRpmReplyMultiError(errors)
	}

	return nil
}

// FilecenterDeployRpmReplyMultiError is an error wrapping multiple validation
// errors returned by FilecenterDeployRpmReply.ValidateAll() if the designated
// constraints aren't met.
type FilecenterDeployRpmReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FilecenterDeployRpmReplyMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FilecenterDeployRpmReplyMultiError) AllErrors() []error { return m }

// FilecenterDeployRpmReplyValidationError is the validation error returned by
// FilecenterDeployRpmReply.Validate if the designated constraints aren't met.
type FilecenterDeployRpmReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FilecenterDeployRpmReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FilecenterDeployRpmReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FilecenterDeployRpmReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FilecenterDeployRpmReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FilecenterDeployRpmReplyValidationError) ErrorName() string {
	return "FilecenterDeployRpmReplyValidationError"
}

// Error satisfies the builtin error interface
func (e FilecenterDeployRpmReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFilecenterDeployRpmReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FilecenterDeployRpmReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FilecenterDeployRpmReplyValidationError{}

// Validate checks the field values on FilecenterDeployMateBinReply with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *FilecenterDeployMateBinReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on FilecenterDeployMateBinReply with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// FilecenterDeployMateBinReplyMultiError, or nil if none found.
func (m *FilecenterDeployMateBinReply) ValidateAll() error {
	return m.validate(true)
}

func (m *FilecenterDeployMateBinReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return FilecenterDeployMateBinReplyMultiError(errors)
	}

	return nil
}

// FilecenterDeployMateBinReplyMultiError is an error wrapping multiple
// validation errors returned by FilecenterDeployMateBinReply.ValidateAll() if
// the designated constraints aren't met.
type FilecenterDeployMateBinReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FilecenterDeployMateBinReplyMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FilecenterDeployMateBinReplyMultiError) AllErrors() []error { return m }

// FilecenterDeployMateBinReplyValidationError is the validation error returned
// by FilecenterDeployMateBinReply.Validate if the designated constraints
// aren't met.
type FilecenterDeployMateBinReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FilecenterDeployMateBinReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FilecenterDeployMateBinReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FilecenterDeployMateBinReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FilecenterDeployMateBinReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FilecenterDeployMateBinReplyValidationError) ErrorName() string {
	return "FilecenterDeployMateBinReplyValidationError"
}

// Error satisfies the builtin error interface
func (e FilecenterDeployMateBinReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFilecenterDeployMateBinReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FilecenterDeployMateBinReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FilecenterDeployMateBinReplyValidationError{}

// Validate checks the field values on FilecenterDeployIaasImageReply with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *FilecenterDeployIaasImageReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on FilecenterDeployIaasImageReply with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// FilecenterDeployIaasImageReplyMultiError, or nil if none found.
func (m *FilecenterDeployIaasImageReply) ValidateAll() error {
	return m.validate(true)
}

func (m *FilecenterDeployIaasImageReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ImageName

	// no validation rules for ImageId

	// no validation rules for OsType

	// no validation rules for Platform

	// no validation rules for ContainerFormat

	// no validation rules for IsProtected

	// no validation rules for RootDeviceType

	// no validation rules for Source

	// no validation rules for Attributes

	// no validation rules for GuestAgent

	// no validation rules for DiskFormat

	if len(errors) > 0 {
		return FilecenterDeployIaasImageReplyMultiError(errors)
	}

	return nil
}

// FilecenterDeployIaasImageReplyMultiError is an error wrapping multiple
// validation errors returned by FilecenterDeployIaasImageReply.ValidateAll()
// if the designated constraints aren't met.
type FilecenterDeployIaasImageReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FilecenterDeployIaasImageReplyMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FilecenterDeployIaasImageReplyMultiError) AllErrors() []error { return m }

// FilecenterDeployIaasImageReplyValidationError is the validation error
// returned by FilecenterDeployIaasImageReply.Validate if the designated
// constraints aren't met.
type FilecenterDeployIaasImageReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FilecenterDeployIaasImageReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FilecenterDeployIaasImageReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FilecenterDeployIaasImageReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FilecenterDeployIaasImageReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FilecenterDeployIaasImageReplyValidationError) ErrorName() string {
	return "FilecenterDeployIaasImageReplyValidationError"
}

// Error satisfies the builtin error interface
func (e FilecenterDeployIaasImageReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFilecenterDeployIaasImageReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FilecenterDeployIaasImageReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FilecenterDeployIaasImageReplyValidationError{}

// Validate checks the field values on CreatePdReq with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *CreatePdReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreatePdReq with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in CreatePdReqMultiError, or
// nil if none found.
func (m *CreatePdReq) ValidateAll() error {
	return m.validate(true)
}

func (m *CreatePdReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	if all {
		switch v := interface{}(m.GetGoods()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreatePdReqValidationError{
					field:  "Goods",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreatePdReqValidationError{
					field:  "Goods",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetGoods()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreatePdReqValidationError{
				field:  "Goods",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Content

	if len(errors) > 0 {
		return CreatePdReqMultiError(errors)
	}

	return nil
}

// CreatePdReqMultiError is an error wrapping multiple validation errors
// returned by CreatePdReq.ValidateAll() if the designated constraints aren't met.
type CreatePdReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreatePdReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreatePdReqMultiError) AllErrors() []error { return m }

// CreatePdReqValidationError is the validation error returned by
// CreatePdReq.Validate if the designated constraints aren't met.
type CreatePdReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreatePdReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreatePdReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreatePdReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreatePdReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreatePdReqValidationError) ErrorName() string { return "CreatePdReqValidationError" }

// Error satisfies the builtin error interface
func (e CreatePdReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreatePdReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreatePdReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreatePdReqValidationError{}

// Validate checks the field values on CreateAdlReq with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *CreateAdlReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateAdlReq with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in CreateAdlReqMultiError, or
// nil if none found.
func (m *CreateAdlReq) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateAdlReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	if all {
		switch v := interface{}(m.GetGoods()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateAdlReqValidationError{
					field:  "Goods",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateAdlReqValidationError{
					field:  "Goods",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetGoods()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateAdlReqValidationError{
				field:  "Goods",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetAdl()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateAdlReqValidationError{
					field:  "Adl",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateAdlReqValidationError{
					field:  "Adl",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAdl()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateAdlReqValidationError{
				field:  "Adl",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return CreateAdlReqMultiError(errors)
	}

	return nil
}

// CreateAdlReqMultiError is an error wrapping multiple validation errors
// returned by CreateAdlReq.ValidateAll() if the designated constraints aren't met.
type CreateAdlReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateAdlReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateAdlReqMultiError) AllErrors() []error { return m }

// CreateAdlReqValidationError is the validation error returned by
// CreateAdlReq.Validate if the designated constraints aren't met.
type CreateAdlReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateAdlReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateAdlReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateAdlReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateAdlReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateAdlReqValidationError) ErrorName() string { return "CreateAdlReqValidationError" }

// Error satisfies the builtin error interface
func (e CreateAdlReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateAdlReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateAdlReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateAdlReqValidationError{}

// Validate checks the field values on AdlReq with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *AdlReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AdlReq with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in AdlReqMultiError, or nil if none found.
func (m *AdlReq) ValidateAll() error {
	return m.validate(true)
}

func (m *AdlReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(m.GetScope()) < 1 {
		err := AdlReqValidationError{
			field:  "Scope",
			reason: "value length must be at least 1 bytes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(m.GetFilePath()) < 1 {
		err := AdlReqValidationError{
			field:  "FilePath",
			reason: "value length must be at least 1 bytes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(m.GetContent()) < 1 {
		err := AdlReqValidationError{
			field:  "Content",
			reason: "value length must be at least 1 bytes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return AdlReqMultiError(errors)
	}

	return nil
}

// AdlReqMultiError is an error wrapping multiple validation errors returned by
// AdlReq.ValidateAll() if the designated constraints aren't met.
type AdlReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AdlReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AdlReqMultiError) AllErrors() []error { return m }

// AdlReqValidationError is the validation error returned by AdlReq.Validate if
// the designated constraints aren't met.
type AdlReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AdlReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AdlReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AdlReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AdlReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AdlReqValidationError) ErrorName() string { return "AdlReqValidationError" }

// Error satisfies the builtin error interface
func (e AdlReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAdlReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AdlReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AdlReqValidationError{}

// Validate checks the field values on CreateMetadataReq with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *CreateMetadataReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateMetadataReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateMetadataReqMultiError, or nil if none found.
func (m *CreateMetadataReq) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateMetadataReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	if all {
		switch v := interface{}(m.GetGoods()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateMetadataReqValidationError{
					field:  "Goods",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateMetadataReqValidationError{
					field:  "Goods",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetGoods()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateMetadataReqValidationError{
				field:  "Goods",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetUnifiedMetadata()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateMetadataReqValidationError{
					field:  "UnifiedMetadata",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateMetadataReqValidationError{
					field:  "UnifiedMetadata",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUnifiedMetadata()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateMetadataReqValidationError{
				field:  "UnifiedMetadata",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return CreateMetadataReqMultiError(errors)
	}

	return nil
}

// CreateMetadataReqMultiError is an error wrapping multiple validation errors
// returned by CreateMetadataReq.ValidateAll() if the designated constraints
// aren't met.
type CreateMetadataReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateMetadataReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateMetadataReqMultiError) AllErrors() []error { return m }

// CreateMetadataReqValidationError is the validation error returned by
// CreateMetadataReq.Validate if the designated constraints aren't met.
type CreateMetadataReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateMetadataReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateMetadataReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateMetadataReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateMetadataReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateMetadataReqValidationError) ErrorName() string {
	return "CreateMetadataReqValidationError"
}

// Error satisfies the builtin error interface
func (e CreateMetadataReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateMetadataReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateMetadataReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateMetadataReqValidationError{}

// Validate checks the field values on CreateFilecenterReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreateFilecenterReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateFilecenterReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateFilecenterReqMultiError, or nil if none found.
func (m *CreateFilecenterReq) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateFilecenterReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	if all {
		switch v := interface{}(m.GetGoods()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateFilecenterReqValidationError{
					field:  "Goods",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateFilecenterReqValidationError{
					field:  "Goods",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetGoods()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateFilecenterReqValidationError{
				field:  "Goods",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if _, ok := _CreateFilecenterReq_Arch_InLookup[m.GetArch()]; !ok {
		err := CreateFilecenterReqValidationError{
			field:  "Arch",
			reason: "value must be in list [x86_64 arm]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if _, ok := _CreateFilecenterReq_HostOsType_InLookup[m.GetHostOsType()]; !ok {
		err := CreateFilecenterReqValidationError{
			field:  "HostOsType",
			reason: "value must be in list [centos openEuler kylin]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if utf8.RuneCountInString(m.GetFileIdentifier()) > 64 {
		err := CreateFilecenterReqValidationError{
			field:  "FileIdentifier",
			reason: "value length must be at most 64 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if _, ok := _CreateFilecenterReq_DirectorySpec_InLookup[m.GetDirectorySpec()]; !ok {
		err := CreateFilecenterReqValidationError{
			field:  "DirectorySpec",
			reason: "value must be in list [arch_fileType_serviceCode arch_serializeGoods]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if utf8.RuneCountInString(m.GetFileName()) > 1000 {
		err := CreateFilecenterReqValidationError{
			field:  "FileName",
			reason: "value length must be at most 1000 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if utf8.RuneCountInString(m.GetFileHash()) > 128 {
		err := CreateFilecenterReqValidationError{
			field:  "FileHash",
			reason: "value length must be at most 128 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetFileSize() < 1 {
		err := CreateFilecenterReqValidationError{
			field:  "FileSize",
			reason: "value must be greater than or equal to 1",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if utf8.RuneCountInString(m.GetImageTags()) > 1000 {
		err := CreateFilecenterReqValidationError{
			field:  "ImageTags",
			reason: "value length must be at most 1000 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if utf8.RuneCountInString(m.GetFileVersion()) > 255 {
		err := CreateFilecenterReqValidationError{
			field:  "FileVersion",
			reason: "value length must be at most 255 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if utf8.RuneCountInString(m.GetFileType()) > 32 {
		err := CreateFilecenterReqValidationError{
			field:  "FileType",
			reason: "value length must be at most 32 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if utf8.RuneCountInString(m.GetFilePath()) > 1000 {
		err := CreateFilecenterReqValidationError{
			field:  "FilePath",
			reason: "value length must be at most 1000 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for Param

	if utf8.RuneCountInString(m.GetRemark()) > 255 {
		err := CreateFilecenterReqValidationError{
			field:  "Remark",
			reason: "value length must be at most 255 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return CreateFilecenterReqMultiError(errors)
	}

	return nil
}

// CreateFilecenterReqMultiError is an error wrapping multiple validation
// errors returned by CreateFilecenterReq.ValidateAll() if the designated
// constraints aren't met.
type CreateFilecenterReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateFilecenterReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateFilecenterReqMultiError) AllErrors() []error { return m }

// CreateFilecenterReqValidationError is the validation error returned by
// CreateFilecenterReq.Validate if the designated constraints aren't met.
type CreateFilecenterReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateFilecenterReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateFilecenterReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateFilecenterReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateFilecenterReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateFilecenterReqValidationError) ErrorName() string {
	return "CreateFilecenterReqValidationError"
}

// Error satisfies the builtin error interface
func (e CreateFilecenterReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateFilecenterReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateFilecenterReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateFilecenterReqValidationError{}

var _CreateFilecenterReq_Arch_InLookup = map[string]struct{}{
	"x86_64": {},
	"arm":    {},
}

var _CreateFilecenterReq_HostOsType_InLookup = map[string]struct{}{
	"centos":    {},
	"openEuler": {},
	"kylin":     {},
}

var _CreateFilecenterReq_DirectorySpec_InLookup = map[string]struct{}{
	"arch_fileType_serviceCode": {},
	"arch_serializeGoods":       {},
}

// Validate checks the field values on DeleteFilecenterReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DeleteFilecenterReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DeleteFilecenterReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DeleteFilecenterReqMultiError, or nil if none found.
func (m *DeleteFilecenterReq) ValidateAll() error {
	return m.validate(true)
}

func (m *DeleteFilecenterReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetId() < 1 {
		err := DeleteFilecenterReqValidationError{
			field:  "Id",
			reason: "value must be greater than or equal to 1",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return DeleteFilecenterReqMultiError(errors)
	}

	return nil
}

// DeleteFilecenterReqMultiError is an error wrapping multiple validation
// errors returned by DeleteFilecenterReq.ValidateAll() if the designated
// constraints aren't met.
type DeleteFilecenterReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DeleteFilecenterReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DeleteFilecenterReqMultiError) AllErrors() []error { return m }

// DeleteFilecenterReqValidationError is the validation error returned by
// DeleteFilecenterReq.Validate if the designated constraints aren't met.
type DeleteFilecenterReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DeleteFilecenterReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DeleteFilecenterReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DeleteFilecenterReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DeleteFilecenterReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DeleteFilecenterReqValidationError) ErrorName() string {
	return "DeleteFilecenterReqValidationError"
}

// Error satisfies the builtin error interface
func (e DeleteFilecenterReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDeleteFilecenterReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DeleteFilecenterReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DeleteFilecenterReqValidationError{}

// Validate checks the field values on DescribeFilecenterReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DescribeFilecenterReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DescribeFilecenterReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DescribeFilecenterReqMultiError, or nil if none found.
func (m *DescribeFilecenterReq) ValidateAll() error {
	return m.validate(true)
}

func (m *DescribeFilecenterReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetId() < 1 {
		err := DescribeFilecenterReqValidationError{
			field:  "Id",
			reason: "value must be greater than or equal to 1",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	for idx, item := range m.GetFilters() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, DescribeFilecenterReqValidationError{
						field:  fmt.Sprintf("Filters[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, DescribeFilecenterReqValidationError{
						field:  fmt.Sprintf("Filters[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return DescribeFilecenterReqValidationError{
					field:  fmt.Sprintf("Filters[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return DescribeFilecenterReqMultiError(errors)
	}

	return nil
}

// DescribeFilecenterReqMultiError is an error wrapping multiple validation
// errors returned by DescribeFilecenterReq.ValidateAll() if the designated
// constraints aren't met.
type DescribeFilecenterReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DescribeFilecenterReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DescribeFilecenterReqMultiError) AllErrors() []error { return m }

// DescribeFilecenterReqValidationError is the validation error returned by
// DescribeFilecenterReq.Validate if the designated constraints aren't met.
type DescribeFilecenterReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DescribeFilecenterReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DescribeFilecenterReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DescribeFilecenterReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DescribeFilecenterReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DescribeFilecenterReqValidationError) ErrorName() string {
	return "DescribeFilecenterReqValidationError"
}

// Error satisfies the builtin error interface
func (e DescribeFilecenterReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDescribeFilecenterReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DescribeFilecenterReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DescribeFilecenterReqValidationError{}

// Validate checks the field values on DescribeFilecenterReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DescribeFilecenterReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DescribeFilecenterReply with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DescribeFilecenterReplyMultiError, or nil if none found.
func (m *DescribeFilecenterReply) ValidateAll() error {
	return m.validate(true)
}

func (m *DescribeFilecenterReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for CreatedAt

	if all {
		switch v := interface{}(m.GetGoods()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DescribeFilecenterReplyValidationError{
					field:  "Goods",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DescribeFilecenterReplyValidationError{
					field:  "Goods",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetGoods()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DescribeFilecenterReplyValidationError{
				field:  "Goods",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Arch

	// no validation rules for DeployMode

	// no validation rules for FileIdentifier

	// no validation rules for HostOsType

	// no validation rules for FileName

	// no validation rules for FileHash

	// no validation rules for FileSize

	if all {
		switch v := interface{}(m.GetSkywingContainer()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DescribeFilecenterReplyValidationError{
					field:  "SkywingContainer",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DescribeFilecenterReplyValidationError{
					field:  "SkywingContainer",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetSkywingContainer()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DescribeFilecenterReplyValidationError{
				field:  "SkywingContainer",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetSkywingPackage()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DescribeFilecenterReplyValidationError{
					field:  "SkywingPackage",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DescribeFilecenterReplyValidationError{
					field:  "SkywingPackage",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetSkywingPackage()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DescribeFilecenterReplyValidationError{
				field:  "SkywingPackage",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetMateContainer()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DescribeFilecenterReplyValidationError{
					field:  "MateContainer",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DescribeFilecenterReplyValidationError{
					field:  "MateContainer",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetMateContainer()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DescribeFilecenterReplyValidationError{
				field:  "MateContainer",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetShell()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DescribeFilecenterReplyValidationError{
					field:  "Shell",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DescribeFilecenterReplyValidationError{
					field:  "Shell",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetShell()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DescribeFilecenterReplyValidationError{
				field:  "Shell",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetHelm()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DescribeFilecenterReplyValidationError{
					field:  "Helm",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DescribeFilecenterReplyValidationError{
					field:  "Helm",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHelm()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DescribeFilecenterReplyValidationError{
				field:  "Helm",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetRpm()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DescribeFilecenterReplyValidationError{
					field:  "Rpm",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DescribeFilecenterReplyValidationError{
					field:  "Rpm",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRpm()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DescribeFilecenterReplyValidationError{
				field:  "Rpm",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetMateBin()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DescribeFilecenterReplyValidationError{
					field:  "MateBin",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DescribeFilecenterReplyValidationError{
					field:  "MateBin",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetMateBin()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DescribeFilecenterReplyValidationError{
				field:  "MateBin",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetMateV2()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DescribeFilecenterReplyValidationError{
					field:  "MateV2",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DescribeFilecenterReplyValidationError{
					field:  "MateV2",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetMateV2()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DescribeFilecenterReplyValidationError{
				field:  "MateV2",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetIaasImage()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DescribeFilecenterReplyValidationError{
					field:  "IaasImage",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DescribeFilecenterReplyValidationError{
					field:  "IaasImage",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetIaasImage()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DescribeFilecenterReplyValidationError{
				field:  "IaasImage",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return DescribeFilecenterReplyMultiError(errors)
	}

	return nil
}

// DescribeFilecenterReplyMultiError is an error wrapping multiple validation
// errors returned by DescribeFilecenterReply.ValidateAll() if the designated
// constraints aren't met.
type DescribeFilecenterReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DescribeFilecenterReplyMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DescribeFilecenterReplyMultiError) AllErrors() []error { return m }

// DescribeFilecenterReplyValidationError is the validation error returned by
// DescribeFilecenterReply.Validate if the designated constraints aren't met.
type DescribeFilecenterReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DescribeFilecenterReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DescribeFilecenterReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DescribeFilecenterReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DescribeFilecenterReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DescribeFilecenterReplyValidationError) ErrorName() string {
	return "DescribeFilecenterReplyValidationError"
}

// Error satisfies the builtin error interface
func (e DescribeFilecenterReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDescribeFilecenterReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DescribeFilecenterReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DescribeFilecenterReplyValidationError{}

// Validate checks the field values on FilecenterReply with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *FilecenterReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on FilecenterReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// FilecenterReplyMultiError, or nil if none found.
func (m *FilecenterReply) ValidateAll() error {
	return m.validate(true)
}

func (m *FilecenterReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for CreatedAt

	// no validation rules for GoodsId

	// no validation rules for Arch

	// no validation rules for DeployMode

	// no validation rules for FileIdentifier

	// no validation rules for HostOsType

	// no validation rules for FileName

	// no validation rules for FileHash

	// no validation rules for FileSize

	if all {
		switch v := interface{}(m.GetSkywingContainer()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, FilecenterReplyValidationError{
					field:  "SkywingContainer",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, FilecenterReplyValidationError{
					field:  "SkywingContainer",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetSkywingContainer()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return FilecenterReplyValidationError{
				field:  "SkywingContainer",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetSkywingPackage()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, FilecenterReplyValidationError{
					field:  "SkywingPackage",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, FilecenterReplyValidationError{
					field:  "SkywingPackage",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetSkywingPackage()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return FilecenterReplyValidationError{
				field:  "SkywingPackage",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetMateContainer()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, FilecenterReplyValidationError{
					field:  "MateContainer",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, FilecenterReplyValidationError{
					field:  "MateContainer",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetMateContainer()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return FilecenterReplyValidationError{
				field:  "MateContainer",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetShell()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, FilecenterReplyValidationError{
					field:  "Shell",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, FilecenterReplyValidationError{
					field:  "Shell",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetShell()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return FilecenterReplyValidationError{
				field:  "Shell",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetHelm()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, FilecenterReplyValidationError{
					field:  "Helm",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, FilecenterReplyValidationError{
					field:  "Helm",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHelm()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return FilecenterReplyValidationError{
				field:  "Helm",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetRpm()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, FilecenterReplyValidationError{
					field:  "Rpm",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, FilecenterReplyValidationError{
					field:  "Rpm",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRpm()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return FilecenterReplyValidationError{
				field:  "Rpm",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetMateBin()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, FilecenterReplyValidationError{
					field:  "MateBin",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, FilecenterReplyValidationError{
					field:  "MateBin",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetMateBin()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return FilecenterReplyValidationError{
				field:  "MateBin",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetMateV2()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, FilecenterReplyValidationError{
					field:  "MateV2",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, FilecenterReplyValidationError{
					field:  "MateV2",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetMateV2()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return FilecenterReplyValidationError{
				field:  "MateV2",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetIaasImage()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, FilecenterReplyValidationError{
					field:  "IaasImage",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, FilecenterReplyValidationError{
					field:  "IaasImage",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetIaasImage()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return FilecenterReplyValidationError{
				field:  "IaasImage",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return FilecenterReplyMultiError(errors)
	}

	return nil
}

// FilecenterReplyMultiError is an error wrapping multiple validation errors
// returned by FilecenterReply.ValidateAll() if the designated constraints
// aren't met.
type FilecenterReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FilecenterReplyMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FilecenterReplyMultiError) AllErrors() []error { return m }

// FilecenterReplyValidationError is the validation error returned by
// FilecenterReply.Validate if the designated constraints aren't met.
type FilecenterReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FilecenterReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FilecenterReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FilecenterReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FilecenterReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FilecenterReplyValidationError) ErrorName() string { return "FilecenterReplyValidationError" }

// Error satisfies the builtin error interface
func (e FilecenterReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFilecenterReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FilecenterReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FilecenterReplyValidationError{}

// Validate checks the field values on DescribesFilecenterReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DescribesFilecenterReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DescribesFilecenterReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DescribesFilecenterReqMultiError, or nil if none found.
func (m *DescribesFilecenterReq) ValidateAll() error {
	return m.validate(true)
}

func (m *DescribesFilecenterReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if val := m.GetPageNumber(); val < -1 || val > 16777216 {
		err := DescribesFilecenterReqValidationError{
			field:  "PageNumber",
			reason: "value must be inside range [-1, 16777216]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if val := m.GetPageSize(); val < 1 || val > 16777216 {
		err := DescribesFilecenterReqValidationError{
			field:  "PageSize",
			reason: "value must be inside range [1, 16777216]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	for idx, item := range m.GetFilters() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, DescribesFilecenterReqValidationError{
						field:  fmt.Sprintf("Filters[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, DescribesFilecenterReqValidationError{
						field:  fmt.Sprintf("Filters[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return DescribesFilecenterReqValidationError{
					field:  fmt.Sprintf("Filters[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	for idx, item := range m.GetSorts() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, DescribesFilecenterReqValidationError{
						field:  fmt.Sprintf("Sorts[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, DescribesFilecenterReqValidationError{
						field:  fmt.Sprintf("Sorts[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return DescribesFilecenterReqValidationError{
					field:  fmt.Sprintf("Sorts[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return DescribesFilecenterReqMultiError(errors)
	}

	return nil
}

// DescribesFilecenterReqMultiError is an error wrapping multiple validation
// errors returned by DescribesFilecenterReq.ValidateAll() if the designated
// constraints aren't met.
type DescribesFilecenterReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DescribesFilecenterReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DescribesFilecenterReqMultiError) AllErrors() []error { return m }

// DescribesFilecenterReqValidationError is the validation error returned by
// DescribesFilecenterReq.Validate if the designated constraints aren't met.
type DescribesFilecenterReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DescribesFilecenterReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DescribesFilecenterReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DescribesFilecenterReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DescribesFilecenterReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DescribesFilecenterReqValidationError) ErrorName() string {
	return "DescribesFilecenterReqValidationError"
}

// Error satisfies the builtin error interface
func (e DescribesFilecenterReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDescribesFilecenterReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DescribesFilecenterReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DescribesFilecenterReqValidationError{}

// Validate checks the field values on DescribesFilecenterReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DescribesFilecenterReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DescribesFilecenterReply with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DescribesFilecenterReplyMultiError, or nil if none found.
func (m *DescribesFilecenterReply) ValidateAll() error {
	return m.validate(true)
}

func (m *DescribesFilecenterReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetList() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, DescribesFilecenterReplyValidationError{
						field:  fmt.Sprintf("List[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, DescribesFilecenterReplyValidationError{
						field:  fmt.Sprintf("List[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return DescribesFilecenterReplyValidationError{
					field:  fmt.Sprintf("List[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for TotalCount

	if len(errors) > 0 {
		return DescribesFilecenterReplyMultiError(errors)
	}

	return nil
}

// DescribesFilecenterReplyMultiError is an error wrapping multiple validation
// errors returned by DescribesFilecenterReply.ValidateAll() if the designated
// constraints aren't met.
type DescribesFilecenterReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DescribesFilecenterReplyMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DescribesFilecenterReplyMultiError) AllErrors() []error { return m }

// DescribesFilecenterReplyValidationError is the validation error returned by
// DescribesFilecenterReply.Validate if the designated constraints aren't met.
type DescribesFilecenterReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DescribesFilecenterReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DescribesFilecenterReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DescribesFilecenterReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DescribesFilecenterReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DescribesFilecenterReplyValidationError) ErrorName() string {
	return "DescribesFilecenterReplyValidationError"
}

// Error satisfies the builtin error interface
func (e DescribesFilecenterReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDescribesFilecenterReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DescribesFilecenterReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DescribesFilecenterReplyValidationError{}

// Validate checks the field values on CreateDeliveryReq with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *CreateDeliveryReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateDeliveryReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateDeliveryReqMultiError, or nil if none found.
func (m *CreateDeliveryReq) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateDeliveryReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	if all {
		switch v := interface{}(m.GetGoods()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateDeliveryReqValidationError{
					field:  "Goods",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateDeliveryReqValidationError{
					field:  "Goods",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetGoods()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateDeliveryReqValidationError{
				field:  "Goods",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if _, ok := _CreateDeliveryReq_Must_InLookup[m.GetMust()]; !ok {
		err := CreateDeliveryReqValidationError{
			field:  "Must",
			reason: "value must be in list [no yes]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return CreateDeliveryReqMultiError(errors)
	}

	return nil
}

// CreateDeliveryReqMultiError is an error wrapping multiple validation errors
// returned by CreateDeliveryReq.ValidateAll() if the designated constraints
// aren't met.
type CreateDeliveryReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateDeliveryReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateDeliveryReqMultiError) AllErrors() []error { return m }

// CreateDeliveryReqValidationError is the validation error returned by
// CreateDeliveryReq.Validate if the designated constraints aren't met.
type CreateDeliveryReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateDeliveryReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateDeliveryReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateDeliveryReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateDeliveryReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateDeliveryReqValidationError) ErrorName() string {
	return "CreateDeliveryReqValidationError"
}

// Error satisfies the builtin error interface
func (e CreateDeliveryReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateDeliveryReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateDeliveryReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateDeliveryReqValidationError{}

var _CreateDeliveryReq_Must_InLookup = map[string]struct{}{
	"no":  {},
	"yes": {},
}

// Validate checks the field values on DeleteDeliveryReq with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *DeleteDeliveryReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DeleteDeliveryReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DeleteDeliveryReqMultiError, or nil if none found.
func (m *DeleteDeliveryReq) ValidateAll() error {
	return m.validate(true)
}

func (m *DeleteDeliveryReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetId() < 1 {
		err := DeleteDeliveryReqValidationError{
			field:  "Id",
			reason: "value must be greater than or equal to 1",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return DeleteDeliveryReqMultiError(errors)
	}

	return nil
}

// DeleteDeliveryReqMultiError is an error wrapping multiple validation errors
// returned by DeleteDeliveryReq.ValidateAll() if the designated constraints
// aren't met.
type DeleteDeliveryReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DeleteDeliveryReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DeleteDeliveryReqMultiError) AllErrors() []error { return m }

// DeleteDeliveryReqValidationError is the validation error returned by
// DeleteDeliveryReq.Validate if the designated constraints aren't met.
type DeleteDeliveryReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DeleteDeliveryReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DeleteDeliveryReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DeleteDeliveryReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DeleteDeliveryReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DeleteDeliveryReqValidationError) ErrorName() string {
	return "DeleteDeliveryReqValidationError"
}

// Error satisfies the builtin error interface
func (e DeleteDeliveryReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDeleteDeliveryReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DeleteDeliveryReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DeleteDeliveryReqValidationError{}

// Validate checks the field values on ModifyDeliveryReq with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *ModifyDeliveryReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ModifyDeliveryReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ModifyDeliveryReqMultiError, or nil if none found.
func (m *ModifyDeliveryReq) ValidateAll() error {
	return m.validate(true)
}

func (m *ModifyDeliveryReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetId() < 1 {
		err := ModifyDeliveryReqValidationError{
			field:  "Id",
			reason: "value must be greater than or equal to 1",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetMust() != "" {

		if _, ok := _ModifyDeliveryReq_Must_InLookup[m.GetMust()]; !ok {
			err := ModifyDeliveryReqValidationError{
				field:  "Must",
				reason: "value must be in list [no yes]",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

	}

	if len(errors) > 0 {
		return ModifyDeliveryReqMultiError(errors)
	}

	return nil
}

// ModifyDeliveryReqMultiError is an error wrapping multiple validation errors
// returned by ModifyDeliveryReq.ValidateAll() if the designated constraints
// aren't met.
type ModifyDeliveryReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ModifyDeliveryReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ModifyDeliveryReqMultiError) AllErrors() []error { return m }

// ModifyDeliveryReqValidationError is the validation error returned by
// ModifyDeliveryReq.Validate if the designated constraints aren't met.
type ModifyDeliveryReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ModifyDeliveryReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ModifyDeliveryReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ModifyDeliveryReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ModifyDeliveryReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ModifyDeliveryReqValidationError) ErrorName() string {
	return "ModifyDeliveryReqValidationError"
}

// Error satisfies the builtin error interface
func (e ModifyDeliveryReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sModifyDeliveryReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ModifyDeliveryReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ModifyDeliveryReqValidationError{}

var _ModifyDeliveryReq_Must_InLookup = map[string]struct{}{
	"no":  {},
	"yes": {},
}

// Validate checks the field values on DescribeDeliveryReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DescribeDeliveryReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DescribeDeliveryReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DescribeDeliveryReqMultiError, or nil if none found.
func (m *DescribeDeliveryReq) ValidateAll() error {
	return m.validate(true)
}

func (m *DescribeDeliveryReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetId() < 1 {
		err := DescribeDeliveryReqValidationError{
			field:  "Id",
			reason: "value must be greater than or equal to 1",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	for idx, item := range m.GetFilters() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, DescribeDeliveryReqValidationError{
						field:  fmt.Sprintf("Filters[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, DescribeDeliveryReqValidationError{
						field:  fmt.Sprintf("Filters[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return DescribeDeliveryReqValidationError{
					field:  fmt.Sprintf("Filters[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return DescribeDeliveryReqMultiError(errors)
	}

	return nil
}

// DescribeDeliveryReqMultiError is an error wrapping multiple validation
// errors returned by DescribeDeliveryReq.ValidateAll() if the designated
// constraints aren't met.
type DescribeDeliveryReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DescribeDeliveryReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DescribeDeliveryReqMultiError) AllErrors() []error { return m }

// DescribeDeliveryReqValidationError is the validation error returned by
// DescribeDeliveryReq.Validate if the designated constraints aren't met.
type DescribeDeliveryReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DescribeDeliveryReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DescribeDeliveryReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DescribeDeliveryReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DescribeDeliveryReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DescribeDeliveryReqValidationError) ErrorName() string {
	return "DescribeDeliveryReqValidationError"
}

// Error satisfies the builtin error interface
func (e DescribeDeliveryReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDescribeDeliveryReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DescribeDeliveryReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DescribeDeliveryReqValidationError{}

// Validate checks the field values on DescribeDeliveryReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DescribeDeliveryReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DescribeDeliveryReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DescribeDeliveryReplyMultiError, or nil if none found.
func (m *DescribeDeliveryReply) ValidateAll() error {
	return m.validate(true)
}

func (m *DescribeDeliveryReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for CreatedAt

	if all {
		switch v := interface{}(m.GetGoods()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DescribeDeliveryReplyValidationError{
					field:  "Goods",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DescribeDeliveryReplyValidationError{
					field:  "Goods",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetGoods()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DescribeDeliveryReplyValidationError{
				field:  "Goods",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Must

	if len(errors) > 0 {
		return DescribeDeliveryReplyMultiError(errors)
	}

	return nil
}

// DescribeDeliveryReplyMultiError is an error wrapping multiple validation
// errors returned by DescribeDeliveryReply.ValidateAll() if the designated
// constraints aren't met.
type DescribeDeliveryReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DescribeDeliveryReplyMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DescribeDeliveryReplyMultiError) AllErrors() []error { return m }

// DescribeDeliveryReplyValidationError is the validation error returned by
// DescribeDeliveryReply.Validate if the designated constraints aren't met.
type DescribeDeliveryReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DescribeDeliveryReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DescribeDeliveryReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DescribeDeliveryReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DescribeDeliveryReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DescribeDeliveryReplyValidationError) ErrorName() string {
	return "DescribeDeliveryReplyValidationError"
}

// Error satisfies the builtin error interface
func (e DescribeDeliveryReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDescribeDeliveryReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DescribeDeliveryReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DescribeDeliveryReplyValidationError{}

// Validate checks the field values on DeliveryReply with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *DeliveryReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DeliveryReply with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in DeliveryReplyMultiError, or
// nil if none found.
func (m *DeliveryReply) ValidateAll() error {
	return m.validate(true)
}

func (m *DeliveryReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for CreatedAt

	// no validation rules for GoodsId

	// no validation rules for Must

	if len(errors) > 0 {
		return DeliveryReplyMultiError(errors)
	}

	return nil
}

// DeliveryReplyMultiError is an error wrapping multiple validation errors
// returned by DeliveryReply.ValidateAll() if the designated constraints
// aren't met.
type DeliveryReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DeliveryReplyMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DeliveryReplyMultiError) AllErrors() []error { return m }

// DeliveryReplyValidationError is the validation error returned by
// DeliveryReply.Validate if the designated constraints aren't met.
type DeliveryReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DeliveryReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DeliveryReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DeliveryReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DeliveryReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DeliveryReplyValidationError) ErrorName() string { return "DeliveryReplyValidationError" }

// Error satisfies the builtin error interface
func (e DeliveryReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDeliveryReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DeliveryReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DeliveryReplyValidationError{}

// Validate checks the field values on DescribesDeliveryReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DescribesDeliveryReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DescribesDeliveryReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DescribesDeliveryReqMultiError, or nil if none found.
func (m *DescribesDeliveryReq) ValidateAll() error {
	return m.validate(true)
}

func (m *DescribesDeliveryReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if val := m.GetPageNumber(); val < -1 || val > 16777216 {
		err := DescribesDeliveryReqValidationError{
			field:  "PageNumber",
			reason: "value must be inside range [-1, 16777216]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if val := m.GetPageSize(); val < 1 || val > 16777216 {
		err := DescribesDeliveryReqValidationError{
			field:  "PageSize",
			reason: "value must be inside range [1, 16777216]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	for idx, item := range m.GetFilters() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, DescribesDeliveryReqValidationError{
						field:  fmt.Sprintf("Filters[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, DescribesDeliveryReqValidationError{
						field:  fmt.Sprintf("Filters[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return DescribesDeliveryReqValidationError{
					field:  fmt.Sprintf("Filters[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	for idx, item := range m.GetSorts() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, DescribesDeliveryReqValidationError{
						field:  fmt.Sprintf("Sorts[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, DescribesDeliveryReqValidationError{
						field:  fmt.Sprintf("Sorts[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return DescribesDeliveryReqValidationError{
					field:  fmt.Sprintf("Sorts[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return DescribesDeliveryReqMultiError(errors)
	}

	return nil
}

// DescribesDeliveryReqMultiError is an error wrapping multiple validation
// errors returned by DescribesDeliveryReq.ValidateAll() if the designated
// constraints aren't met.
type DescribesDeliveryReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DescribesDeliveryReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DescribesDeliveryReqMultiError) AllErrors() []error { return m }

// DescribesDeliveryReqValidationError is the validation error returned by
// DescribesDeliveryReq.Validate if the designated constraints aren't met.
type DescribesDeliveryReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DescribesDeliveryReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DescribesDeliveryReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DescribesDeliveryReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DescribesDeliveryReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DescribesDeliveryReqValidationError) ErrorName() string {
	return "DescribesDeliveryReqValidationError"
}

// Error satisfies the builtin error interface
func (e DescribesDeliveryReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDescribesDeliveryReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DescribesDeliveryReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DescribesDeliveryReqValidationError{}

// Validate checks the field values on DescribesDeliveryReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DescribesDeliveryReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DescribesDeliveryReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DescribesDeliveryReplyMultiError, or nil if none found.
func (m *DescribesDeliveryReply) ValidateAll() error {
	return m.validate(true)
}

func (m *DescribesDeliveryReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetList() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, DescribesDeliveryReplyValidationError{
						field:  fmt.Sprintf("List[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, DescribesDeliveryReplyValidationError{
						field:  fmt.Sprintf("List[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return DescribesDeliveryReplyValidationError{
					field:  fmt.Sprintf("List[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for TotalCount

	if len(errors) > 0 {
		return DescribesDeliveryReplyMultiError(errors)
	}

	return nil
}

// DescribesDeliveryReplyMultiError is an error wrapping multiple validation
// errors returned by DescribesDeliveryReply.ValidateAll() if the designated
// constraints aren't met.
type DescribesDeliveryReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DescribesDeliveryReplyMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DescribesDeliveryReplyMultiError) AllErrors() []error { return m }

// DescribesDeliveryReplyValidationError is the validation error returned by
// DescribesDeliveryReply.Validate if the designated constraints aren't met.
type DescribesDeliveryReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DescribesDeliveryReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DescribesDeliveryReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DescribesDeliveryReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DescribesDeliveryReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DescribesDeliveryReplyValidationError) ErrorName() string {
	return "DescribesDeliveryReplyValidationError"
}

// Error satisfies the builtin error interface
func (e DescribesDeliveryReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDescribesDeliveryReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DescribesDeliveryReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DescribesDeliveryReplyValidationError{}

// Validate checks the field values on CreateDomainReq with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *CreateDomainReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateDomainReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateDomainReqMultiError, or nil if none found.
func (m *CreateDomainReq) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateDomainReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	if all {
		switch v := interface{}(m.GetGoods()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateDomainReqValidationError{
					field:  "Goods",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateDomainReqValidationError{
					field:  "Goods",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetGoods()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateDomainReqValidationError{
				field:  "Goods",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if l := len(m.GetTemplate()); l < 1 || l > 255 {
		err := CreateDomainReqValidationError{
			field:  "Template",
			reason: "value length must be between 1 and 255 bytes, inclusive",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if _, ok := _CreateDomainReq_DomainUse_InLookup[m.GetDomainUse()]; !ok {
		err := CreateDomainReqValidationError{
			field:  "DomainUse",
			reason: "value must be in list [loadBalancer a]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if val := m.GetTargetPort(); val < 0 || val > 16777216 {
		err := CreateDomainReqValidationError{
			field:  "TargetPort",
			reason: "value must be inside range [0, 16777216]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetProtocol() != "" {

		if _, ok := _CreateDomainReq_Protocol_InLookup[m.GetProtocol()]; !ok {
			err := CreateDomainReqValidationError{
				field:  "Protocol",
				reason: "value must be in list [http https tcp]",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

	}

	if len(m.GetDescription()) > 255 {
		err := CreateDomainReqValidationError{
			field:  "Description",
			reason: "value length must be at most 255 bytes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if val := m.GetPort(); val < 0 || val > 16777216 {
		err := CreateDomainReqValidationError{
			field:  "Port",
			reason: "value must be inside range [0, 16777216]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return CreateDomainReqMultiError(errors)
	}

	return nil
}

// CreateDomainReqMultiError is an error wrapping multiple validation errors
// returned by CreateDomainReq.ValidateAll() if the designated constraints
// aren't met.
type CreateDomainReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateDomainReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateDomainReqMultiError) AllErrors() []error { return m }

// CreateDomainReqValidationError is the validation error returned by
// CreateDomainReq.Validate if the designated constraints aren't met.
type CreateDomainReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateDomainReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateDomainReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateDomainReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateDomainReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateDomainReqValidationError) ErrorName() string { return "CreateDomainReqValidationError" }

// Error satisfies the builtin error interface
func (e CreateDomainReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateDomainReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateDomainReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateDomainReqValidationError{}

var _CreateDomainReq_DomainUse_InLookup = map[string]struct{}{
	"loadBalancer": {},
	"a":            {},
}

var _CreateDomainReq_Protocol_InLookup = map[string]struct{}{
	"http":  {},
	"https": {},
	"tcp":   {},
}

// Validate checks the field values on DeleteDomainReq with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *DeleteDomainReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DeleteDomainReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DeleteDomainReqMultiError, or nil if none found.
func (m *DeleteDomainReq) ValidateAll() error {
	return m.validate(true)
}

func (m *DeleteDomainReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetId() < 1 {
		err := DeleteDomainReqValidationError{
			field:  "Id",
			reason: "value must be greater than or equal to 1",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return DeleteDomainReqMultiError(errors)
	}

	return nil
}

// DeleteDomainReqMultiError is an error wrapping multiple validation errors
// returned by DeleteDomainReq.ValidateAll() if the designated constraints
// aren't met.
type DeleteDomainReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DeleteDomainReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DeleteDomainReqMultiError) AllErrors() []error { return m }

// DeleteDomainReqValidationError is the validation error returned by
// DeleteDomainReq.Validate if the designated constraints aren't met.
type DeleteDomainReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DeleteDomainReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DeleteDomainReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DeleteDomainReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DeleteDomainReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DeleteDomainReqValidationError) ErrorName() string { return "DeleteDomainReqValidationError" }

// Error satisfies the builtin error interface
func (e DeleteDomainReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDeleteDomainReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DeleteDomainReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DeleteDomainReqValidationError{}

// Validate checks the field values on ModifyDomainReq with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *ModifyDomainReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ModifyDomainReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ModifyDomainReqMultiError, or nil if none found.
func (m *ModifyDomainReq) ValidateAll() error {
	return m.validate(true)
}

func (m *ModifyDomainReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetId() < 1 {
		err := ModifyDomainReqValidationError{
			field:  "Id",
			reason: "value must be greater than or equal to 1",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if l := len(m.GetTemplate()); l < 0 || l > 255 {
		err := ModifyDomainReqValidationError{
			field:  "Template",
			reason: "value length must be between 0 and 255 bytes, inclusive",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetDomainUse() != "" {

		if _, ok := _ModifyDomainReq_DomainUse_InLookup[m.GetDomainUse()]; !ok {
			err := ModifyDomainReqValidationError{
				field:  "DomainUse",
				reason: "value must be in list [loadBalancer a]",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

	}

	if val := m.GetTargetPort(); val < 0 || val > 16777216 {
		err := ModifyDomainReqValidationError{
			field:  "TargetPort",
			reason: "value must be inside range [0, 16777216]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetProtocol() != "" {

		if _, ok := _ModifyDomainReq_Protocol_InLookup[m.GetProtocol()]; !ok {
			err := ModifyDomainReqValidationError{
				field:  "Protocol",
				reason: "value must be in list [http https tcp]",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

	}

	if len(m.GetDescription()) > 255 {
		err := ModifyDomainReqValidationError{
			field:  "Description",
			reason: "value length must be at most 255 bytes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for Port

	if len(errors) > 0 {
		return ModifyDomainReqMultiError(errors)
	}

	return nil
}

// ModifyDomainReqMultiError is an error wrapping multiple validation errors
// returned by ModifyDomainReq.ValidateAll() if the designated constraints
// aren't met.
type ModifyDomainReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ModifyDomainReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ModifyDomainReqMultiError) AllErrors() []error { return m }

// ModifyDomainReqValidationError is the validation error returned by
// ModifyDomainReq.Validate if the designated constraints aren't met.
type ModifyDomainReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ModifyDomainReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ModifyDomainReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ModifyDomainReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ModifyDomainReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ModifyDomainReqValidationError) ErrorName() string { return "ModifyDomainReqValidationError" }

// Error satisfies the builtin error interface
func (e ModifyDomainReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sModifyDomainReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ModifyDomainReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ModifyDomainReqValidationError{}

var _ModifyDomainReq_DomainUse_InLookup = map[string]struct{}{
	"loadBalancer": {},
	"a":            {},
}

var _ModifyDomainReq_Protocol_InLookup = map[string]struct{}{
	"http":  {},
	"https": {},
	"tcp":   {},
}

// Validate checks the field values on DescribeDomainReq with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *DescribeDomainReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DescribeDomainReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DescribeDomainReqMultiError, or nil if none found.
func (m *DescribeDomainReq) ValidateAll() error {
	return m.validate(true)
}

func (m *DescribeDomainReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetId() < 1 {
		err := DescribeDomainReqValidationError{
			field:  "Id",
			reason: "value must be greater than or equal to 1",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	for idx, item := range m.GetFilters() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, DescribeDomainReqValidationError{
						field:  fmt.Sprintf("Filters[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, DescribeDomainReqValidationError{
						field:  fmt.Sprintf("Filters[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return DescribeDomainReqValidationError{
					field:  fmt.Sprintf("Filters[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return DescribeDomainReqMultiError(errors)
	}

	return nil
}

// DescribeDomainReqMultiError is an error wrapping multiple validation errors
// returned by DescribeDomainReq.ValidateAll() if the designated constraints
// aren't met.
type DescribeDomainReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DescribeDomainReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DescribeDomainReqMultiError) AllErrors() []error { return m }

// DescribeDomainReqValidationError is the validation error returned by
// DescribeDomainReq.Validate if the designated constraints aren't met.
type DescribeDomainReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DescribeDomainReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DescribeDomainReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DescribeDomainReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DescribeDomainReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DescribeDomainReqValidationError) ErrorName() string {
	return "DescribeDomainReqValidationError"
}

// Error satisfies the builtin error interface
func (e DescribeDomainReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDescribeDomainReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DescribeDomainReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DescribeDomainReqValidationError{}

// Validate checks the field values on DescribeDomainReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DescribeDomainReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DescribeDomainReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DescribeDomainReplyMultiError, or nil if none found.
func (m *DescribeDomainReply) ValidateAll() error {
	return m.validate(true)
}

func (m *DescribeDomainReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for CreatedAt

	if all {
		switch v := interface{}(m.GetGoods()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DescribeDomainReplyValidationError{
					field:  "Goods",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DescribeDomainReplyValidationError{
					field:  "Goods",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetGoods()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DescribeDomainReplyValidationError{
				field:  "Goods",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Template

	// no validation rules for DomainUse

	// no validation rules for TargetPort

	// no validation rules for Protocol

	// no validation rules for Description

	// no validation rules for Port

	if len(errors) > 0 {
		return DescribeDomainReplyMultiError(errors)
	}

	return nil
}

// DescribeDomainReplyMultiError is an error wrapping multiple validation
// errors returned by DescribeDomainReply.ValidateAll() if the designated
// constraints aren't met.
type DescribeDomainReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DescribeDomainReplyMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DescribeDomainReplyMultiError) AllErrors() []error { return m }

// DescribeDomainReplyValidationError is the validation error returned by
// DescribeDomainReply.Validate if the designated constraints aren't met.
type DescribeDomainReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DescribeDomainReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DescribeDomainReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DescribeDomainReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DescribeDomainReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DescribeDomainReplyValidationError) ErrorName() string {
	return "DescribeDomainReplyValidationError"
}

// Error satisfies the builtin error interface
func (e DescribeDomainReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDescribeDomainReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DescribeDomainReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DescribeDomainReplyValidationError{}

// Validate checks the field values on DomainReply with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *DomainReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DomainReply with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in DomainReplyMultiError, or
// nil if none found.
func (m *DomainReply) ValidateAll() error {
	return m.validate(true)
}

func (m *DomainReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for CreatedAt

	// no validation rules for GoodsId

	// no validation rules for Template

	// no validation rules for DomainUse

	// no validation rules for TargetPort

	// no validation rules for Protocol

	// no validation rules for Description

	// no validation rules for Port

	if len(errors) > 0 {
		return DomainReplyMultiError(errors)
	}

	return nil
}

// DomainReplyMultiError is an error wrapping multiple validation errors
// returned by DomainReply.ValidateAll() if the designated constraints aren't met.
type DomainReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DomainReplyMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DomainReplyMultiError) AllErrors() []error { return m }

// DomainReplyValidationError is the validation error returned by
// DomainReply.Validate if the designated constraints aren't met.
type DomainReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DomainReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DomainReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DomainReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DomainReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DomainReplyValidationError) ErrorName() string { return "DomainReplyValidationError" }

// Error satisfies the builtin error interface
func (e DomainReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDomainReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DomainReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DomainReplyValidationError{}

// Validate checks the field values on DescribesDomainReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DescribesDomainReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DescribesDomainReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DescribesDomainReqMultiError, or nil if none found.
func (m *DescribesDomainReq) ValidateAll() error {
	return m.validate(true)
}

func (m *DescribesDomainReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if val := m.GetPageNumber(); val < -1 || val > 16777216 {
		err := DescribesDomainReqValidationError{
			field:  "PageNumber",
			reason: "value must be inside range [-1, 16777216]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if val := m.GetPageSize(); val < 1 || val > 16777216 {
		err := DescribesDomainReqValidationError{
			field:  "PageSize",
			reason: "value must be inside range [1, 16777216]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	for idx, item := range m.GetFilters() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, DescribesDomainReqValidationError{
						field:  fmt.Sprintf("Filters[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, DescribesDomainReqValidationError{
						field:  fmt.Sprintf("Filters[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return DescribesDomainReqValidationError{
					field:  fmt.Sprintf("Filters[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	for idx, item := range m.GetSorts() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, DescribesDomainReqValidationError{
						field:  fmt.Sprintf("Sorts[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, DescribesDomainReqValidationError{
						field:  fmt.Sprintf("Sorts[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return DescribesDomainReqValidationError{
					field:  fmt.Sprintf("Sorts[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return DescribesDomainReqMultiError(errors)
	}

	return nil
}

// DescribesDomainReqMultiError is an error wrapping multiple validation errors
// returned by DescribesDomainReq.ValidateAll() if the designated constraints
// aren't met.
type DescribesDomainReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DescribesDomainReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DescribesDomainReqMultiError) AllErrors() []error { return m }

// DescribesDomainReqValidationError is the validation error returned by
// DescribesDomainReq.Validate if the designated constraints aren't met.
type DescribesDomainReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DescribesDomainReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DescribesDomainReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DescribesDomainReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DescribesDomainReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DescribesDomainReqValidationError) ErrorName() string {
	return "DescribesDomainReqValidationError"
}

// Error satisfies the builtin error interface
func (e DescribesDomainReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDescribesDomainReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DescribesDomainReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DescribesDomainReqValidationError{}

// Validate checks the field values on DescribesDomainReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DescribesDomainReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DescribesDomainReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DescribesDomainReplyMultiError, or nil if none found.
func (m *DescribesDomainReply) ValidateAll() error {
	return m.validate(true)
}

func (m *DescribesDomainReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetList() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, DescribesDomainReplyValidationError{
						field:  fmt.Sprintf("List[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, DescribesDomainReplyValidationError{
						field:  fmt.Sprintf("List[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return DescribesDomainReplyValidationError{
					field:  fmt.Sprintf("List[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for TotalCount

	if len(errors) > 0 {
		return DescribesDomainReplyMultiError(errors)
	}

	return nil
}

// DescribesDomainReplyMultiError is an error wrapping multiple validation
// errors returned by DescribesDomainReply.ValidateAll() if the designated
// constraints aren't met.
type DescribesDomainReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DescribesDomainReplyMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DescribesDomainReplyMultiError) AllErrors() []error { return m }

// DescribesDomainReplyValidationError is the validation error returned by
// DescribesDomainReply.Validate if the designated constraints aren't met.
type DescribesDomainReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DescribesDomainReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DescribesDomainReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DescribesDomainReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DescribesDomainReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DescribesDomainReplyValidationError) ErrorName() string {
	return "DescribesDomainReplyValidationError"
}

// Error satisfies the builtin error interface
func (e DescribesDomainReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDescribesDomainReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DescribesDomainReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DescribesDomainReplyValidationError{}

// Validate checks the field values on CreateFlavorReq with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *CreateFlavorReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateFlavorReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateFlavorReqMultiError, or nil if none found.
func (m *CreateFlavorReq) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateFlavorReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	if all {
		switch v := interface{}(m.GetGoods()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateFlavorReqValidationError{
					field:  "Goods",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateFlavorReqValidationError{
					field:  "Goods",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetGoods()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateFlavorReqValidationError{
				field:  "Goods",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if _, ok := _CreateFlavorReq_ClusterSize_InLookup[m.GetClusterSize()]; !ok {
		err := CreateFlavorReqValidationError{
			field:  "ClusterSize",
			reason: "value must be in list [poc light small middle large]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if val := m.GetCoreRequest(); val < 0 || val > 16777216 {
		err := CreateFlavorReqValidationError{
			field:  "CoreRequest",
			reason: "value must be inside range [0, 16777216]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if val := m.GetCoreLimit(); val < 0 || val > 16777216 {
		err := CreateFlavorReqValidationError{
			field:  "CoreLimit",
			reason: "value must be inside range [0, 16777216]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if val := m.GetMemoryRequest(); val < 0 || val > 16777216 {
		err := CreateFlavorReqValidationError{
			field:  "MemoryRequest",
			reason: "value must be inside range [0, 16777216]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if val := m.GetMemoryLimit(); val < 0 || val > 16777216 {
		err := CreateFlavorReqValidationError{
			field:  "MemoryLimit",
			reason: "value must be inside range [0, 16777216]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if val := m.GetDiskRequest(); val < 0 || val > 16777216 {
		err := CreateFlavorReqValidationError{
			field:  "DiskRequest",
			reason: "value must be inside range [0, 16777216]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if val := m.GetDiskLimit(); val < 0 || val > 16777216 {
		err := CreateFlavorReqValidationError{
			field:  "DiskLimit",
			reason: "value must be inside range [0, 16777216]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if val := m.GetReplicas(); val < 0 || val > 16777216 {
		err := CreateFlavorReqValidationError{
			field:  "Replicas",
			reason: "value must be inside range [0, 16777216]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if _, ok := _CreateFlavorReq_Arch_InLookup[m.GetArch()]; !ok {
		err := CreateFlavorReqValidationError{
			field:  "Arch",
			reason: "value must be in list [x86_64 arm]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return CreateFlavorReqMultiError(errors)
	}

	return nil
}

// CreateFlavorReqMultiError is an error wrapping multiple validation errors
// returned by CreateFlavorReq.ValidateAll() if the designated constraints
// aren't met.
type CreateFlavorReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateFlavorReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateFlavorReqMultiError) AllErrors() []error { return m }

// CreateFlavorReqValidationError is the validation error returned by
// CreateFlavorReq.Validate if the designated constraints aren't met.
type CreateFlavorReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateFlavorReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateFlavorReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateFlavorReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateFlavorReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateFlavorReqValidationError) ErrorName() string { return "CreateFlavorReqValidationError" }

// Error satisfies the builtin error interface
func (e CreateFlavorReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateFlavorReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateFlavorReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateFlavorReqValidationError{}

var _CreateFlavorReq_ClusterSize_InLookup = map[string]struct{}{
	"poc":    {},
	"light":  {},
	"small":  {},
	"middle": {},
	"large":  {},
}

var _CreateFlavorReq_Arch_InLookup = map[string]struct{}{
	"x86_64": {},
	"arm":    {},
}

// Validate checks the field values on DeleteFlavorReq with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *DeleteFlavorReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DeleteFlavorReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DeleteFlavorReqMultiError, or nil if none found.
func (m *DeleteFlavorReq) ValidateAll() error {
	return m.validate(true)
}

func (m *DeleteFlavorReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetId() < 1 {
		err := DeleteFlavorReqValidationError{
			field:  "Id",
			reason: "value must be greater than or equal to 1",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return DeleteFlavorReqMultiError(errors)
	}

	return nil
}

// DeleteFlavorReqMultiError is an error wrapping multiple validation errors
// returned by DeleteFlavorReq.ValidateAll() if the designated constraints
// aren't met.
type DeleteFlavorReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DeleteFlavorReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DeleteFlavorReqMultiError) AllErrors() []error { return m }

// DeleteFlavorReqValidationError is the validation error returned by
// DeleteFlavorReq.Validate if the designated constraints aren't met.
type DeleteFlavorReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DeleteFlavorReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DeleteFlavorReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DeleteFlavorReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DeleteFlavorReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DeleteFlavorReqValidationError) ErrorName() string { return "DeleteFlavorReqValidationError" }

// Error satisfies the builtin error interface
func (e DeleteFlavorReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDeleteFlavorReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DeleteFlavorReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DeleteFlavorReqValidationError{}

// Validate checks the field values on ModifyFlavorReq with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *ModifyFlavorReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ModifyFlavorReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ModifyFlavorReqMultiError, or nil if none found.
func (m *ModifyFlavorReq) ValidateAll() error {
	return m.validate(true)
}

func (m *ModifyFlavorReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetId() < 1 {
		err := ModifyFlavorReqValidationError{
			field:  "Id",
			reason: "value must be greater than or equal to 1",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetCoreRequest() > 16777216 {
		err := ModifyFlavorReqValidationError{
			field:  "CoreRequest",
			reason: "value must be less than or equal to 16777216",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetCoreLimit() > 16777216 {
		err := ModifyFlavorReqValidationError{
			field:  "CoreLimit",
			reason: "value must be less than or equal to 16777216",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetMemoryRequest() > 16777216 {
		err := ModifyFlavorReqValidationError{
			field:  "MemoryRequest",
			reason: "value must be less than or equal to 16777216",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetMemoryLimit() > 16777216 {
		err := ModifyFlavorReqValidationError{
			field:  "MemoryLimit",
			reason: "value must be less than or equal to 16777216",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetDiskRequest() > 16777216 {
		err := ModifyFlavorReqValidationError{
			field:  "DiskRequest",
			reason: "value must be less than or equal to 16777216",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetDiskLimit() > 16777216 {
		err := ModifyFlavorReqValidationError{
			field:  "DiskLimit",
			reason: "value must be less than or equal to 16777216",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetReplicas() > 16777216 {
		err := ModifyFlavorReqValidationError{
			field:  "Replicas",
			reason: "value must be less than or equal to 16777216",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return ModifyFlavorReqMultiError(errors)
	}

	return nil
}

// ModifyFlavorReqMultiError is an error wrapping multiple validation errors
// returned by ModifyFlavorReq.ValidateAll() if the designated constraints
// aren't met.
type ModifyFlavorReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ModifyFlavorReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ModifyFlavorReqMultiError) AllErrors() []error { return m }

// ModifyFlavorReqValidationError is the validation error returned by
// ModifyFlavorReq.Validate if the designated constraints aren't met.
type ModifyFlavorReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ModifyFlavorReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ModifyFlavorReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ModifyFlavorReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ModifyFlavorReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ModifyFlavorReqValidationError) ErrorName() string { return "ModifyFlavorReqValidationError" }

// Error satisfies the builtin error interface
func (e ModifyFlavorReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sModifyFlavorReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ModifyFlavorReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ModifyFlavorReqValidationError{}

// Validate checks the field values on DescribeFlavorReq with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *DescribeFlavorReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DescribeFlavorReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DescribeFlavorReqMultiError, or nil if none found.
func (m *DescribeFlavorReq) ValidateAll() error {
	return m.validate(true)
}

func (m *DescribeFlavorReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetId() < 1 {
		err := DescribeFlavorReqValidationError{
			field:  "Id",
			reason: "value must be greater than or equal to 1",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	for idx, item := range m.GetFilters() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, DescribeFlavorReqValidationError{
						field:  fmt.Sprintf("Filters[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, DescribeFlavorReqValidationError{
						field:  fmt.Sprintf("Filters[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return DescribeFlavorReqValidationError{
					field:  fmt.Sprintf("Filters[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return DescribeFlavorReqMultiError(errors)
	}

	return nil
}

// DescribeFlavorReqMultiError is an error wrapping multiple validation errors
// returned by DescribeFlavorReq.ValidateAll() if the designated constraints
// aren't met.
type DescribeFlavorReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DescribeFlavorReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DescribeFlavorReqMultiError) AllErrors() []error { return m }

// DescribeFlavorReqValidationError is the validation error returned by
// DescribeFlavorReq.Validate if the designated constraints aren't met.
type DescribeFlavorReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DescribeFlavorReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DescribeFlavorReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DescribeFlavorReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DescribeFlavorReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DescribeFlavorReqValidationError) ErrorName() string {
	return "DescribeFlavorReqValidationError"
}

// Error satisfies the builtin error interface
func (e DescribeFlavorReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDescribeFlavorReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DescribeFlavorReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DescribeFlavorReqValidationError{}

// Validate checks the field values on DescribeFlavorReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DescribeFlavorReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DescribeFlavorReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DescribeFlavorReplyMultiError, or nil if none found.
func (m *DescribeFlavorReply) ValidateAll() error {
	return m.validate(true)
}

func (m *DescribeFlavorReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for CreatedAt

	if all {
		switch v := interface{}(m.GetGoods()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DescribeFlavorReplyValidationError{
					field:  "Goods",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DescribeFlavorReplyValidationError{
					field:  "Goods",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetGoods()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DescribeFlavorReplyValidationError{
				field:  "Goods",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for ClusterSize

	// no validation rules for CoreRequest

	// no validation rules for CoreLimit

	// no validation rules for MemoryRequest

	// no validation rules for MemoryLimit

	// no validation rules for DiskRequest

	// no validation rules for DiskLimit

	// no validation rules for Replicas

	// no validation rules for Arch

	if len(errors) > 0 {
		return DescribeFlavorReplyMultiError(errors)
	}

	return nil
}

// DescribeFlavorReplyMultiError is an error wrapping multiple validation
// errors returned by DescribeFlavorReply.ValidateAll() if the designated
// constraints aren't met.
type DescribeFlavorReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DescribeFlavorReplyMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DescribeFlavorReplyMultiError) AllErrors() []error { return m }

// DescribeFlavorReplyValidationError is the validation error returned by
// DescribeFlavorReply.Validate if the designated constraints aren't met.
type DescribeFlavorReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DescribeFlavorReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DescribeFlavorReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DescribeFlavorReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DescribeFlavorReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DescribeFlavorReplyValidationError) ErrorName() string {
	return "DescribeFlavorReplyValidationError"
}

// Error satisfies the builtin error interface
func (e DescribeFlavorReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDescribeFlavorReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DescribeFlavorReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DescribeFlavorReplyValidationError{}

// Validate checks the field values on FlavorReply with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *FlavorReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on FlavorReply with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in FlavorReplyMultiError, or
// nil if none found.
func (m *FlavorReply) ValidateAll() error {
	return m.validate(true)
}

func (m *FlavorReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for CreatedAt

	// no validation rules for GoodsId

	// no validation rules for ClusterSize

	// no validation rules for CoreRequest

	// no validation rules for CoreLimit

	// no validation rules for MemoryRequest

	// no validation rules for MemoryLimit

	// no validation rules for DiskRequest

	// no validation rules for DiskLimit

	// no validation rules for Replicas

	// no validation rules for Arch

	if len(errors) > 0 {
		return FlavorReplyMultiError(errors)
	}

	return nil
}

// FlavorReplyMultiError is an error wrapping multiple validation errors
// returned by FlavorReply.ValidateAll() if the designated constraints aren't met.
type FlavorReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FlavorReplyMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FlavorReplyMultiError) AllErrors() []error { return m }

// FlavorReplyValidationError is the validation error returned by
// FlavorReply.Validate if the designated constraints aren't met.
type FlavorReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FlavorReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FlavorReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FlavorReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FlavorReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FlavorReplyValidationError) ErrorName() string { return "FlavorReplyValidationError" }

// Error satisfies the builtin error interface
func (e FlavorReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFlavorReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FlavorReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FlavorReplyValidationError{}

// Validate checks the field values on DescribesFlavorReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DescribesFlavorReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DescribesFlavorReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DescribesFlavorReqMultiError, or nil if none found.
func (m *DescribesFlavorReq) ValidateAll() error {
	return m.validate(true)
}

func (m *DescribesFlavorReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if val := m.GetPageNumber(); val < -1 || val > 16777216 {
		err := DescribesFlavorReqValidationError{
			field:  "PageNumber",
			reason: "value must be inside range [-1, 16777216]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if val := m.GetPageSize(); val < 1 || val > 16777216 {
		err := DescribesFlavorReqValidationError{
			field:  "PageSize",
			reason: "value must be inside range [1, 16777216]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	for idx, item := range m.GetFilters() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, DescribesFlavorReqValidationError{
						field:  fmt.Sprintf("Filters[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, DescribesFlavorReqValidationError{
						field:  fmt.Sprintf("Filters[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return DescribesFlavorReqValidationError{
					field:  fmt.Sprintf("Filters[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	for idx, item := range m.GetSorts() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, DescribesFlavorReqValidationError{
						field:  fmt.Sprintf("Sorts[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, DescribesFlavorReqValidationError{
						field:  fmt.Sprintf("Sorts[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return DescribesFlavorReqValidationError{
					field:  fmt.Sprintf("Sorts[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return DescribesFlavorReqMultiError(errors)
	}

	return nil
}

// DescribesFlavorReqMultiError is an error wrapping multiple validation errors
// returned by DescribesFlavorReq.ValidateAll() if the designated constraints
// aren't met.
type DescribesFlavorReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DescribesFlavorReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DescribesFlavorReqMultiError) AllErrors() []error { return m }

// DescribesFlavorReqValidationError is the validation error returned by
// DescribesFlavorReq.Validate if the designated constraints aren't met.
type DescribesFlavorReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DescribesFlavorReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DescribesFlavorReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DescribesFlavorReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DescribesFlavorReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DescribesFlavorReqValidationError) ErrorName() string {
	return "DescribesFlavorReqValidationError"
}

// Error satisfies the builtin error interface
func (e DescribesFlavorReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDescribesFlavorReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DescribesFlavorReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DescribesFlavorReqValidationError{}

// Validate checks the field values on DescribesFlavorReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DescribesFlavorReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DescribesFlavorReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DescribesFlavorReplyMultiError, or nil if none found.
func (m *DescribesFlavorReply) ValidateAll() error {
	return m.validate(true)
}

func (m *DescribesFlavorReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetList() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, DescribesFlavorReplyValidationError{
						field:  fmt.Sprintf("List[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, DescribesFlavorReplyValidationError{
						field:  fmt.Sprintf("List[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return DescribesFlavorReplyValidationError{
					field:  fmt.Sprintf("List[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for TotalCount

	if len(errors) > 0 {
		return DescribesFlavorReplyMultiError(errors)
	}

	return nil
}

// DescribesFlavorReplyMultiError is an error wrapping multiple validation
// errors returned by DescribesFlavorReply.ValidateAll() if the designated
// constraints aren't met.
type DescribesFlavorReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DescribesFlavorReplyMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DescribesFlavorReplyMultiError) AllErrors() []error { return m }

// DescribesFlavorReplyValidationError is the validation error returned by
// DescribesFlavorReply.Validate if the designated constraints aren't met.
type DescribesFlavorReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DescribesFlavorReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DescribesFlavorReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DescribesFlavorReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DescribesFlavorReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DescribesFlavorReplyValidationError) ErrorName() string {
	return "DescribesFlavorReplyValidationError"
}

// Error satisfies the builtin error interface
func (e DescribesFlavorReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDescribesFlavorReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DescribesFlavorReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DescribesFlavorReplyValidationError{}

// Validate checks the field values on CreateIacReq with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *CreateIacReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateIacReq with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in CreateIacReqMultiError, or
// nil if none found.
func (m *CreateIacReq) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateIacReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	if all {
		switch v := interface{}(m.GetGoods()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateIacReqValidationError{
					field:  "Goods",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateIacReqValidationError{
					field:  "Goods",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetGoods()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateIacReqValidationError{
				field:  "Goods",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if _, ok := _CreateIacReq_Mode_InLookup[m.GetMode()]; !ok {
		err := CreateIacReqValidationError{
			field:  "Mode",
			reason: "value must be in list [deploy upgrade]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return CreateIacReqMultiError(errors)
	}

	return nil
}

// CreateIacReqMultiError is an error wrapping multiple validation errors
// returned by CreateIacReq.ValidateAll() if the designated constraints aren't met.
type CreateIacReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateIacReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateIacReqMultiError) AllErrors() []error { return m }

// CreateIacReqValidationError is the validation error returned by
// CreateIacReq.Validate if the designated constraints aren't met.
type CreateIacReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateIacReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateIacReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateIacReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateIacReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateIacReqValidationError) ErrorName() string { return "CreateIacReqValidationError" }

// Error satisfies the builtin error interface
func (e CreateIacReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateIacReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateIacReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateIacReqValidationError{}

var _CreateIacReq_Mode_InLookup = map[string]struct{}{
	"deploy":  {},
	"upgrade": {},
}

// Validate checks the field values on DeleteIacReq with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *DeleteIacReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DeleteIacReq with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in DeleteIacReqMultiError, or
// nil if none found.
func (m *DeleteIacReq) ValidateAll() error {
	return m.validate(true)
}

func (m *DeleteIacReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetId() < 1 {
		err := DeleteIacReqValidationError{
			field:  "Id",
			reason: "value must be greater than or equal to 1",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return DeleteIacReqMultiError(errors)
	}

	return nil
}

// DeleteIacReqMultiError is an error wrapping multiple validation errors
// returned by DeleteIacReq.ValidateAll() if the designated constraints aren't met.
type DeleteIacReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DeleteIacReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DeleteIacReqMultiError) AllErrors() []error { return m }

// DeleteIacReqValidationError is the validation error returned by
// DeleteIacReq.Validate if the designated constraints aren't met.
type DeleteIacReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DeleteIacReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DeleteIacReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DeleteIacReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DeleteIacReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DeleteIacReqValidationError) ErrorName() string { return "DeleteIacReqValidationError" }

// Error satisfies the builtin error interface
func (e DeleteIacReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDeleteIacReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DeleteIacReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DeleteIacReqValidationError{}

// Validate checks the field values on ModifyIacReq with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *ModifyIacReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ModifyIacReq with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in ModifyIacReqMultiError, or
// nil if none found.
func (m *ModifyIacReq) ValidateAll() error {
	return m.validate(true)
}

func (m *ModifyIacReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetId() < 1 {
		err := ModifyIacReqValidationError{
			field:  "Id",
			reason: "value must be greater than or equal to 1",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return ModifyIacReqMultiError(errors)
	}

	return nil
}

// ModifyIacReqMultiError is an error wrapping multiple validation errors
// returned by ModifyIacReq.ValidateAll() if the designated constraints aren't met.
type ModifyIacReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ModifyIacReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ModifyIacReqMultiError) AllErrors() []error { return m }

// ModifyIacReqValidationError is the validation error returned by
// ModifyIacReq.Validate if the designated constraints aren't met.
type ModifyIacReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ModifyIacReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ModifyIacReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ModifyIacReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ModifyIacReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ModifyIacReqValidationError) ErrorName() string { return "ModifyIacReqValidationError" }

// Error satisfies the builtin error interface
func (e ModifyIacReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sModifyIacReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ModifyIacReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ModifyIacReqValidationError{}

// Validate checks the field values on DescribeIacReq with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *DescribeIacReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DescribeIacReq with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in DescribeIacReqMultiError,
// or nil if none found.
func (m *DescribeIacReq) ValidateAll() error {
	return m.validate(true)
}

func (m *DescribeIacReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetId() < 1 {
		err := DescribeIacReqValidationError{
			field:  "Id",
			reason: "value must be greater than or equal to 1",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	for idx, item := range m.GetFilters() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, DescribeIacReqValidationError{
						field:  fmt.Sprintf("Filters[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, DescribeIacReqValidationError{
						field:  fmt.Sprintf("Filters[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return DescribeIacReqValidationError{
					field:  fmt.Sprintf("Filters[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return DescribeIacReqMultiError(errors)
	}

	return nil
}

// DescribeIacReqMultiError is an error wrapping multiple validation errors
// returned by DescribeIacReq.ValidateAll() if the designated constraints
// aren't met.
type DescribeIacReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DescribeIacReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DescribeIacReqMultiError) AllErrors() []error { return m }

// DescribeIacReqValidationError is the validation error returned by
// DescribeIacReq.Validate if the designated constraints aren't met.
type DescribeIacReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DescribeIacReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DescribeIacReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DescribeIacReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DescribeIacReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DescribeIacReqValidationError) ErrorName() string { return "DescribeIacReqValidationError" }

// Error satisfies the builtin error interface
func (e DescribeIacReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDescribeIacReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DescribeIacReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DescribeIacReqValidationError{}

// Validate checks the field values on DescribeIacReply with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *DescribeIacReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DescribeIacReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DescribeIacReplyMultiError, or nil if none found.
func (m *DescribeIacReply) ValidateAll() error {
	return m.validate(true)
}

func (m *DescribeIacReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for CreatedAt

	if all {
		switch v := interface{}(m.GetGoods()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DescribeIacReplyValidationError{
					field:  "Goods",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DescribeIacReplyValidationError{
					field:  "Goods",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetGoods()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DescribeIacReplyValidationError{
				field:  "Goods",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Mode

	if len(errors) > 0 {
		return DescribeIacReplyMultiError(errors)
	}

	return nil
}

// DescribeIacReplyMultiError is an error wrapping multiple validation errors
// returned by DescribeIacReply.ValidateAll() if the designated constraints
// aren't met.
type DescribeIacReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DescribeIacReplyMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DescribeIacReplyMultiError) AllErrors() []error { return m }

// DescribeIacReplyValidationError is the validation error returned by
// DescribeIacReply.Validate if the designated constraints aren't met.
type DescribeIacReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DescribeIacReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DescribeIacReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DescribeIacReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DescribeIacReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DescribeIacReplyValidationError) ErrorName() string { return "DescribeIacReplyValidationError" }

// Error satisfies the builtin error interface
func (e DescribeIacReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDescribeIacReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DescribeIacReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DescribeIacReplyValidationError{}

// Validate checks the field values on IacReply with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *IacReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on IacReply with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in IacReplyMultiError, or nil
// if none found.
func (m *IacReply) ValidateAll() error {
	return m.validate(true)
}

func (m *IacReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for CreatedAt

	// no validation rules for GoodsId

	// no validation rules for Mode

	if len(errors) > 0 {
		return IacReplyMultiError(errors)
	}

	return nil
}

// IacReplyMultiError is an error wrapping multiple validation errors returned
// by IacReply.ValidateAll() if the designated constraints aren't met.
type IacReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m IacReplyMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m IacReplyMultiError) AllErrors() []error { return m }

// IacReplyValidationError is the validation error returned by
// IacReply.Validate if the designated constraints aren't met.
type IacReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e IacReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e IacReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e IacReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e IacReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e IacReplyValidationError) ErrorName() string { return "IacReplyValidationError" }

// Error satisfies the builtin error interface
func (e IacReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sIacReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = IacReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = IacReplyValidationError{}

// Validate checks the field values on DescribesIacReq with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *DescribesIacReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DescribesIacReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DescribesIacReqMultiError, or nil if none found.
func (m *DescribesIacReq) ValidateAll() error {
	return m.validate(true)
}

func (m *DescribesIacReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if val := m.GetPageNumber(); val < -1 || val > 16777216 {
		err := DescribesIacReqValidationError{
			field:  "PageNumber",
			reason: "value must be inside range [-1, 16777216]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if val := m.GetPageSize(); val < 1 || val > 16777216 {
		err := DescribesIacReqValidationError{
			field:  "PageSize",
			reason: "value must be inside range [1, 16777216]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	for idx, item := range m.GetFilters() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, DescribesIacReqValidationError{
						field:  fmt.Sprintf("Filters[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, DescribesIacReqValidationError{
						field:  fmt.Sprintf("Filters[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return DescribesIacReqValidationError{
					field:  fmt.Sprintf("Filters[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	for idx, item := range m.GetSorts() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, DescribesIacReqValidationError{
						field:  fmt.Sprintf("Sorts[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, DescribesIacReqValidationError{
						field:  fmt.Sprintf("Sorts[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return DescribesIacReqValidationError{
					field:  fmt.Sprintf("Sorts[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return DescribesIacReqMultiError(errors)
	}

	return nil
}

// DescribesIacReqMultiError is an error wrapping multiple validation errors
// returned by DescribesIacReq.ValidateAll() if the designated constraints
// aren't met.
type DescribesIacReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DescribesIacReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DescribesIacReqMultiError) AllErrors() []error { return m }

// DescribesIacReqValidationError is the validation error returned by
// DescribesIacReq.Validate if the designated constraints aren't met.
type DescribesIacReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DescribesIacReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DescribesIacReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DescribesIacReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DescribesIacReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DescribesIacReqValidationError) ErrorName() string { return "DescribesIacReqValidationError" }

// Error satisfies the builtin error interface
func (e DescribesIacReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDescribesIacReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DescribesIacReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DescribesIacReqValidationError{}

// Validate checks the field values on DescribesIacReply with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *DescribesIacReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DescribesIacReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DescribesIacReplyMultiError, or nil if none found.
func (m *DescribesIacReply) ValidateAll() error {
	return m.validate(true)
}

func (m *DescribesIacReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetList() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, DescribesIacReplyValidationError{
						field:  fmt.Sprintf("List[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, DescribesIacReplyValidationError{
						field:  fmt.Sprintf("List[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return DescribesIacReplyValidationError{
					field:  fmt.Sprintf("List[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for TotalCount

	if len(errors) > 0 {
		return DescribesIacReplyMultiError(errors)
	}

	return nil
}

// DescribesIacReplyMultiError is an error wrapping multiple validation errors
// returned by DescribesIacReply.ValidateAll() if the designated constraints
// aren't met.
type DescribesIacReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DescribesIacReplyMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DescribesIacReplyMultiError) AllErrors() []error { return m }

// DescribesIacReplyValidationError is the validation error returned by
// DescribesIacReply.Validate if the designated constraints aren't met.
type DescribesIacReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DescribesIacReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DescribesIacReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DescribesIacReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DescribesIacReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DescribesIacReplyValidationError) ErrorName() string {
	return "DescribesIacReplyValidationError"
}

// Error satisfies the builtin error interface
func (e DescribesIacReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDescribesIacReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DescribesIacReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DescribesIacReplyValidationError{}

// Validate checks the field values on MiddlewareParams with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *MiddlewareParams) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on MiddlewareParams with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// MiddlewareParamsMultiError, or nil if none found.
func (m *MiddlewareParams) ValidateAll() error {
	return m.validate(true)
}

func (m *MiddlewareParams) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for User

	// no validation rules for Password

	// no validation rules for Database

	if len(errors) > 0 {
		return MiddlewareParamsMultiError(errors)
	}

	return nil
}

// MiddlewareParamsMultiError is an error wrapping multiple validation errors
// returned by MiddlewareParams.ValidateAll() if the designated constraints
// aren't met.
type MiddlewareParamsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m MiddlewareParamsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m MiddlewareParamsMultiError) AllErrors() []error { return m }

// MiddlewareParamsValidationError is the validation error returned by
// MiddlewareParams.Validate if the designated constraints aren't met.
type MiddlewareParamsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e MiddlewareParamsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e MiddlewareParamsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e MiddlewareParamsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e MiddlewareParamsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e MiddlewareParamsValidationError) ErrorName() string { return "MiddlewareParamsValidationError" }

// Error satisfies the builtin error interface
func (e MiddlewareParamsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sMiddlewareParams.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = MiddlewareParamsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = MiddlewareParamsValidationError{}

// Validate checks the field values on CreateMiddlewareReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreateMiddlewareReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateMiddlewareReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateMiddlewareReqMultiError, or nil if none found.
func (m *CreateMiddlewareReq) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateMiddlewareReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	for idx, item := range m.GetGoods() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, CreateMiddlewareReqValidationError{
						field:  fmt.Sprintf("Goods[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, CreateMiddlewareReqValidationError{
						field:  fmt.Sprintf("Goods[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return CreateMiddlewareReqValidationError{
					field:  fmt.Sprintf("Goods[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(m.GetName()) < 1 {
		err := CreateMiddlewareReqValidationError{
			field:  "Name",
			reason: "value length must be at least 1 bytes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if _, ok := _CreateMiddlewareReq_Kind_InLookup[m.GetKind()]; !ok {
		err := CreateMiddlewareReqValidationError{
			field:  "Kind",
			reason: "value must be in list [mysql postgresql elasticsearch redis zookeeper etcd clickhouse kafka mongodb]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(m.GetUrlTemplate()) > 800 {
		err := CreateMiddlewareReqValidationError{
			field:  "UrlTemplate",
			reason: "value length must be at most 800 bytes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetProtocol() != "" {

		if _, ok := _CreateMiddlewareReq_Protocol_InLookup[m.GetProtocol()]; !ok {
			err := CreateMiddlewareReqValidationError{
				field:  "Protocol",
				reason: "value must be in list [http https tcp]",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

	}

	if _, ok := _CreateMiddlewareReq_DomainUse_InLookup[m.GetDomainUse()]; !ok {
		err := CreateMiddlewareReqValidationError{
			field:  "DomainUse",
			reason: "value must be in list [loadBalancer a]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if val := m.GetTargetPort(); val < 0 || val > 16777216 {
		err := CreateMiddlewareReqValidationError{
			field:  "TargetPort",
			reason: "value must be inside range [0, 16777216]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetParams()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateMiddlewareReqValidationError{
					field:  "Params",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateMiddlewareReqValidationError{
					field:  "Params",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetParams()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateMiddlewareReqValidationError{
				field:  "Params",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if val := m.GetPort(); val < 0 || val > 16777216 {
		err := CreateMiddlewareReqValidationError{
			field:  "Port",
			reason: "value must be inside range [0, 16777216]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if val := m.GetCoreRequest(); val < 0 || val > 1000 {
		err := CreateMiddlewareReqValidationError{
			field:  "CoreRequest",
			reason: "value must be inside range [0, 1000]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if val := m.GetCoreLimit(); val < 0 || val > 1000 {
		err := CreateMiddlewareReqValidationError{
			field:  "CoreLimit",
			reason: "value must be inside range [0, 1000]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetMemoryRequest() < 0 {
		err := CreateMiddlewareReqValidationError{
			field:  "MemoryRequest",
			reason: "value must be greater than or equal to 0",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if val := m.GetMemoryLimit(); val < 0 || val > 1000 {
		err := CreateMiddlewareReqValidationError{
			field:  "MemoryLimit",
			reason: "value must be inside range [0, 1000]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if val := m.GetDiskRequest(); val < 0 || val > 1000 {
		err := CreateMiddlewareReqValidationError{
			field:  "DiskRequest",
			reason: "value must be inside range [0, 1000]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if val := m.GetDiskLimit(); val < 0 || val > 1000 {
		err := CreateMiddlewareReqValidationError{
			field:  "DiskLimit",
			reason: "value must be inside range [0, 1000]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetShareMode() != "" {

		if _, ok := _CreateMiddlewareReq_ShareMode_InLookup[m.GetShareMode()]; !ok {
			err := CreateMiddlewareReqValidationError{
				field:  "ShareMode",
				reason: "value must be in list [no yes]",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

	}

	if len(errors) > 0 {
		return CreateMiddlewareReqMultiError(errors)
	}

	return nil
}

// CreateMiddlewareReqMultiError is an error wrapping multiple validation
// errors returned by CreateMiddlewareReq.ValidateAll() if the designated
// constraints aren't met.
type CreateMiddlewareReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateMiddlewareReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateMiddlewareReqMultiError) AllErrors() []error { return m }

// CreateMiddlewareReqValidationError is the validation error returned by
// CreateMiddlewareReq.Validate if the designated constraints aren't met.
type CreateMiddlewareReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateMiddlewareReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateMiddlewareReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateMiddlewareReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateMiddlewareReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateMiddlewareReqValidationError) ErrorName() string {
	return "CreateMiddlewareReqValidationError"
}

// Error satisfies the builtin error interface
func (e CreateMiddlewareReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateMiddlewareReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateMiddlewareReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateMiddlewareReqValidationError{}

var _CreateMiddlewareReq_Kind_InLookup = map[string]struct{}{
	"mysql":         {},
	"postgresql":    {},
	"elasticsearch": {},
	"redis":         {},
	"zookeeper":     {},
	"etcd":          {},
	"clickhouse":    {},
	"kafka":         {},
	"mongodb":       {},
}

var _CreateMiddlewareReq_Protocol_InLookup = map[string]struct{}{
	"http":  {},
	"https": {},
	"tcp":   {},
}

var _CreateMiddlewareReq_DomainUse_InLookup = map[string]struct{}{
	"loadBalancer": {},
	"a":            {},
}

var _CreateMiddlewareReq_ShareMode_InLookup = map[string]struct{}{
	"no":  {},
	"yes": {},
}

// Validate checks the field values on DeleteMiddlewareReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DeleteMiddlewareReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DeleteMiddlewareReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DeleteMiddlewareReqMultiError, or nil if none found.
func (m *DeleteMiddlewareReq) ValidateAll() error {
	return m.validate(true)
}

func (m *DeleteMiddlewareReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for GoodsId

	// no validation rules for Name

	// no validation rules for Kind

	if len(errors) > 0 {
		return DeleteMiddlewareReqMultiError(errors)
	}

	return nil
}

// DeleteMiddlewareReqMultiError is an error wrapping multiple validation
// errors returned by DeleteMiddlewareReq.ValidateAll() if the designated
// constraints aren't met.
type DeleteMiddlewareReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DeleteMiddlewareReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DeleteMiddlewareReqMultiError) AllErrors() []error { return m }

// DeleteMiddlewareReqValidationError is the validation error returned by
// DeleteMiddlewareReq.Validate if the designated constraints aren't met.
type DeleteMiddlewareReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DeleteMiddlewareReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DeleteMiddlewareReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DeleteMiddlewareReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DeleteMiddlewareReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DeleteMiddlewareReqValidationError) ErrorName() string {
	return "DeleteMiddlewareReqValidationError"
}

// Error satisfies the builtin error interface
func (e DeleteMiddlewareReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDeleteMiddlewareReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DeleteMiddlewareReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DeleteMiddlewareReqValidationError{}

// Validate checks the field values on ModifyMiddlewareReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ModifyMiddlewareReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ModifyMiddlewareReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ModifyMiddlewareReqMultiError, or nil if none found.
func (m *ModifyMiddlewareReq) ValidateAll() error {
	return m.validate(true)
}

func (m *ModifyMiddlewareReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetId() < 1 {
		err := ModifyMiddlewareReqValidationError{
			field:  "Id",
			reason: "value must be greater than or equal to 1",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(m.GetUrlTemplate()) > 800 {
		err := ModifyMiddlewareReqValidationError{
			field:  "UrlTemplate",
			reason: "value length must be at most 800 bytes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetProtocol() != "" {

		if _, ok := _ModifyMiddlewareReq_Protocol_InLookup[m.GetProtocol()]; !ok {
			err := ModifyMiddlewareReqValidationError{
				field:  "Protocol",
				reason: "value must be in list [http https tcp]",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

	}

	if m.GetDomainUse() != "" {

		if _, ok := _ModifyMiddlewareReq_DomainUse_InLookup[m.GetDomainUse()]; !ok {
			err := ModifyMiddlewareReqValidationError{
				field:  "DomainUse",
				reason: "value must be in list [loadBalancer a]",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

	}

	if val := m.GetTargetPort(); val < 0 || val > 16777216 {
		err := ModifyMiddlewareReqValidationError{
			field:  "TargetPort",
			reason: "value must be inside range [0, 16777216]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetParams()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ModifyMiddlewareReqValidationError{
					field:  "Params",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ModifyMiddlewareReqValidationError{
					field:  "Params",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetParams()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ModifyMiddlewareReqValidationError{
				field:  "Params",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Port

	if val := m.GetCoreRequest(); val < 0 || val > 1000 {
		err := ModifyMiddlewareReqValidationError{
			field:  "CoreRequest",
			reason: "value must be inside range [0, 1000]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if val := m.GetCoreLimit(); val < 0 || val > 1000 {
		err := ModifyMiddlewareReqValidationError{
			field:  "CoreLimit",
			reason: "value must be inside range [0, 1000]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetMemoryRequest() < 0 {
		err := ModifyMiddlewareReqValidationError{
			field:  "MemoryRequest",
			reason: "value must be greater than or equal to 0",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if val := m.GetMemoryLimit(); val < 0 || val > 1000 {
		err := ModifyMiddlewareReqValidationError{
			field:  "MemoryLimit",
			reason: "value must be inside range [0, 1000]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if val := m.GetDiskRequest(); val < 0 || val > 1000 {
		err := ModifyMiddlewareReqValidationError{
			field:  "DiskRequest",
			reason: "value must be inside range [0, 1000]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if val := m.GetDiskLimit(); val < 0 || val > 1000 {
		err := ModifyMiddlewareReqValidationError{
			field:  "DiskLimit",
			reason: "value must be inside range [0, 1000]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetShareMode() != "" {

		if _, ok := _ModifyMiddlewareReq_ShareMode_InLookup[m.GetShareMode()]; !ok {
			err := ModifyMiddlewareReqValidationError{
				field:  "ShareMode",
				reason: "value must be in list [no yes]",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

	}

	if len(errors) > 0 {
		return ModifyMiddlewareReqMultiError(errors)
	}

	return nil
}

// ModifyMiddlewareReqMultiError is an error wrapping multiple validation
// errors returned by ModifyMiddlewareReq.ValidateAll() if the designated
// constraints aren't met.
type ModifyMiddlewareReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ModifyMiddlewareReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ModifyMiddlewareReqMultiError) AllErrors() []error { return m }

// ModifyMiddlewareReqValidationError is the validation error returned by
// ModifyMiddlewareReq.Validate if the designated constraints aren't met.
type ModifyMiddlewareReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ModifyMiddlewareReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ModifyMiddlewareReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ModifyMiddlewareReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ModifyMiddlewareReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ModifyMiddlewareReqValidationError) ErrorName() string {
	return "ModifyMiddlewareReqValidationError"
}

// Error satisfies the builtin error interface
func (e ModifyMiddlewareReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sModifyMiddlewareReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ModifyMiddlewareReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ModifyMiddlewareReqValidationError{}

var _ModifyMiddlewareReq_Protocol_InLookup = map[string]struct{}{
	"http":  {},
	"https": {},
	"tcp":   {},
}

var _ModifyMiddlewareReq_DomainUse_InLookup = map[string]struct{}{
	"loadBalancer": {},
	"a":            {},
}

var _ModifyMiddlewareReq_ShareMode_InLookup = map[string]struct{}{
	"no":  {},
	"yes": {},
}

// Validate checks the field values on DescribeMiddlewareReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DescribeMiddlewareReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DescribeMiddlewareReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DescribeMiddlewareReqMultiError, or nil if none found.
func (m *DescribeMiddlewareReq) ValidateAll() error {
	return m.validate(true)
}

func (m *DescribeMiddlewareReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	for idx, item := range m.GetFilters() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, DescribeMiddlewareReqValidationError{
						field:  fmt.Sprintf("Filters[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, DescribeMiddlewareReqValidationError{
						field:  fmt.Sprintf("Filters[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return DescribeMiddlewareReqValidationError{
					field:  fmt.Sprintf("Filters[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for Name

	// no validation rules for Kind

	if len(errors) > 0 {
		return DescribeMiddlewareReqMultiError(errors)
	}

	return nil
}

// DescribeMiddlewareReqMultiError is an error wrapping multiple validation
// errors returned by DescribeMiddlewareReq.ValidateAll() if the designated
// constraints aren't met.
type DescribeMiddlewareReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DescribeMiddlewareReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DescribeMiddlewareReqMultiError) AllErrors() []error { return m }

// DescribeMiddlewareReqValidationError is the validation error returned by
// DescribeMiddlewareReq.Validate if the designated constraints aren't met.
type DescribeMiddlewareReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DescribeMiddlewareReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DescribeMiddlewareReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DescribeMiddlewareReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DescribeMiddlewareReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DescribeMiddlewareReqValidationError) ErrorName() string {
	return "DescribeMiddlewareReqValidationError"
}

// Error satisfies the builtin error interface
func (e DescribeMiddlewareReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDescribeMiddlewareReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DescribeMiddlewareReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DescribeMiddlewareReqValidationError{}

// Validate checks the field values on DescribeMiddlewareReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DescribeMiddlewareReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DescribeMiddlewareReply with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DescribeMiddlewareReplyMultiError, or nil if none found.
func (m *DescribeMiddlewareReply) ValidateAll() error {
	return m.validate(true)
}

func (m *DescribeMiddlewareReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	for idx, item := range m.GetGoods() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, DescribeMiddlewareReplyValidationError{
						field:  fmt.Sprintf("Goods[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, DescribeMiddlewareReplyValidationError{
						field:  fmt.Sprintf("Goods[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return DescribeMiddlewareReplyValidationError{
					field:  fmt.Sprintf("Goods[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for CreatedAt

	// no validation rules for Name

	// no validation rules for Kind

	// no validation rules for UrlTemplate

	// no validation rules for Protocol

	// no validation rules for DomainUse

	// no validation rules for TargetPort

	if all {
		switch v := interface{}(m.GetParams()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DescribeMiddlewareReplyValidationError{
					field:  "Params",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DescribeMiddlewareReplyValidationError{
					field:  "Params",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetParams()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DescribeMiddlewareReplyValidationError{
				field:  "Params",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Port

	// no validation rules for CoreRequest

	// no validation rules for CoreLimit

	// no validation rules for MemoryRequest

	// no validation rules for MemoryLimit

	// no validation rules for DiskRequest

	// no validation rules for DiskLimit

	// no validation rules for ShareMode

	if len(errors) > 0 {
		return DescribeMiddlewareReplyMultiError(errors)
	}

	return nil
}

// DescribeMiddlewareReplyMultiError is an error wrapping multiple validation
// errors returned by DescribeMiddlewareReply.ValidateAll() if the designated
// constraints aren't met.
type DescribeMiddlewareReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DescribeMiddlewareReplyMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DescribeMiddlewareReplyMultiError) AllErrors() []error { return m }

// DescribeMiddlewareReplyValidationError is the validation error returned by
// DescribeMiddlewareReply.Validate if the designated constraints aren't met.
type DescribeMiddlewareReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DescribeMiddlewareReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DescribeMiddlewareReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DescribeMiddlewareReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DescribeMiddlewareReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DescribeMiddlewareReplyValidationError) ErrorName() string {
	return "DescribeMiddlewareReplyValidationError"
}

// Error satisfies the builtin error interface
func (e DescribeMiddlewareReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDescribeMiddlewareReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DescribeMiddlewareReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DescribeMiddlewareReplyValidationError{}

// Validate checks the field values on MiddlewareReply with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *MiddlewareReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on MiddlewareReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// MiddlewareReplyMultiError, or nil if none found.
func (m *MiddlewareReply) ValidateAll() error {
	return m.validate(true)
}

func (m *MiddlewareReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for CreatedAt

	// no validation rules for Name

	// no validation rules for Kind

	// no validation rules for UrlTemplate

	// no validation rules for Protocol

	// no validation rules for DomainUse

	// no validation rules for TargetPort

	if all {
		switch v := interface{}(m.GetParams()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, MiddlewareReplyValidationError{
					field:  "Params",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, MiddlewareReplyValidationError{
					field:  "Params",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetParams()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return MiddlewareReplyValidationError{
				field:  "Params",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Port

	// no validation rules for CoreRequest

	// no validation rules for CoreLimit

	// no validation rules for MemoryRequest

	// no validation rules for MemoryLimit

	// no validation rules for DiskRequest

	// no validation rules for DiskLimit

	// no validation rules for ShareMode

	if len(errors) > 0 {
		return MiddlewareReplyMultiError(errors)
	}

	return nil
}

// MiddlewareReplyMultiError is an error wrapping multiple validation errors
// returned by MiddlewareReply.ValidateAll() if the designated constraints
// aren't met.
type MiddlewareReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m MiddlewareReplyMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m MiddlewareReplyMultiError) AllErrors() []error { return m }

// MiddlewareReplyValidationError is the validation error returned by
// MiddlewareReply.Validate if the designated constraints aren't met.
type MiddlewareReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e MiddlewareReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e MiddlewareReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e MiddlewareReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e MiddlewareReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e MiddlewareReplyValidationError) ErrorName() string { return "MiddlewareReplyValidationError" }

// Error satisfies the builtin error interface
func (e MiddlewareReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sMiddlewareReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = MiddlewareReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = MiddlewareReplyValidationError{}

// Validate checks the field values on DescribesMiddlewareReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DescribesMiddlewareReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DescribesMiddlewareReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DescribesMiddlewareReqMultiError, or nil if none found.
func (m *DescribesMiddlewareReq) ValidateAll() error {
	return m.validate(true)
}

func (m *DescribesMiddlewareReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if val := m.GetPageNumber(); val < -1 || val > 16777216 {
		err := DescribesMiddlewareReqValidationError{
			field:  "PageNumber",
			reason: "value must be inside range [-1, 16777216]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if val := m.GetPageSize(); val < 1 || val > 16777216 {
		err := DescribesMiddlewareReqValidationError{
			field:  "PageSize",
			reason: "value must be inside range [1, 16777216]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	for idx, item := range m.GetFilters() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, DescribesMiddlewareReqValidationError{
						field:  fmt.Sprintf("Filters[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, DescribesMiddlewareReqValidationError{
						field:  fmt.Sprintf("Filters[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return DescribesMiddlewareReqValidationError{
					field:  fmt.Sprintf("Filters[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	for idx, item := range m.GetSorts() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, DescribesMiddlewareReqValidationError{
						field:  fmt.Sprintf("Sorts[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, DescribesMiddlewareReqValidationError{
						field:  fmt.Sprintf("Sorts[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return DescribesMiddlewareReqValidationError{
					field:  fmt.Sprintf("Sorts[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return DescribesMiddlewareReqMultiError(errors)
	}

	return nil
}

// DescribesMiddlewareReqMultiError is an error wrapping multiple validation
// errors returned by DescribesMiddlewareReq.ValidateAll() if the designated
// constraints aren't met.
type DescribesMiddlewareReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DescribesMiddlewareReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DescribesMiddlewareReqMultiError) AllErrors() []error { return m }

// DescribesMiddlewareReqValidationError is the validation error returned by
// DescribesMiddlewareReq.Validate if the designated constraints aren't met.
type DescribesMiddlewareReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DescribesMiddlewareReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DescribesMiddlewareReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DescribesMiddlewareReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DescribesMiddlewareReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DescribesMiddlewareReqValidationError) ErrorName() string {
	return "DescribesMiddlewareReqValidationError"
}

// Error satisfies the builtin error interface
func (e DescribesMiddlewareReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDescribesMiddlewareReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DescribesMiddlewareReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DescribesMiddlewareReqValidationError{}

// Validate checks the field values on DescribesMiddlewareReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DescribesMiddlewareReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DescribesMiddlewareReply with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DescribesMiddlewareReplyMultiError, or nil if none found.
func (m *DescribesMiddlewareReply) ValidateAll() error {
	return m.validate(true)
}

func (m *DescribesMiddlewareReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetList() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, DescribesMiddlewareReplyValidationError{
						field:  fmt.Sprintf("List[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, DescribesMiddlewareReplyValidationError{
						field:  fmt.Sprintf("List[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return DescribesMiddlewareReplyValidationError{
					field:  fmt.Sprintf("List[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for TotalCount

	if len(errors) > 0 {
		return DescribesMiddlewareReplyMultiError(errors)
	}

	return nil
}

// DescribesMiddlewareReplyMultiError is an error wrapping multiple validation
// errors returned by DescribesMiddlewareReply.ValidateAll() if the designated
// constraints aren't met.
type DescribesMiddlewareReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DescribesMiddlewareReplyMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DescribesMiddlewareReplyMultiError) AllErrors() []error { return m }

// DescribesMiddlewareReplyValidationError is the validation error returned by
// DescribesMiddlewareReply.Validate if the designated constraints aren't met.
type DescribesMiddlewareReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DescribesMiddlewareReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DescribesMiddlewareReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DescribesMiddlewareReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DescribesMiddlewareReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DescribesMiddlewareReplyValidationError) ErrorName() string {
	return "DescribesMiddlewareReplyValidationError"
}

// Error satisfies the builtin error interface
func (e DescribesMiddlewareReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDescribesMiddlewareReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DescribesMiddlewareReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DescribesMiddlewareReplyValidationError{}

// Validate checks the field values on CreateHardwareRoleUnionReq with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreateHardwareRoleUnionReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateHardwareRoleUnionReq with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateHardwareRoleUnionReqMultiError, or nil if none found.
func (m *CreateHardwareRoleUnionReq) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateHardwareRoleUnionReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetGoods() == nil {
		err := CreateHardwareRoleUnionReqValidationError{
			field:  "Goods",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetGoods()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateHardwareRoleUnionReqValidationError{
					field:  "Goods",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateHardwareRoleUnionReqValidationError{
					field:  "Goods",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetGoods()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateHardwareRoleUnionReqValidationError{
				field:  "Goods",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetHardwareRole() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, CreateHardwareRoleUnionReqValidationError{
						field:  fmt.Sprintf("HardwareRole[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, CreateHardwareRoleUnionReqValidationError{
						field:  fmt.Sprintf("HardwareRole[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return CreateHardwareRoleUnionReqValidationError{
					field:  fmt.Sprintf("HardwareRole[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	for idx, item := range m.GetHardwareRoleLabel() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, CreateHardwareRoleUnionReqValidationError{
						field:  fmt.Sprintf("HardwareRoleLabel[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, CreateHardwareRoleUnionReqValidationError{
						field:  fmt.Sprintf("HardwareRoleLabel[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return CreateHardwareRoleUnionReqValidationError{
					field:  fmt.Sprintf("HardwareRoleLabel[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return CreateHardwareRoleUnionReqMultiError(errors)
	}

	return nil
}

// CreateHardwareRoleUnionReqMultiError is an error wrapping multiple
// validation errors returned by CreateHardwareRoleUnionReq.ValidateAll() if
// the designated constraints aren't met.
type CreateHardwareRoleUnionReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateHardwareRoleUnionReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateHardwareRoleUnionReqMultiError) AllErrors() []error { return m }

// CreateHardwareRoleUnionReqValidationError is the validation error returned
// by CreateHardwareRoleUnionReq.Validate if the designated constraints aren't met.
type CreateHardwareRoleUnionReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateHardwareRoleUnionReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateHardwareRoleUnionReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateHardwareRoleUnionReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateHardwareRoleUnionReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateHardwareRoleUnionReqValidationError) ErrorName() string {
	return "CreateHardwareRoleUnionReqValidationError"
}

// Error satisfies the builtin error interface
func (e CreateHardwareRoleUnionReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateHardwareRoleUnionReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateHardwareRoleUnionReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateHardwareRoleUnionReqValidationError{}

// Validate checks the field values on CreateHardwareRoleReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreateHardwareRoleReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateHardwareRoleReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateHardwareRoleReqMultiError, or nil if none found.
func (m *CreateHardwareRoleReq) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateHardwareRoleReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(m.GetGoodsId()) < 1 {
		err := CreateHardwareRoleReqValidationError{
			field:  "GoodsId",
			reason: "value length must be at least 1 bytes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetHardwareRoleId() < 1 {
		err := CreateHardwareRoleReqValidationError{
			field:  "HardwareRoleId",
			reason: "value must be greater than or equal to 1",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return CreateHardwareRoleReqMultiError(errors)
	}

	return nil
}

// CreateHardwareRoleReqMultiError is an error wrapping multiple validation
// errors returned by CreateHardwareRoleReq.ValidateAll() if the designated
// constraints aren't met.
type CreateHardwareRoleReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateHardwareRoleReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateHardwareRoleReqMultiError) AllErrors() []error { return m }

// CreateHardwareRoleReqValidationError is the validation error returned by
// CreateHardwareRoleReq.Validate if the designated constraints aren't met.
type CreateHardwareRoleReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateHardwareRoleReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateHardwareRoleReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateHardwareRoleReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateHardwareRoleReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateHardwareRoleReqValidationError) ErrorName() string {
	return "CreateHardwareRoleReqValidationError"
}

// Error satisfies the builtin error interface
func (e CreateHardwareRoleReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateHardwareRoleReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateHardwareRoleReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateHardwareRoleReqValidationError{}

// Validate checks the field values on DeleteHardwareRoleReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DeleteHardwareRoleReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DeleteHardwareRoleReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DeleteHardwareRoleReqMultiError, or nil if none found.
func (m *DeleteHardwareRoleReq) ValidateAll() error {
	return m.validate(true)
}

func (m *DeleteHardwareRoleReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(m.GetGoodsId()) < 1 {
		err := DeleteHardwareRoleReqValidationError{
			field:  "GoodsId",
			reason: "value length must be at least 1 bytes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetHardwareRoleId() < 1 {
		err := DeleteHardwareRoleReqValidationError{
			field:  "HardwareRoleId",
			reason: "value must be greater than or equal to 1",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return DeleteHardwareRoleReqMultiError(errors)
	}

	return nil
}

// DeleteHardwareRoleReqMultiError is an error wrapping multiple validation
// errors returned by DeleteHardwareRoleReq.ValidateAll() if the designated
// constraints aren't met.
type DeleteHardwareRoleReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DeleteHardwareRoleReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DeleteHardwareRoleReqMultiError) AllErrors() []error { return m }

// DeleteHardwareRoleReqValidationError is the validation error returned by
// DeleteHardwareRoleReq.Validate if the designated constraints aren't met.
type DeleteHardwareRoleReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DeleteHardwareRoleReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DeleteHardwareRoleReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DeleteHardwareRoleReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DeleteHardwareRoleReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DeleteHardwareRoleReqValidationError) ErrorName() string {
	return "DeleteHardwareRoleReqValidationError"
}

// Error satisfies the builtin error interface
func (e DeleteHardwareRoleReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDeleteHardwareRoleReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DeleteHardwareRoleReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DeleteHardwareRoleReqValidationError{}

// Validate checks the field values on DescribeHardwareRoleReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DescribeHardwareRoleReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DescribeHardwareRoleReq with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DescribeHardwareRoleReqMultiError, or nil if none found.
func (m *DescribeHardwareRoleReq) ValidateAll() error {
	return m.validate(true)
}

func (m *DescribeHardwareRoleReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetId() != 0 {

		if m.GetId() < 0 {
			err := DescribeHardwareRoleReqValidationError{
				field:  "Id",
				reason: "value must be greater than or equal to 0",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

	}

	// no validation rules for Name

	for idx, item := range m.GetFilters() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, DescribeHardwareRoleReqValidationError{
						field:  fmt.Sprintf("Filters[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, DescribeHardwareRoleReqValidationError{
						field:  fmt.Sprintf("Filters[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return DescribeHardwareRoleReqValidationError{
					field:  fmt.Sprintf("Filters[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return DescribeHardwareRoleReqMultiError(errors)
	}

	return nil
}

// DescribeHardwareRoleReqMultiError is an error wrapping multiple validation
// errors returned by DescribeHardwareRoleReq.ValidateAll() if the designated
// constraints aren't met.
type DescribeHardwareRoleReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DescribeHardwareRoleReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DescribeHardwareRoleReqMultiError) AllErrors() []error { return m }

// DescribeHardwareRoleReqValidationError is the validation error returned by
// DescribeHardwareRoleReq.Validate if the designated constraints aren't met.
type DescribeHardwareRoleReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DescribeHardwareRoleReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DescribeHardwareRoleReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DescribeHardwareRoleReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DescribeHardwareRoleReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DescribeHardwareRoleReqValidationError) ErrorName() string {
	return "DescribeHardwareRoleReqValidationError"
}

// Error satisfies the builtin error interface
func (e DescribeHardwareRoleReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDescribeHardwareRoleReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DescribeHardwareRoleReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DescribeHardwareRoleReqValidationError{}

// Validate checks the field values on DescribeHardwareRoleReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DescribeHardwareRoleReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DescribeHardwareRoleReply with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DescribeHardwareRoleReplyMultiError, or nil if none found.
func (m *DescribeHardwareRoleReply) ValidateAll() error {
	return m.validate(true)
}

func (m *DescribeHardwareRoleReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for Name

	// no validation rules for CreatedAt

	// no validation rules for Hardware

	// no validation rules for Description

	if all {
		switch v := interface{}(m.GetFromVersion()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DescribeHardwareRoleReplyValidationError{
					field:  "FromVersion",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DescribeHardwareRoleReplyValidationError{
					field:  "FromVersion",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetFromVersion()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DescribeHardwareRoleReplyValidationError{
				field:  "FromVersion",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetVersion() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, DescribeHardwareRoleReplyValidationError{
						field:  fmt.Sprintf("Version[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, DescribeHardwareRoleReplyValidationError{
						field:  fmt.Sprintf("Version[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return DescribeHardwareRoleReplyValidationError{
					field:  fmt.Sprintf("Version[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	for idx, item := range m.GetServerPlane() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, DescribeHardwareRoleReplyValidationError{
						field:  fmt.Sprintf("ServerPlane[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, DescribeHardwareRoleReplyValidationError{
						field:  fmt.Sprintf("ServerPlane[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return DescribeHardwareRoleReplyValidationError{
					field:  fmt.Sprintf("ServerPlane[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	for idx, item := range m.GetLabel() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, DescribeHardwareRoleReplyValidationError{
						field:  fmt.Sprintf("Label[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, DescribeHardwareRoleReplyValidationError{
						field:  fmt.Sprintf("Label[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return DescribeHardwareRoleReplyValidationError{
					field:  fmt.Sprintf("Label[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	for idx, item := range m.GetGoods() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, DescribeHardwareRoleReplyValidationError{
						field:  fmt.Sprintf("Goods[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, DescribeHardwareRoleReplyValidationError{
						field:  fmt.Sprintf("Goods[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return DescribeHardwareRoleReplyValidationError{
					field:  fmt.Sprintf("Goods[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return DescribeHardwareRoleReplyMultiError(errors)
	}

	return nil
}

// DescribeHardwareRoleReplyMultiError is an error wrapping multiple validation
// errors returned by DescribeHardwareRoleReply.ValidateAll() if the
// designated constraints aren't met.
type DescribeHardwareRoleReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DescribeHardwareRoleReplyMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DescribeHardwareRoleReplyMultiError) AllErrors() []error { return m }

// DescribeHardwareRoleReplyValidationError is the validation error returned by
// DescribeHardwareRoleReply.Validate if the designated constraints aren't met.
type DescribeHardwareRoleReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DescribeHardwareRoleReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DescribeHardwareRoleReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DescribeHardwareRoleReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DescribeHardwareRoleReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DescribeHardwareRoleReplyValidationError) ErrorName() string {
	return "DescribeHardwareRoleReplyValidationError"
}

// Error satisfies the builtin error interface
func (e DescribeHardwareRoleReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDescribeHardwareRoleReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DescribeHardwareRoleReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DescribeHardwareRoleReplyValidationError{}

// Validate checks the field values on HardwareRoleReply with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *HardwareRoleReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on HardwareRoleReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// HardwareRoleReplyMultiError, or nil if none found.
func (m *HardwareRoleReply) ValidateAll() error {
	return m.validate(true)
}

func (m *HardwareRoleReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for Name

	// no validation rules for CreatedAt

	// no validation rules for Hardware

	// no validation rules for Description

	if all {
		switch v := interface{}(m.GetFromVersion()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, HardwareRoleReplyValidationError{
					field:  "FromVersion",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, HardwareRoleReplyValidationError{
					field:  "FromVersion",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetFromVersion()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return HardwareRoleReplyValidationError{
				field:  "FromVersion",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetVersion() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, HardwareRoleReplyValidationError{
						field:  fmt.Sprintf("Version[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, HardwareRoleReplyValidationError{
						field:  fmt.Sprintf("Version[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return HardwareRoleReplyValidationError{
					field:  fmt.Sprintf("Version[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	for idx, item := range m.GetServerPlane() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, HardwareRoleReplyValidationError{
						field:  fmt.Sprintf("ServerPlane[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, HardwareRoleReplyValidationError{
						field:  fmt.Sprintf("ServerPlane[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return HardwareRoleReplyValidationError{
					field:  fmt.Sprintf("ServerPlane[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	for idx, item := range m.GetLabel() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, HardwareRoleReplyValidationError{
						field:  fmt.Sprintf("Label[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, HardwareRoleReplyValidationError{
						field:  fmt.Sprintf("Label[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return HardwareRoleReplyValidationError{
					field:  fmt.Sprintf("Label[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return HardwareRoleReplyMultiError(errors)
	}

	return nil
}

// HardwareRoleReplyMultiError is an error wrapping multiple validation errors
// returned by HardwareRoleReply.ValidateAll() if the designated constraints
// aren't met.
type HardwareRoleReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m HardwareRoleReplyMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m HardwareRoleReplyMultiError) AllErrors() []error { return m }

// HardwareRoleReplyValidationError is the validation error returned by
// HardwareRoleReply.Validate if the designated constraints aren't met.
type HardwareRoleReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e HardwareRoleReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e HardwareRoleReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e HardwareRoleReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e HardwareRoleReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e HardwareRoleReplyValidationError) ErrorName() string {
	return "HardwareRoleReplyValidationError"
}

// Error satisfies the builtin error interface
func (e HardwareRoleReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sHardwareRoleReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = HardwareRoleReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = HardwareRoleReplyValidationError{}

// Validate checks the field values on DescribesHardwareRoleReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DescribesHardwareRoleReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DescribesHardwareRoleReq with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DescribesHardwareRoleReqMultiError, or nil if none found.
func (m *DescribesHardwareRoleReq) ValidateAll() error {
	return m.validate(true)
}

func (m *DescribesHardwareRoleReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if val := m.GetPageNumber(); val < -1 || val > 16777216 {
		err := DescribesHardwareRoleReqValidationError{
			field:  "PageNumber",
			reason: "value must be inside range [-1, 16777216]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if val := m.GetPageSize(); val < 1 || val > 16777216 {
		err := DescribesHardwareRoleReqValidationError{
			field:  "PageSize",
			reason: "value must be inside range [1, 16777216]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	for idx, item := range m.GetFilters() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, DescribesHardwareRoleReqValidationError{
						field:  fmt.Sprintf("Filters[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, DescribesHardwareRoleReqValidationError{
						field:  fmt.Sprintf("Filters[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return DescribesHardwareRoleReqValidationError{
					field:  fmt.Sprintf("Filters[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	for idx, item := range m.GetSorts() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, DescribesHardwareRoleReqValidationError{
						field:  fmt.Sprintf("Sorts[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, DescribesHardwareRoleReqValidationError{
						field:  fmt.Sprintf("Sorts[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return DescribesHardwareRoleReqValidationError{
					field:  fmt.Sprintf("Sorts[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return DescribesHardwareRoleReqMultiError(errors)
	}

	return nil
}

// DescribesHardwareRoleReqMultiError is an error wrapping multiple validation
// errors returned by DescribesHardwareRoleReq.ValidateAll() if the designated
// constraints aren't met.
type DescribesHardwareRoleReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DescribesHardwareRoleReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DescribesHardwareRoleReqMultiError) AllErrors() []error { return m }

// DescribesHardwareRoleReqValidationError is the validation error returned by
// DescribesHardwareRoleReq.Validate if the designated constraints aren't met.
type DescribesHardwareRoleReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DescribesHardwareRoleReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DescribesHardwareRoleReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DescribesHardwareRoleReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DescribesHardwareRoleReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DescribesHardwareRoleReqValidationError) ErrorName() string {
	return "DescribesHardwareRoleReqValidationError"
}

// Error satisfies the builtin error interface
func (e DescribesHardwareRoleReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDescribesHardwareRoleReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DescribesHardwareRoleReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DescribesHardwareRoleReqValidationError{}

// Validate checks the field values on DescribesHardwareRoleReply with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DescribesHardwareRoleReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DescribesHardwareRoleReply with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DescribesHardwareRoleReplyMultiError, or nil if none found.
func (m *DescribesHardwareRoleReply) ValidateAll() error {
	return m.validate(true)
}

func (m *DescribesHardwareRoleReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetList() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, DescribesHardwareRoleReplyValidationError{
						field:  fmt.Sprintf("List[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, DescribesHardwareRoleReplyValidationError{
						field:  fmt.Sprintf("List[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return DescribesHardwareRoleReplyValidationError{
					field:  fmt.Sprintf("List[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for TotalCount

	if len(errors) > 0 {
		return DescribesHardwareRoleReplyMultiError(errors)
	}

	return nil
}

// DescribesHardwareRoleReplyMultiError is an error wrapping multiple
// validation errors returned by DescribesHardwareRoleReply.ValidateAll() if
// the designated constraints aren't met.
type DescribesHardwareRoleReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DescribesHardwareRoleReplyMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DescribesHardwareRoleReplyMultiError) AllErrors() []error { return m }

// DescribesHardwareRoleReplyValidationError is the validation error returned
// by DescribesHardwareRoleReply.Validate if the designated constraints aren't met.
type DescribesHardwareRoleReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DescribesHardwareRoleReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DescribesHardwareRoleReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DescribesHardwareRoleReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DescribesHardwareRoleReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DescribesHardwareRoleReplyValidationError) ErrorName() string {
	return "DescribesHardwareRoleReplyValidationError"
}

// Error satisfies the builtin error interface
func (e DescribesHardwareRoleReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDescribesHardwareRoleReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DescribesHardwareRoleReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DescribesHardwareRoleReplyValidationError{}

// Validate checks the field values on CreateHardwareRoleLabelReq with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreateHardwareRoleLabelReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateHardwareRoleLabelReq with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateHardwareRoleLabelReqMultiError, or nil if none found.
func (m *CreateHardwareRoleLabelReq) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateHardwareRoleLabelReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(m.GetGoodsId()) < 1 {
		err := CreateHardwareRoleLabelReqValidationError{
			field:  "GoodsId",
			reason: "value length must be at least 1 bytes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetHardwareRoleLabelId() < 1 {
		err := CreateHardwareRoleLabelReqValidationError{
			field:  "HardwareRoleLabelId",
			reason: "value must be greater than or equal to 1",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return CreateHardwareRoleLabelReqMultiError(errors)
	}

	return nil
}

// CreateHardwareRoleLabelReqMultiError is an error wrapping multiple
// validation errors returned by CreateHardwareRoleLabelReq.ValidateAll() if
// the designated constraints aren't met.
type CreateHardwareRoleLabelReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateHardwareRoleLabelReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateHardwareRoleLabelReqMultiError) AllErrors() []error { return m }

// CreateHardwareRoleLabelReqValidationError is the validation error returned
// by CreateHardwareRoleLabelReq.Validate if the designated constraints aren't met.
type CreateHardwareRoleLabelReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateHardwareRoleLabelReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateHardwareRoleLabelReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateHardwareRoleLabelReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateHardwareRoleLabelReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateHardwareRoleLabelReqValidationError) ErrorName() string {
	return "CreateHardwareRoleLabelReqValidationError"
}

// Error satisfies the builtin error interface
func (e CreateHardwareRoleLabelReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateHardwareRoleLabelReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateHardwareRoleLabelReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateHardwareRoleLabelReqValidationError{}

// Validate checks the field values on DeleteHardwareRoleLabelReq with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DeleteHardwareRoleLabelReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DeleteHardwareRoleLabelReq with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DeleteHardwareRoleLabelReqMultiError, or nil if none found.
func (m *DeleteHardwareRoleLabelReq) ValidateAll() error {
	return m.validate(true)
}

func (m *DeleteHardwareRoleLabelReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(m.GetGoodsId()) < 1 {
		err := DeleteHardwareRoleLabelReqValidationError{
			field:  "GoodsId",
			reason: "value length must be at least 1 bytes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetHardwareRoleLabelId() < 1 {
		err := DeleteHardwareRoleLabelReqValidationError{
			field:  "HardwareRoleLabelId",
			reason: "value must be greater than or equal to 1",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return DeleteHardwareRoleLabelReqMultiError(errors)
	}

	return nil
}

// DeleteHardwareRoleLabelReqMultiError is an error wrapping multiple
// validation errors returned by DeleteHardwareRoleLabelReq.ValidateAll() if
// the designated constraints aren't met.
type DeleteHardwareRoleLabelReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DeleteHardwareRoleLabelReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DeleteHardwareRoleLabelReqMultiError) AllErrors() []error { return m }

// DeleteHardwareRoleLabelReqValidationError is the validation error returned
// by DeleteHardwareRoleLabelReq.Validate if the designated constraints aren't met.
type DeleteHardwareRoleLabelReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DeleteHardwareRoleLabelReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DeleteHardwareRoleLabelReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DeleteHardwareRoleLabelReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DeleteHardwareRoleLabelReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DeleteHardwareRoleLabelReqValidationError) ErrorName() string {
	return "DeleteHardwareRoleLabelReqValidationError"
}

// Error satisfies the builtin error interface
func (e DeleteHardwareRoleLabelReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDeleteHardwareRoleLabelReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DeleteHardwareRoleLabelReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DeleteHardwareRoleLabelReqValidationError{}

// Validate checks the field values on DescribeHardwareRoleLabelReq with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DescribeHardwareRoleLabelReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DescribeHardwareRoleLabelReq with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DescribeHardwareRoleLabelReqMultiError, or nil if none found.
func (m *DescribeHardwareRoleLabelReq) ValidateAll() error {
	return m.validate(true)
}

func (m *DescribeHardwareRoleLabelReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetId() != 0 {

		if m.GetId() < 0 {
			err := DescribeHardwareRoleLabelReqValidationError{
				field:  "Id",
				reason: "value must be greater than or equal to 0",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

	}

	// no validation rules for Key

	// no validation rules for Value

	for idx, item := range m.GetFilters() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, DescribeHardwareRoleLabelReqValidationError{
						field:  fmt.Sprintf("Filters[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, DescribeHardwareRoleLabelReqValidationError{
						field:  fmt.Sprintf("Filters[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return DescribeHardwareRoleLabelReqValidationError{
					field:  fmt.Sprintf("Filters[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return DescribeHardwareRoleLabelReqMultiError(errors)
	}

	return nil
}

// DescribeHardwareRoleLabelReqMultiError is an error wrapping multiple
// validation errors returned by DescribeHardwareRoleLabelReq.ValidateAll() if
// the designated constraints aren't met.
type DescribeHardwareRoleLabelReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DescribeHardwareRoleLabelReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DescribeHardwareRoleLabelReqMultiError) AllErrors() []error { return m }

// DescribeHardwareRoleLabelReqValidationError is the validation error returned
// by DescribeHardwareRoleLabelReq.Validate if the designated constraints
// aren't met.
type DescribeHardwareRoleLabelReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DescribeHardwareRoleLabelReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DescribeHardwareRoleLabelReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DescribeHardwareRoleLabelReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DescribeHardwareRoleLabelReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DescribeHardwareRoleLabelReqValidationError) ErrorName() string {
	return "DescribeHardwareRoleLabelReqValidationError"
}

// Error satisfies the builtin error interface
func (e DescribeHardwareRoleLabelReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDescribeHardwareRoleLabelReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DescribeHardwareRoleLabelReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DescribeHardwareRoleLabelReqValidationError{}

// Validate checks the field values on DescribeHardwareRoleLabelReply with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DescribeHardwareRoleLabelReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DescribeHardwareRoleLabelReply with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// DescribeHardwareRoleLabelReplyMultiError, or nil if none found.
func (m *DescribeHardwareRoleLabelReply) ValidateAll() error {
	return m.validate(true)
}

func (m *DescribeHardwareRoleLabelReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for CreatedAt

	if all {
		switch v := interface{}(m.GetRole()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DescribeHardwareRoleLabelReplyValidationError{
					field:  "Role",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DescribeHardwareRoleLabelReplyValidationError{
					field:  "Role",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRole()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DescribeHardwareRoleLabelReplyValidationError{
				field:  "Role",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Key

	// no validation rules for Value

	// no validation rules for Description

	for idx, item := range m.GetGoods() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, DescribeHardwareRoleLabelReplyValidationError{
						field:  fmt.Sprintf("Goods[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, DescribeHardwareRoleLabelReplyValidationError{
						field:  fmt.Sprintf("Goods[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return DescribeHardwareRoleLabelReplyValidationError{
					field:  fmt.Sprintf("Goods[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return DescribeHardwareRoleLabelReplyMultiError(errors)
	}

	return nil
}

// DescribeHardwareRoleLabelReplyMultiError is an error wrapping multiple
// validation errors returned by DescribeHardwareRoleLabelReply.ValidateAll()
// if the designated constraints aren't met.
type DescribeHardwareRoleLabelReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DescribeHardwareRoleLabelReplyMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DescribeHardwareRoleLabelReplyMultiError) AllErrors() []error { return m }

// DescribeHardwareRoleLabelReplyValidationError is the validation error
// returned by DescribeHardwareRoleLabelReply.Validate if the designated
// constraints aren't met.
type DescribeHardwareRoleLabelReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DescribeHardwareRoleLabelReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DescribeHardwareRoleLabelReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DescribeHardwareRoleLabelReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DescribeHardwareRoleLabelReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DescribeHardwareRoleLabelReplyValidationError) ErrorName() string {
	return "DescribeHardwareRoleLabelReplyValidationError"
}

// Error satisfies the builtin error interface
func (e DescribeHardwareRoleLabelReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDescribeHardwareRoleLabelReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DescribeHardwareRoleLabelReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DescribeHardwareRoleLabelReplyValidationError{}

// Validate checks the field values on HardwareRoleLabelReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *HardwareRoleLabelReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on HardwareRoleLabelReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// HardwareRoleLabelReplyMultiError, or nil if none found.
func (m *HardwareRoleLabelReply) ValidateAll() error {
	return m.validate(true)
}

func (m *HardwareRoleLabelReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for CreatedAt

	// no validation rules for HardwareRoleId

	// no validation rules for Key

	// no validation rules for Value

	// no validation rules for Description

	if len(errors) > 0 {
		return HardwareRoleLabelReplyMultiError(errors)
	}

	return nil
}

// HardwareRoleLabelReplyMultiError is an error wrapping multiple validation
// errors returned by HardwareRoleLabelReply.ValidateAll() if the designated
// constraints aren't met.
type HardwareRoleLabelReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m HardwareRoleLabelReplyMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m HardwareRoleLabelReplyMultiError) AllErrors() []error { return m }

// HardwareRoleLabelReplyValidationError is the validation error returned by
// HardwareRoleLabelReply.Validate if the designated constraints aren't met.
type HardwareRoleLabelReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e HardwareRoleLabelReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e HardwareRoleLabelReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e HardwareRoleLabelReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e HardwareRoleLabelReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e HardwareRoleLabelReplyValidationError) ErrorName() string {
	return "HardwareRoleLabelReplyValidationError"
}

// Error satisfies the builtin error interface
func (e HardwareRoleLabelReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sHardwareRoleLabelReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = HardwareRoleLabelReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = HardwareRoleLabelReplyValidationError{}

// Validate checks the field values on DescribesHardwareRoleLabelReq with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DescribesHardwareRoleLabelReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DescribesHardwareRoleLabelReq with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// DescribesHardwareRoleLabelReqMultiError, or nil if none found.
func (m *DescribesHardwareRoleLabelReq) ValidateAll() error {
	return m.validate(true)
}

func (m *DescribesHardwareRoleLabelReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if val := m.GetPageNumber(); val < -1 || val > 16777216 {
		err := DescribesHardwareRoleLabelReqValidationError{
			field:  "PageNumber",
			reason: "value must be inside range [-1, 16777216]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if val := m.GetPageSize(); val < 1 || val > 16777216 {
		err := DescribesHardwareRoleLabelReqValidationError{
			field:  "PageSize",
			reason: "value must be inside range [1, 16777216]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	for idx, item := range m.GetFilters() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, DescribesHardwareRoleLabelReqValidationError{
						field:  fmt.Sprintf("Filters[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, DescribesHardwareRoleLabelReqValidationError{
						field:  fmt.Sprintf("Filters[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return DescribesHardwareRoleLabelReqValidationError{
					field:  fmt.Sprintf("Filters[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	for idx, item := range m.GetSorts() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, DescribesHardwareRoleLabelReqValidationError{
						field:  fmt.Sprintf("Sorts[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, DescribesHardwareRoleLabelReqValidationError{
						field:  fmt.Sprintf("Sorts[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return DescribesHardwareRoleLabelReqValidationError{
					field:  fmt.Sprintf("Sorts[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return DescribesHardwareRoleLabelReqMultiError(errors)
	}

	return nil
}

// DescribesHardwareRoleLabelReqMultiError is an error wrapping multiple
// validation errors returned by DescribesHardwareRoleLabelReq.ValidateAll()
// if the designated constraints aren't met.
type DescribesHardwareRoleLabelReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DescribesHardwareRoleLabelReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DescribesHardwareRoleLabelReqMultiError) AllErrors() []error { return m }

// DescribesHardwareRoleLabelReqValidationError is the validation error
// returned by DescribesHardwareRoleLabelReq.Validate if the designated
// constraints aren't met.
type DescribesHardwareRoleLabelReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DescribesHardwareRoleLabelReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DescribesHardwareRoleLabelReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DescribesHardwareRoleLabelReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DescribesHardwareRoleLabelReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DescribesHardwareRoleLabelReqValidationError) ErrorName() string {
	return "DescribesHardwareRoleLabelReqValidationError"
}

// Error satisfies the builtin error interface
func (e DescribesHardwareRoleLabelReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDescribesHardwareRoleLabelReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DescribesHardwareRoleLabelReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DescribesHardwareRoleLabelReqValidationError{}

// Validate checks the field values on DescribesHardwareRoleLabelReply with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DescribesHardwareRoleLabelReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DescribesHardwareRoleLabelReply with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// DescribesHardwareRoleLabelReplyMultiError, or nil if none found.
func (m *DescribesHardwareRoleLabelReply) ValidateAll() error {
	return m.validate(true)
}

func (m *DescribesHardwareRoleLabelReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetList() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, DescribesHardwareRoleLabelReplyValidationError{
						field:  fmt.Sprintf("List[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, DescribesHardwareRoleLabelReplyValidationError{
						field:  fmt.Sprintf("List[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return DescribesHardwareRoleLabelReplyValidationError{
					field:  fmt.Sprintf("List[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for TotalCount

	if len(errors) > 0 {
		return DescribesHardwareRoleLabelReplyMultiError(errors)
	}

	return nil
}

// DescribesHardwareRoleLabelReplyMultiError is an error wrapping multiple
// validation errors returned by DescribesHardwareRoleLabelReply.ValidateAll()
// if the designated constraints aren't met.
type DescribesHardwareRoleLabelReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DescribesHardwareRoleLabelReplyMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DescribesHardwareRoleLabelReplyMultiError) AllErrors() []error { return m }

// DescribesHardwareRoleLabelReplyValidationError is the validation error
// returned by DescribesHardwareRoleLabelReply.Validate if the designated
// constraints aren't met.
type DescribesHardwareRoleLabelReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DescribesHardwareRoleLabelReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DescribesHardwareRoleLabelReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DescribesHardwareRoleLabelReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DescribesHardwareRoleLabelReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DescribesHardwareRoleLabelReplyValidationError) ErrorName() string {
	return "DescribesHardwareRoleLabelReplyValidationError"
}

// Error satisfies the builtin error interface
func (e DescribesHardwareRoleLabelReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDescribesHardwareRoleLabelReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DescribesHardwareRoleLabelReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DescribesHardwareRoleLabelReplyValidationError{}

// Validate checks the field values on CreateChainReq with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *CreateChainReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateChainReq with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in CreateChainReqMultiError,
// or nil if none found.
func (m *CreateChainReq) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateChainReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	if all {
		switch v := interface{}(m.GetToGoods()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateChainReqValidationError{
					field:  "ToGoods",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateChainReqValidationError{
					field:  "ToGoods",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetToGoods()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateChainReqValidationError{
				field:  "ToGoods",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetFromGoods()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateChainReqValidationError{
					field:  "FromGoods",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateChainReqValidationError{
					field:  "FromGoods",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetFromGoods()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateChainReqValidationError{
				field:  "FromGoods",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if _, ok := _CreateChainReq_ResKind_InLookup[m.GetResKind()]; !ok {
		err := CreateChainReqValidationError{
			field:  "ResKind",
			reason: "value must be in list [domain middleware delivery upgrade]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for ResId

	if len(errors) > 0 {
		return CreateChainReqMultiError(errors)
	}

	return nil
}

// CreateChainReqMultiError is an error wrapping multiple validation errors
// returned by CreateChainReq.ValidateAll() if the designated constraints
// aren't met.
type CreateChainReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateChainReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateChainReqMultiError) AllErrors() []error { return m }

// CreateChainReqValidationError is the validation error returned by
// CreateChainReq.Validate if the designated constraints aren't met.
type CreateChainReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateChainReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateChainReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateChainReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateChainReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateChainReqValidationError) ErrorName() string { return "CreateChainReqValidationError" }

// Error satisfies the builtin error interface
func (e CreateChainReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateChainReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateChainReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateChainReqValidationError{}

var _CreateChainReq_ResKind_InLookup = map[string]struct{}{
	"domain":     {},
	"middleware": {},
	"delivery":   {},
	"upgrade":    {},
}

// Validate checks the field values on DescribeChainReq with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *DescribeChainReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DescribeChainReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DescribeChainReqMultiError, or nil if none found.
func (m *DescribeChainReq) ValidateAll() error {
	return m.validate(true)
}

func (m *DescribeChainReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetId() < 0 {
		err := DescribeChainReqValidationError{
			field:  "Id",
			reason: "value must be greater than or equal to 0",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	for idx, item := range m.GetFilters() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, DescribeChainReqValidationError{
						field:  fmt.Sprintf("Filters[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, DescribeChainReqValidationError{
						field:  fmt.Sprintf("Filters[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return DescribeChainReqValidationError{
					field:  fmt.Sprintf("Filters[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return DescribeChainReqMultiError(errors)
	}

	return nil
}

// DescribeChainReqMultiError is an error wrapping multiple validation errors
// returned by DescribeChainReq.ValidateAll() if the designated constraints
// aren't met.
type DescribeChainReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DescribeChainReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DescribeChainReqMultiError) AllErrors() []error { return m }

// DescribeChainReqValidationError is the validation error returned by
// DescribeChainReq.Validate if the designated constraints aren't met.
type DescribeChainReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DescribeChainReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DescribeChainReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DescribeChainReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DescribeChainReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DescribeChainReqValidationError) ErrorName() string { return "DescribeChainReqValidationError" }

// Error satisfies the builtin error interface
func (e DescribeChainReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDescribeChainReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DescribeChainReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DescribeChainReqValidationError{}

// Validate checks the field values on DescribeChainReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DescribeChainReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DescribeChainReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DescribeChainReplyMultiError, or nil if none found.
func (m *DescribeChainReply) ValidateAll() error {
	return m.validate(true)
}

func (m *DescribeChainReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for CreatedAt

	if all {
		switch v := interface{}(m.GetToGoods()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DescribeChainReplyValidationError{
					field:  "ToGoods",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DescribeChainReplyValidationError{
					field:  "ToGoods",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetToGoods()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DescribeChainReplyValidationError{
				field:  "ToGoods",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetFromGoods()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DescribeChainReplyValidationError{
					field:  "FromGoods",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DescribeChainReplyValidationError{
					field:  "FromGoods",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetFromGoods()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DescribeChainReplyValidationError{
				field:  "FromGoods",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for ResKind

	// no validation rules for ResId

	if all {
		switch v := interface{}(m.GetDomain()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DescribeChainReplyValidationError{
					field:  "Domain",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DescribeChainReplyValidationError{
					field:  "Domain",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDomain()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DescribeChainReplyValidationError{
				field:  "Domain",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetMiddleware()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DescribeChainReplyValidationError{
					field:  "Middleware",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DescribeChainReplyValidationError{
					field:  "Middleware",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetMiddleware()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DescribeChainReplyValidationError{
				field:  "Middleware",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for DeployType

	// no validation rules for ClusterType

	if len(errors) > 0 {
		return DescribeChainReplyMultiError(errors)
	}

	return nil
}

// DescribeChainReplyMultiError is an error wrapping multiple validation errors
// returned by DescribeChainReply.ValidateAll() if the designated constraints
// aren't met.
type DescribeChainReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DescribeChainReplyMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DescribeChainReplyMultiError) AllErrors() []error { return m }

// DescribeChainReplyValidationError is the validation error returned by
// DescribeChainReply.Validate if the designated constraints aren't met.
type DescribeChainReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DescribeChainReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DescribeChainReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DescribeChainReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DescribeChainReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DescribeChainReplyValidationError) ErrorName() string {
	return "DescribeChainReplyValidationError"
}

// Error satisfies the builtin error interface
func (e DescribeChainReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDescribeChainReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DescribeChainReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DescribeChainReplyValidationError{}

// Validate checks the field values on DescribesChainReq with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *DescribesChainReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DescribesChainReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DescribesChainReqMultiError, or nil if none found.
func (m *DescribesChainReq) ValidateAll() error {
	return m.validate(true)
}

func (m *DescribesChainReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if val := m.GetPageNumber(); val < -1 || val > 16777216 {
		err := DescribesChainReqValidationError{
			field:  "PageNumber",
			reason: "value must be inside range [-1, 16777216]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if val := m.GetPageSize(); val < 1 || val > 16777216 {
		err := DescribesChainReqValidationError{
			field:  "PageSize",
			reason: "value must be inside range [1, 16777216]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	for idx, item := range m.GetFilters() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, DescribesChainReqValidationError{
						field:  fmt.Sprintf("Filters[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, DescribesChainReqValidationError{
						field:  fmt.Sprintf("Filters[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return DescribesChainReqValidationError{
					field:  fmt.Sprintf("Filters[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	for idx, item := range m.GetSorts() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, DescribesChainReqValidationError{
						field:  fmt.Sprintf("Sorts[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, DescribesChainReqValidationError{
						field:  fmt.Sprintf("Sorts[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return DescribesChainReqValidationError{
					field:  fmt.Sprintf("Sorts[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return DescribesChainReqMultiError(errors)
	}

	return nil
}

// DescribesChainReqMultiError is an error wrapping multiple validation errors
// returned by DescribesChainReq.ValidateAll() if the designated constraints
// aren't met.
type DescribesChainReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DescribesChainReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DescribesChainReqMultiError) AllErrors() []error { return m }

// DescribesChainReqValidationError is the validation error returned by
// DescribesChainReq.Validate if the designated constraints aren't met.
type DescribesChainReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DescribesChainReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DescribesChainReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DescribesChainReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DescribesChainReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DescribesChainReqValidationError) ErrorName() string {
	return "DescribesChainReqValidationError"
}

// Error satisfies the builtin error interface
func (e DescribesChainReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDescribesChainReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DescribesChainReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DescribesChainReqValidationError{}

// Validate checks the field values on DescribesChainReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DescribesChainReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DescribesChainReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DescribesChainReplyMultiError, or nil if none found.
func (m *DescribesChainReply) ValidateAll() error {
	return m.validate(true)
}

func (m *DescribesChainReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetList() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, DescribesChainReplyValidationError{
						field:  fmt.Sprintf("List[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, DescribesChainReplyValidationError{
						field:  fmt.Sprintf("List[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return DescribesChainReplyValidationError{
					field:  fmt.Sprintf("List[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for TotalCount

	if len(errors) > 0 {
		return DescribesChainReplyMultiError(errors)
	}

	return nil
}

// DescribesChainReplyMultiError is an error wrapping multiple validation
// errors returned by DescribesChainReply.ValidateAll() if the designated
// constraints aren't met.
type DescribesChainReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DescribesChainReplyMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DescribesChainReplyMultiError) AllErrors() []error { return m }

// DescribesChainReplyValidationError is the validation error returned by
// DescribesChainReply.Validate if the designated constraints aren't met.
type DescribesChainReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DescribesChainReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DescribesChainReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DescribesChainReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DescribesChainReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DescribesChainReplyValidationError) ErrorName() string {
	return "DescribesChainReplyValidationError"
}

// Error satisfies the builtin error interface
func (e DescribesChainReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDescribesChainReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DescribesChainReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DescribesChainReplyValidationError{}

// Validate checks the field values on DeleteChainReq with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *DeleteChainReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DeleteChainReq with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in DeleteChainReqMultiError,
// or nil if none found.
func (m *DeleteChainReq) ValidateAll() error {
	return m.validate(true)
}

func (m *DeleteChainReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetId() < 1 {
		err := DeleteChainReqValidationError{
			field:  "Id",
			reason: "value must be greater than or equal to 1",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return DeleteChainReqMultiError(errors)
	}

	return nil
}

// DeleteChainReqMultiError is an error wrapping multiple validation errors
// returned by DeleteChainReq.ValidateAll() if the designated constraints
// aren't met.
type DeleteChainReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DeleteChainReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DeleteChainReqMultiError) AllErrors() []error { return m }

// DeleteChainReqValidationError is the validation error returned by
// DeleteChainReq.Validate if the designated constraints aren't met.
type DeleteChainReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DeleteChainReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DeleteChainReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DeleteChainReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DeleteChainReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DeleteChainReqValidationError) ErrorName() string { return "DeleteChainReqValidationError" }

// Error satisfies the builtin error interface
func (e DeleteChainReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDeleteChainReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DeleteChainReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DeleteChainReqValidationError{}

// Validate checks the field values on DescribePdReply with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *DescribePdReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DescribePdReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DescribePdReplyMultiError, or nil if none found.
func (m *DescribePdReply) ValidateAll() error {
	return m.validate(true)
}

func (m *DescribePdReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for CreatedAt

	// no validation rules for UpdatedAt

	// no validation rules for Content

	if len(errors) > 0 {
		return DescribePdReplyMultiError(errors)
	}

	return nil
}

// DescribePdReplyMultiError is an error wrapping multiple validation errors
// returned by DescribePdReply.ValidateAll() if the designated constraints
// aren't met.
type DescribePdReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DescribePdReplyMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DescribePdReplyMultiError) AllErrors() []error { return m }

// DescribePdReplyValidationError is the validation error returned by
// DescribePdReply.Validate if the designated constraints aren't met.
type DescribePdReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DescribePdReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DescribePdReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DescribePdReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DescribePdReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DescribePdReplyValidationError) ErrorName() string { return "DescribePdReplyValidationError" }

// Error satisfies the builtin error interface
func (e DescribePdReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDescribePdReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DescribePdReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DescribePdReplyValidationError{}

// Validate checks the field values on DescribesPdReply with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *DescribesPdReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DescribesPdReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DescribesPdReplyMultiError, or nil if none found.
func (m *DescribesPdReply) ValidateAll() error {
	return m.validate(true)
}

func (m *DescribesPdReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetList() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, DescribesPdReplyValidationError{
						field:  fmt.Sprintf("List[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, DescribesPdReplyValidationError{
						field:  fmt.Sprintf("List[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return DescribesPdReplyValidationError{
					field:  fmt.Sprintf("List[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for TotalCount

	if len(errors) > 0 {
		return DescribesPdReplyMultiError(errors)
	}

	return nil
}

// DescribesPdReplyMultiError is an error wrapping multiple validation errors
// returned by DescribesPdReply.ValidateAll() if the designated constraints
// aren't met.
type DescribesPdReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DescribesPdReplyMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DescribesPdReplyMultiError) AllErrors() []error { return m }

// DescribesPdReplyValidationError is the validation error returned by
// DescribesPdReply.Validate if the designated constraints aren't met.
type DescribesPdReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DescribesPdReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DescribesPdReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DescribesPdReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DescribesPdReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DescribesPdReplyValidationError) ErrorName() string { return "DescribesPdReplyValidationError" }

// Error satisfies the builtin error interface
func (e DescribesPdReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDescribesPdReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DescribesPdReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DescribesPdReplyValidationError{}

// Validate checks the field values on DescribeUpgradeReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DescribeUpgradeReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DescribeUpgradeReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DescribeUpgradeReplyMultiError, or nil if none found.
func (m *DescribeUpgradeReply) ValidateAll() error {
	return m.validate(true)
}

func (m *DescribeUpgradeReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for CreatedAt

	// no validation rules for UpdatedAt

	// no validation rules for Content

	if len(errors) > 0 {
		return DescribeUpgradeReplyMultiError(errors)
	}

	return nil
}

// DescribeUpgradeReplyMultiError is an error wrapping multiple validation
// errors returned by DescribeUpgradeReply.ValidateAll() if the designated
// constraints aren't met.
type DescribeUpgradeReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DescribeUpgradeReplyMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DescribeUpgradeReplyMultiError) AllErrors() []error { return m }

// DescribeUpgradeReplyValidationError is the validation error returned by
// DescribeUpgradeReply.Validate if the designated constraints aren't met.
type DescribeUpgradeReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DescribeUpgradeReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DescribeUpgradeReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DescribeUpgradeReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DescribeUpgradeReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DescribeUpgradeReplyValidationError) ErrorName() string {
	return "DescribeUpgradeReplyValidationError"
}

// Error satisfies the builtin error interface
func (e DescribeUpgradeReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDescribeUpgradeReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DescribeUpgradeReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DescribeUpgradeReplyValidationError{}

// Validate checks the field values on DescribesUpgradeReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DescribesUpgradeReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DescribesUpgradeReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DescribesUpgradeReplyMultiError, or nil if none found.
func (m *DescribesUpgradeReply) ValidateAll() error {
	return m.validate(true)
}

func (m *DescribesUpgradeReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetList() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, DescribesUpgradeReplyValidationError{
						field:  fmt.Sprintf("List[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, DescribesUpgradeReplyValidationError{
						field:  fmt.Sprintf("List[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return DescribesUpgradeReplyValidationError{
					field:  fmt.Sprintf("List[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for TotalCount

	if len(errors) > 0 {
		return DescribesUpgradeReplyMultiError(errors)
	}

	return nil
}

// DescribesUpgradeReplyMultiError is an error wrapping multiple validation
// errors returned by DescribesUpgradeReply.ValidateAll() if the designated
// constraints aren't met.
type DescribesUpgradeReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DescribesUpgradeReplyMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DescribesUpgradeReplyMultiError) AllErrors() []error { return m }

// DescribesUpgradeReplyValidationError is the validation error returned by
// DescribesUpgradeReply.Validate if the designated constraints aren't met.
type DescribesUpgradeReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DescribesUpgradeReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DescribesUpgradeReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DescribesUpgradeReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DescribesUpgradeReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DescribesUpgradeReplyValidationError) ErrorName() string {
	return "DescribesUpgradeReplyValidationError"
}

// Error satisfies the builtin error interface
func (e DescribesUpgradeReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDescribesUpgradeReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DescribesUpgradeReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DescribesUpgradeReplyValidationError{}

// Validate checks the field values on DescribeMetadataReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DescribeMetadataReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DescribeMetadataReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DescribeMetadataReplyMultiError, or nil if none found.
func (m *DescribeMetadataReply) ValidateAll() error {
	return m.validate(true)
}

func (m *DescribeMetadataReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for CreatedAt

	// no validation rules for UpdatedAt

	// no validation rules for Type

	// no validation rules for VersionName

	// no validation rules for VersionId

	// no validation rules for UnifiedMetadataId

	if len(errors) > 0 {
		return DescribeMetadataReplyMultiError(errors)
	}

	return nil
}

// DescribeMetadataReplyMultiError is an error wrapping multiple validation
// errors returned by DescribeMetadataReply.ValidateAll() if the designated
// constraints aren't met.
type DescribeMetadataReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DescribeMetadataReplyMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DescribeMetadataReplyMultiError) AllErrors() []error { return m }

// DescribeMetadataReplyValidationError is the validation error returned by
// DescribeMetadataReply.Validate if the designated constraints aren't met.
type DescribeMetadataReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DescribeMetadataReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DescribeMetadataReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DescribeMetadataReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DescribeMetadataReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DescribeMetadataReplyValidationError) ErrorName() string {
	return "DescribeMetadataReplyValidationError"
}

// Error satisfies the builtin error interface
func (e DescribeMetadataReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDescribeMetadataReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DescribeMetadataReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DescribeMetadataReplyValidationError{}

// Validate checks the field values on DescribesMetadataReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DescribesMetadataReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DescribesMetadataReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DescribesMetadataReplyMultiError, or nil if none found.
func (m *DescribesMetadataReply) ValidateAll() error {
	return m.validate(true)
}

func (m *DescribesMetadataReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetList() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, DescribesMetadataReplyValidationError{
						field:  fmt.Sprintf("List[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, DescribesMetadataReplyValidationError{
						field:  fmt.Sprintf("List[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return DescribesMetadataReplyValidationError{
					field:  fmt.Sprintf("List[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for TotalCount

	if len(errors) > 0 {
		return DescribesMetadataReplyMultiError(errors)
	}

	return nil
}

// DescribesMetadataReplyMultiError is an error wrapping multiple validation
// errors returned by DescribesMetadataReply.ValidateAll() if the designated
// constraints aren't met.
type DescribesMetadataReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DescribesMetadataReplyMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DescribesMetadataReplyMultiError) AllErrors() []error { return m }

// DescribesMetadataReplyValidationError is the validation error returned by
// DescribesMetadataReply.Validate if the designated constraints aren't met.
type DescribesMetadataReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DescribesMetadataReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DescribesMetadataReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DescribesMetadataReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DescribesMetadataReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DescribesMetadataReplyValidationError) ErrorName() string {
	return "DescribesMetadataReplyValidationError"
}

// Error satisfies the builtin error interface
func (e DescribesMetadataReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDescribesMetadataReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DescribesMetadataReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DescribesMetadataReplyValidationError{}

// Validate checks the field values on CreateConfigReq with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *CreateConfigReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateConfigReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateConfigReqMultiError, or nil if none found.
func (m *CreateConfigReq) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateConfigReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	if all {
		switch v := interface{}(m.GetGoods()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateConfigReqValidationError{
					field:  "Goods",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateConfigReqValidationError{
					field:  "Goods",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetGoods()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateConfigReqValidationError{
				field:  "Goods",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Kind

	// no validation rules for Data

	if len(errors) > 0 {
		return CreateConfigReqMultiError(errors)
	}

	return nil
}

// CreateConfigReqMultiError is an error wrapping multiple validation errors
// returned by CreateConfigReq.ValidateAll() if the designated constraints
// aren't met.
type CreateConfigReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateConfigReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateConfigReqMultiError) AllErrors() []error { return m }

// CreateConfigReqValidationError is the validation error returned by
// CreateConfigReq.Validate if the designated constraints aren't met.
type CreateConfigReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateConfigReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateConfigReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateConfigReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateConfigReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateConfigReqValidationError) ErrorName() string { return "CreateConfigReqValidationError" }

// Error satisfies the builtin error interface
func (e CreateConfigReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateConfigReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateConfigReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateConfigReqValidationError{}

// Validate checks the field values on DescribeConfigReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DescribeConfigReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DescribeConfigReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DescribeConfigReplyMultiError, or nil if none found.
func (m *DescribeConfigReply) ValidateAll() error {
	return m.validate(true)
}

func (m *DescribeConfigReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for CreatedAt

	// no validation rules for UpdatedAt

	// no validation rules for Kind

	// no validation rules for Data

	if len(errors) > 0 {
		return DescribeConfigReplyMultiError(errors)
	}

	return nil
}

// DescribeConfigReplyMultiError is an error wrapping multiple validation
// errors returned by DescribeConfigReply.ValidateAll() if the designated
// constraints aren't met.
type DescribeConfigReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DescribeConfigReplyMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DescribeConfigReplyMultiError) AllErrors() []error { return m }

// DescribeConfigReplyValidationError is the validation error returned by
// DescribeConfigReply.Validate if the designated constraints aren't met.
type DescribeConfigReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DescribeConfigReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DescribeConfigReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DescribeConfigReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DescribeConfigReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DescribeConfigReplyValidationError) ErrorName() string {
	return "DescribeConfigReplyValidationError"
}

// Error satisfies the builtin error interface
func (e DescribeConfigReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDescribeConfigReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DescribeConfigReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DescribeConfigReplyValidationError{}

// Validate checks the field values on CreateDeliveryRuleGroupReq with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreateDeliveryRuleGroupReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateDeliveryRuleGroupReq with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateDeliveryRuleGroupReqMultiError, or nil if none found.
func (m *CreateDeliveryRuleGroupReq) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateDeliveryRuleGroupReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Name

	if len(errors) > 0 {
		return CreateDeliveryRuleGroupReqMultiError(errors)
	}

	return nil
}

// CreateDeliveryRuleGroupReqMultiError is an error wrapping multiple
// validation errors returned by CreateDeliveryRuleGroupReq.ValidateAll() if
// the designated constraints aren't met.
type CreateDeliveryRuleGroupReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateDeliveryRuleGroupReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateDeliveryRuleGroupReqMultiError) AllErrors() []error { return m }

// CreateDeliveryRuleGroupReqValidationError is the validation error returned
// by CreateDeliveryRuleGroupReq.Validate if the designated constraints aren't met.
type CreateDeliveryRuleGroupReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateDeliveryRuleGroupReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateDeliveryRuleGroupReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateDeliveryRuleGroupReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateDeliveryRuleGroupReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateDeliveryRuleGroupReqValidationError) ErrorName() string {
	return "CreateDeliveryRuleGroupReqValidationError"
}

// Error satisfies the builtin error interface
func (e CreateDeliveryRuleGroupReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateDeliveryRuleGroupReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateDeliveryRuleGroupReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateDeliveryRuleGroupReqValidationError{}

// Validate checks the field values on GoodsInfo with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *GoodsInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GoodsInfo with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in GoodsInfoMultiError, or nil
// if none found.
func (m *GoodsInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *GoodsInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Category

	// no validation rules for Service

	// no validation rules for Resource

	// no validation rules for Application

	if len(errors) > 0 {
		return GoodsInfoMultiError(errors)
	}

	return nil
}

// GoodsInfoMultiError is an error wrapping multiple validation errors returned
// by GoodsInfo.ValidateAll() if the designated constraints aren't met.
type GoodsInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GoodsInfoMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GoodsInfoMultiError) AllErrors() []error { return m }

// GoodsInfoValidationError is the validation error returned by
// GoodsInfo.Validate if the designated constraints aren't met.
type GoodsInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GoodsInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GoodsInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GoodsInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GoodsInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GoodsInfoValidationError) ErrorName() string { return "GoodsInfoValidationError" }

// Error satisfies the builtin error interface
func (e GoodsInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGoodsInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GoodsInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GoodsInfoValidationError{}

// Validate checks the field values on SyncGoodsActionReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *SyncGoodsActionReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SyncGoodsActionReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// SyncGoodsActionReqMultiError, or nil if none found.
func (m *SyncGoodsActionReq) ValidateAll() error {
	return m.validate(true)
}

func (m *SyncGoodsActionReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(m.GetVersion()) < 1 {
		err := SyncGoodsActionReqValidationError{
			field:  "Version",
			reason: "value length must be at least 1 bytes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if !_SyncGoodsActionReq_Version_Pattern.MatchString(m.GetVersion()) {
		err := SyncGoodsActionReqValidationError{
			field:  "Version",
			reason: "value does not match regex pattern \"^[0-9a-zA-Z.]+$\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	for idx, item := range m.GetGoodsList() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, SyncGoodsActionReqValidationError{
						field:  fmt.Sprintf("GoodsList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, SyncGoodsActionReqValidationError{
						field:  fmt.Sprintf("GoodsList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return SyncGoodsActionReqValidationError{
					field:  fmt.Sprintf("GoodsList[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return SyncGoodsActionReqMultiError(errors)
	}

	return nil
}

// SyncGoodsActionReqMultiError is an error wrapping multiple validation errors
// returned by SyncGoodsActionReq.ValidateAll() if the designated constraints
// aren't met.
type SyncGoodsActionReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SyncGoodsActionReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SyncGoodsActionReqMultiError) AllErrors() []error { return m }

// SyncGoodsActionReqValidationError is the validation error returned by
// SyncGoodsActionReq.Validate if the designated constraints aren't met.
type SyncGoodsActionReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SyncGoodsActionReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SyncGoodsActionReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SyncGoodsActionReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SyncGoodsActionReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SyncGoodsActionReqValidationError) ErrorName() string {
	return "SyncGoodsActionReqValidationError"
}

// Error satisfies the builtin error interface
func (e SyncGoodsActionReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSyncGoodsActionReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SyncGoodsActionReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SyncGoodsActionReqValidationError{}

var _SyncGoodsActionReq_Version_Pattern = regexp.MustCompile("^[0-9a-zA-Z.]+$")

// Validate checks the field values on SyncGoodsActionReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *SyncGoodsActionReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SyncGoodsActionReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// SyncGoodsActionReplyMultiError, or nil if none found.
func (m *SyncGoodsActionReply) ValidateAll() error {
	return m.validate(true)
}

func (m *SyncGoodsActionReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for OfflineCount

	if len(errors) > 0 {
		return SyncGoodsActionReplyMultiError(errors)
	}

	return nil
}

// SyncGoodsActionReplyMultiError is an error wrapping multiple validation
// errors returned by SyncGoodsActionReply.ValidateAll() if the designated
// constraints aren't met.
type SyncGoodsActionReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SyncGoodsActionReplyMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SyncGoodsActionReplyMultiError) AllErrors() []error { return m }

// SyncGoodsActionReplyValidationError is the validation error returned by
// SyncGoodsActionReply.Validate if the designated constraints aren't met.
type SyncGoodsActionReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SyncGoodsActionReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SyncGoodsActionReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SyncGoodsActionReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SyncGoodsActionReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SyncGoodsActionReplyValidationError) ErrorName() string {
	return "SyncGoodsActionReplyValidationError"
}

// Error satisfies the builtin error interface
func (e SyncGoodsActionReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSyncGoodsActionReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SyncGoodsActionReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SyncGoodsActionReplyValidationError{}
