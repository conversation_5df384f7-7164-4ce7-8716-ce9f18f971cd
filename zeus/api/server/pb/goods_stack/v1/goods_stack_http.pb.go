// Code generated by protoc-gen-go-http. DO NOT EDIT.
// versions:
// - protoc-gen-go-http v2.6.2.230413
// - protoc             v3.21.1
// source: goods_stack.proto

package pbGoodsStack

import (
	context1 "coding.jd.com/pcd-application/win-go/context"
	http1 "coding.jd.com/pcd-application/win-go/transport/http"
	context "context"
	http "github.com/go-kratos/kratos/v2/transport/http"
	binding "github.com/go-kratos/kratos/v2/transport/http/binding"
	empty "github.com/golang/protobuf/ptypes/empty"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the kratos package it is being compiled against.
var _ = new(context.Context)
var _ = binding.EncodeURL
var _ = new(context1.Context)

const _ = http.SupportPackageIsVersion1
const _ = http1.SupportPackageIsVersion1

const OperationGoodsStackCreate = "/jdstack.zeus.v1.goodsStack.GoodsStack/Create"
const OperationGoodsStackCreateChain = "/jdstack.zeus.v1.goodsStack.GoodsStack/CreateChain"
const OperationGoodsStackCreateDelivery = "/jdstack.zeus.v1.goodsStack.GoodsStack/CreateDelivery"
const OperationGoodsStackCreateDomain = "/jdstack.zeus.v1.goodsStack.GoodsStack/CreateDomain"
const OperationGoodsStackCreateFilecenter = "/jdstack.zeus.v1.goodsStack.GoodsStack/CreateFilecenter"
const OperationGoodsStackCreateFlavor = "/jdstack.zeus.v1.goodsStack.GoodsStack/CreateFlavor"
const OperationGoodsStackCreateHardwareRole = "/jdstack.zeus.v1.goodsStack.GoodsStack/CreateHardwareRole"
const OperationGoodsStackCreateHardwareRoleLabel = "/jdstack.zeus.v1.goodsStack.GoodsStack/CreateHardwareRoleLabel"
const OperationGoodsStackCreateHardwareRoleUnion = "/jdstack.zeus.v1.goodsStack.GoodsStack/CreateHardwareRoleUnion"
const OperationGoodsStackCreateIac = "/jdstack.zeus.v1.goodsStack.GoodsStack/CreateIac"
const OperationGoodsStackCreateMiddleware = "/jdstack.zeus.v1.goodsStack.GoodsStack/CreateMiddleware"
const OperationGoodsStackDeleteChain = "/jdstack.zeus.v1.goodsStack.GoodsStack/DeleteChain"
const OperationGoodsStackDeleteDelivery = "/jdstack.zeus.v1.goodsStack.GoodsStack/DeleteDelivery"
const OperationGoodsStackDeleteDomain = "/jdstack.zeus.v1.goodsStack.GoodsStack/DeleteDomain"
const OperationGoodsStackDeleteFilecenter = "/jdstack.zeus.v1.goodsStack.GoodsStack/DeleteFilecenter"
const OperationGoodsStackDeleteFlavor = "/jdstack.zeus.v1.goodsStack.GoodsStack/DeleteFlavor"
const OperationGoodsStackDeleteHardwareRole = "/jdstack.zeus.v1.goodsStack.GoodsStack/DeleteHardwareRole"
const OperationGoodsStackDeleteHardwareRoleLabel = "/jdstack.zeus.v1.goodsStack.GoodsStack/DeleteHardwareRoleLabel"
const OperationGoodsStackDeleteIac = "/jdstack.zeus.v1.goodsStack.GoodsStack/DeleteIac"
const OperationGoodsStackDeleteMiddleware = "/jdstack.zeus.v1.goodsStack.GoodsStack/DeleteMiddleware"
const OperationGoodsStackDescribe = "/jdstack.zeus.v1.goodsStack.GoodsStack/Describe"
const OperationGoodsStackDescribeChain = "/jdstack.zeus.v1.goodsStack.GoodsStack/DescribeChain"
const OperationGoodsStackDescribeDelivery = "/jdstack.zeus.v1.goodsStack.GoodsStack/DescribeDelivery"
const OperationGoodsStackDescribeDomain = "/jdstack.zeus.v1.goodsStack.GoodsStack/DescribeDomain"
const OperationGoodsStackDescribeFilecenter = "/jdstack.zeus.v1.goodsStack.GoodsStack/DescribeFilecenter"
const OperationGoodsStackDescribeFlavor = "/jdstack.zeus.v1.goodsStack.GoodsStack/DescribeFlavor"
const OperationGoodsStackDescribeHardwareRole = "/jdstack.zeus.v1.goodsStack.GoodsStack/DescribeHardwareRole"
const OperationGoodsStackDescribeHardwareRoleLabel = "/jdstack.zeus.v1.goodsStack.GoodsStack/DescribeHardwareRoleLabel"
const OperationGoodsStackDescribeIac = "/jdstack.zeus.v1.goodsStack.GoodsStack/DescribeIac"
const OperationGoodsStackDescribeMiddleware = "/jdstack.zeus.v1.goodsStack.GoodsStack/DescribeMiddleware"
const OperationGoodsStackDescribes = "/jdstack.zeus.v1.goodsStack.GoodsStack/Describes"
const OperationGoodsStackDescribesChain = "/jdstack.zeus.v1.goodsStack.GoodsStack/DescribesChain"
const OperationGoodsStackDescribesDelivery = "/jdstack.zeus.v1.goodsStack.GoodsStack/DescribesDelivery"
const OperationGoodsStackDescribesDomain = "/jdstack.zeus.v1.goodsStack.GoodsStack/DescribesDomain"
const OperationGoodsStackDescribesFilecenter = "/jdstack.zeus.v1.goodsStack.GoodsStack/DescribesFilecenter"
const OperationGoodsStackDescribesFlavor = "/jdstack.zeus.v1.goodsStack.GoodsStack/DescribesFlavor"
const OperationGoodsStackDescribesHardwareRole = "/jdstack.zeus.v1.goodsStack.GoodsStack/DescribesHardwareRole"
const OperationGoodsStackDescribesHardwareRoleLabel = "/jdstack.zeus.v1.goodsStack.GoodsStack/DescribesHardwareRoleLabel"
const OperationGoodsStackDescribesIac = "/jdstack.zeus.v1.goodsStack.GoodsStack/DescribesIac"
const OperationGoodsStackDescribesMiddleware = "/jdstack.zeus.v1.goodsStack.GoodsStack/DescribesMiddleware"
const OperationGoodsStackModifyDelivery = "/jdstack.zeus.v1.goodsStack.GoodsStack/ModifyDelivery"
const OperationGoodsStackModifyDomain = "/jdstack.zeus.v1.goodsStack.GoodsStack/ModifyDomain"
const OperationGoodsStackModifyFlavor = "/jdstack.zeus.v1.goodsStack.GoodsStack/ModifyFlavor"
const OperationGoodsStackModifyIac = "/jdstack.zeus.v1.goodsStack.GoodsStack/ModifyIac"
const OperationGoodsStackModifyMiddleware = "/jdstack.zeus.v1.goodsStack.GoodsStack/ModifyMiddleware"
const OperationGoodsStackModifySyncGoodsAction = "/jdstack.zeus.v1.goodsStack.GoodsStack/ModifySyncGoodsAction"

type GoodsStackHTTPServer interface {
	Create(context.Context, context1.Context, *CreateReq) (*DescribeReply, error)
	CreateChain(context.Context, context1.Context, *CreateChainReq) (*DescribeChainReply, error)
	CreateDelivery(context.Context, context1.Context, *CreateDeliveryReq) (*DescribeDeliveryReply, error)
	CreateDomain(context.Context, context1.Context, *CreateDomainReq) (*DescribeDomainReply, error)
	CreateFilecenter(context.Context, context1.Context, *CreateFilecenterReq) (*DescribeFilecenterReply, error)
	CreateFlavor(context.Context, context1.Context, *CreateFlavorReq) (*DescribeFlavorReply, error)
	CreateHardwareRole(context.Context, context1.Context, *CreateHardwareRoleReq) (*empty.Empty, error)
	CreateHardwareRoleLabel(context.Context, context1.Context, *CreateHardwareRoleLabelReq) (*empty.Empty, error)
	CreateHardwareRoleUnion(context.Context, context1.Context, *CreateHardwareRoleUnionReq) (*empty.Empty, error)
	CreateIac(context.Context, context1.Context, *CreateIacReq) (*DescribeIacReply, error)
	CreateMiddleware(context.Context, context1.Context, *CreateMiddlewareReq) (*DescribeMiddlewareReply, error)
	DeleteChain(context.Context, context1.Context, *DeleteChainReq) (*empty.Empty, error)
	DeleteDelivery(context.Context, context1.Context, *DeleteDeliveryReq) (*empty.Empty, error)
	DeleteDomain(context.Context, context1.Context, *DeleteDomainReq) (*empty.Empty, error)
	DeleteFilecenter(context.Context, context1.Context, *DeleteFilecenterReq) (*empty.Empty, error)
	DeleteFlavor(context.Context, context1.Context, *DeleteFlavorReq) (*empty.Empty, error)
	DeleteHardwareRole(context.Context, context1.Context, *DeleteHardwareRoleReq) (*empty.Empty, error)
	DeleteHardwareRoleLabel(context.Context, context1.Context, *DeleteHardwareRoleLabelReq) (*empty.Empty, error)
	DeleteIac(context.Context, context1.Context, *DeleteIacReq) (*empty.Empty, error)
	DeleteMiddleware(context.Context, context1.Context, *DeleteMiddlewareReq) (*empty.Empty, error)
	Describe(context.Context, context1.Context, *DescribeReq) (*DescribeReply, error)
	DescribeChain(context.Context, context1.Context, *DescribeChainReq) (*DescribeChainReply, error)
	DescribeDelivery(context.Context, context1.Context, *DescribeDeliveryReq) (*DescribeDeliveryReply, error)
	DescribeDomain(context.Context, context1.Context, *DescribeDomainReq) (*DescribeDomainReply, error)
	DescribeFilecenter(context.Context, context1.Context, *DescribeFilecenterReq) (*DescribeFilecenterReply, error)
	DescribeFlavor(context.Context, context1.Context, *DescribeFlavorReq) (*DescribeFlavorReply, error)
	DescribeHardwareRole(context.Context, context1.Context, *DescribeHardwareRoleReq) (*DescribeHardwareRoleReply, error)
	DescribeHardwareRoleLabel(context.Context, context1.Context, *DescribeHardwareRoleLabelReq) (*DescribeHardwareRoleLabelReply, error)
	DescribeIac(context.Context, context1.Context, *DescribeIacReq) (*DescribeIacReply, error)
	DescribeMiddleware(context.Context, context1.Context, *DescribeMiddlewareReq) (*DescribeMiddlewareReply, error)
	Describes(context.Context, context1.Context, *DescribesReq) (*DescribesReply, error)
	DescribesChain(context.Context, context1.Context, *DescribesChainReq) (*DescribesChainReply, error)
	DescribesDelivery(context.Context, context1.Context, *DescribesDeliveryReq) (*DescribesDeliveryReply, error)
	DescribesDomain(context.Context, context1.Context, *DescribesDomainReq) (*DescribesDomainReply, error)
	DescribesFilecenter(context.Context, context1.Context, *DescribesFilecenterReq) (*DescribesFilecenterReply, error)
	DescribesFlavor(context.Context, context1.Context, *DescribesFlavorReq) (*DescribesFlavorReply, error)
	DescribesHardwareRole(context.Context, context1.Context, *DescribesHardwareRoleReq) (*DescribesHardwareRoleReply, error)
	DescribesHardwareRoleLabel(context.Context, context1.Context, *DescribesHardwareRoleLabelReq) (*DescribesHardwareRoleLabelReply, error)
	DescribesIac(context.Context, context1.Context, *DescribesIacReq) (*DescribesIacReply, error)
	DescribesMiddleware(context.Context, context1.Context, *DescribesMiddlewareReq) (*DescribesMiddlewareReply, error)
	ModifyDelivery(context.Context, context1.Context, *ModifyDeliveryReq) (*empty.Empty, error)
	ModifyDomain(context.Context, context1.Context, *ModifyDomainReq) (*empty.Empty, error)
	ModifyFlavor(context.Context, context1.Context, *ModifyFlavorReq) (*empty.Empty, error)
	ModifyIac(context.Context, context1.Context, *ModifyIacReq) (*empty.Empty, error)
	ModifyMiddleware(context.Context, context1.Context, *ModifyMiddlewareReq) (*empty.Empty, error)
	ModifySyncGoodsAction(context.Context, context1.Context, *SyncGoodsActionReq) (*SyncGoodsActionReply, error)
}

func RegisterGoodsStackHTTPServer(r *http1.Router, srv GoodsStackHTTPServer) {
	r.POST("/goodsStack/{goodsId}/hardwareRoleLabel/{hardwareRoleLabelId}", _GoodsStack_CreateHardwareRoleLabel0_HTTP_Handler(srv))
	r.DELETE("/goodsStack/{goodsId}/hardwareRoleLabel/{hardwareRoleLabelId}", _GoodsStack_DeleteHardwareRoleLabel0_HTTP_Handler(srv))
	r.POST("/goodsStack/{goodsId}/hardwareRole/{hardwareRoleId}", _GoodsStack_CreateHardwareRole0_HTTP_Handler(srv))
	r.DELETE("/goodsStack/{goodsId}/hardwareRole/{hardwareRoleId}", _GoodsStack_DeleteHardwareRole0_HTTP_Handler(srv))
	r.POST("/version/{version}/goodsStack:syncAction", _GoodsStack_ModifySyncGoodsAction0_HTTP_Handler(srv))
	r.GET("/goodsStackHardwareRoleLabel/{id}", _GoodsStack_DescribeHardwareRoleLabel0_HTTP_Handler(srv))
	r.POST("/version/{version}/goodsStack", _GoodsStack_Create0_HTTP_Handler(srv))
	r.GET("/version/{version}/goodsStack", _GoodsStack_Describe0_HTTP_Handler(srv))
	r.GET("/goodsStackHardwareRole/{id}", _GoodsStack_DescribeHardwareRole0_HTTP_Handler(srv))
	r.GET("/goodsStackHardwareRoleLabel", _GoodsStack_DescribesHardwareRoleLabel0_HTTP_Handler(srv))
	r.DELETE("/goodsStackFilecenter/{id}", _GoodsStack_DeleteFilecenter0_HTTP_Handler(srv))
	r.GET("/goodsStackFilecenter/{id}", _GoodsStack_DescribeFilecenter0_HTTP_Handler(srv))
	r.DELETE("/goodsStackMiddleware/{id}", _GoodsStack_DeleteMiddleware0_HTTP_Handler(srv))
	r.PUT("/goodsStackMiddleware/{id}", _GoodsStack_ModifyMiddleware0_HTTP_Handler(srv))
	r.GET("/goodsStackMiddleware/{id}", _GoodsStack_DescribeMiddleware0_HTTP_Handler(srv))
	r.DELETE("/goodsStackDelivery/{id}", _GoodsStack_DeleteDelivery0_HTTP_Handler(srv))
	r.PUT("/goodsStackDelivery/{id}", _GoodsStack_ModifyDelivery0_HTTP_Handler(srv))
	r.GET("/goodsStackDelivery/{id}", _GoodsStack_DescribeDelivery0_HTTP_Handler(srv))
	r.POST("/goodsStackHardwareRole", _GoodsStack_CreateHardwareRoleUnion0_HTTP_Handler(srv))
	r.GET("/goodsStackHardwareRole", _GoodsStack_DescribesHardwareRole0_HTTP_Handler(srv))
	r.DELETE("/goodsStackDomain/{id}", _GoodsStack_DeleteDomain0_HTTP_Handler(srv))
	r.PUT("/goodsStackDomain/{id}", _GoodsStack_ModifyDomain0_HTTP_Handler(srv))
	r.GET("/goodsStackDomain/{id}", _GoodsStack_DescribeDomain0_HTTP_Handler(srv))
	r.DELETE("/goodsStackFlavor/{id}", _GoodsStack_DeleteFlavor0_HTTP_Handler(srv))
	r.PUT("/goodsStackFlavor/{id}", _GoodsStack_ModifyFlavor0_HTTP_Handler(srv))
	r.GET("/goodsStackFlavor/{id}", _GoodsStack_DescribeFlavor0_HTTP_Handler(srv))
	r.POST("/goodsStackFilecenter", _GoodsStack_CreateFilecenter0_HTTP_Handler(srv))
	r.GET("/goodsStackFilecenter", _GoodsStack_DescribesFilecenter0_HTTP_Handler(srv))
	r.POST("/goodsStackMiddleware", _GoodsStack_CreateMiddleware0_HTTP_Handler(srv))
	r.GET("/goodsStackMiddleware", _GoodsStack_DescribesMiddleware0_HTTP_Handler(srv))
	r.GET("/goodsStackChain/{id}", _GoodsStack_DescribeChain0_HTTP_Handler(srv))
	r.DELETE("/goodsStackChain/{id}", _GoodsStack_DeleteChain0_HTTP_Handler(srv))
	r.POST("/goodsStackDelivery", _GoodsStack_CreateDelivery0_HTTP_Handler(srv))
	r.GET("/goodsStackDelivery", _GoodsStack_DescribesDelivery0_HTTP_Handler(srv))
	r.DELETE("/goodsStackIac/{id}", _GoodsStack_DeleteIac0_HTTP_Handler(srv))
	r.PUT("/goodsStackIac/{id}", _GoodsStack_ModifyIac0_HTTP_Handler(srv))
	r.GET("/goodsStackIac/{id}", _GoodsStack_DescribeIac0_HTTP_Handler(srv))
	r.POST("/goodsStackDomain", _GoodsStack_CreateDomain0_HTTP_Handler(srv))
	r.GET("/goodsStackDomain", _GoodsStack_DescribesDomain0_HTTP_Handler(srv))
	r.POST("/goodsStackFlavor", _GoodsStack_CreateFlavor0_HTTP_Handler(srv))
	r.GET("/goodsStackFlavor", _GoodsStack_DescribesFlavor0_HTTP_Handler(srv))
	r.POST("/goodsStackChain", _GoodsStack_CreateChain0_HTTP_Handler(srv))
	r.GET("/goodsStackChain", _GoodsStack_DescribesChain0_HTTP_Handler(srv))
	r.POST("/goodsStackIac", _GoodsStack_CreateIac0_HTTP_Handler(srv))
	r.GET("/goodsStackIac", _GoodsStack_DescribesIac0_HTTP_Handler(srv))
	r.GET("/goodsStack", _GoodsStack_Describes0_HTTP_Handler(srv))
}

func _GoodsStack_CreateHardwareRoleLabel0_HTTP_Handler(srv GoodsStackHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in CreateHardwareRoleLabelReq
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationGoodsStackCreateHardwareRoleLabel)
		h := ctx.Middleware(func(ctx context.Context, req any) (any, error) {
			return srv.CreateHardwareRoleLabel(ctx, context1.ParseContext(ctx), req.(*CreateHardwareRoleLabelReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*empty.Empty)
		return ctx.Result(200, reply)
	}
}

func _GoodsStack_DeleteHardwareRoleLabel0_HTTP_Handler(srv GoodsStackHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in DeleteHardwareRoleLabelReq
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationGoodsStackDeleteHardwareRoleLabel)
		h := ctx.Middleware(func(ctx context.Context, req any) (any, error) {
			return srv.DeleteHardwareRoleLabel(ctx, context1.ParseContext(ctx), req.(*DeleteHardwareRoleLabelReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*empty.Empty)
		return ctx.Result(200, reply)
	}
}

func _GoodsStack_CreateHardwareRole0_HTTP_Handler(srv GoodsStackHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in CreateHardwareRoleReq
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationGoodsStackCreateHardwareRole)
		h := ctx.Middleware(func(ctx context.Context, req any) (any, error) {
			return srv.CreateHardwareRole(ctx, context1.ParseContext(ctx), req.(*CreateHardwareRoleReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*empty.Empty)
		return ctx.Result(200, reply)
	}
}

func _GoodsStack_DeleteHardwareRole0_HTTP_Handler(srv GoodsStackHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in DeleteHardwareRoleReq
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationGoodsStackDeleteHardwareRole)
		h := ctx.Middleware(func(ctx context.Context, req any) (any, error) {
			return srv.DeleteHardwareRole(ctx, context1.ParseContext(ctx), req.(*DeleteHardwareRoleReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*empty.Empty)
		return ctx.Result(200, reply)
	}
}

func _GoodsStack_ModifySyncGoodsAction0_HTTP_Handler(srv GoodsStackHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in SyncGoodsActionReq
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationGoodsStackModifySyncGoodsAction)
		h := ctx.Middleware(func(ctx context.Context, req any) (any, error) {
			return srv.ModifySyncGoodsAction(ctx, context1.ParseContext(ctx), req.(*SyncGoodsActionReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*SyncGoodsActionReply)
		return ctx.Result(200, reply)
	}
}

func _GoodsStack_DescribeHardwareRoleLabel0_HTTP_Handler(srv GoodsStackHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in DescribeHardwareRoleLabelReq
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationGoodsStackDescribeHardwareRoleLabel)
		h := ctx.Middleware(func(ctx context.Context, req any) (any, error) {
			return srv.DescribeHardwareRoleLabel(ctx, context1.ParseContext(ctx), req.(*DescribeHardwareRoleLabelReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*DescribeHardwareRoleLabelReply)
		return ctx.Result(200, reply)
	}
}

func _GoodsStack_Create0_HTTP_Handler(srv GoodsStackHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in CreateReq
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationGoodsStackCreate)
		h := ctx.Middleware(func(ctx context.Context, req any) (any, error) {
			return srv.Create(ctx, context1.ParseContext(ctx), req.(*CreateReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*DescribeReply)
		return ctx.Result(200, reply)
	}
}

func _GoodsStack_Describe0_HTTP_Handler(srv GoodsStackHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in DescribeReq
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationGoodsStackDescribe)
		h := ctx.Middleware(func(ctx context.Context, req any) (any, error) {
			return srv.Describe(ctx, context1.ParseContext(ctx), req.(*DescribeReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*DescribeReply)
		return ctx.Result(200, reply)
	}
}

func _GoodsStack_DescribeHardwareRole0_HTTP_Handler(srv GoodsStackHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in DescribeHardwareRoleReq
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationGoodsStackDescribeHardwareRole)
		h := ctx.Middleware(func(ctx context.Context, req any) (any, error) {
			return srv.DescribeHardwareRole(ctx, context1.ParseContext(ctx), req.(*DescribeHardwareRoleReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*DescribeHardwareRoleReply)
		return ctx.Result(200, reply)
	}
}

func _GoodsStack_DescribesHardwareRoleLabel0_HTTP_Handler(srv GoodsStackHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in DescribesHardwareRoleLabelReq
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationGoodsStackDescribesHardwareRoleLabel)
		h := ctx.Middleware(func(ctx context.Context, req any) (any, error) {
			return srv.DescribesHardwareRoleLabel(ctx, context1.ParseContext(ctx), req.(*DescribesHardwareRoleLabelReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*DescribesHardwareRoleLabelReply)
		return ctx.Result(200, reply)
	}
}

func _GoodsStack_DeleteFilecenter0_HTTP_Handler(srv GoodsStackHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in DeleteFilecenterReq
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationGoodsStackDeleteFilecenter)
		h := ctx.Middleware(func(ctx context.Context, req any) (any, error) {
			return srv.DeleteFilecenter(ctx, context1.ParseContext(ctx), req.(*DeleteFilecenterReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*empty.Empty)
		return ctx.Result(200, reply)
	}
}

func _GoodsStack_DescribeFilecenter0_HTTP_Handler(srv GoodsStackHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in DescribeFilecenterReq
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationGoodsStackDescribeFilecenter)
		h := ctx.Middleware(func(ctx context.Context, req any) (any, error) {
			return srv.DescribeFilecenter(ctx, context1.ParseContext(ctx), req.(*DescribeFilecenterReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*DescribeFilecenterReply)
		return ctx.Result(200, reply)
	}
}

func _GoodsStack_DeleteMiddleware0_HTTP_Handler(srv GoodsStackHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in DeleteMiddlewareReq
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationGoodsStackDeleteMiddleware)
		h := ctx.Middleware(func(ctx context.Context, req any) (any, error) {
			return srv.DeleteMiddleware(ctx, context1.ParseContext(ctx), req.(*DeleteMiddlewareReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*empty.Empty)
		return ctx.Result(200, reply)
	}
}

func _GoodsStack_ModifyMiddleware0_HTTP_Handler(srv GoodsStackHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ModifyMiddlewareReq
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationGoodsStackModifyMiddleware)
		h := ctx.Middleware(func(ctx context.Context, req any) (any, error) {
			return srv.ModifyMiddleware(ctx, context1.ParseContext(ctx), req.(*ModifyMiddlewareReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*empty.Empty)
		return ctx.Result(200, reply)
	}
}

func _GoodsStack_DescribeMiddleware0_HTTP_Handler(srv GoodsStackHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in DescribeMiddlewareReq
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationGoodsStackDescribeMiddleware)
		h := ctx.Middleware(func(ctx context.Context, req any) (any, error) {
			return srv.DescribeMiddleware(ctx, context1.ParseContext(ctx), req.(*DescribeMiddlewareReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*DescribeMiddlewareReply)
		return ctx.Result(200, reply)
	}
}

func _GoodsStack_DeleteDelivery0_HTTP_Handler(srv GoodsStackHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in DeleteDeliveryReq
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationGoodsStackDeleteDelivery)
		h := ctx.Middleware(func(ctx context.Context, req any) (any, error) {
			return srv.DeleteDelivery(ctx, context1.ParseContext(ctx), req.(*DeleteDeliveryReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*empty.Empty)
		return ctx.Result(200, reply)
	}
}

func _GoodsStack_ModifyDelivery0_HTTP_Handler(srv GoodsStackHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ModifyDeliveryReq
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationGoodsStackModifyDelivery)
		h := ctx.Middleware(func(ctx context.Context, req any) (any, error) {
			return srv.ModifyDelivery(ctx, context1.ParseContext(ctx), req.(*ModifyDeliveryReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*empty.Empty)
		return ctx.Result(200, reply)
	}
}

func _GoodsStack_DescribeDelivery0_HTTP_Handler(srv GoodsStackHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in DescribeDeliveryReq
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationGoodsStackDescribeDelivery)
		h := ctx.Middleware(func(ctx context.Context, req any) (any, error) {
			return srv.DescribeDelivery(ctx, context1.ParseContext(ctx), req.(*DescribeDeliveryReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*DescribeDeliveryReply)
		return ctx.Result(200, reply)
	}
}

func _GoodsStack_CreateHardwareRoleUnion0_HTTP_Handler(srv GoodsStackHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in CreateHardwareRoleUnionReq
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationGoodsStackCreateHardwareRoleUnion)
		h := ctx.Middleware(func(ctx context.Context, req any) (any, error) {
			return srv.CreateHardwareRoleUnion(ctx, context1.ParseContext(ctx), req.(*CreateHardwareRoleUnionReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*empty.Empty)
		return ctx.Result(200, reply)
	}
}

func _GoodsStack_DescribesHardwareRole0_HTTP_Handler(srv GoodsStackHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in DescribesHardwareRoleReq
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationGoodsStackDescribesHardwareRole)
		h := ctx.Middleware(func(ctx context.Context, req any) (any, error) {
			return srv.DescribesHardwareRole(ctx, context1.ParseContext(ctx), req.(*DescribesHardwareRoleReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*DescribesHardwareRoleReply)
		return ctx.Result(200, reply)
	}
}

func _GoodsStack_DeleteDomain0_HTTP_Handler(srv GoodsStackHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in DeleteDomainReq
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationGoodsStackDeleteDomain)
		h := ctx.Middleware(func(ctx context.Context, req any) (any, error) {
			return srv.DeleteDomain(ctx, context1.ParseContext(ctx), req.(*DeleteDomainReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*empty.Empty)
		return ctx.Result(200, reply)
	}
}

func _GoodsStack_ModifyDomain0_HTTP_Handler(srv GoodsStackHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ModifyDomainReq
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationGoodsStackModifyDomain)
		h := ctx.Middleware(func(ctx context.Context, req any) (any, error) {
			return srv.ModifyDomain(ctx, context1.ParseContext(ctx), req.(*ModifyDomainReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*empty.Empty)
		return ctx.Result(200, reply)
	}
}

func _GoodsStack_DescribeDomain0_HTTP_Handler(srv GoodsStackHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in DescribeDomainReq
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationGoodsStackDescribeDomain)
		h := ctx.Middleware(func(ctx context.Context, req any) (any, error) {
			return srv.DescribeDomain(ctx, context1.ParseContext(ctx), req.(*DescribeDomainReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*DescribeDomainReply)
		return ctx.Result(200, reply)
	}
}

func _GoodsStack_DeleteFlavor0_HTTP_Handler(srv GoodsStackHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in DeleteFlavorReq
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationGoodsStackDeleteFlavor)
		h := ctx.Middleware(func(ctx context.Context, req any) (any, error) {
			return srv.DeleteFlavor(ctx, context1.ParseContext(ctx), req.(*DeleteFlavorReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*empty.Empty)
		return ctx.Result(200, reply)
	}
}

func _GoodsStack_ModifyFlavor0_HTTP_Handler(srv GoodsStackHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ModifyFlavorReq
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationGoodsStackModifyFlavor)
		h := ctx.Middleware(func(ctx context.Context, req any) (any, error) {
			return srv.ModifyFlavor(ctx, context1.ParseContext(ctx), req.(*ModifyFlavorReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*empty.Empty)
		return ctx.Result(200, reply)
	}
}

func _GoodsStack_DescribeFlavor0_HTTP_Handler(srv GoodsStackHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in DescribeFlavorReq
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationGoodsStackDescribeFlavor)
		h := ctx.Middleware(func(ctx context.Context, req any) (any, error) {
			return srv.DescribeFlavor(ctx, context1.ParseContext(ctx), req.(*DescribeFlavorReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*DescribeFlavorReply)
		return ctx.Result(200, reply)
	}
}

func _GoodsStack_CreateFilecenter0_HTTP_Handler(srv GoodsStackHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in CreateFilecenterReq
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationGoodsStackCreateFilecenter)
		h := ctx.Middleware(func(ctx context.Context, req any) (any, error) {
			return srv.CreateFilecenter(ctx, context1.ParseContext(ctx), req.(*CreateFilecenterReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*DescribeFilecenterReply)
		return ctx.Result(200, reply)
	}
}

func _GoodsStack_DescribesFilecenter0_HTTP_Handler(srv GoodsStackHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in DescribesFilecenterReq
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationGoodsStackDescribesFilecenter)
		h := ctx.Middleware(func(ctx context.Context, req any) (any, error) {
			return srv.DescribesFilecenter(ctx, context1.ParseContext(ctx), req.(*DescribesFilecenterReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*DescribesFilecenterReply)
		return ctx.Result(200, reply)
	}
}

func _GoodsStack_CreateMiddleware0_HTTP_Handler(srv GoodsStackHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in CreateMiddlewareReq
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationGoodsStackCreateMiddleware)
		h := ctx.Middleware(func(ctx context.Context, req any) (any, error) {
			return srv.CreateMiddleware(ctx, context1.ParseContext(ctx), req.(*CreateMiddlewareReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*DescribeMiddlewareReply)
		return ctx.Result(200, reply)
	}
}

func _GoodsStack_DescribesMiddleware0_HTTP_Handler(srv GoodsStackHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in DescribesMiddlewareReq
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationGoodsStackDescribesMiddleware)
		h := ctx.Middleware(func(ctx context.Context, req any) (any, error) {
			return srv.DescribesMiddleware(ctx, context1.ParseContext(ctx), req.(*DescribesMiddlewareReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*DescribesMiddlewareReply)
		return ctx.Result(200, reply)
	}
}

func _GoodsStack_DescribeChain0_HTTP_Handler(srv GoodsStackHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in DescribeChainReq
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationGoodsStackDescribeChain)
		h := ctx.Middleware(func(ctx context.Context, req any) (any, error) {
			return srv.DescribeChain(ctx, context1.ParseContext(ctx), req.(*DescribeChainReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*DescribeChainReply)
		return ctx.Result(200, reply)
	}
}

func _GoodsStack_DeleteChain0_HTTP_Handler(srv GoodsStackHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in DeleteChainReq
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationGoodsStackDeleteChain)
		h := ctx.Middleware(func(ctx context.Context, req any) (any, error) {
			return srv.DeleteChain(ctx, context1.ParseContext(ctx), req.(*DeleteChainReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*empty.Empty)
		return ctx.Result(200, reply)
	}
}

func _GoodsStack_CreateDelivery0_HTTP_Handler(srv GoodsStackHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in CreateDeliveryReq
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationGoodsStackCreateDelivery)
		h := ctx.Middleware(func(ctx context.Context, req any) (any, error) {
			return srv.CreateDelivery(ctx, context1.ParseContext(ctx), req.(*CreateDeliveryReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*DescribeDeliveryReply)
		return ctx.Result(200, reply)
	}
}

func _GoodsStack_DescribesDelivery0_HTTP_Handler(srv GoodsStackHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in DescribesDeliveryReq
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationGoodsStackDescribesDelivery)
		h := ctx.Middleware(func(ctx context.Context, req any) (any, error) {
			return srv.DescribesDelivery(ctx, context1.ParseContext(ctx), req.(*DescribesDeliveryReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*DescribesDeliveryReply)
		return ctx.Result(200, reply)
	}
}

func _GoodsStack_DeleteIac0_HTTP_Handler(srv GoodsStackHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in DeleteIacReq
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationGoodsStackDeleteIac)
		h := ctx.Middleware(func(ctx context.Context, req any) (any, error) {
			return srv.DeleteIac(ctx, context1.ParseContext(ctx), req.(*DeleteIacReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*empty.Empty)
		return ctx.Result(200, reply)
	}
}

func _GoodsStack_ModifyIac0_HTTP_Handler(srv GoodsStackHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ModifyIacReq
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationGoodsStackModifyIac)
		h := ctx.Middleware(func(ctx context.Context, req any) (any, error) {
			return srv.ModifyIac(ctx, context1.ParseContext(ctx), req.(*ModifyIacReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*empty.Empty)
		return ctx.Result(200, reply)
	}
}

func _GoodsStack_DescribeIac0_HTTP_Handler(srv GoodsStackHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in DescribeIacReq
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationGoodsStackDescribeIac)
		h := ctx.Middleware(func(ctx context.Context, req any) (any, error) {
			return srv.DescribeIac(ctx, context1.ParseContext(ctx), req.(*DescribeIacReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*DescribeIacReply)
		return ctx.Result(200, reply)
	}
}

func _GoodsStack_CreateDomain0_HTTP_Handler(srv GoodsStackHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in CreateDomainReq
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationGoodsStackCreateDomain)
		h := ctx.Middleware(func(ctx context.Context, req any) (any, error) {
			return srv.CreateDomain(ctx, context1.ParseContext(ctx), req.(*CreateDomainReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*DescribeDomainReply)
		return ctx.Result(200, reply)
	}
}

func _GoodsStack_DescribesDomain0_HTTP_Handler(srv GoodsStackHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in DescribesDomainReq
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationGoodsStackDescribesDomain)
		h := ctx.Middleware(func(ctx context.Context, req any) (any, error) {
			return srv.DescribesDomain(ctx, context1.ParseContext(ctx), req.(*DescribesDomainReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*DescribesDomainReply)
		return ctx.Result(200, reply)
	}
}

func _GoodsStack_CreateFlavor0_HTTP_Handler(srv GoodsStackHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in CreateFlavorReq
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationGoodsStackCreateFlavor)
		h := ctx.Middleware(func(ctx context.Context, req any) (any, error) {
			return srv.CreateFlavor(ctx, context1.ParseContext(ctx), req.(*CreateFlavorReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*DescribeFlavorReply)
		return ctx.Result(200, reply)
	}
}

func _GoodsStack_DescribesFlavor0_HTTP_Handler(srv GoodsStackHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in DescribesFlavorReq
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationGoodsStackDescribesFlavor)
		h := ctx.Middleware(func(ctx context.Context, req any) (any, error) {
			return srv.DescribesFlavor(ctx, context1.ParseContext(ctx), req.(*DescribesFlavorReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*DescribesFlavorReply)
		return ctx.Result(200, reply)
	}
}

func _GoodsStack_CreateChain0_HTTP_Handler(srv GoodsStackHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in CreateChainReq
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationGoodsStackCreateChain)
		h := ctx.Middleware(func(ctx context.Context, req any) (any, error) {
			return srv.CreateChain(ctx, context1.ParseContext(ctx), req.(*CreateChainReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*DescribeChainReply)
		return ctx.Result(200, reply)
	}
}

func _GoodsStack_DescribesChain0_HTTP_Handler(srv GoodsStackHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in DescribesChainReq
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationGoodsStackDescribesChain)
		h := ctx.Middleware(func(ctx context.Context, req any) (any, error) {
			return srv.DescribesChain(ctx, context1.ParseContext(ctx), req.(*DescribesChainReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*DescribesChainReply)
		return ctx.Result(200, reply)
	}
}

func _GoodsStack_CreateIac0_HTTP_Handler(srv GoodsStackHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in CreateIacReq
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationGoodsStackCreateIac)
		h := ctx.Middleware(func(ctx context.Context, req any) (any, error) {
			return srv.CreateIac(ctx, context1.ParseContext(ctx), req.(*CreateIacReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*DescribeIacReply)
		return ctx.Result(200, reply)
	}
}

func _GoodsStack_DescribesIac0_HTTP_Handler(srv GoodsStackHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in DescribesIacReq
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationGoodsStackDescribesIac)
		h := ctx.Middleware(func(ctx context.Context, req any) (any, error) {
			return srv.DescribesIac(ctx, context1.ParseContext(ctx), req.(*DescribesIacReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*DescribesIacReply)
		return ctx.Result(200, reply)
	}
}

func _GoodsStack_Describes0_HTTP_Handler(srv GoodsStackHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in DescribesReq
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationGoodsStackDescribes)
		h := ctx.Middleware(func(ctx context.Context, req any) (any, error) {
			return srv.Describes(ctx, context1.ParseContext(ctx), req.(*DescribesReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*DescribesReply)
		return ctx.Result(200, reply)
	}
}

type GoodsStackHTTPClient interface {
	Create(ctx context.Context, req *CreateReq, opts ...http.CallOption) (rsp *DescribeReply, err error)
	CreateChain(ctx context.Context, req *CreateChainReq, opts ...http.CallOption) (rsp *DescribeChainReply, err error)
	CreateDelivery(ctx context.Context, req *CreateDeliveryReq, opts ...http.CallOption) (rsp *DescribeDeliveryReply, err error)
	CreateDomain(ctx context.Context, req *CreateDomainReq, opts ...http.CallOption) (rsp *DescribeDomainReply, err error)
	CreateFilecenter(ctx context.Context, req *CreateFilecenterReq, opts ...http.CallOption) (rsp *DescribeFilecenterReply, err error)
	CreateFlavor(ctx context.Context, req *CreateFlavorReq, opts ...http.CallOption) (rsp *DescribeFlavorReply, err error)
	CreateHardwareRole(ctx context.Context, req *CreateHardwareRoleReq, opts ...http.CallOption) (rsp *empty.Empty, err error)
	CreateHardwareRoleLabel(ctx context.Context, req *CreateHardwareRoleLabelReq, opts ...http.CallOption) (rsp *empty.Empty, err error)
	CreateHardwareRoleUnion(ctx context.Context, req *CreateHardwareRoleUnionReq, opts ...http.CallOption) (rsp *empty.Empty, err error)
	CreateIac(ctx context.Context, req *CreateIacReq, opts ...http.CallOption) (rsp *DescribeIacReply, err error)
	CreateMiddleware(ctx context.Context, req *CreateMiddlewareReq, opts ...http.CallOption) (rsp *DescribeMiddlewareReply, err error)
	DeleteChain(ctx context.Context, req *DeleteChainReq, opts ...http.CallOption) (rsp *empty.Empty, err error)
	DeleteDelivery(ctx context.Context, req *DeleteDeliveryReq, opts ...http.CallOption) (rsp *empty.Empty, err error)
	DeleteDomain(ctx context.Context, req *DeleteDomainReq, opts ...http.CallOption) (rsp *empty.Empty, err error)
	DeleteFilecenter(ctx context.Context, req *DeleteFilecenterReq, opts ...http.CallOption) (rsp *empty.Empty, err error)
	DeleteFlavor(ctx context.Context, req *DeleteFlavorReq, opts ...http.CallOption) (rsp *empty.Empty, err error)
	DeleteHardwareRole(ctx context.Context, req *DeleteHardwareRoleReq, opts ...http.CallOption) (rsp *empty.Empty, err error)
	DeleteHardwareRoleLabel(ctx context.Context, req *DeleteHardwareRoleLabelReq, opts ...http.CallOption) (rsp *empty.Empty, err error)
	DeleteIac(ctx context.Context, req *DeleteIacReq, opts ...http.CallOption) (rsp *empty.Empty, err error)
	DeleteMiddleware(ctx context.Context, req *DeleteMiddlewareReq, opts ...http.CallOption) (rsp *empty.Empty, err error)
	Describe(ctx context.Context, req *DescribeReq, opts ...http.CallOption) (rsp *DescribeReply, err error)
	DescribeChain(ctx context.Context, req *DescribeChainReq, opts ...http.CallOption) (rsp *DescribeChainReply, err error)
	DescribeDelivery(ctx context.Context, req *DescribeDeliveryReq, opts ...http.CallOption) (rsp *DescribeDeliveryReply, err error)
	DescribeDomain(ctx context.Context, req *DescribeDomainReq, opts ...http.CallOption) (rsp *DescribeDomainReply, err error)
	DescribeFilecenter(ctx context.Context, req *DescribeFilecenterReq, opts ...http.CallOption) (rsp *DescribeFilecenterReply, err error)
	DescribeFlavor(ctx context.Context, req *DescribeFlavorReq, opts ...http.CallOption) (rsp *DescribeFlavorReply, err error)
	DescribeHardwareRole(ctx context.Context, req *DescribeHardwareRoleReq, opts ...http.CallOption) (rsp *DescribeHardwareRoleReply, err error)
	DescribeHardwareRoleLabel(ctx context.Context, req *DescribeHardwareRoleLabelReq, opts ...http.CallOption) (rsp *DescribeHardwareRoleLabelReply, err error)
	DescribeIac(ctx context.Context, req *DescribeIacReq, opts ...http.CallOption) (rsp *DescribeIacReply, err error)
	DescribeMiddleware(ctx context.Context, req *DescribeMiddlewareReq, opts ...http.CallOption) (rsp *DescribeMiddlewareReply, err error)
	Describes(ctx context.Context, req *DescribesReq, opts ...http.CallOption) (rsp *DescribesReply, err error)
	DescribesChain(ctx context.Context, req *DescribesChainReq, opts ...http.CallOption) (rsp *DescribesChainReply, err error)
	DescribesDelivery(ctx context.Context, req *DescribesDeliveryReq, opts ...http.CallOption) (rsp *DescribesDeliveryReply, err error)
	DescribesDomain(ctx context.Context, req *DescribesDomainReq, opts ...http.CallOption) (rsp *DescribesDomainReply, err error)
	DescribesFilecenter(ctx context.Context, req *DescribesFilecenterReq, opts ...http.CallOption) (rsp *DescribesFilecenterReply, err error)
	DescribesFlavor(ctx context.Context, req *DescribesFlavorReq, opts ...http.CallOption) (rsp *DescribesFlavorReply, err error)
	DescribesHardwareRole(ctx context.Context, req *DescribesHardwareRoleReq, opts ...http.CallOption) (rsp *DescribesHardwareRoleReply, err error)
	DescribesHardwareRoleLabel(ctx context.Context, req *DescribesHardwareRoleLabelReq, opts ...http.CallOption) (rsp *DescribesHardwareRoleLabelReply, err error)
	DescribesIac(ctx context.Context, req *DescribesIacReq, opts ...http.CallOption) (rsp *DescribesIacReply, err error)
	DescribesMiddleware(ctx context.Context, req *DescribesMiddlewareReq, opts ...http.CallOption) (rsp *DescribesMiddlewareReply, err error)
	ModifyDelivery(ctx context.Context, req *ModifyDeliveryReq, opts ...http.CallOption) (rsp *empty.Empty, err error)
	ModifyDomain(ctx context.Context, req *ModifyDomainReq, opts ...http.CallOption) (rsp *empty.Empty, err error)
	ModifyFlavor(ctx context.Context, req *ModifyFlavorReq, opts ...http.CallOption) (rsp *empty.Empty, err error)
	ModifyIac(ctx context.Context, req *ModifyIacReq, opts ...http.CallOption) (rsp *empty.Empty, err error)
	ModifyMiddleware(ctx context.Context, req *ModifyMiddlewareReq, opts ...http.CallOption) (rsp *empty.Empty, err error)
	ModifySyncGoodsAction(ctx context.Context, req *SyncGoodsActionReq, opts ...http.CallOption) (rsp *SyncGoodsActionReply, err error)
}

type GoodsStackHTTPClientImpl struct {
	cc *http.Client
}

func NewGoodsStackHTTPClient(client *http.Client) GoodsStackHTTPClient {
	return &GoodsStackHTTPClientImpl{client}
}

func (c *GoodsStackHTTPClientImpl) Create(ctx context.Context, in *CreateReq, opts ...http.CallOption) (*DescribeReply, error) {
	var out DescribeReply
	pattern := "/version/{version}/goodsStack"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationGoodsStackCreate))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *GoodsStackHTTPClientImpl) CreateChain(ctx context.Context, in *CreateChainReq, opts ...http.CallOption) (*DescribeChainReply, error) {
	var out DescribeChainReply
	pattern := "/goodsStackChain"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationGoodsStackCreateChain))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *GoodsStackHTTPClientImpl) CreateDelivery(ctx context.Context, in *CreateDeliveryReq, opts ...http.CallOption) (*DescribeDeliveryReply, error) {
	var out DescribeDeliveryReply
	pattern := "/goodsStackDelivery"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationGoodsStackCreateDelivery))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *GoodsStackHTTPClientImpl) CreateDomain(ctx context.Context, in *CreateDomainReq, opts ...http.CallOption) (*DescribeDomainReply, error) {
	var out DescribeDomainReply
	pattern := "/goodsStackDomain"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationGoodsStackCreateDomain))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *GoodsStackHTTPClientImpl) CreateFilecenter(ctx context.Context, in *CreateFilecenterReq, opts ...http.CallOption) (*DescribeFilecenterReply, error) {
	var out DescribeFilecenterReply
	pattern := "/goodsStackFilecenter"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationGoodsStackCreateFilecenter))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *GoodsStackHTTPClientImpl) CreateFlavor(ctx context.Context, in *CreateFlavorReq, opts ...http.CallOption) (*DescribeFlavorReply, error) {
	var out DescribeFlavorReply
	pattern := "/goodsStackFlavor"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationGoodsStackCreateFlavor))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *GoodsStackHTTPClientImpl) CreateHardwareRole(ctx context.Context, in *CreateHardwareRoleReq, opts ...http.CallOption) (*empty.Empty, error) {
	var out empty.Empty
	pattern := "/goodsStack/{goodsId}/hardwareRole/{hardwareRoleId}"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationGoodsStackCreateHardwareRole))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *GoodsStackHTTPClientImpl) CreateHardwareRoleLabel(ctx context.Context, in *CreateHardwareRoleLabelReq, opts ...http.CallOption) (*empty.Empty, error) {
	var out empty.Empty
	pattern := "/goodsStack/{goodsId}/hardwareRoleLabel/{hardwareRoleLabelId}"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationGoodsStackCreateHardwareRoleLabel))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *GoodsStackHTTPClientImpl) CreateHardwareRoleUnion(ctx context.Context, in *CreateHardwareRoleUnionReq, opts ...http.CallOption) (*empty.Empty, error) {
	var out empty.Empty
	pattern := "/goodsStackHardwareRole"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationGoodsStackCreateHardwareRoleUnion))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *GoodsStackHTTPClientImpl) CreateIac(ctx context.Context, in *CreateIacReq, opts ...http.CallOption) (*DescribeIacReply, error) {
	var out DescribeIacReply
	pattern := "/goodsStackIac"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationGoodsStackCreateIac))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *GoodsStackHTTPClientImpl) CreateMiddleware(ctx context.Context, in *CreateMiddlewareReq, opts ...http.CallOption) (*DescribeMiddlewareReply, error) {
	var out DescribeMiddlewareReply
	pattern := "/goodsStackMiddleware"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationGoodsStackCreateMiddleware))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *GoodsStackHTTPClientImpl) DeleteChain(ctx context.Context, in *DeleteChainReq, opts ...http.CallOption) (*empty.Empty, error) {
	var out empty.Empty
	pattern := "/goodsStackChain/{id}"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationGoodsStackDeleteChain))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "DELETE", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *GoodsStackHTTPClientImpl) DeleteDelivery(ctx context.Context, in *DeleteDeliveryReq, opts ...http.CallOption) (*empty.Empty, error) {
	var out empty.Empty
	pattern := "/goodsStackDelivery/{id}"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationGoodsStackDeleteDelivery))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "DELETE", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *GoodsStackHTTPClientImpl) DeleteDomain(ctx context.Context, in *DeleteDomainReq, opts ...http.CallOption) (*empty.Empty, error) {
	var out empty.Empty
	pattern := "/goodsStackDomain/{id}"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationGoodsStackDeleteDomain))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "DELETE", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *GoodsStackHTTPClientImpl) DeleteFilecenter(ctx context.Context, in *DeleteFilecenterReq, opts ...http.CallOption) (*empty.Empty, error) {
	var out empty.Empty
	pattern := "/goodsStackFilecenter/{id}"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationGoodsStackDeleteFilecenter))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "DELETE", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *GoodsStackHTTPClientImpl) DeleteFlavor(ctx context.Context, in *DeleteFlavorReq, opts ...http.CallOption) (*empty.Empty, error) {
	var out empty.Empty
	pattern := "/goodsStackFlavor/{id}"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationGoodsStackDeleteFlavor))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "DELETE", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *GoodsStackHTTPClientImpl) DeleteHardwareRole(ctx context.Context, in *DeleteHardwareRoleReq, opts ...http.CallOption) (*empty.Empty, error) {
	var out empty.Empty
	pattern := "/goodsStack/{goodsId}/hardwareRole/{hardwareRoleId}"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationGoodsStackDeleteHardwareRole))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "DELETE", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *GoodsStackHTTPClientImpl) DeleteHardwareRoleLabel(ctx context.Context, in *DeleteHardwareRoleLabelReq, opts ...http.CallOption) (*empty.Empty, error) {
	var out empty.Empty
	pattern := "/goodsStack/{goodsId}/hardwareRoleLabel/{hardwareRoleLabelId}"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationGoodsStackDeleteHardwareRoleLabel))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "DELETE", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *GoodsStackHTTPClientImpl) DeleteIac(ctx context.Context, in *DeleteIacReq, opts ...http.CallOption) (*empty.Empty, error) {
	var out empty.Empty
	pattern := "/goodsStackIac/{id}"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationGoodsStackDeleteIac))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "DELETE", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *GoodsStackHTTPClientImpl) DeleteMiddleware(ctx context.Context, in *DeleteMiddlewareReq, opts ...http.CallOption) (*empty.Empty, error) {
	var out empty.Empty
	pattern := "/goodsStackMiddleware/{id}"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationGoodsStackDeleteMiddleware))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "DELETE", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *GoodsStackHTTPClientImpl) Describe(ctx context.Context, in *DescribeReq, opts ...http.CallOption) (*DescribeReply, error) {
	var out DescribeReply
	pattern := "/version/{version}/goodsStack"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationGoodsStackDescribe))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *GoodsStackHTTPClientImpl) DescribeChain(ctx context.Context, in *DescribeChainReq, opts ...http.CallOption) (*DescribeChainReply, error) {
	var out DescribeChainReply
	pattern := "/goodsStackChain/{id}"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationGoodsStackDescribeChain))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *GoodsStackHTTPClientImpl) DescribeDelivery(ctx context.Context, in *DescribeDeliveryReq, opts ...http.CallOption) (*DescribeDeliveryReply, error) {
	var out DescribeDeliveryReply
	pattern := "/goodsStackDelivery/{id}"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationGoodsStackDescribeDelivery))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *GoodsStackHTTPClientImpl) DescribeDomain(ctx context.Context, in *DescribeDomainReq, opts ...http.CallOption) (*DescribeDomainReply, error) {
	var out DescribeDomainReply
	pattern := "/goodsStackDomain/{id}"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationGoodsStackDescribeDomain))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *GoodsStackHTTPClientImpl) DescribeFilecenter(ctx context.Context, in *DescribeFilecenterReq, opts ...http.CallOption) (*DescribeFilecenterReply, error) {
	var out DescribeFilecenterReply
	pattern := "/goodsStackFilecenter/{id}"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationGoodsStackDescribeFilecenter))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *GoodsStackHTTPClientImpl) DescribeFlavor(ctx context.Context, in *DescribeFlavorReq, opts ...http.CallOption) (*DescribeFlavorReply, error) {
	var out DescribeFlavorReply
	pattern := "/goodsStackFlavor/{id}"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationGoodsStackDescribeFlavor))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *GoodsStackHTTPClientImpl) DescribeHardwareRole(ctx context.Context, in *DescribeHardwareRoleReq, opts ...http.CallOption) (*DescribeHardwareRoleReply, error) {
	var out DescribeHardwareRoleReply
	pattern := "/goodsStackHardwareRole/{id}"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationGoodsStackDescribeHardwareRole))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *GoodsStackHTTPClientImpl) DescribeHardwareRoleLabel(ctx context.Context, in *DescribeHardwareRoleLabelReq, opts ...http.CallOption) (*DescribeHardwareRoleLabelReply, error) {
	var out DescribeHardwareRoleLabelReply
	pattern := "/goodsStackHardwareRoleLabel/{id}"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationGoodsStackDescribeHardwareRoleLabel))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *GoodsStackHTTPClientImpl) DescribeIac(ctx context.Context, in *DescribeIacReq, opts ...http.CallOption) (*DescribeIacReply, error) {
	var out DescribeIacReply
	pattern := "/goodsStackIac/{id}"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationGoodsStackDescribeIac))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *GoodsStackHTTPClientImpl) DescribeMiddleware(ctx context.Context, in *DescribeMiddlewareReq, opts ...http.CallOption) (*DescribeMiddlewareReply, error) {
	var out DescribeMiddlewareReply
	pattern := "/goodsStackMiddleware/{id}"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationGoodsStackDescribeMiddleware))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *GoodsStackHTTPClientImpl) Describes(ctx context.Context, in *DescribesReq, opts ...http.CallOption) (*DescribesReply, error) {
	var out DescribesReply
	pattern := "/goodsStack"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationGoodsStackDescribes))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *GoodsStackHTTPClientImpl) DescribesChain(ctx context.Context, in *DescribesChainReq, opts ...http.CallOption) (*DescribesChainReply, error) {
	var out DescribesChainReply
	pattern := "/goodsStackChain"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationGoodsStackDescribesChain))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *GoodsStackHTTPClientImpl) DescribesDelivery(ctx context.Context, in *DescribesDeliveryReq, opts ...http.CallOption) (*DescribesDeliveryReply, error) {
	var out DescribesDeliveryReply
	pattern := "/goodsStackDelivery"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationGoodsStackDescribesDelivery))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *GoodsStackHTTPClientImpl) DescribesDomain(ctx context.Context, in *DescribesDomainReq, opts ...http.CallOption) (*DescribesDomainReply, error) {
	var out DescribesDomainReply
	pattern := "/goodsStackDomain"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationGoodsStackDescribesDomain))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *GoodsStackHTTPClientImpl) DescribesFilecenter(ctx context.Context, in *DescribesFilecenterReq, opts ...http.CallOption) (*DescribesFilecenterReply, error) {
	var out DescribesFilecenterReply
	pattern := "/goodsStackFilecenter"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationGoodsStackDescribesFilecenter))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *GoodsStackHTTPClientImpl) DescribesFlavor(ctx context.Context, in *DescribesFlavorReq, opts ...http.CallOption) (*DescribesFlavorReply, error) {
	var out DescribesFlavorReply
	pattern := "/goodsStackFlavor"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationGoodsStackDescribesFlavor))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *GoodsStackHTTPClientImpl) DescribesHardwareRole(ctx context.Context, in *DescribesHardwareRoleReq, opts ...http.CallOption) (*DescribesHardwareRoleReply, error) {
	var out DescribesHardwareRoleReply
	pattern := "/goodsStackHardwareRole"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationGoodsStackDescribesHardwareRole))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *GoodsStackHTTPClientImpl) DescribesHardwareRoleLabel(ctx context.Context, in *DescribesHardwareRoleLabelReq, opts ...http.CallOption) (*DescribesHardwareRoleLabelReply, error) {
	var out DescribesHardwareRoleLabelReply
	pattern := "/goodsStackHardwareRoleLabel"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationGoodsStackDescribesHardwareRoleLabel))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *GoodsStackHTTPClientImpl) DescribesIac(ctx context.Context, in *DescribesIacReq, opts ...http.CallOption) (*DescribesIacReply, error) {
	var out DescribesIacReply
	pattern := "/goodsStackIac"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationGoodsStackDescribesIac))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *GoodsStackHTTPClientImpl) DescribesMiddleware(ctx context.Context, in *DescribesMiddlewareReq, opts ...http.CallOption) (*DescribesMiddlewareReply, error) {
	var out DescribesMiddlewareReply
	pattern := "/goodsStackMiddleware"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationGoodsStackDescribesMiddleware))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *GoodsStackHTTPClientImpl) ModifyDelivery(ctx context.Context, in *ModifyDeliveryReq, opts ...http.CallOption) (*empty.Empty, error) {
	var out empty.Empty
	pattern := "/goodsStackDelivery/{id}"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationGoodsStackModifyDelivery))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "PUT", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *GoodsStackHTTPClientImpl) ModifyDomain(ctx context.Context, in *ModifyDomainReq, opts ...http.CallOption) (*empty.Empty, error) {
	var out empty.Empty
	pattern := "/goodsStackDomain/{id}"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationGoodsStackModifyDomain))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "PUT", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *GoodsStackHTTPClientImpl) ModifyFlavor(ctx context.Context, in *ModifyFlavorReq, opts ...http.CallOption) (*empty.Empty, error) {
	var out empty.Empty
	pattern := "/goodsStackFlavor/{id}"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationGoodsStackModifyFlavor))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "PUT", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *GoodsStackHTTPClientImpl) ModifyIac(ctx context.Context, in *ModifyIacReq, opts ...http.CallOption) (*empty.Empty, error) {
	var out empty.Empty
	pattern := "/goodsStackIac/{id}"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationGoodsStackModifyIac))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "PUT", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *GoodsStackHTTPClientImpl) ModifyMiddleware(ctx context.Context, in *ModifyMiddlewareReq, opts ...http.CallOption) (*empty.Empty, error) {
	var out empty.Empty
	pattern := "/goodsStackMiddleware/{id}"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationGoodsStackModifyMiddleware))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "PUT", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *GoodsStackHTTPClientImpl) ModifySyncGoodsAction(ctx context.Context, in *SyncGoodsActionReq, opts ...http.CallOption) (*SyncGoodsActionReply, error) {
	var out SyncGoodsActionReply
	pattern := "/version/{version}/goodsStack:syncAction"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationGoodsStackModifySyncGoodsAction))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}
