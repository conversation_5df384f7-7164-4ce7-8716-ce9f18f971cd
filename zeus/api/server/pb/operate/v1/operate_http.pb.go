// Code generated by protoc-gen-go-http. DO NOT EDIT.
// versions:
// - protoc-gen-go-http v2.6.2.230413
// - protoc             v3.21.1
// source: operate.proto

package pbOperate

import (
	context1 "coding.jd.com/pcd-application/win-go/context"
	http1 "coding.jd.com/pcd-application/win-go/transport/http"
	context "context"
	http "github.com/go-kratos/kratos/v2/transport/http"
	binding "github.com/go-kratos/kratos/v2/transport/http/binding"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the kratos package it is being compiled against.
var _ = new(context.Context)
var _ = binding.EncodeURL
var _ = new(context1.Context)

const _ = http.SupportPackageIsVersion1
const _ = http1.SupportPackageIsVersion1

const OperationOperateCreateExcelContent = "/jdstack.zeus.v1.operate.Operate/CreateExcelContent"
const OperationOperateCreatePattern = "/jdstack.zeus.v1.operate.Operate/CreatePattern"
const OperationOperateDescribeExcelContent = "/jdstack.zeus.v1.operate.Operate/DescribeExcelContent"
const OperationOperateDescribeFieldEnum = "/jdstack.zeus.v1.operate.Operate/DescribeFieldEnum"

type OperateHTTPServer interface {
	CreateExcelContent(context.Context, context1.Context, *CreateExcelContentReq) (*CreateExcelContentReply, error)
	CreatePattern(context.Context, context1.Context, *CreatePatternReq) (*CreatePatternReply, error)
	DescribeExcelContent(context.Context, context1.Context, *DescribeExcelContentReq) (*DescribeExcelContentReply, error)
	DescribeFieldEnum(context.Context, context1.Context, *DescribeFieldEnumReq) (*DescribeFieldEnumReply, error)
}

func RegisterOperateHTTPServer(r *http1.Router, srv OperateHTTPServer) {
	r.POST("/excel:content", _Operate_CreateExcelContent0_HTTP_Handler(srv))
	r.GET("/excel:content", _Operate_DescribeExcelContent0_HTTP_Handler(srv))
	r.GET("/field:enum", _Operate_DescribeFieldEnum0_HTTP_Handler(srv))
	r.POST("/pattern", _Operate_CreatePattern0_HTTP_Handler(srv))
}

func _Operate_CreateExcelContent0_HTTP_Handler(srv OperateHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in CreateExcelContentReq
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationOperateCreateExcelContent)
		h := ctx.Middleware(func(ctx context.Context, req any) (any, error) {
			return srv.CreateExcelContent(ctx, context1.ParseContext(ctx), req.(*CreateExcelContentReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*CreateExcelContentReply)
		return ctx.Result(200, reply)
	}
}

func _Operate_DescribeExcelContent0_HTTP_Handler(srv OperateHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in DescribeExcelContentReq
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationOperateDescribeExcelContent)
		h := ctx.Middleware(func(ctx context.Context, req any) (any, error) {
			return srv.DescribeExcelContent(ctx, context1.ParseContext(ctx), req.(*DescribeExcelContentReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*DescribeExcelContentReply)
		return ctx.Result(200, reply)
	}
}

func _Operate_DescribeFieldEnum0_HTTP_Handler(srv OperateHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in DescribeFieldEnumReq
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationOperateDescribeFieldEnum)
		h := ctx.Middleware(func(ctx context.Context, req any) (any, error) {
			return srv.DescribeFieldEnum(ctx, context1.ParseContext(ctx), req.(*DescribeFieldEnumReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*DescribeFieldEnumReply)
		return ctx.Result(200, reply)
	}
}

func _Operate_CreatePattern0_HTTP_Handler(srv OperateHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in CreatePatternReq
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationOperateCreatePattern)
		h := ctx.Middleware(func(ctx context.Context, req any) (any, error) {
			return srv.CreatePattern(ctx, context1.ParseContext(ctx), req.(*CreatePatternReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*CreatePatternReply)
		return ctx.Result(200, reply)
	}
}

type OperateHTTPClient interface {
	CreateExcelContent(ctx context.Context, req *CreateExcelContentReq, opts ...http.CallOption) (rsp *CreateExcelContentReply, err error)
	CreatePattern(ctx context.Context, req *CreatePatternReq, opts ...http.CallOption) (rsp *CreatePatternReply, err error)
	DescribeExcelContent(ctx context.Context, req *DescribeExcelContentReq, opts ...http.CallOption) (rsp *DescribeExcelContentReply, err error)
	DescribeFieldEnum(ctx context.Context, req *DescribeFieldEnumReq, opts ...http.CallOption) (rsp *DescribeFieldEnumReply, err error)
}

type OperateHTTPClientImpl struct {
	cc *http.Client
}

func NewOperateHTTPClient(client *http.Client) OperateHTTPClient {
	return &OperateHTTPClientImpl{client}
}

func (c *OperateHTTPClientImpl) CreateExcelContent(ctx context.Context, in *CreateExcelContentReq, opts ...http.CallOption) (*CreateExcelContentReply, error) {
	var out CreateExcelContentReply
	pattern := "/excel:content"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationOperateCreateExcelContent))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *OperateHTTPClientImpl) CreatePattern(ctx context.Context, in *CreatePatternReq, opts ...http.CallOption) (*CreatePatternReply, error) {
	var out CreatePatternReply
	pattern := "/pattern"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationOperateCreatePattern))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *OperateHTTPClientImpl) DescribeExcelContent(ctx context.Context, in *DescribeExcelContentReq, opts ...http.CallOption) (*DescribeExcelContentReply, error) {
	var out DescribeExcelContentReply
	pattern := "/excel:content"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationOperateDescribeExcelContent))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *OperateHTTPClientImpl) DescribeFieldEnum(ctx context.Context, in *DescribeFieldEnumReq, opts ...http.CallOption) (*DescribeFieldEnumReply, error) {
	var out DescribeFieldEnumReply
	pattern := "/field:enum"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationOperateDescribeFieldEnum))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}
