// Code generated by Wire. DO NOT EDIT.

//go:generate go run -mod=mod github.com/google/wire/cmd/wire
//go:build !wireinject
// +build !wireinject

package main

import (
	"coding.jd.com/fabric/zeusV2/zeus/internal/server/biz/v1/baseline"
	"coding.jd.com/fabric/zeusV2/zeus/internal/server/biz/v1/goods_stack"
	"coding.jd.com/fabric/zeusV2/zeus/internal/server/biz/v1/hardware_role"
	"coding.jd.com/fabric/zeusV2/zeus/internal/server/biz/v1/jdstack_delivery_rule"
	"coding.jd.com/fabric/zeusV2/zeus/internal/server/biz/v1/jdstack_filecenter"
	"coding.jd.com/fabric/zeusV2/zeus/internal/server/biz/v1/jdstack_server_scale"
	"coding.jd.com/fabric/zeusV2/zeus/internal/server/biz/v1/metadata"
	"coding.jd.com/fabric/zeusV2/zeus/internal/server/biz/v1/operate"
	"coding.jd.com/fabric/zeusV2/zeus/internal/server/biz/v1/server_plane"
	"coding.jd.com/fabric/zeusV2/zeus/internal/server/biz/v1/version"
	"coding.jd.com/fabric/zeusV2/zeus/internal/server/conf"
	"coding.jd.com/fabric/zeusV2/zeus/internal/server/data/db_master/client"
	"coding.jd.com/fabric/zeusV2/zeus/internal/server/data/db_master/repo/baseline_cpu"
	"coding.jd.com/fabric/zeusV2/zeus/internal/server/data/db_master/repo/baseline_gpu"
	"coding.jd.com/fabric/zeusV2/zeus/internal/server/data/db_master/repo/baseline_nic"
	"coding.jd.com/fabric/zeusV2/zeus/internal/server/data/db_master/repo/baseline_role"
	"coding.jd.com/fabric/zeusV2/zeus/internal/server/data/db_master/repo/baseline_server"
	"coding.jd.com/fabric/zeusV2/zeus/internal/server/data/db_master/repo/delivery_rule"
	"coding.jd.com/fabric/zeusV2/zeus/internal/server/data/db_master/repo/goods"
	"coding.jd.com/fabric/zeusV2/zeus/internal/server/data/db_master/repo/hardware_role"
	"coding.jd.com/fabric/zeusV2/zeus/internal/server/data/db_master/repo/jdstack_release"
	"coding.jd.com/fabric/zeusV2/zeus/internal/server/data/db_master/repo/metadata_application"
	"coding.jd.com/fabric/zeusV2/zeus/internal/server/data/db_master/repo/metadata_category"
	"coding.jd.com/fabric/zeusV2/zeus/internal/server/data/db_master/repo/metadata_resource"
	"coding.jd.com/fabric/zeusV2/zeus/internal/server/data/db_master/repo/metadata_service"
	"coding.jd.com/fabric/zeusV2/zeus/internal/server/data/db_master/repo/release_config_file"
	"coding.jd.com/fabric/zeusV2/zeus/internal/server/data/db_master/repo/server_plane"
	"coding.jd.com/fabric/zeusV2/zeus/internal/server/data/db_master/repo/serverscale_hardware_role_resource_reserved"
	"coding.jd.com/fabric/zeusV2/zeus/internal/server/data/db_master/repo/unified_metadata"
	"coding.jd.com/fabric/zeusV2/zeus/internal/server/server"
	"coding.jd.com/fabric/zeusV2/zeus/internal/server/service"
	"coding.jd.com/fabric/zeusV2/zeus/internal/server/service/v1/baseline"
	"coding.jd.com/fabric/zeusV2/zeus/internal/server/service/v1/delivery_rule"
	"coding.jd.com/fabric/zeusV2/zeus/internal/server/service/v1/goods_delivery_rule"
	"coding.jd.com/fabric/zeusV2/zeus/internal/server/service/v1/goods_stack"
	"coding.jd.com/fabric/zeusV2/zeus/internal/server/service/v1/hardware_role"
	"coding.jd.com/fabric/zeusV2/zeus/internal/server/service/v1/jdstack_delivery_rule"
	"coding.jd.com/fabric/zeusV2/zeus/internal/server/service/v1/jdstack_filecenter"
	"coding.jd.com/fabric/zeusV2/zeus/internal/server/service/v1/jdstack_server_scale"
	"coding.jd.com/fabric/zeusV2/zeus/internal/server/service/v1/metadata"
	"coding.jd.com/fabric/zeusV2/zeus/internal/server/service/v1/operate"
	"coding.jd.com/fabric/zeusV2/zeus/internal/server/service/v1/server_plane"
	"coding.jd.com/fabric/zeusV2/zeus/internal/server/service/v1/unified_metadata"
	"coding.jd.com/fabric/zeusV2/zeus/internal/server/service/v1/version"
	"coding.jd.com/pcd-application/win-go/app"
	"coding.jd.com/pcd-application/win-go/cache"
	"coding.jd.com/pcd-application/win-go/cron"
	"coding.jd.com/pcd-application/win-go/distributed_mutex"
	"coding.jd.com/pcd-application/win-go/dto"
	"coding.jd.com/pcd-application/win-go/project/biz/task"
	"coding.jd.com/pcd-application/win-go/project/data/task/client"
	"coding.jd.com/pcd-application/win-go/project/data/task/repo"
	"coding.jd.com/pcd-application/win-go/release"
	"coding.jd.com/pcd-application/win-go/third_party/winconf"
	"github.com/go-kratos/kratos/v2/log"
)

import (
	_ "go.uber.org/automaxprocs"
)

// Injectors from wire.go:

func initApp(kindRelease jRelease.KindRelease, logger log.Logger, env *winconf.Env, web *winconf.Web, httpClient *winconf.HttpClient, arg map[string]*winconf.Middleware_Database, arg2 map[int32]*winconf.Task, arg3 map[string]*winconf.SDK, job *winconf.Job, other *conf.Other) (*jApp.App, func(), error) {
	idto := jDto.NewDTO(logger)
	excelOperateCase, err := bizOperate.NewExcelOperateUsecase(logger)
	if err != nil {
		return nil, nil, err
	}
	fieldEnumOperateCase, err := bizOperate.NewFieldEnumOperateUsecase(logger)
	if err != nil {
		return nil, nil, err
	}
	patternOperateCase, err := bizOperate.NewPatternOperateUsecase(logger)
	if err != nil {
		return nil, nil, err
	}
	client, cleanup, err := zeus.NewEntClient(env, arg, logger)
	if err != nil {
		return nil, nil, err
	}
	serverPlaneRepo, err := repoServerPlane.NewServerPlaneRepo(client, logger)
	if err != nil {
		cleanup()
		return nil, nil, err
	}
	serverPlaneCase, err := bizServerPlane.NewServerPlaneUsecase(logger, idto, excelOperateCase, fieldEnumOperateCase, serverPlaneRepo)
	if err != nil {
		cleanup()
		return nil, nil, err
	}
	idMutex := jDMutex.NewMemory(logger)
	adapter := jCache.NewMemoryCache()
	daoClient, cleanup2, err := task.NewEntClient(env, arg, arg2, logger)
	if err != nil {
		cleanup()
		return nil, nil, err
	}
	taskRepo := repoTask.NewTaskRepo(daoClient, logger)
	other_TaskConfig := other.TaskConfig
	int32_2 := other_TaskConfig.Concurrency
	taskCase, err := bizTask.NewTaskUsecase(logger, idMutex, adapter, taskRepo, int32_2)
	if err != nil {
		cleanup2()
		cleanup()
		return nil, nil, err
	}
	jdstackReleaseRepo, err := repoJdstackRelease.NewJdstackReleaseRepo(client, logger)
	if err != nil {
		cleanup2()
		cleanup()
		return nil, nil, err
	}
	releaseConfigFileRepo, err := repoReleaseConfigFile.NewReleaseConfigFileRepo(client, logger)
	if err != nil {
		cleanup2()
		cleanup()
		return nil, nil, err
	}
	metadataCategoryRepo, err := repoMetadataCategory.NewMetadataCategoryRepo(client, logger)
	if err != nil {
		cleanup2()
		cleanup()
		return nil, nil, err
	}
	metadataServiceRepo, err := repoMetadataService.NewMetadataServiceRepo(client, logger)
	if err != nil {
		cleanup2()
		cleanup()
		return nil, nil, err
	}
	metadataResourceRepo, err := repoMetadataResource.NewMetadataResourceRepo(client, logger)
	if err != nil {
		cleanup2()
		cleanup()
		return nil, nil, err
	}
	goodsChainRepo, err := repoGoods.NewGoodsChainRepo(client, logger)
	if err != nil {
		cleanup2()
		cleanup()
		return nil, nil, err
	}
	goodsFilecenterRepo, err := repoGoods.NewGoodsFilecenterRepo(client, logger)
	if err != nil {
		cleanup2()
		cleanup()
		return nil, nil, err
	}
	goodsDeliveryRepo, err := repoGoods.NewGoodsDeliveryRepo(client, logger)
	if err != nil {
		cleanup2()
		cleanup()
		return nil, nil, err
	}
	goodsDomainRepo, err := repoGoods.NewGoodsDomainRepo(client, logger)
	if err != nil {
		cleanup2()
		cleanup()
		return nil, nil, err
	}
	goodsFlavorRepo, err := repoGoods.NewGoodsFlavorRepo(client, logger)
	if err != nil {
		cleanup2()
		cleanup()
		return nil, nil, err
	}
	goodsIacRepo, err := repoGoods.NewGoodsIacRepo(client, logger)
	if err != nil {
		cleanup2()
		cleanup()
		return nil, nil, err
	}
	goodsMiddlewareRepo, err := repoGoods.NewGoodsMiddlewareRepo(client, logger)
	if err != nil {
		cleanup2()
		cleanup()
		return nil, nil, err
	}
	goodsPerformanceMeasurementRepo, err := repoGoods.NewGoodsPerformanceMeasurementRepo(client, logger)
	if err != nil {
		cleanup2()
		cleanup()
		return nil, nil, err
	}
	goodsQPSGrowthRepo, err := repoGoods.NewGoodsQPSGrowthRepo(client, logger)
	if err != nil {
		cleanup2()
		cleanup()
		return nil, nil, err
	}
	goodsPdRepo, err := repoGoods.NewGoodsPdRepo(client, logger)
	if err != nil {
		cleanup2()
		cleanup()
		return nil, nil, err
	}
	goodsUpgradeRepo, err := repoGoods.NewGoodsUpgradeRepo(client, logger)
	if err != nil {
		cleanup2()
		cleanup()
		return nil, nil, err
	}
	goodsMetadataRepo, err := repoGoods.NewGoodsMetadataRepo(client, logger)
	if err != nil {
		cleanup2()
		cleanup()
		return nil, nil, err
	}
	goodsConfigRepo, err := repoGoods.NewGoodsConfigRepo(client, logger)
	if err != nil {
		cleanup2()
		cleanup()
		return nil, nil, err
	}
	goodsRepo, err := repoGoods.NewGoodsRepo(client, logger, goodsChainRepo, goodsFilecenterRepo, goodsDeliveryRepo, goodsDomainRepo, goodsFlavorRepo, goodsIacRepo, goodsMiddlewareRepo, goodsPerformanceMeasurementRepo, goodsQPSGrowthRepo, goodsPdRepo, goodsUpgradeRepo, goodsMetadataRepo, goodsConfigRepo)
	if err != nil {
		cleanup2()
		cleanup()
		return nil, nil, err
	}
	unifiedMetadataRepo, err := repoUnifiedMetadata.NewUnifiedMetadataRepo(client, logger)
	if err != nil {
		cleanup2()
		cleanup()
		return nil, nil, err
	}
	versionCase, err := bizVersion.NewVersionUsecase(kindRelease, logger, client, idto, other, excelOperateCase, fieldEnumOperateCase, taskCase, jdstackReleaseRepo, releaseConfigFileRepo, metadataCategoryRepo, metadataServiceRepo, metadataResourceRepo, goodsRepo, goodsChainRepo, unifiedMetadataRepo)
	if err != nil {
		cleanup2()
		cleanup()
		return nil, nil, err
	}
	hardwareRoleLabelRepo, err := repoHardwareRole.NewHardwareRoleLabelRepo(client, logger)
	if err != nil {
		cleanup2()
		cleanup()
		return nil, nil, err
	}
	hardwareRoleRepo, err := repoHardwareRole.NewHardwareRoleRepo(client, logger, hardwareRoleLabelRepo)
	if err != nil {
		cleanup2()
		cleanup()
		return nil, nil, err
	}
	hardwareRoleCase, err := bizHardwareRole.NewHardwareRoleUsecase(logger, idto, excelOperateCase, fieldEnumOperateCase, serverPlaneCase, versionCase, hardwareRoleRepo, hardwareRoleLabelRepo)
	if err != nil {
		cleanup2()
		cleanup()
		return nil, nil, err
	}
	baselineCpuRepo, err := repoBaselineCpu.NewBaselineCpuRepo(client, logger)
	if err != nil {
		cleanup2()
		cleanup()
		return nil, nil, err
	}
	baselineGpuRepo, err := repoBaselineGpu.NewBaselineGpuRepo(client, logger)
	if err != nil {
		cleanup2()
		cleanup()
		return nil, nil, err
	}
	baselineNicRepo, err := repoBaselineNic.NewBaselineNicRepo(client, logger)
	if err != nil {
		cleanup2()
		cleanup()
		return nil, nil, err
	}
	baselineRoleSizeRepo, err := repoBaselineRole.NewBaselineRoleSizeRepo(client, logger)
	if err != nil {
		cleanup2()
		cleanup()
		return nil, nil, err
	}
	baselineRoleDiskRepo, err := repoBaselineRole.NewBaselineRoleDiskRepo(client, logger)
	if err != nil {
		cleanup2()
		cleanup()
		return nil, nil, err
	}
	baselineRoleNicRepo, err := repoBaselineRole.NewBaselineRoleNicRepo(client, logger)
	if err != nil {
		cleanup2()
		cleanup()
		return nil, nil, err
	}
	baselineRoleRepo, err := repoBaselineRole.NewBaselineRoleRepo(client, logger, baselineRoleSizeRepo, baselineRoleDiskRepo, baselineRoleNicRepo)
	if err != nil {
		cleanup2()
		cleanup()
		return nil, nil, err
	}
	baselineServerCpuRepo, err := repoBaselineServer.NewBaselineServerCpuRepo(client, logger)
	if err != nil {
		cleanup2()
		cleanup()
		return nil, nil, err
	}
	baselineServerDiskRepo, err := repoBaselineServer.NewBaselineServerDiskRepo(client, logger)
	if err != nil {
		cleanup2()
		cleanup()
		return nil, nil, err
	}
	baselineServerMemoryRepo, err := repoBaselineServer.NewBaselineServerMemoryRepo(client, logger)
	if err != nil {
		cleanup2()
		cleanup()
		return nil, nil, err
	}
	baselineServerNicRepo, err := repoBaselineServer.NewBaselineServerNicRepo(client, logger)
	if err != nil {
		cleanup2()
		cleanup()
		return nil, nil, err
	}
	baselineServerGpuRepo, err := repoBaselineServer.NewBaselineServerGpuRepo(client, logger)
	if err != nil {
		cleanup2()
		cleanup()
		return nil, nil, err
	}
	baselineServerServerPlaneRepo, err := repoBaselineServer.NewBaselineServerServerPlaneRepo(client, logger)
	if err != nil {
		cleanup2()
		cleanup()
		return nil, nil, err
	}
	baselineServerAdaptationRepo, err := repoBaselineServer.NewBaselineServerAdaptationRepo(client, logger, baselineServerCpuRepo, baselineServerDiskRepo, baselineServerMemoryRepo, baselineServerNicRepo, baselineServerGpuRepo, baselineServerServerPlaneRepo)
	if err != nil {
		cleanup2()
		cleanup()
		return nil, nil, err
	}
	baselineServerRepo, err := repoBaselineServer.NewBaselineServerRepo(client, logger, baselineServerAdaptationRepo, baselineServerCpuRepo, baselineServerDiskRepo, baselineServerMemoryRepo, baselineServerNicRepo, baselineServerGpuRepo, baselineServerServerPlaneRepo)
	if err != nil {
		cleanup2()
		cleanup()
		return nil, nil, err
	}
	baselineCase, err := bizBaseline.NewBaselineUsecase(logger, idto, excelOperateCase, fieldEnumOperateCase, patternOperateCase, hardwareRoleCase, serverPlaneCase, versionCase, baselineCpuRepo, baselineGpuRepo, baselineNicRepo, baselineRoleRepo, baselineRoleSizeRepo, baselineRoleDiskRepo, baselineRoleNicRepo, baselineServerRepo, baselineServerAdaptationRepo, baselineServerCpuRepo, baselineServerDiskRepo, baselineServerGpuRepo, baselineServerMemoryRepo, baselineServerNicRepo, baselineServerServerPlaneRepo, serverPlaneRepo, jdstackReleaseRepo)
	if err != nil {
		cleanup2()
		cleanup()
		return nil, nil, err
	}
	baselineHTTPServer := servBaseline.NewBaselineServer(logger, idto, baselineCase, baselineCpuRepo, baselineGpuRepo, baselineNicRepo, baselineRoleRepo, baselineRoleSizeRepo, baselineRoleNicRepo, baselineRoleDiskRepo, baselineServerRepo, baselineServerAdaptationRepo, baselineServerCpuRepo, baselineServerDiskRepo, baselineServerGpuRepo, baselineServerMemoryRepo, baselineServerNicRepo, baselineServerServerPlaneRepo, hardwareRoleRepo, serverPlaneRepo, jdstackReleaseRepo)
	operateHTTPServer := servExcelOperate.NewOperateServer(logger, excelOperateCase, fieldEnumOperateCase, patternOperateCase)
	metadataApplicationRepo, err := repoMetadataApplication.NewMetadataApplicationRepo(client, logger)
	if err != nil {
		cleanup2()
		cleanup()
		return nil, nil, err
	}
	metadataCase, err := bizMetadata.NewMetadataUsecase(logger, idto, excelOperateCase, fieldEnumOperateCase, metadataCategoryRepo, metadataServiceRepo, metadataResourceRepo, metadataApplicationRepo)
	if err != nil {
		cleanup2()
		cleanup()
		return nil, nil, err
	}
	goodsDeliveryRuleGroupRepo, err := repoGoods.NewGoodsDeliveryRuleGroupRepo(client, logger)
	if err != nil {
		cleanup2()
		cleanup()
		return nil, nil, err
	}
	goodsDeliveryRuleRepo, err := repoGoods.NewGoodsDeliveryRuleRepo(client, logger)
	if err != nil {
		cleanup2()
		cleanup()
		return nil, nil, err
	}
	goodsStackCase, err := bizGoodsStack.NewGoodsStackUsecase(logger, idto, excelOperateCase, fieldEnumOperateCase, hardwareRoleCase, metadataCase, versionCase, goodsRepo, goodsFilecenterRepo, goodsDeliveryRepo, goodsDomainRepo, goodsFlavorRepo, goodsIacRepo, goodsMiddlewareRepo, goodsChainRepo, goodsDeliveryRuleGroupRepo, goodsDeliveryRuleRepo, metadataCategoryRepo, metadataServiceRepo, metadataResourceRepo, metadataApplicationRepo, jdstackReleaseRepo, goodsMetadataRepo, unifiedMetadataRepo, goodsPdRepo, goodsConfigRepo)
	if err != nil {
		cleanup2()
		cleanup()
		return nil, nil, err
	}
	goodsStackHTTPServer := servGoodsStack.NewGoodsStackServer(logger, idto, goodsStackCase, goodsRepo, goodsFilecenterRepo, goodsDeliveryRepo, goodsDomainRepo, goodsFlavorRepo, goodsIacRepo, goodsMiddlewareRepo, goodsChainRepo, hardwareRoleRepo, hardwareRoleLabelRepo)
	hardwareRoleHTTPServer := servHardwareRole.NewHardwareRoleServer(logger, idto, hardwareRoleCase, hardwareRoleRepo)
	metadataHTTPServer := servMetadata.NewMetadataServer(logger, idto, metadataCase, metadataCategoryRepo, metadataServiceRepo, metadataResourceRepo, metadataApplicationRepo)
	serverPlaneHTTPServer := servServerPlane.NewServerPlaneServer(logger, idto, serverPlaneCase, serverPlaneRepo)
	versionHTTPServer := servVersion.NewVersionServer(logger, idto, versionCase, jdstackReleaseRepo, releaseConfigFileRepo)
	deliveryRuleRepo, err := repoDeliveryRule.NewDeliveryRuleRepo(client, logger)
	if err != nil {
		cleanup2()
		cleanup()
		return nil, nil, err
	}
	jdStackDeliveryRuleCase, err := bizJDStackDeliveryRule.NewJDStackDeliveryRule(logger, idto, hardwareRoleRepo, hardwareRoleLabelRepo, deliveryRuleRepo, goodsRepo)
	if err != nil {
		cleanup2()
		cleanup()
		return nil, nil, err
	}
	jdStackDeliveryRuleHTTPServer := servJDStackDeliveryRule.NewJDStackDeliveryRuleServer(logger, idto, jdStackDeliveryRuleCase)
	jdStackFilecenterCase, err := bizJDStackFilecenter.NewJDStackFilecenter(logger, idto, idMutex, other, goodsStackCase, goodsRepo, goodsFilecenterRepo, taskCase, taskRepo)
	if err != nil {
		cleanup2()
		cleanup()
		return nil, nil, err
	}
	other_Filecenter := other.Filecenter
	jdstackFilecenterHTTPServer := servJDStackFilecenter.NewJDStackFilecenterServer(logger, idto, goodsStackCase, goodsRepo, jdstackReleaseRepo, jdStackFilecenterCase, other_Filecenter)
	serverscaleHardwareRoleResourceReservedRepo, err := repoServerscaleHardwareRoleResourceReserved.NewServerscaleHardwareRoleResourceReservedRepo(client, logger)
	if err != nil {
		cleanup2()
		cleanup()
		return nil, nil, err
	}
	serverScaleCase, err := bizServerScale.NewServerScaleUsecase(logger, goodsRepo, goodsPerformanceMeasurementRepo, goodsQPSGrowthRepo, goodsChainRepo, baselineServerRepo, baselineServerDiskRepo, serverscaleHardwareRoleResourceReservedRepo)
	if err != nil {
		cleanup2()
		cleanup()
		return nil, nil, err
	}
	jdStackServerScaleHTTPServer := servServerScale.NewServerScaleServer(logger, serverScaleCase)
	deliveryRuleHTTPServer := servDeliveryRule.NewDeliveryRuleServer(logger, idto, deliveryRuleRepo)
	goodsDeliveryRuleHTTPServer := servGoodsDeliveryRule.NewGoodsDeliveryRuleServer(logger, idto, goodsDeliveryRuleRepo, goodsDeliveryRuleGroupRepo)
	unifiedMetadataHTTPServer := servUnifiedMetadata.NewUnifiedMetadataServer(logger, idto, unifiedMetadataRepo)
	jHttpServer := server.NewHTTPServer(logger, web, baselineHTTPServer, operateHTTPServer, goodsStackHTTPServer, hardwareRoleHTTPServer, metadataHTTPServer, serverPlaneHTTPServer, versionHTTPServer, jdStackDeliveryRuleHTTPServer, jdstackFilecenterHTTPServer, jdStackServerScaleHTTPServer, deliveryRuleHTTPServer, goodsDeliveryRuleHTTPServer, unifiedMetadataHTTPServer)
	jobService := service.NewJobService(logger, taskCase)
	cron, cleanup3 := jCron.NewCron(idMutex)
	cronWorker := server.NewCronWorker(kindRelease, logger, jobService, cron, job)
	app, err := newApp(logger, jHttpServer, cronWorker, idto)
	if err != nil {
		cleanup3()
		cleanup2()
		cleanup()
		return nil, nil, err
	}
	return app, func() {
		cleanup3()
		cleanup2()
		cleanup()
	}, nil
}
