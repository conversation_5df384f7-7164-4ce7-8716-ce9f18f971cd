package leaderelection

import (
	"context"
	"errors"
	"os"
	"sync/atomic"
	"time"

	"github.com/go-kratos/kratos/v2/log"
	"k8s.io/client-go/kubernetes"
	"k8s.io/client-go/rest"
	"k8s.io/client-go/tools/clientcmd"
	"k8s.io/client-go/tools/leaderelection"
	"k8s.io/client-go/tools/leaderelection/resourcelock"
)

// LeaderElector 封装了Leader选举的核心功能
type LeaderElector struct {
	log       *log.Helper
	isLeader  atomic.Bool
	namespace string
	podName   string
	lockName  string
}

// Config Leader选举配置
type Config struct {
	// Kubernetes命名空间
	Namespace string
	// 锁资源名称
	LockName string
	// 租约时长
	LeaseDuration time.Duration
	// 续约超时
	RenewDeadline time.Duration
	// 重试间隔
	RetryPeriod time.Duration
}

// DefaultConfig 返回默认配置
func DefaultConfig() *Config {
	return &Config{
		Namespace:     "default",
		LockName:      "zeus-leader-lock",
		LeaseDuration: 15 * time.Second,
		RenewDeadline: 10 * time.Second,
		RetryPeriod:   2 * time.Second,
	}
}

// NewLeaderElector 创建一个新的Leader选举器
func NewLeaderElector(logger log.Logger, config *Config) (*LeaderElector, error) {
	if config == nil {
		config = DefaultConfig()
	}

	// 获取Pod名称作为身份标识
	podName := os.Getenv("POD_NAME")
	if podName == "" {
		// 如果不在K8s环境，使用主机名作为标识
		var err error
		podName, err = os.Hostname()
		if err != nil {
			podName = "unknown-pod"
		}
	}

	return &LeaderElector{
		log:       log.NewHelper(log.With(logger, "module", "leader-election")),
		namespace: config.Namespace,
		podName:   podName,
		lockName:  config.LockName,
	}, nil
}

// IsLeader 返回当前实例是否为Leader
func (le *LeaderElector) IsLeader() bool {
	return le.isLeader.Load()
}

// Run 启动Leader选举流程
func (le *LeaderElector) Run(ctx context.Context, config *Config, onStartedLeading, onStoppedLeading func()) error {
	if config == nil {
		config = DefaultConfig()
	}

	le.log.Infof("Starting leader election in namespace %s with lock name %s", le.namespace, le.lockName)
	le.log.Infof("Pod %s attempting to acquire leadership", le.podName)

	// 获取K8s客户端配置
	kubeConfig, err := le.getKubeConfig()
	if err != nil {
		// 如果不在K8s环境中，自动将当前实例设置为Leader
		le.log.Infof("Not running in Kubernetes environment: %v", err)
		le.log.Info("Setting up non-k8s leader election mode")
		le.isLeader.Store(true)
		le.log.Infof("Initial leadership status in non-k8s mode - Pod: %s, IsLeader: true", le.podName)

		if onStartedLeading != nil {
			le.log.Info("Calling onStartedLeading callback in non-k8s mode")
			go onStartedLeading()
		}

		// 监听上下文取消，以便在程序退出时调用onStoppedLeading
		go func() {
			<-ctx.Done()
			le.log.Info("Context done in non-k8s mode, stopping leadership")
			le.isLeader.Store(false)
			le.log.Infof("Final leadership status in non-k8s mode - Pod: %s, IsLeader: false", le.podName)
			if onStoppedLeading != nil {
				le.log.Info("Calling onStoppedLeading callback in non-k8s mode")
				onStoppedLeading()
			}
		}()

		return nil
	}

	// 创建K8s客户端
	client, err := kubernetes.NewForConfig(kubeConfig)
	if err != nil {
		le.log.Errorf("Failed to create kubernetes client: %v", err)
		return err
	}

	le.log.Infof("Successfully created Kubernetes client, creating resource lock")

	// 创建资源锁
	lock, err := resourcelock.New(
		resourcelock.LeasesResourceLock,
		le.namespace,
		le.lockName,
		client.CoreV1(),
		client.CoordinationV1(),
		resourcelock.ResourceLockConfig{
			Identity: le.podName,
		},
	)
	if err != nil {
		le.log.Errorf("Failed to create resource lock: %v", err)
		return err
	}

	le.log.Infof("Resource lock created successfully, configuring leader election")

	// 配置Leader选举
	leaderConfig := leaderelection.LeaderElectionConfig{
		Lock:            lock,
		ReleaseOnCancel: true,
		LeaseDuration:   config.LeaseDuration,
		RenewDeadline:   config.RenewDeadline,
		RetryPeriod:     config.RetryPeriod,
		Callbacks: leaderelection.LeaderCallbacks{
			OnStartedLeading: func(ctx context.Context) {
				le.log.Infof("Pod %s became leader with lease duration %v", le.podName, config.LeaseDuration)
				le.log.Infof("Next lease renewal will be attempted in %v", config.RenewDeadline)
				le.isLeader.Store(true)
				le.log.Infof("Leadership status updated - Pod: %s, IsLeader: true", le.podName)
				if onStartedLeading != nil {
					onStartedLeading()
				}
			},
			OnStoppedLeading: func() {
				le.log.Infof("Pod %s stopped leading - lease lost or expired", le.podName)
				le.log.Infof("Will retry leadership acquisition in %v", config.RetryPeriod)
				le.isLeader.Store(false)
				le.log.Infof("Leadership status updated - Pod: %s, IsLeader: false", le.podName)
				if onStoppedLeading != nil {
					onStoppedLeading()
				}
			},
			OnNewLeader: func(identity string) {
				if identity == le.podName {
					le.log.Infof("Leadership confirmed: Pod %s is now the active leader", le.podName)
					le.log.Infof("Lease duration: %v, Renewal deadline: %v", config.LeaseDuration, config.RenewDeadline)
				} else {
					le.log.Infof("New leader elected: %s (local pod: %s)", identity, le.podName)
					le.log.Infof("Will attempt to acquire leadership again in %v if current leader fails", config.RetryPeriod)
				}
			},
		},
	}

	// 启动Leader选举
	leaderElector, err := leaderelection.NewLeaderElector(leaderConfig)
	if err != nil {
		le.log.Errorf("Failed to create leader elector: %v", err)
		return err
	}

	// 在后台运行Leader选举
	go func() {
		le.log.Info("Leader election process started")
		le.log.Infof("Configuration: LeaseDuration=%v, RenewDeadline=%v, RetryPeriod=%v",
			config.LeaseDuration, config.RenewDeadline, config.RetryPeriod)
		leaderElector.Run(ctx)
		le.log.Info("Leader election process stopped")
	}()

	return nil
}

// getKubeConfig 获取Kubernetes配置
// 如果不在K8s环境中，返回错误
func (le *LeaderElector) getKubeConfig() (*rest.Config, error) {
	// 尝试获取集群内配置
	config, err := rest.InClusterConfig()
	if err == nil {
		le.log.Info("Successfully obtained in-cluster Kubernetes configuration")
		return config, nil
	}

	le.log.Infof("Failed to get in-cluster config: %v, trying local kubeconfig", err)

	// 如果不在集群内，尝试使用本地kubeconfig
	kubeconfig := os.Getenv("KUBECONFIG")
	if kubeconfig == "" {
		kubeconfig = os.ExpandEnv("$HOME/.kube/config")
		le.log.Infof("KUBECONFIG not set, using default path: %s", kubeconfig)
	} else {
		le.log.Infof("Using KUBECONFIG from environment: %s", kubeconfig)
	}

	// 检查kubeconfig文件是否存在
	if _, err := os.Stat(kubeconfig); os.IsNotExist(err) {
		le.log.Error("No kubeconfig file found")
		return nil, errors.New("not running in kubernetes and no kubeconfig found")
	}

	le.log.Info("Found kubeconfig file, building configuration")
	// 使用本地kubeconfig
	return clientcmd.BuildConfigFromFlags("", kubeconfig)
}
