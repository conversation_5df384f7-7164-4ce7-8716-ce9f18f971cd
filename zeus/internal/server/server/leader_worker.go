package server

import (
	"context"
	"sync/atomic"
	"time"

	"github.com/go-kratos/kratos/v2/log"

	"coding.jd.com/fabric/zeusV2/zeus/internal/server/leaderelection"
)

// LeaderWorker 管理Leader状态和任务调度
type LeaderWorker struct {
	log         *log.Helper
	leaderElect *leaderelection.LeaderElector
	isLeader    atomic.Bool
	ctx         context.Context
	cancel      context.CancelFunc
}

// NewLeaderWorker 创建一个新的LeaderWorker
func NewLeaderWorker(logger log.Logger) *LeaderWorker {
	ctx, cancel := context.WithCancel(context.Background())
	return &LeaderWorker{
		log:    log.NewHelper(log.With(logger, "module", "leader-worker")),
		ctx:    ctx,
		cancel: cancel,
	}
}

// Start 启动Leader选举
func (w *LeaderWorker) Start() error {
	w.log.Info("Starting leader worker with task scheduling capabilities")

	// 创建Leader选举配置
	config := leaderelection.DefaultConfig()
	config.Namespace = "jdock-zeus" // 设置正确的命名空间
	config.LockName = "zeus-task-leader"
	config.LeaseDuration = 15 * time.Second
	config.RenewDeadline = 10 * time.Second
	config.RetryPeriod = 2 * time.Second

	w.log.Infof("Leader election configuration: Namespace=%s, LockName=%s",
		config.Namespace, config.LockName)
	w.log.Infof("Timing configuration: LeaseDuration=%v, RenewDeadline=%v, RetryPeriod=%v",
		config.LeaseDuration, config.RenewDeadline, config.RetryPeriod)

	// 创建Leader选举器
	// 使用原始logger而不是helper，因为NewLeaderElector需要log.Logger接口
	loggerForElector := log.With(log.GetLogger(), "module", "leader-elector")
	leaderElect, err := leaderelection.NewLeaderElector(loggerForElector, config)
	if err != nil {
		w.log.Errorf("Failed to create leader elector: %v", err)
		return err
	}
	w.leaderElect = leaderElect

	w.log.Info("Successfully created leader elector, starting election process")

	// 启动Leader选举
	err = leaderElect.Run(w.ctx, config, w.onStartedLeading, w.onStoppedLeading)
	if err != nil {
		// 即使启动失败，也不要立即返回错误
		w.log.Warnf("Failed to start leader election (possibly not in k8s): %v", err)
		w.log.Info("Running in non-k8s mode, automatically becoming leader")
		w.isLeader.Store(true)
		if w.onStartedLeading != nil {
			go w.onStartedLeading()
		}
		// 不返回错误，让程序继续运行
		return nil
	}

	w.log.Info("Leader worker started successfully")
	return nil
}

// Stop 停止Leader选举
func (w *LeaderWorker) Stop() {
	w.log.Info("Initiating leader worker shutdown")
	if w.IsLeader() {
		w.log.Info("Currently the leader, preparing to release leadership")
	}
	w.cancel()
	w.log.Info("Leader worker shutdown completed")
}

// IsLeader 返回当前实例是否为Leader
func (w *LeaderWorker) IsLeader() bool {
	if w.leaderElect == nil {
		// 如果leaderElect为nil，使用本地isLeader状态
		return w.isLeader.Load()
	}
	return w.leaderElect.IsLeader()
}

// onStartedLeading 当成为Leader时的回调
func (w *LeaderWorker) onStartedLeading() {
	w.log.Info("=== LEADERSHIP ACQUIRED ===")
	w.log.Info("This instance became the leader, starting task scheduling")
	w.isLeader.Store(true)
	w.log.Info("Task scheduling system is now active")
	// 这里可以启动任务调度相关的服务
}

// onStoppedLeading 当停止Leader时的回调
func (w *LeaderWorker) onStoppedLeading() {
	w.log.Info("=== LEADERSHIP LOST ===")
	w.log.Info("This instance stopped being the leader, stopping task scheduling")
	w.isLeader.Store(false)
	w.log.Info("Task scheduling system is now inactive")
	// 这里可以停止任务调度相关的服务
}
