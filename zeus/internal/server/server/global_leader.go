package server

import (
	"sync/atomic"

	"github.com/go-kratos/kratos/v2/log"
)

var (
	// GlobalLeaderWorker 是全局的LeaderWorker实例
	GlobalLeaderWorker *LeaderWorker
)

// InitGlobalLeaderWorker 初始化全局LeaderWorker
func InitGlobalLeaderWorker(logger log.Logger) {
	if GlobalLeaderWorker == nil {
		GlobalLeaderWorker = NewLeaderWorker(logger)
	}
}

// IsLeader 返回当前实例是否为Leader的全局函数
func IsLeader() bool {
	if GlobalLeaderWorker == nil {
		return false
	}
	return GlobalLeaderWorker.IsLeader()
}

// 修改LeaderWorker结构体，添加一个公共的IsLeaderAtomic方法，返回atomic.Bool指针
// 这样其他包可以直接使用这个原子变量进行判断，避免频繁的函数调用
func (w *LeaderWorker) IsLeaderAtomic() *atomic.Bool {
	return &w.isLeader
}
