package moVersion

import (
	"strings"
)

const (
	ProductYamlFile           = "product.yaml"
	UpgradeYamlFile           = "upgrade.yaml"
	BusinessYamlFilePrefix    = "v1-system"
	FusionCoonfigSubDir       = "./jdstack"
	PdDependenciesFile        = "pd_dependencies.json"
	PluginPdDependenciesFile  = "plugin_pd_dependencies.json"
	MiddlewaresFile           = "middlewares.json"
	HostLayerProductsFile     = "host-layer-products.json"
	CatalogsFile              = "catalogs.json"
	ProductsDir               = "products"
	ConfigsDir                = "configs"
	HostLayerCategory         = "主机层基础服务"
	MiddlewareCategory        = "依赖的中间件数据库"
	MiddlewareOpenAPIProvider = "openapi"
)

const (
	uniqueIDSeparator       = "___"
	ClusterType_master      = "master"
	ClusterType_slave       = "slave"
	DummyType_product       = "product"
	DummyType_host          = "Host"
	DummyType_MySQL         = "MySQL"
	DummyType_Redis         = "Redis"
	DummyType_MongoDB       = "MongoDB"
	DummyType_Etcd          = "Etcd"
	DummyType_Elasticsearch = "Elasticsearch"
	DummyType_Kafka         = "Kafka"
	DummyType_Zookeeper     = "Zookeeper"
	DummyType_Clickhouse    = "Clickhouse"
)

const (
	CloudTypeJdstack = "jdstack"
	CloudTypeCvessel = "cvessel"
)

func GetMiddlewareDummyType(dummyType string) string {
	switch strings.ToLower(dummyType) {
	case strings.ToLower(DummyType_MySQL):
		return DummyType_MySQL
	case strings.ToLower(DummyType_Redis):
		return DummyType_Redis
	case strings.ToLower(DummyType_MongoDB):
		return DummyType_MongoDB
	case strings.ToLower(DummyType_Etcd):
		return DummyType_Etcd
	case strings.ToLower(DummyType_Elasticsearch):
		return DummyType_Elasticsearch
	case strings.ToLower(DummyType_Kafka):
		return DummyType_Kafka
	case strings.ToLower(DummyType_Zookeeper):
		return DummyType_Zookeeper
	case strings.ToLower(DummyType_Clickhouse):
		return DummyType_Clickhouse
	default:
		return ""
	}
}

type CvesselSnapshotMetadata struct {
	Version                        string
	VersionType                    string
	SnapshotVersion                string
	PreVersion                     string
	JDStackVersion                 string
	Categories                     []string // FIXME: 暂时将一二级分类合并
	ProductsInfo                   map[string]*CvesselProductInfo
	ProductBusinessInfo            map[string]*BusinessMetadataInfo
	ProductDependencyInfo4Standard map[string]*ClusterDependencyConfig
	FusionScene                    bool
	ProductDependencyInfo4Fusion   map[string]*ClusterDependencyConfig
	CvesselConfigFilesMap          map[string]*string
}

type CvesselProductInfo struct {
	Name                      string
	ServiceCode               string
	Virtual                   bool
	DummyType                 string
	NameCN                    string
	Version                   string
	Category                  string
	ProductYamlContent        *string
	ProductUpgradeYamlContent *string
}

type BusinessMetadataInfo struct {
	VersionName     string
	VersionId       string
	MetadataContent *string
}

// DependencyConfig 依赖配置
type DependencyConfig struct {
	MiddleWares MiddleWareDependencyConfig `json:"middlewares"`
	Products    []*ProductDependencyConfig `json:"products"`
}

type MiddleWareDependencyConfig map[string][]string

type ProductDependencyConfig struct {
	Id           string   `json:"id"`
	Dependencies []string `json:"dependencies"`
	Required     bool     `json:"required"`
	Catalog      string   `json:"catalog"`
	Name         string   `json:"name"`
	Version      string   `json:"version"`
	Revision     int64    `json:"revision"`
}

type ClusterDependencyConfig struct {
	MasterDeploy      bool // 为了标记主集群部署但不依赖任何产品的场景
	SlaveDeploy       bool
	MasterClusterDeps []string
	SlaveClusterDeps  []string
}

// ProductYamlSpec 用于解析pd关键字段
type ProductYamlSpec struct {
	Metadata Metadata `json:"metadata"`
}

// Metadata 非完整字段
type Metadata struct {
	Name                string   `json:"name,omitempty" yaml:"name,omitempty"`
	ServiceCode         string   `json:"serviceCode,omitempty" yaml:"serviceCode,omitempty"`
	Catalog             string   `json:"catalog,omitempty" yaml:"catalog,omitempty"`
	AppType             string   `json:"appType,omitempty" yaml:"appType,omitempty"`
	Code                string   `json:"code,omitempty" yaml:"code,omitempty"`
	Version             string   `json:"version,omitempty" yaml:"version,omitempty" validate:"required"`
	Revision            int64    `json:"revision,omitempty" yaml:"revision,omitempty" validate:"required"`
	Description         string   `json:"description,omitempty" yaml:"description,omitempty" validate:"required"`
	Provider            string   `json:"provider,omitempty" yaml:"provider,omitempty" validate:"required"`
	Feature             []string `json:"feature,omitempty,omitempty" yaml:"feature,omitempty"`
	SupportMultiCluster bool     `json:"supportMultiCluster,omitempty" yaml:"supportMultiCluster"`
}

type Category struct {
	ID    string `json:"id"`
	Name  string `json:"name"`
	Types []Type `json:"types"`
}

type Type struct {
	ID    string   `json:"id"`
	Name  string   `json:"name"`
	Codes []string `json:"codes"`
}

// MiddleWareInfo 数据库中间件文件描述
type MiddleWareInfo map[string][]*MiddleWareMetaInfo

type MiddleWareMetaInfo struct {
	ID            string            `json:"id"`
	Share         bool              `json:"share"`
	Provider      string            `json:"provider"`
	Version       string            `json:"version"`
	Username      string            `json:"username"`
	Password      string            `json:"password"`
	Envs          map[string]string `json:"envs"`
	Products      []string          `json:"products,omitempty"`                 // Use omitempty for optional fields
	SupportOsArch []PlatformInfo    `json:"supportOsArch" yaml:"supportOsArch"` // 支持的系统架构
}

type PlatformInfo struct {
	OS   string `json:"os" description:"操作系统"`
	Arch string `json:"arch" description:"系统架构"`
}

// HostLayerProductsInfo 主机层服务配置文件 - 维护主机层部署的产品信息若干，未在天基上维护，历史版本问题不好追补
type HostLayerProductsInfo map[string]HostLayerProductInfo

type HostLayerProductInfo struct {
	ID           string   `json:"id"`
	Name         string   `json:"name"`
	Version      string   `json:"version"`
	Category     string   `json:"category"`
	Dependencies []string `json:"dependencies"`
	Description  string   `json:"description"`
}
