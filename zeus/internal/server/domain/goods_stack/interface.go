package doGoodsStack

import (
	"context"

	pbGoodsStack "coding.jd.com/fabric/zeusV2/zeus/api/server/pb/goods_stack/v1"
	"coding.jd.com/fabric/zeusV2/zeus/internal/server/data/db_master/dao"
	jPolicy "coding.jd.com/pcd-application/win-go/biz_policy"
	jErr "coding.jd.com/pcd-application/win-go/error"
	"coding.jd.com/pcd-application/win-go/third_party/winapi"
)

type GoodsStackCase interface {
	// Create 创建-标品
	Create(ctx context.Context, req *pbGoodsStack.CreateReq) jErr.Error

	// CreateFilecenter 创建-标品-文件或镜像
	CreateFilecenter(ctx context.Context, req *pbGoodsStack.CreateFilecenterReq) (*dao.GoodsFilecenter, jErr.Error)
	// CreateDelivery 创建-标品-交付
	CreateDelivery(ctx context.Context, req *pbGoodsStack.CreateDeliveryReq) (*dao.GoodsDelivery, jErr.Error)
	// CreateDomain 创建-标品-域名
	CreateDomain(ctx context.Context, req *pbGoodsStack.CreateDomainReq) (*dao.GoodsDomain, jErr.Error)
	// CreateFlavor 创建-标品-云翼配置
	CreateFlavor(ctx context.Context, req *pbGoodsStack.CreateFlavorReq) (*dao.GoodsFlavor, jErr.Error)
	// CreateIac 创建-标品-IAC描述
	CreateIac(ctx context.Context, req *pbGoodsStack.CreateIacReq) (*dao.GoodsIac, jErr.Error)
	// CreateMiddleware 创建-标品-中间件
	CreateMiddleware(ctx context.Context, req *pbGoodsStack.CreateMiddlewareReq) (*dao.GoodsMiddleware, jErr.Error)
	// CreateHardwareRoleUnion 关联-标品-硬件角色、标签
	CreateHardwareRoleUnion(ctx context.Context, req *pbGoodsStack.CreateHardwareRoleUnionReq) jErr.Error
	// CreateChain 创建-标品-调用链路
	CreateChain(ctx context.Context, node *pbGoodsStack.CreateChainReq) (*dao.GoodsChain, jErr.Error)
	// CreateRevision 创建-标品-修订版
	// CreateRevision(ctx context.Context, node *pbGoodsStack.CreateRevisionReq) (*dao.GoodsRevision, jErr.Error)
	// ModifySyncGoodsAction 同步标品状态，将不在列表中的标品状态设置为offline
	ModifySyncGoodsAction(ctx context.Context, req *pbGoodsStack.SyncGoodsActionReq) (int32, jErr.Error)
}

type GoodsRepo interface {
	// Create 创建-标品
	Create(ctx context.Context, node *dao.Goods) (*dao.Goods, jErr.Error)
	CreateTx(ctx context.Context, tx *dao.Tx, node *dao.Goods) (*dao.Goods, jErr.Error)
	// CreateMany 批量创建-标品
	CreateMany(ctx context.Context, node []*dao.Goods) jErr.Error
	// Update 修改-标品
	Update(ctx context.Context, node *dao.Goods, clearFields ...string) jErr.Error

	// GetID 查询-标品
	GetID(ctx context.Context, id string, filters ...*winapi.Filter) (*dao.Goods, jErr.Error)
	// Get 查询-标品
	Get(ctx context.Context, filters ...*winapi.Filter) (*dao.Goods, jErr.Error)
	// Exists 存在-标品
	Exists(ctx context.Context, filters ...*winapi.Filter) (exists bool, err jErr.Error)
	ExistsTx(ctx context.Context, query *dao.GoodsQuery, filters ...*winapi.Filter) (bool, jErr.Error)

	// List 列表-标品
	List(ctx context.Context, pageNumber, pageSize int64, filters winapi.Filters, sorts winapi.Sorts) ([]*dao.Goods, jErr.Error)
	// TotalCount 总数-标品
	TotalCount(ctx context.Context, filters winapi.Filters) int64

	// PolicyOperate 操作策略-标品
	PolicyOperate(ctx context.Context, node *dao.Goods, policy *jPolicy.PolicyStatus) (*dao.Goods, jErr.Error)

	// CreateHardwareRoleID 关联-标品-硬件角色
	CreateHardwareRoleID(ctx context.Context, id string, target ...int64) jErr.Error
	// DeleteHardwareRoleID 删除关联-标品-硬件角色
	DeleteHardwareRoleID(ctx context.Context, id string, target ...int64) jErr.Error
	// CreateHardwareRoleLabelID 关联-标品-硬件角色标签
	CreateHardwareRoleLabelID(ctx context.Context, id string, target ...int64) jErr.Error
	// DeleteHardwareRoleLabelID 删除关联-标品-硬件角色标签
	DeleteHardwareRoleLabelID(ctx context.Context, id string, target ...int64) jErr.Error
	// CreateGoodsMiddlewareID 关联-标品-中间件
	CreateGoodsMiddlewareID(ctx context.Context, id string, target ...int64) jErr.Error
	// DeleteGoodsMiddlewareID 删除关联-标品-中间件
	DeleteGoodsMiddlewareID(ctx context.Context, id string, target ...int64) jErr.Error
	// CreateGoodsDeliveryRuleGroupID 关联-标品-交付规则组
	CreateGoodsDeliveryRuleGroupID(ctx context.Context, id string, target ...int64) (err jErr.Error)
	// DeleteGoodsDeliveryRuleGroupID 删除关联-标品-交付规则组
	DeleteGoodsDeliveryRuleGroupID(ctx context.Context, id string, target ...int64) (err jErr.Error)
}

type GoodsFilecenterRepo interface {
	// Create 创建-标品-文件或镜像
	Create(ctx context.Context, node *dao.GoodsFilecenter) (*dao.GoodsFilecenter, jErr.Error)
	// DeleteID 删除-标品-文件或镜像
	DeleteID(ctx context.Context, id int64) jErr.Error
	// Update 修改-标品-文件或镜像
	Update(ctx context.Context, node *dao.GoodsFilecenter, clearFields ...string) jErr.Error
	// GetID 查询-标品-文件或镜像
	GetID(ctx context.Context, id int64, filters ...*winapi.Filter) (*dao.GoodsFilecenter, jErr.Error)
	// Exists 存在-标品-文件或镜像
	Exists(ctx context.Context, filters ...*winapi.Filter) (exists bool, err jErr.Error)

	// List 列表-标品-文件或镜像
	List(ctx context.Context, pageNumber, pageSize int64, filters winapi.Filters, sorts winapi.Sorts) ([]*dao.GoodsFilecenter, jErr.Error)
	// TotalCount 总数-标品-文件或镜像
	TotalCount(ctx context.Context, filters winapi.Filters) int64

	// PolicyOperate 操作策略-标品-文件或镜像
	PolicyOperate(ctx context.Context, node *dao.GoodsFilecenter, policy *jPolicy.PolicyStatus) (*dao.GoodsFilecenter, jErr.Error)
}

type GoodsDomainRepo interface {
	// Create 创建-标品-域名
	Create(ctx context.Context, node *dao.GoodsDomain) (*dao.GoodsDomain, jErr.Error)
	// DeleteID 删除-标品-域名
	DeleteID(ctx context.Context, id int64) jErr.Error
	// Update 修改-标品-域名
	Update(ctx context.Context, node *dao.GoodsDomain, clearFields ...string) jErr.Error
	// GetID 查询-标品-域名
	GetID(ctx context.Context, id int64, filters ...*winapi.Filter) (*dao.GoodsDomain, jErr.Error)
	// Exists 存在-标品-域名
	Exists(ctx context.Context, filters ...*winapi.Filter) (exists bool, err jErr.Error)

	// List 列表-标品-域名
	List(ctx context.Context, pageNumber, pageSize int64, filters winapi.Filters, sorts winapi.Sorts) ([]*dao.GoodsDomain, jErr.Error)
	// TotalCount 总数-标品-域名
	TotalCount(ctx context.Context, filters winapi.Filters) int64

	// PolicyOperate 操作策略-标品-域名
	PolicyOperate(ctx context.Context, node *dao.GoodsDomain, policy *jPolicy.PolicyStatus) (*dao.GoodsDomain, jErr.Error)
}

type GoodsChainRepo interface {
	// Create 创建-标品-调用链路
	Create(ctx context.Context, node *dao.GoodsChain) (*dao.GoodsChain, jErr.Error)
	CreateTx(ctx context.Context, tx *dao.Tx, node *dao.GoodsChain) (*dao.GoodsChain, jErr.Error)
	CreateBatchTx(ctx context.Context, tx *dao.Tx, nodes []*dao.GoodsChain) ([]*dao.GoodsChain, jErr.Error)
	// DeleteID 删除-标品-调用链路
	DeleteID(ctx context.Context, id int64) jErr.Error
	// GetID 查询-标品-调用链路
	GetID(ctx context.Context, id int64, filters ...*winapi.Filter) (*dao.GoodsChain, jErr.Error)
	// Get 查询-标品-调用链路
	Get(ctx context.Context, filters ...*winapi.Filter) (*dao.GoodsChain, jErr.Error)
	// Exists 存在-标品-调用链路
	Exists(ctx context.Context, filters ...*winapi.Filter) (exists bool, err jErr.Error)

	// List 列表-标品-调用链路
	List(ctx context.Context, pageNumber, pageSize int64, filters winapi.Filters, sorts winapi.Sorts) ([]*dao.GoodsChain, jErr.Error)
	// TotalCount 总数-标品-调用链路
	TotalCount(ctx context.Context, filters winapi.Filters) int64

	// PolicyOperate 操作策略-标品-调用链路
	PolicyOperate(ctx context.Context, node *dao.GoodsChain, policy *jPolicy.PolicyStatus) (*dao.GoodsChain, jErr.Error)
}

type GoodsDeliveryRuleGroupRepo interface {
	// Create 创建-标品-交付规则组
	Create(ctx context.Context, node *dao.GoodsDeliveryRuleGroup) (*dao.GoodsDeliveryRuleGroup, jErr.Error)
	// GetID 查询-标品-交付规则组
	GetID(ctx context.Context, id int64, filters ...*winapi.Filter) (*dao.GoodsDeliveryRuleGroup, jErr.Error)
	// Exists 存在-标品-交付规则组
	Exists(ctx context.Context, filters ...*winapi.Filter) (exists bool, err jErr.Error)

	// List 列表-标品-交付规则组
	List(ctx context.Context, pageNumber, pageSize int64, filters winapi.Filters, sorts winapi.Sorts) ([]*dao.GoodsDeliveryRuleGroup, jErr.Error)
	// TotalCount 总数-标品-交付规则组
	TotalCount(ctx context.Context, filters winapi.Filters) int64

	// PolicyOperate 操作策略-标品-交付规则组
	PolicyOperate(ctx context.Context, node *dao.GoodsDeliveryRuleGroup, policy *jPolicy.PolicyStatus) (*dao.GoodsDeliveryRuleGroup, jErr.Error)
}

type GoodsDeliveryRuleRepo interface {
	// Create 创建-标品-交付规则
	Create(ctx context.Context, node *dao.GoodsDeliveryRule) (*dao.GoodsDeliveryRule, jErr.Error)
	// GetID 查询-标品-交付规则
	GetID(ctx context.Context, id int64, filters ...*winapi.Filter) (*dao.GoodsDeliveryRule, jErr.Error)
	// Exists 存在-标品-交付规则
	Exists(ctx context.Context, filters ...*winapi.Filter) (exists bool, err jErr.Error)

	// List 列表-标品-交付规则
	List(ctx context.Context, pageNumber, pageSize int64, filters winapi.Filters, sorts winapi.Sorts) ([]*dao.GoodsDeliveryRule, jErr.Error)
	// TotalCount 总数-标品-交付规则
	TotalCount(ctx context.Context, filters winapi.Filters) int64

	// PolicyOperate 操作策略-标品-交付规则
	PolicyOperate(ctx context.Context, node *dao.GoodsDeliveryRule, policy *jPolicy.PolicyStatus) (*dao.GoodsDeliveryRule, jErr.Error)
}

type GoodsQPSGrowthRepo interface {
	// List 列表-标品-QPS阈值
	List(ctx context.Context, pageNumber, pageSize int64, filters winapi.Filters, sorts winapi.Sorts) ([]*dao.GoodsQPSGrowth, jErr.Error)
}

type GoodsPdRepo interface {
	// Create 创建-标品-pd
	Create(ctx context.Context, node *dao.GoodsPd) (*dao.GoodsPd, jErr.Error)
	// DeleteID 删除-标品-pd
	DeleteID(ctx context.Context, id int64) jErr.Error
	// Update 修改-标品-pd
	Update(ctx context.Context, node *dao.GoodsPd, clearFields ...string) jErr.Error
	// GetID 查询-标品-pd
	GetID(ctx context.Context, id int64, filters ...*winapi.Filter) (*dao.GoodsPd, jErr.Error)
	// Exists 存在-标品-pd
	Exists(ctx context.Context, filters ...*winapi.Filter) (exists bool, err jErr.Error)

	// List 列表-标品-pd
	List(ctx context.Context, pageNumber, pageSize int64, filters winapi.Filters, sorts winapi.Sorts) ([]*dao.GoodsPd, jErr.Error)
	// TotalCount 总数-标品-pd
	TotalCount(ctx context.Context, filters winapi.Filters) int64

	// PolicyOperate 操作策略-标品-pd
	PolicyOperate(ctx context.Context, node *dao.GoodsPd, policy *jPolicy.PolicyStatus) (*dao.GoodsPd, jErr.Error)
}

type GoodsUpgradeRepo interface {
	// Create 创建-标品-升级描述
	Create(ctx context.Context, node *dao.GoodsUpgrade) (*dao.GoodsUpgrade, jErr.Error)
	// DeleteID 删除-标品-升级描述
	DeleteID(ctx context.Context, id int64) jErr.Error
	// Update 修改-标品-升级描述
	Update(ctx context.Context, node *dao.GoodsUpgrade, clearFields ...string) jErr.Error
	// GetID 查询-标品-升级描述
	GetID(ctx context.Context, id int64, filters ...*winapi.Filter) (*dao.GoodsUpgrade, jErr.Error)
	// Exists 存在-标品-升级描述
	Exists(ctx context.Context, filters ...*winapi.Filter) (exists bool, err jErr.Error)

	// List 列表-标品-升级描述
	List(ctx context.Context, pageNumber, pageSize int64, filters winapi.Filters, sorts winapi.Sorts) ([]*dao.GoodsUpgrade, jErr.Error)
	// TotalCount 总数-标品-升级描述
	TotalCount(ctx context.Context, filters winapi.Filters) int64

	// PolicyOperate 操作策略-标品-升级描述
	PolicyOperate(ctx context.Context, node *dao.GoodsUpgrade, policy *jPolicy.PolicyStatus) (*dao.GoodsUpgrade, jErr.Error)
}

type GoodsMetadataRepo interface {
	// Create 创建-标品-元数据(商业平台/网关)
	Create(ctx context.Context, node *dao.GoodsMetadata) (*dao.GoodsMetadata, jErr.Error)
	// DeleteID 删除-标品-元数据(商业平台/网关)
	DeleteID(ctx context.Context, id int64) jErr.Error
	// Update 修改-标品-元数据(商业平台/网关)
	Update(ctx context.Context, node *dao.GoodsMetadata, clearFields ...string) jErr.Error
	// GetID 查询-标品-元数据(商业平台/网关)
	GetID(ctx context.Context, id int64, filters ...*winapi.Filter) (*dao.GoodsMetadata, jErr.Error)
	// Exists 存在-标品-元数据(商业平台/网关)
	Exists(ctx context.Context, filters ...*winapi.Filter) (exists bool, err jErr.Error)

	// List 列表-标品-元数据(商业平台/网关)
	List(ctx context.Context, pageNumber, pageSize int64, filters winapi.Filters, sorts winapi.Sorts) ([]*dao.GoodsMetadata, jErr.Error)
	// TotalCount 总数-标品-元数据(商业平台/网关)
	TotalCount(ctx context.Context, filters winapi.Filters) int64

	// PolicyOperate 操作策略-标品-元数据(商业平台/网关)
	PolicyOperate(ctx context.Context, node *dao.GoodsMetadata, policy *jPolicy.PolicyStatus) (*dao.GoodsMetadata, jErr.Error)
}
type GoodsConfigRepo interface {
	// Create 创建-标品-配置
	Create(ctx context.Context, node *dao.GoodsConfig) (*dao.GoodsConfig, jErr.Error)
	// DeleteID 删除-标品-配置
	DeleteID(ctx context.Context, id int64) jErr.Error
	// Update 修改-标品-配置
	Update(ctx context.Context, node *dao.GoodsConfig, clearFields ...string) jErr.Error
	// GetID 查询-标品-配置
	GetID(ctx context.Context, id int64, filters ...*winapi.Filter) (*dao.GoodsConfig, jErr.Error)
	// Exists 存在-标品-配置
	Exists(ctx context.Context, filters ...*winapi.Filter) (exists bool, err jErr.Error)

	// List 列表-标品-配置
	List(ctx context.Context, pageNumber, pageSize int64, filters winapi.Filters, sorts winapi.Sorts) ([]*dao.GoodsConfig, jErr.Error)
	// TotalCount 总数-标品-配置
	TotalCount(ctx context.Context, filters winapi.Filters) int64

	// PolicyOperate 操作策略-标品-配置
	PolicyOperate(ctx context.Context, node *dao.GoodsConfig, policy *jPolicy.PolicyStatus) (*dao.GoodsConfig, jErr.Error)
}
