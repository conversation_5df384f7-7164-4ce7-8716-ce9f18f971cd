package bizVersion

import (
	"context"
	"fmt"
	"strings"
	"time"

	pbVersion "coding.jd.com/fabric/zeusV2/zeus/api/server/pb/version/v1"
	"coding.jd.com/fabric/zeusV2/zeus/internal/server/data/db_master/dao"
	"coding.jd.com/fabric/zeusV2/zeus/internal/server/data/db_master/dao/goods"
	"coding.jd.com/fabric/zeusV2/zeus/internal/server/data/db_master/dao/goodschain"
	"coding.jd.com/fabric/zeusV2/zeus/internal/server/data/db_master/dao/goodsmetadata"
	"coding.jd.com/fabric/zeusV2/zeus/internal/server/data/db_master/dao/goodspd"
	"coding.jd.com/fabric/zeusV2/zeus/internal/server/data/db_master/dao/goodsupgrade"
	"coding.jd.com/fabric/zeusV2/zeus/internal/server/data/db_master/dao/jdstackrelease"
	"coding.jd.com/fabric/zeusV2/zeus/internal/server/data/db_master/dao/releaseconfigfile"
	jErr "coding.jd.com/pcd-application/win-go/error"
	"coding.jd.com/pcd-application/win-go/third_party/winapi"
)

// DeleteCvesselMetadataSql FIXME:为保证版本创建的顺序，这里不会删除版本的sql
func (uc *versionUsecase) DeleteCvesselMetadataSql(ctx context.Context, req *pbVersion.DeleteCvesselMetadataReq) jErr.Error {
	errHandler := func(err error) jErr.Error {
		return jErr.NewMessagef(Self, err.Error())
	}
	// 重设超时时间
	ctx, cancel := context.WithTimeout(context.Background(), 3*time.Minute)
	defer cancel()
	// 开启事务
	tx, err := uc.data.Tx(ctx)
	if err != nil {
		return errHandler(err)
	}
	defer func() {
		if r := recover(); r != nil {
			_ = tx.Rollback()
			panic(r)
		}
	}()

	if req.VersionType == jdstackrelease.CloudTypeJdstack.String() {
		// 【版本统一】处理云舰组件在天基将封版申请统一到jdstack封版版本之后的删除逻辑
		jError := uc.dealWithJdstackSyncDataDeleteCase(ctx, tx, req)
		if jError != nil {
			_ = tx.Rollback()
			jError = jError.AddReasonf("删除jdstack版本中云舰组件的数据失败")
			return jError
		}
	} else if req.VersionType == jdstackrelease.CloudTypeCvessel.String() {
		// 处理原有的融合逻辑以及云舰封版类型的版本数据
		jError := uc.dealWithCvesselSyncDataDeleteCase(ctx, tx, req)
		if jError != nil {
			_ = tx.Rollback()
			jError = jError.AddReasonf("删除云舰版本中数据失败")
			return jError
		}
	} else {
		_ = tx.Rollback()
		return jErr.NewMessagef(Self, "不支持的版本类型")
	}

	// 提交事务
	err = tx.Commit()
	if err != nil {
		_ = tx.Rollback()
		return errHandler(err)
	}
	return nil
}

func (uc *versionUsecase) dealWithJdstackSyncDataDeleteCase(ctx context.Context, tx *dao.Tx, req *pbVersion.DeleteCvesselMetadataReq) jErr.Error {
	filters := winapi.Filters{
		&winapi.Filter{
			Name:   jdstackrelease.FieldName,
			Values: []string{req.Version},
		},
		&winapi.Filter{
			Name:   jdstackrelease.FieldCloudType,
			Values: []string{req.VersionType},
		},
		&winapi.Filter{
			Name:   jdstackrelease.FilterWithRelatedRelease,
			Values: []string{"-"},
		},
	}

	exist, jError := uc.JdstackReleaseRepo.Exists(ctx, filters...)
	if jError != nil {
		return jError
	}
	if !exist {
		return jErr.NewMessagef(Self, fmt.Sprintf("jdstack版本 %s 不存在,操作无效", req.Version))
	} else {
		var jdstackReleaseDao *dao.JdstackRelease
		jdstackReleaseDao, jError = uc.JdstackReleaseRepo.GetName(ctx, req.Version, filters...)
		if jError != nil {
			jError = jError.AddReasonf("查询jdstack版本失败")
			return jError
		}

		// 【版本统一】删除jdstack版本中融合的云舰数据
		jError = uc.deleteStandardAndFusionCvesselMetadataSql(ctx, tx, jdstackReleaseDao)
		if jError != nil {
			jError = jError.AddReasonf("删除jdstack版本中云舰组件的标准交付数据失败")
			return jError
		}

	}
	return nil
}

func (uc *versionUsecase) dealWithCvesselSyncDataDeleteCase(ctx context.Context, tx *dao.Tx, req *pbVersion.DeleteCvesselMetadataReq) jErr.Error {
	filters := winapi.Filters{
		&winapi.Filter{
			Name:   jdstackrelease.FieldName,
			Values: []string{req.Version},
		},
		&winapi.Filter{
			Name:   jdstackrelease.FieldCloudType,
			Values: []string{req.VersionType},
		},
		&winapi.Filter{
			Name:   jdstackrelease.FilterWithRelatedRelease,
			Values: []string{"-"},
		},
	}

	exist, jError := uc.JdstackReleaseRepo.Exists(ctx, filters...)
	if jError != nil {
		return jError
	}
	if !exist {
		// HACK: 因为克隆出来的jdstack版本，已经包含了上一个jdstack版本所关联云舰的元数据，直接创建会报关联数据唯一键冲突，所以第一次导入云舰数据的时候，需要单独删除一次
		// 仅处理融合场景（实际关联JDStack版本）
		if req.JdstackVersion != "" {
			filtersTmp := winapi.Filters{
				&winapi.Filter{
					Name:   jdstackrelease.FieldName,
					Values: []string{req.JdstackVersion},
				},
				&winapi.Filter{
					Name:   jdstackrelease.FieldCloudType,
					Values: []string{jdstackrelease.CloudTypeJdstack.String()},
				},
			}
			var jdstackReleaseDao *dao.JdstackRelease
			jdstackReleaseDao, jError = uc.JdstackReleaseRepo.GetName(ctx, req.JdstackVersion, filtersTmp...)
			if jError != nil {
				jError = jError.AddReasonf("【首次创建】删除JDSack版本中融合云舰数据失败,查询版本失败")
				return jError.Get()
			}
			jError = uc.deleteStandardAndFusionCvesselMetadataSql(ctx, tx, jdstackReleaseDao)
			if jError != nil {
				jError = jError.AddReasonf("【首次创建】删除JDSack版本中融合云舰数据失败，删除数据失败")
				return jError.Get()
			}
		}
	} else {
		var cvesselReleaseDao *dao.JdstackRelease
		cvesselReleaseDao, jError = uc.JdstackReleaseRepo.GetName(ctx, req.Version, filters...)
		if jError != nil {
			jError = jError.AddReasonf("查询云舰版本失败")
			return jError
		}

		// 删除标准交付场景的数据 - 只要云舰版本配置过融合数据,在删除的时候就必须删除,即使是仅更新标准交付场景
		jError = uc.deleteStandardAndFusionCvesselMetadataSql(ctx, tx, cvesselReleaseDao)
		if jError != nil {
			jError = jError.AddReasonf("删除云舰版本中标准交付数据失败")
			return jError
		}

		// 删除融合场景下jdstack包含的数据
		if cvesselReleaseDao != nil && cvesselReleaseDao.Edges.RelatedRelease != nil {
			jdstackReleaseDao := cvesselReleaseDao.Edges.RelatedRelease
			jError = uc.deleteStandardAndFusionCvesselMetadataSql(ctx, tx, jdstackReleaseDao)
			if jError != nil {
				jError = jError.AddReasonf("删除JDSack版本中融合云舰数据失败")
				return jError
			}
		}
	}
	return nil
}

// 删除标准交付场景 / 融合场景的数据
// 标准场景：传入cvesselRelease / 融合场景：传入cvesselRelease.Edges.RelatedRelease
func (uc *versionUsecase) deleteStandardAndFusionCvesselMetadataSql(ctx context.Context, tx *dao.Tx, releaseDao *dao.JdstackRelease) jErr.Error {
	errHandler := func(err error) jErr.Error {
		return jErr.NewMessagef(Self, err.Error())
	}
	if releaseDao == nil {
		return errHandler(fmt.Errorf("releaseDao is nil"))
	}

	cloudType4ConfigFile := releaseconfigfile.CloudTypeCvessel
	if releaseDao.CloudType != nil && *releaseDao.CloudType == jdstackrelease.CloudTypeJdstack {
		// HACK: 硬编码，5.6.0版本之后引入的变更（5.5.*和5.4.*才接入天基，延续原来的逻辑）
		if releaseDao.Name != nil && (strings.HasPrefix(*releaseDao.Name, "5.5") || strings.HasPrefix(*releaseDao.Name, "5.4")) {
			cloudType4ConfigFile = releaseconfigfile.CloudTypeCvessel
		} else {
			cloudType4ConfigFile = releaseconfigfile.CloudTypeJdstack
		}
	}

	filters4ConfigFile := winapi.Filters{
		&winapi.Filter{
			Name:     releaseconfigfile.FieldReleaseID,
			Operator: "eq",
			Values:   []string{fmt.Sprintf("%d", releaseDao.ID)},
		},
		&winapi.Filter{
			Name:     releaseconfigfile.FieldCloudType,
			Operator: "eq",
			Values:   []string{cloudType4ConfigFile.String()},
		},
	}
	// 删除配置文件
	configFiles, jError := uc.ReleaseConfigFileRepo.List(ctx, -1, 1, filters4ConfigFile, winapi.Sorts{})
	if jError != nil {
		return jError
	}
	fileIds := make([]int32, 0)
	for _, configFile := range configFiles {
		fileIds = append(fileIds, configFile.ID)
	}
	_, err := tx.ReleaseConfigFile.Delete().Where(releaseconfigfile.IDIn(fileIds...)).Exec(ctx)
	if err != nil {
		return errHandler(err)
	}

	// metadata_category不删除
	// metadata_service不删除
	// metadata_resource不删除

	filters4Goods := winapi.Filters{
		&winapi.Filter{
			Name:     goods.FieldReleaseID,
			Operator: "eq",
			Values:   []string{fmt.Sprintf("%d", releaseDao.ID)},
		},
		&winapi.Filter{
			Name:     goods.FieldCloudType,
			Operator: "eq",
			Values:   []string{goods.CloudTypeCvessel.String()},
		},
	}
	// 删除goods信息
	goodsDaos, jError := uc.GoodsRepo.List(ctx, -1, 1, filters4Goods, winapi.Sorts{})
	if jError != nil {
		return jError
	}
	goodsIds := make([]string, 0)
	for _, goodsDao := range goodsDaos {
		if goodsDao.CloudType != nil && *goodsDao.CloudType != goods.CloudTypeCvessel {
			continue
		}
		goodsIds = append(goodsIds, goodsDao.ID)
	}
	_, err = tx.Goods.Delete().Where(goods.IDIn(goodsIds...), goods.CloudTypeEQ(goods.CloudTypeCvessel.String())).Exec(ctx)
	if err != nil {
		return errHandler(err)
	}

	// 删除goodsPd信息
	_, err = tx.GoodsPd.Delete().Where(goodspd.GoodsIDIn(goodsIds...)).Exec(ctx)
	if err != nil {
		return errHandler(err)
	}

	// 删除goodsUpgrade信息
	_, err = tx.GoodsUpgrade.Delete().Where(goodsupgrade.GoodsIDIn(goodsIds...)).Exec(ctx)
	if err != nil {
		return errHandler(err)
	}

	// 删除goodsMetadata信息
	_, err = tx.GoodsMetadata.Delete().Where(goodsmetadata.GoodsIDIn(goodsIds...)).Exec(ctx)
	if err != nil {
		return errHandler(err)
	}

	// 删除goodsChain信息
	_, err = tx.GoodsChain.Delete().Where(goodschain.ToIDIn(goodsIds...), goodschain.ResKindEQ(goodschain.ResKindDelivery.String()), goodschain.ResID(0)).Exec(ctx)
	if err != nil {
		return errHandler(err)
	}

	// 最后删除云舰版本 - 为了保持在sql中的顺序，不删除这里的实际数据
	// err = tx.JdstackRelease.DeleteOne(cvesselRelease).Exec(ctx)
	// if err != nil {
	// 	return errHandler(err)
	// }
	return nil
}
