package bizVersion

import (
	"context"
	"fmt"

	pbVersion "coding.jd.com/fabric/zeusV2/zeus/api/server/pb/version/v1"
	"coding.jd.com/fabric/zeusV2/zeus/internal/server/data/db_master/dao"
	"coding.jd.com/fabric/zeusV2/zeus/internal/server/data/db_master/dao/jdstackrelease"
	moVersion "coding.jd.com/fabric/zeusV2/zeus/internal/server/domain/version/model"
	jErr "coding.jd.com/pcd-application/win-go/error"
	"coding.jd.com/pcd-application/win-go/third_party/winapi"
)

// ModifyCvesselMetadataSql FIXME: 暂时通过删除新建来更新,需要切换为真正的更新，同时添加上线/下线操作
func (uc *versionUsecase) ModifyCvesselMetadataSql(ctx context.Context, metadata *moVersion.CvesselSnapshotMetadata) (*dao.JdstackRelease, jErr.Error) {
	// 检查欲关联的JDStack版本是否存在
	if metadata.JDStackVersion != "" {
		filters := winapi.Filters{
			&winapi.Filter{
				Name:   jdstackrelease.FieldName,
				Values: []string{metadata.JDStackVersion},
			},
			&winapi.Filter{
				Name:   jdstackrelease.FieldCloudType,
				Values: []string{jdstackrelease.CloudTypeJdstack.String()},
			},
		}
		exist, jError := uc.JdstackReleaseRepo.Exists(ctx, filters...)
		if jError != nil {
			return nil, jError
		}
		if !exist {
			return nil, jErr.NewMessagef(Self, fmt.Sprintf("欲关联的JDStack版本(%s)在Zeus内不存在", metadata.JDStackVersion))
		}
	}
	// 不管云舰版本是否存在，都删除重建（适配第一次创建）
	// 【版本统一】如果传入的是jdstack封版版本类型，则删除jdstack版本下的云舰数据，不动stack原本的数据
	if err := uc.DeleteCvesselMetadataSql(ctx, &pbVersion.DeleteCvesselMetadataReq{
		Version:        metadata.Version,
		VersionType:    metadata.VersionType,
		JdstackVersion: metadata.JDStackVersion,
	}); err != nil {
		return nil, err
	}

	return uc.CreateCvesselMetadataSql(ctx, metadata)
}
