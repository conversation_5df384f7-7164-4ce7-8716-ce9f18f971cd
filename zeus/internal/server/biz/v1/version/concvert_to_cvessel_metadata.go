package bizVersion

import (
	"archive/tar"
	"bytes"
	"compress/gzip"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"os"
	"path/filepath"
	"strings"

	"gopkg.in/yaml.v3"

	pbVersion "coding.jd.com/fabric/zeusV2/zeus/api/server/pb/version/v1"
	moVersion "coding.jd.com/fabric/zeusV2/zeus/internal/server/domain/version/model"
	jErr "coding.jd.com/pcd-application/win-go/error"
)

/*
  将云舰版本元数据导入zeus - 预计时间2mins
  这里直接根据是否关联jdstack版本以及配置文件是否包含依赖配置来处理
	- 关联jdstack版本：融合标准存两份数据
	- 不关联jdstack版本：只存储标准一份数据
  在以上两种场景下细分
	- 包含依赖配置(云舰新版本)： 按照最全面的内容处理
	- 不包含配置文件(云舰老版本)：不配置依赖关系及configFile，仅导入pd
*/

func (uc *versionUsecase) ConvertReqToCvesselMetadata(ctx context.Context, req *pbVersion.CreateCvesselMetadataReq) (*moVersion.CvesselSnapshotMetadata, jErr.Error) {
	if req == nil {
		return nil, jErr.NewMessagef(Self, "req is nil")
	}
	fileDataSource, cleanup, err := NewFileDataSource(req)
	if err != nil {
		return nil, jErr.NewMessagef(Self, "NewFileDataSource failed: %v", err)
	}
	defer cleanup()

	metadata, err := fileDataSource.GenerateMetadataFromReq(req)
	if err != nil {
		return nil, jErr.NewMessagef(Self, "GenerateMetadataFromReq failed: %v", err)
	}
	return metadata, nil
}

type FileDataSource struct {
	OldVersion                          bool // 适配云舰3.7及之前的版本
	FusionScene                         bool
	Dir                                 string
	Categories                          []string
	ProductYamlsContentMap              map[string]*string
	ProductUpgradeYamlsContentMap       map[string]*string
	DependencyMiddleWareConfig4Standard *DependencyMiddleWareConfig // 区分不同的交付场景：fusion / standard
	DependencyMiddleWareConfig4Fusion   *DependencyMiddleWareConfig
	HostLayerProductInfoMap             moVersion.HostLayerProductsInfo
	// metadata实际内容
	ProductsInfo                   map[string]*moVersion.CvesselProductInfo
	ProductBusinessInfoMap         map[string]*moVersion.BusinessMetadataInfo
	ProductDependencyInfo4Standard map[string]*moVersion.ClusterDependencyConfig
	ProductDependencyInfo4Fusion   map[string]*moVersion.ClusterDependencyConfig
	CvesselConfigFilesMap          map[string]*string
}

type DependencyMiddleWareConfig struct {
	MasterClusterProductDependencyMap map[string]*moVersion.ProductDependencyConfig
	SlaveClusterProductDependencyMap  map[string]*moVersion.ProductDependencyConfig
	MiddlewareDependencyMap           moVersion.MiddleWareDependencyConfig
	MiddlewareMap                     moVersion.MiddleWareInfo
}

func NewFileDataSource(req *pbVersion.CreateCvesselMetadataReq) (*FileDataSource, func(), error) {
	// 创建临时目录
	tempDir, err := os.MkdirTemp(os.TempDir(), "")
	if err != nil {
		return nil, nil, err
	}
	// 定义清理函数
	cleanup := func() {
		os.RemoveAll(tempDir)
	}
	// 解压数据
	if err = unpackTarGzFile(req.Data, tempDir); err != nil {
		cleanup()
		return nil, nil, fmt.Errorf("unpack tar.gz file: %w", err)
	}
	// 查找目标目录
	var versionDir string
	if err = filepath.Walk(tempDir, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			return err
		}
		if info.IsDir() && strings.Contains(info.Name(), req.Version) {
			versionDir = path
			return filepath.SkipAll
		}
		return nil
	}); err != nil {
		cleanup()
		return nil, nil, fmt.Errorf("error while searching for version directory: %w", err)
	}
	if versionDir == "" {
		cleanup()
		return nil, nil, fmt.Errorf("could not find directory containing version %s", req.Version)
	}
	// 判断是否为云舰3.7及之前的版本,3.7及之前的版本仅导入pd
	isOldVersion := false
	if _, err = os.Stat(filepath.Join(versionDir, moVersion.ConfigsDir, moVersion.PdDependenciesFile)); err != nil {
		if os.IsNotExist(err) {
			isOldVersion = true
		}
	}
	// 判断是否包含融合场景 - 如果未关联jdstack版本则认为标准场景
	fusionScene := true
	if req.JdstackVersion == "" {
		fusionScene = false
	}
	// 【版本统一】如果是JDStack封版版本导入云舰组件的PD数据，则默认是非融合场景
	if req.VersionType == moVersion.CloudTypeJdstack {
		fusionScene = false
	}
	// if _, err = os.Stat(filepath.Join(versionDir, moVersion.ConfigsDir, moVersion.FusionCoonfigSubDir, moVersion.PdDependenciesFile)); err != nil {
	// 	if os.IsNotExist(err) {
	// 		fusionScene = false
	// 	}
	// }
	if fusionScene {
		if req.JdstackVersion == "" {
			return nil, nil, fmt.Errorf("当前封版版本已配置融合场景依赖配置,但未配置关联的jdstack版本")
		}
	}
	fileDataSource := &FileDataSource{
		OldVersion:                          isOldVersion,
		FusionScene:                         fusionScene,
		Dir:                                 versionDir,
		DependencyMiddleWareConfig4Standard: &DependencyMiddleWareConfig{},
		DependencyMiddleWareConfig4Fusion:   &DependencyMiddleWareConfig{},
		ProductsInfo:                        make(map[string]*moVersion.CvesselProductInfo),
		ProductBusinessInfoMap:              make(map[string]*moVersion.BusinessMetadataInfo),
		ProductDependencyInfo4Standard:      make(map[string]*moVersion.ClusterDependencyConfig),
		ProductDependencyInfo4Fusion:        make(map[string]*moVersion.ClusterDependencyConfig),
	}
	return fileDataSource, cleanup, nil
}

func (f *FileDataSource) GenerateMetadataFromReq(req *pbVersion.CreateCvesselMetadataReq) (*moVersion.CvesselSnapshotMetadata, error) {
	metadata := &moVersion.CvesselSnapshotMetadata{
		Version:         req.Version,
		VersionType:     req.VersionType,
		SnapshotVersion: req.SnapshotVersion,
		PreVersion:      req.PreVersion,
		JDStackVersion:  req.JdstackVersion,
	}
	if f.OldVersion {
		if err := f.RenderCvesselMetadataOldVersion(metadata); err != nil {
			return nil, fmt.Errorf("RenderCvesselMetadata failed: %v", err)
		}
	} else {
		// NOTE: 需要在传输过来的产品中添加多种场景的配置文件
		/*
			- config
				- ./pd_dependencies.json
				- ./plugin_pd_dependencies.json
				- ./middlewares.json
				- ./jdstack/
					- ./pd_dependencies.json
					- ./plugin_pd_dependencies.json
					- ./middlewares.json
		*/
		if err := f.RenderCvesselMetadata(metadata); err != nil {
			return nil, fmt.Errorf("RenderCvesselMetadata failed: %v", err)
		}
	}

	return metadata, nil
}

func (f *FileDataSource) RenderCvesselMetadataOldVersion(metadata *moVersion.CvesselSnapshotMetadata) error {
	// 获取pd / upgrade内容
	err := f.ConfigAllSpecYamlsContent()
	if err != nil {
		return fmt.Errorf("ConfigAllSpecYamlsContent: %w", err)
	}
	// 获取全部的配置文件内容
	err = f.configAllConfigFiles()
	if err != nil {
		return fmt.Errorf("configAllConfigFiles: %w", err)
	}
	// 填充主集群产品信息 - 默认都是主集群
	err = f.FillWithProductInfoAndCategoryInfo()
	if err != nil {
		return fmt.Errorf("FillWithMasterClusterProductsOldVersion: %w", err)
	}
	err = f.FillWithClusterProductsDependency4OldVersion()
	if err != nil {
		return fmt.Errorf("FillWithClusterProductsDependency4OldVersion: %w", err)
	}
	metadata.Categories = f.Categories
	metadata.ProductsInfo = f.ProductsInfo
	metadata.ProductDependencyInfo4Standard = f.ProductDependencyInfo4Standard
	metadata.FusionScene = f.FusionScene // 如果老版本为融合场景,数据同样需要创建两份，一份根据JDStack版本Id去创建
	metadata.ProductDependencyInfo4Fusion = f.ProductDependencyInfo4Fusion
	metadata.CvesselConfigFilesMap = f.CvesselConfigFilesMap
	return nil
}

func (f *FileDataSource) RenderCvesselMetadata(metadata *moVersion.CvesselSnapshotMetadata) error {
	// 获取pd / upgrade / metadata内容
	err := f.ConfigAllSpecYamlsContent()
	if err != nil {
		return fmt.Errorf("ConfigAllSpecYamlsContent: %w", err)
	}
	// 获取全部的配置文件内容
	err = f.configAllConfigFiles()
	if err != nil {
		return fmt.Errorf("configAllConfigFiles: %w", err)
	}
	// 获取多场景下主子集群、中间件数据库依赖关系描述内容, 配置产品分类映射
	err = f.ConfigClusterAndMiddleWareDependencyMap()
	if err != nil {
		return fmt.Errorf("ConfigClusterAndMiddleWareDependencyMap: %w", err)
	}
	// 获取中间件数据库描述文件
	err = f.ConfigMiddlewareInfoMap()
	if err != nil {
		return fmt.Errorf("ConfigMiddlewareInfoMap: %w", err)
	}
	// FIXME: 获取主机层虚拟产品信息（需要注入sql,未通过天基维护,通过配置文件维护）
	// FIXME: 这个文件是非必须的，在容器云场景也需要，不再区分融合非融合
	err = f.ConfigHostLayerProductsInfoMap()
	if err != nil {
		return fmt.Errorf("ConfigHostLayerProductsInfoMap: %w", err)
	}
	// 填充产品分类集合以及全量pd类型的productInfos
	err = f.FillWithProductInfoAndCategoryInfo()
	if err != nil {
		return fmt.Errorf("FillWithProductInfoAndCategoryInfo: %w", err)
	}
	// 填充中间件虚拟产品 / 添加中间件依赖关系 - 需要放到配置产品依赖之前，否则虚拟产品不在实际的pds中
	err = f.FillWithMiddleWareAndMiddlewareDependency()
	if err != nil {
		return fmt.Errorf("FillWithMiddleWareAndMiddlewareDependency: %w", err)
	}
	// 填充多场景下的主子集群产品依赖
	err = f.FillWithClusterProductsDependency()
	if err != nil {
		return fmt.Errorf("FillWithClusterProductsDependency: %w", err)
	}
	// 填充主机层虚拟产品信息 - 无依赖,需要放在填充主子集群依赖之后，跳过校验，仅做productInfo数据的导入
	err = f.FillWithHostLayerProducts()
	if err != nil {
		return fmt.Errorf("FillWithHostLayerProducts: %w", err)
	}

	metadata.Categories = f.Categories
	metadata.ProductsInfo = f.ProductsInfo
	metadata.ProductBusinessInfo = f.ProductBusinessInfoMap
	metadata.ProductDependencyInfo4Standard = f.ProductDependencyInfo4Standard
	metadata.FusionScene = f.FusionScene
	metadata.ProductDependencyInfo4Fusion = f.ProductDependencyInfo4Fusion
	metadata.CvesselConfigFilesMap = f.CvesselConfigFilesMap
	return nil
}

func (f *FileDataSource) ConfigAllSpecYamlsContent() error {
	productYamlsContentMap := make(map[string]*string)
	productUpgradeYamlsContentMap := make(map[string]*string)
	productBusinessInfoMap := make(map[string]*moVersion.BusinessMetadataInfo)

	productsDir := filepath.Join(f.Dir, moVersion.ProductsDir)
	err := filepath.Walk(productsDir, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			return err
		}

		if !info.IsDir() && (info.Name() == moVersion.ProductYamlFile || info.Name() == moVersion.UpgradeYamlFile || strings.HasPrefix(info.Name(), moVersion.BusinessYamlFilePrefix)) {
			// 获取相对于 productsDir 的路径
			relPath, err := filepath.Rel(productsDir, filepath.Dir(path))
			if err != nil {
				return fmt.Errorf("failed to get relative path: %v", err)
			}

			// 使用第一级子目录作为 key (产品代码)
			parts := strings.Split(relPath, string(os.PathSeparator))
			if len(parts) == 0 {
				return fmt.Errorf("failed to get product code from path: %s", relPath)
			}
			productCode := parts[0]

			content, err := os.ReadFile(path)
			if err != nil {
				return fmt.Errorf("failed to ReadFile for %s: %v", path, err)
			}
			contentStr := string(content)

			switch {
			case info.Name() == moVersion.ProductYamlFile:
				productYamlsContentMap[productCode] = &contentStr
			case info.Name() == moVersion.UpgradeYamlFile:
				productUpgradeYamlsContentMap[productCode] = &contentStr
			case strings.HasPrefix(info.Name(), moVersion.BusinessYamlFilePrefix):
				// 检查路径中是否包含metadata目录
				if !strings.Contains(relPath, "metadata") {
					return fmt.Errorf("business metadata file not in metadata directory: %s", relPath)
				}

				// 解析metadata信息
				// parts格式应该是: [productCode, version, metadata, versionId, ...]
				if len(parts) < 4 {
					return fmt.Errorf("invalid business metadata path structure: %s", relPath)
				}

				versionId := parts[3]                                   // 获取versionId
				versionName := strings.TrimSuffix(info.Name(), ".yaml") // 从文件名获取versionName

				businessInfo := &moVersion.BusinessMetadataInfo{
					MetadataContent: &contentStr,
					VersionId:       versionId,
					VersionName:     versionName,
				}
				productBusinessInfoMap[productCode] = businessInfo
			}
		}
		return nil
	})

	if err != nil {
		return fmt.Errorf("error walking the path %s: %v", productsDir, err)
	}

	if len(productYamlsContentMap) == 0 {
		return fmt.Errorf("no product.yaml files found in %s", productsDir)
	}

	f.ProductYamlsContentMap = productYamlsContentMap
	f.ProductUpgradeYamlsContentMap = productUpgradeYamlsContentMap
	f.ProductBusinessInfoMap = productBusinessInfoMap
	return nil
}

func (f *FileDataSource) configAllConfigFiles() error {
	configFilesContentMap := make(map[string]*string)

	configFilesDir := filepath.Join(f.Dir, moVersion.ConfigsDir)
	err := filepath.Walk(configFilesDir, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			return err
		}
		if !info.IsDir() {
			content, err := os.ReadFile(path)
			if err != nil {
				return fmt.Errorf("failed to ReadFile for %s: %v", path, err)
			}
			contentStr := string(content)
			relPath, err := filepath.Rel(configFilesDir, path)
			if err != nil {
				return fmt.Errorf("failed to get relative path for %s: %v", path, err)
			}
			configFilesContentMap[relPath] = &contentStr
		}
		return nil
	})
	if err != nil {
		return fmt.Errorf("error walking the path %s: %v", configFilesDir, err)
	}
	f.CvesselConfigFilesMap = configFilesContentMap
	return nil
}

func (f *FileDataSource) ConfigClusterAndMiddleWareDependencyMap() error {
	getDepConfigFunc := func(fileName string) (map[string]*moVersion.ProductDependencyConfig, moVersion.MiddleWareDependencyConfig, error) {
		productDependencyJsonContent, err := getSpecFileContent(filepath.Join(f.Dir, moVersion.ConfigsDir), fileName)
		if err != nil {
			return nil, nil, fmt.Errorf("get %s pd dependencies: %w", fileName, err)
		}
		clusterProductDependency := new(moVersion.DependencyConfig)
		if err = json.Unmarshal([]byte(*productDependencyJsonContent), &clusterProductDependency); err != nil {
			return nil, nil, fmt.Errorf("unmarshal %s pd dependencies: %w", fileName, err)
		}
		clusterProductDependencyMap := make(map[string]*moVersion.ProductDependencyConfig)
		for _, productDepInfo := range clusterProductDependency.Products {
			if _, ok := clusterProductDependencyMap[productDepInfo.Id]; ok {
				return nil, nil, fmt.Errorf("duplicate %s pd dependencies id: %s", fileName, productDepInfo.Id)
			}
			clusterProductDependencyMap[productDepInfo.Id] = productDepInfo
		}
		return clusterProductDependencyMap, clusterProductDependency.MiddleWares, nil
	}

	// 标准交付场景
	masterClusterProductDependencyMap, middlewareDependencyMap, err := getDepConfigFunc(moVersion.PdDependenciesFile)
	if err != nil {
		return fmt.Errorf("get %s pd dependencies: %w", moVersion.PdDependenciesFile, err)
	}
	slaveClusterProductDependencyMap, _, err := getDepConfigFunc(moVersion.PluginPdDependenciesFile)
	if err != nil {
		return fmt.Errorf("get %s pd dependencies: %w", moVersion.PluginPdDependenciesFile, err)
	}
	f.DependencyMiddleWareConfig4Standard.MasterClusterProductDependencyMap = masterClusterProductDependencyMap
	f.DependencyMiddleWareConfig4Standard.MiddlewareDependencyMap = middlewareDependencyMap
	f.DependencyMiddleWareConfig4Standard.SlaveClusterProductDependencyMap = slaveClusterProductDependencyMap

	// 融合交付场景
	if f.FusionScene {
		masterClusterProductDependencyMap4Fusion, middlewareDependencyMap4Fusion, err := getDepConfigFunc(filepath.Join(moVersion.FusionCoonfigSubDir, moVersion.PdDependenciesFile))
		if err != nil {
			return fmt.Errorf("get fusion %s/%s pd dependencies: %w", moVersion.FusionCoonfigSubDir, moVersion.PdDependenciesFile, err)
		}
		slaveClusterProductDependencyMap4Fusion, _, err := getDepConfigFunc(filepath.Join(moVersion.FusionCoonfigSubDir, moVersion.PluginPdDependenciesFile))
		if err != nil {
			return fmt.Errorf("get fusion %s/%s pd dependencies: %w", moVersion.FusionCoonfigSubDir, moVersion.PluginPdDependenciesFile, err)
		}
		f.DependencyMiddleWareConfig4Fusion.MasterClusterProductDependencyMap = masterClusterProductDependencyMap4Fusion
		f.DependencyMiddleWareConfig4Fusion.MiddlewareDependencyMap = middlewareDependencyMap4Fusion
		f.DependencyMiddleWareConfig4Fusion.SlaveClusterProductDependencyMap = slaveClusterProductDependencyMap4Fusion
	}

	return nil
}

func (f *FileDataSource) ConfigMiddlewareInfoMap() error {
	// 标准场景
	middlewareJsonContent, err := getSpecFileContent(filepath.Join(f.Dir, moVersion.ConfigsDir), moVersion.MiddlewaresFile)
	if err != nil {
		return fmt.Errorf("get plugin pd dependencies: %w", err)
	}
	var middlewareMap moVersion.MiddleWareInfo
	if err = json.Unmarshal([]byte(*middlewareJsonContent), &middlewareMap); err != nil {
		return fmt.Errorf("unmarshal middleware pd dependencies: %w", err)
	}
	f.DependencyMiddleWareConfig4Standard.MiddlewareMap = middlewareMap

	// 融合场景
	if f.FusionScene {
		middlewareJsonContent4Fusion, err := getSpecFileContent(filepath.Join(f.Dir, moVersion.ConfigsDir, moVersion.FusionCoonfigSubDir), moVersion.MiddlewaresFile)
		if err != nil {
			return fmt.Errorf("get plugin pd dependencies: %w", err)
		}
		var middlewareMap4Fusion moVersion.MiddleWareInfo
		if err = json.Unmarshal([]byte(*middlewareJsonContent4Fusion), &middlewareMap4Fusion); err != nil {
			return fmt.Errorf("unmarshal middleware pd dependencies: %w", err)
		}
		f.DependencyMiddleWareConfig4Fusion.MiddlewareMap = middlewareMap4Fusion
	}

	return nil
}

func (f *FileDataSource) ConfigHostLayerProductsInfoMap() error {
	hostLayerProductJsonContent, err := getSpecFileContent(filepath.Join(f.Dir, moVersion.ConfigsDir), moVersion.HostLayerProductsFile)
	if err != nil {
		if strings.Contains(err.Error(), "not found") {
			return nil
		}
		return fmt.Errorf("获取主机层服务配置文件%s失败: %w", moVersion.HostLayerProductsFile, err)
	}
	var hostLayerProductInfoMap moVersion.HostLayerProductsInfo
	if err = json.Unmarshal([]byte(*hostLayerProductJsonContent), &hostLayerProductInfoMap); err != nil {
		return fmt.Errorf("unmarshal host layer pd dependencies: %w", err)
	}
	f.HostLayerProductInfoMap = hostLayerProductInfoMap
	return nil
}

func (f *FileDataSource) FillWithProductInfoAndCategoryInfo() error {
	f.Categories = make([]string, 0)
	categoriesMap := make(map[string]struct{})
	for productCode, content := range f.ProductYamlsContentMap {
		var spec moVersion.ProductYamlSpec
		if err := yaml.Unmarshal([]byte(*content), &spec); err != nil {
			return fmt.Errorf("unmarshal product spec: %w", err)
		}
		productInfo := &moVersion.CvesselProductInfo{
			Name:               productCode,
			ServiceCode:        spec.Metadata.ServiceCode,
			Virtual:            false,
			NameCN:             spec.Metadata.Name,
			Version:            spec.Metadata.Version,
			ProductYamlContent: content,
		}
		category, exist := getCategoryName(spec.Metadata.Catalog, spec.Metadata.AppType)
		if !exist {
			return fmt.Errorf("当前产品pd(%s)内存储的产品分类不存在: %s/%s", productCode, spec.Metadata.Catalog, spec.Metadata.AppType)
		}
		productInfo.Category = category
		categoriesMap[category] = struct{}{}
		if contentTmp, ok := f.ProductUpgradeYamlsContentMap[productCode]; ok {
			productInfo.ProductUpgradeYamlContent = contentTmp
		}
		if _, ok := f.ProductsInfo[productCode]; ok {
			return fmt.Errorf("duplicate product info: %s", productCode)
		}
		f.ProductsInfo[productCode] = productInfo
	}
	for category := range categoriesMap {
		f.Categories = append(f.Categories, category)
	}
	return nil
}

func (f *FileDataSource) FillWithClusterProductsDependency() error {
	// NOTE: 需要注意部分组件的部署无需依赖任意产品，它们需要以特殊的方式落入goods_chain（from_id配置为to_id），因为目前的goods_chain还承担部署类型及集群类型的功能
	for productCode := range f.ProductsInfo {
		// 处理标准云舰场景的主子集群依赖
		if _, ok := f.ProductDependencyInfo4Standard[productCode]; ok {
			continue
		}
		f.ProductDependencyInfo4Standard[productCode] = &moVersion.ClusterDependencyConfig{
			MasterDeploy:      false,
			SlaveDeploy:       false,
			MasterClusterDeps: make([]string, 0),
			SlaveClusterDeps:  make([]string, 0),
		}
		if masterDepConfig, ok := f.DependencyMiddleWareConfig4Standard.MasterClusterProductDependencyMap[productCode]; ok {
			depPds := make([]string, 0)
			for _, depPd := range masterDepConfig.Dependencies {
				_, ok = f.ProductsInfo[depPd]
				if !ok {
					return fmt.Errorf("standard master depPd not found in productInfos: %s", depPd)
				}
				depPds = append(depPds, depPd)
			}
			f.ProductDependencyInfo4Standard[productCode].MasterClusterDeps = depPds
			f.ProductDependencyInfo4Standard[productCode].MasterDeploy = true
		}
		if slaveDepConfig, ok := f.DependencyMiddleWareConfig4Standard.SlaveClusterProductDependencyMap[productCode]; ok {
			depPds := make([]string, 0)
			for _, depPd := range slaveDepConfig.Dependencies {
				_, ok = f.ProductsInfo[depPd]
				if !ok {
					return fmt.Errorf("standard slave depPd not found in productInfos: %s", depPd)
				}
				depPds = append(depPds, depPd)
			}
			f.ProductDependencyInfo4Standard[productCode].SlaveClusterDeps = depPds
			f.ProductDependencyInfo4Standard[productCode].SlaveDeploy = true
		}
	}
	if !f.FusionScene {
		return nil
	}
	for productCode := range f.ProductsInfo {
		// 处理融合场景的主子集群依赖
		if _, ok := f.ProductDependencyInfo4Fusion[productCode]; ok {
			continue
		}

		f.ProductDependencyInfo4Fusion[productCode] = &moVersion.ClusterDependencyConfig{
			MasterDeploy:      false,
			SlaveDeploy:       false,
			MasterClusterDeps: make([]string, 0),
			SlaveClusterDeps:  make([]string, 0),
		}
		if masterDepConfig, ok := f.DependencyMiddleWareConfig4Fusion.MasterClusterProductDependencyMap[productCode]; ok {
			depPds := make([]string, 0)
			for _, depPd := range masterDepConfig.Dependencies {
				_, ok = f.ProductsInfo[depPd]
				if !ok {
					return fmt.Errorf("fusion master depPd not found in productInfos: %s", depPd)
				}
				depPds = append(depPds, depPd)
			}
			f.ProductDependencyInfo4Fusion[productCode].MasterClusterDeps = depPds
			f.ProductDependencyInfo4Fusion[productCode].MasterDeploy = true
		}
		if slaveDepConfig, ok := f.DependencyMiddleWareConfig4Fusion.SlaveClusterProductDependencyMap[productCode]; ok {
			depPds := make([]string, 0)
			for _, depPd := range slaveDepConfig.Dependencies {
				_, ok = f.ProductsInfo[depPd]
				if !ok {
					return fmt.Errorf("fusion slave depPd not found in productInfos: %s", depPd)
				}
				depPds = append(depPds, depPd)
			}
			f.ProductDependencyInfo4Fusion[productCode].SlaveClusterDeps = depPds
			f.ProductDependencyInfo4Fusion[productCode].SlaveDeploy = true
		}
	}
	return nil
}

func (f *FileDataSource) FillWithClusterProductsDependency4OldVersion() error {
	// NOTE: 需要注意部分组件的部署无需依赖任意产品，它们需要以特殊的方式落入goods_chain（from_id配置为to_id），因为目前的goods_chain还承担部署类型及集群类型的功能
	// 针对3.7.0及之前的版本，在检索时必须依赖goods_chain信息作筛选，所以每个产品要创建自身依赖的数据
	// 仅设置MasterDeploy为true,在后续处理过程中会创建goods_chain（from_id配置为to_id）的数据
	for productCode := range f.ProductsInfo {
		// 处理标准云舰场景
		if _, ok := f.ProductDependencyInfo4Standard[productCode]; ok {
			continue
		}
		// 老版本的全部产品都设置为主集群部署
		f.ProductDependencyInfo4Standard[productCode] = &moVersion.ClusterDependencyConfig{
			MasterDeploy:      true,
			SlaveDeploy:       false,
			MasterClusterDeps: make([]string, 0),
			SlaveClusterDeps:  make([]string, 0),
		}
	}
	if !f.FusionScene {
		return nil
	}
	for productCode := range f.ProductsInfo {
		// 处理融合场景
		if _, ok := f.ProductDependencyInfo4Fusion[productCode]; ok {
			continue
		}
		// 老版本的全部产品都设置为主集群部署
		f.ProductDependencyInfo4Fusion[productCode] = &moVersion.ClusterDependencyConfig{
			MasterDeploy:      true,
			SlaveDeploy:       false,
			MasterClusterDeps: make([]string, 0),
			SlaveClusterDeps:  make([]string, 0),
		}
	}
	return nil
}

func (f *FileDataSource) FillWithHostLayerProducts() error {
	// 填充独立的主机层虚拟服务 - 不存在依赖关系
	category := moVersion.HostLayerCategory
	// 将虚拟产品的类型填充到分类汇总中
	f.Categories = append(f.Categories, category)
	for _, hostLayerProductInfo := range f.HostLayerProductInfoMap {
		productCode := hostLayerProductInfo.ID
		productInfo := &moVersion.CvesselProductInfo{
			Name:        productCode,
			ServiceCode: "host_layer", // FIXME: 这几类主机产品暂时使用产品"host_layer"代替serviceCode
			Virtual:     true,
			DummyType:   moVersion.DummyType_host,
			NameCN:      hostLayerProductInfo.Name,
			Version:     hostLayerProductInfo.Version,
			Category:    category,
		}
		if _, ok := f.ProductsInfo[productCode]; ok {
			return fmt.Errorf("duplicate product info: %s", productCode)
		}
		f.ProductsInfo[productCode] = productInfo
		// FIXME: 填充主机层依赖关系 - 区分场景
		if _, ok := f.ProductDependencyInfo4Standard[productCode]; !ok {
			f.ProductDependencyInfo4Standard[productCode] = &moVersion.ClusterDependencyConfig{
				MasterDeploy:      true,
				MasterClusterDeps: make([]string, 0),
				SlaveClusterDeps:  make([]string, 0),
			}
		}
		if _, ok := f.ProductDependencyInfo4Fusion[productCode]; !ok {
			f.ProductDependencyInfo4Fusion[productCode] = &moVersion.ClusterDependencyConfig{
				MasterDeploy:      true,
				MasterClusterDeps: make([]string, 0),
				SlaveClusterDeps:  make([]string, 0),
			}
		}
	}
	return nil
}

func (f *FileDataSource) FillWithMiddleWareAndMiddlewareDependency() error {
	// 填充数据库虚拟产品信息 / 优先填充数据库虚拟产品对实际pd的依赖关系
	category := moVersion.MiddlewareCategory
	// 将虚拟产品的类型填充到分类汇总中
	f.Categories = append(f.Categories, category)
	// 处理标准交付场景
	for dummyType, middlewares := range f.DependencyMiddleWareConfig4Standard.MiddlewareMap {
		for _, middleware := range middlewares {
			if middleware.Provider != moVersion.MiddlewareOpenAPIProvider {
				continue
			}
			depPds := make([]string, 0)
			// 关于openapi类型的中间件声明的依赖配置（中间件声明依赖于哪些pd产品），统一维护在pd_dependency.json中
			if middlewareDepPds, ok := f.DependencyMiddleWareConfig4Standard.MiddlewareDependencyMap[dummyType]; ok {
				for _, middlewareDepPd := range middlewareDepPds {
					_, ok = f.ProductsInfo[middlewareDepPd]
					if !ok {
						return fmt.Errorf("middileware depend not found in productsInfos: %s", middlewareDepPd)
					}
					depPds = append(depPds, middlewareDepPd)
				}
			}
			productCode := middleware.ID
			productInfo := &moVersion.CvesselProductInfo{
				Name:        productCode,
				ServiceCode: dummyType, // FIXME: 这几类中间件数据库产品暂时使用dummyType代替serviceCode
				Virtual:     true,
				DummyType:   moVersion.GetMiddlewareDummyType(dummyType),
				NameCN:      productCode,
				Version:     middleware.Version,
				Category:    category,
			}
			if _, ok := f.ProductsInfo[productCode]; ok {
				return fmt.Errorf("声明的openapi中间件的唯一Id与当前版本内产品唯一Id冲突,请修改: %s", productCode)
			}
			f.ProductsInfo[productCode] = productInfo
			// 优先将数据库的依赖关系填充
			if _, ok := f.ProductDependencyInfo4Standard[productCode]; !ok {
				f.ProductDependencyInfo4Standard[productCode] = &moVersion.ClusterDependencyConfig{
					MasterClusterDeps: make([]string, 0),
					SlaveClusterDeps:  make([]string, 0),
				}
				f.ProductDependencyInfo4Standard[productCode].MasterClusterDeps = depPds
				f.ProductDependencyInfo4Standard[productCode].MasterDeploy = true
			}
		}
	}
	if !f.FusionScene {
		return nil
	}
	// 处理融合场景
	for dummyType, middlewares := range f.DependencyMiddleWareConfig4Fusion.MiddlewareMap {
		for _, middleware := range middlewares {
			if middleware.Provider != moVersion.MiddlewareOpenAPIProvider {
				continue
			}
			if _, ok := f.ProductsInfo[middleware.ID]; ok {
				// 认定处理逻辑与标准场景一致
				continue
			}
			depPds := make([]string, 0)
			// 关于openapi类型的中间件声明的依赖配置（中间件声明依赖于哪些pd产品），统一维护在pd_dependency.json中
			if middlewareDepPds, ok := f.DependencyMiddleWareConfig4Standard.MiddlewareDependencyMap[dummyType]; ok {
				for _, middlewareDepPd := range middlewareDepPds {
					_, ok = f.ProductsInfo[middlewareDepPd]
					if !ok {
						return fmt.Errorf("middileware depend not found in productsInfos: %s", middlewareDepPd)
					}
					depPds = append(depPds, middlewareDepPd)
				}
			}
			productCode := middleware.ID
			productInfo := &moVersion.CvesselProductInfo{
				Name:        productCode,
				ServiceCode: dummyType, // FIXME: 这几类中间件数据库产品暂时使用dummyType代替serviceCode
				Virtual:     true,
				DummyType:   moVersion.GetMiddlewareDummyType(dummyType),
				NameCN:      productCode,
				Version:     middleware.Version,
				Category:    category,
			}
			if _, ok := f.ProductsInfo[productCode]; ok {
				return fmt.Errorf("声明的openapi中间件的唯一Id与当前版本内产品唯一Id冲突,请修改: %s", productCode)
			}
			f.ProductsInfo[productCode] = productInfo
			// 优先将数据库的依赖关系填充
			if _, ok := f.ProductDependencyInfo4Fusion[productCode]; !ok {
				f.ProductDependencyInfo4Fusion[productCode] = &moVersion.ClusterDependencyConfig{
					MasterClusterDeps: make([]string, 0),
					SlaveClusterDeps:  make([]string, 0),
				}
				f.ProductDependencyInfo4Fusion[productCode].MasterClusterDeps = depPds
				f.ProductDependencyInfo4Fusion[productCode].MasterDeploy = true
			}
		}
	}
	return nil
}

func getSpecFileContent(fileDir string, fileName string) (*string, error) {
	filePath := filepath.Join(fileDir, fileName)

	content, err := os.ReadFile(filePath)
	if err != nil {
		if os.IsNotExist(err) {
			return nil, fmt.Errorf("file %s not found in %s", fileName, fileDir)
		}
		return nil, fmt.Errorf("failed to read file %s: %v", filePath, err)
	}

	contentStr := string(content)
	return &contentStr, nil
}

func unpackTarGzFile(data []byte, destDir string) error {
	// 确保目标目录存在
	if err := os.MkdirAll(destDir, 0755); err != nil {
		return fmt.Errorf("failed to create destination directory: %w", err)
	}

	gzr, err := gzip.NewReader(bytes.NewReader(data))
	if err != nil {
		return fmt.Errorf("failed to create gzip reader: %w", err)
	}
	defer gzr.Close()

	tr := tar.NewReader(gzr)

	for {
		header, err := tr.Next()
		if err == io.EOF {
			break
		}
		if err != nil {
			return fmt.Errorf("tar reading error: %w", err)
		}

		target := filepath.Join(destDir, header.Name)

		// 安全检查：确保文件不会被解压到目标目录之外
		if !strings.HasPrefix(target, filepath.Clean(destDir)+string(os.PathSeparator)) {
			return fmt.Errorf("invalid file path: %s", target)
		}

		switch header.Typeflag {
		case tar.TypeDir:
			if err := os.MkdirAll(target, os.FileMode(header.Mode)); err != nil {
				return fmt.Errorf("failed to create directory %s: %w", target, err)
			}
		case tar.TypeReg:
			// 确保父目录存在
			dir := filepath.Dir(target)
			if err := os.MkdirAll(dir, 0755); err != nil {
				return fmt.Errorf("failed to create parent directory for %s: %w", target, err)
			}

			f, err := os.OpenFile(target, os.O_CREATE|os.O_RDWR, os.FileMode(header.Mode))
			if err != nil {
				return fmt.Errorf("failed to create file %s: %w", target, err)
			}

			_, err = io.Copy(f, tr)
			f.Close() // 确保文件被关闭，即使发生错误
			if err != nil {
				return fmt.Errorf("failed to write to file %s: %w", target, err)
			}
		default:
			return fmt.Errorf("unsupported file type for %s in tar", target)
		}
	}

	return nil
}

func getCategoryName(firstLevel, secondLevel string) (string, bool) {
	if secondLevelMap, ok := categoryMap[firstLevel]; ok {
		if name, exists := secondLevelMap[secondLevel]; exists {
			return name, true
		}
	}
	return "", false
}

var categoryMap = map[string]map[string]string{
	"store": {
		"databasecache": "云产品/数据库与缓存",
		"middleware":    "云产品/中间件",
		"cloudnative":   "云产品/云原生",
		"security":      "云产品/安全中心",
		"opmanage":      "云产品/运维中心",
		"appdev":        "云产品/应用中心",
		"others":        "云产品/其他",
		"migration":     "云产品/迁移中心",
	},
	"systemApplication": {
		"network":  "系统组件/网络",
		"storage":  "系统组件/存储",
		"others":   "系统组件/其他",
		"opmanage": "系统组件/运维管理",
	},
	"productPlugin": {
		"databasecache":   "产品插件/数据库与缓存",
		"middleware":      "产品插件/中间件",
		"cloudnative":     "产品插件/云原生",
		"security":        "产品插件/安全中心",
		"opmanage":        "产品插件/运维中心",
		"appdev":          "产品插件/应用中心",
		"others":          "产品插件/其他",
		"migration":       "产品插件/迁移中心",
		"serviceexposure": "产品插件/服务暴露",
	},
	"base": {
		"basemiddleware": "云舰底座/基础中间件",
		"baseservice":    "云舰底座/基础服务",
		"baseops":        "云舰底座/平台运维",
		"biz":            "云舰底座/商业平台",
	},
	"jdstack": {
		"network":   "专有云/虚拟网络产品",
		"manage":    "专有云/管理产品",
		"database":  "专有云/云数据库产品",
		"aidc":      "专有云/智能AIDC产品",
		"domain":    "专有云/域名与备案产品",
		"cloudpc":   "专有云/云电脑",
		"basic":     "专有云/基础服务",
		"ops":       "专有云/运维管理",
		"security":  "专有云/安全产品",
		"compute":   "专有云/弹性计算产品",
		"monitor":   "专有云/监控与运维产品",
		"storage":   "专有云/存储产品",
		"public":    "专有云/公共服务",
		"operation": "专有云/运营管理",
	},
}
