package bizJDStackDeliveryRule

import (
	"github.com/openlyinc/pointy"

	pbGoodsDeliveryRule "coding.jd.com/fabric/zeusV2/zeus/api/server/pb/goods_delivery_rule/v1"
	"coding.jd.com/fabric/zeusV2/zeus/internal/server/data/db_master/dao"
	jDto "coding.jd.com/pcd-application/win-go/dto"
	jErr "coding.jd.com/pcd-application/win-go/error"
)

// 处理规则结构体：对相同规则下的角色标签进行追加处理
func (uc *jdstackDeliveryRuleUsecase) mr2DeliveryRule(rules []*dao.DeliveryRule, mrRules []*dao.DeliveryRule) []*dao.DeliveryRule {
	if len(mrRules) == 0 {
		return rules
	}

	if len(rules) == 0 {
		rules = mrRules
		return rules
	}

	for mrIdx, _ := range mrRules {
		if len(mrRules[mrIdx].Edges.HardwareRole) == 0 && len(mrRules[mrIdx].Edges.HardwareRoleLabel) == 0 {
			continue
		}
		isExist := false
		for idx, _ := range rules {
			if rules[idx].ID == mrRules[mrIdx].ID {
				rules[idx].Edges.HardwareRole = append(rules[idx].Edges.HardwareRole, mrRules[mrIdx].Edges.HardwareRole...)
				rules[idx].Edges.HardwareRoleLabel = append(rules[idx].Edges.HardwareRoleLabel, mrRules[mrIdx].Edges.HardwareRoleLabel...)
				isExist = true
			}
		}
		if !isExist {
			rules = append(rules, mrRules[mrIdx])
		}
	}

	return rules
}

// DeliveryRuleGroup2DeliveryRule
// 交付规则组处理原则:
// (1)同一种类型的规则,最多存在一条;
// (2)根据权重进行互斥规则的覆盖
func (uc *jdstackDeliveryRuleUsecase) DeliveryRuleGroup2DeliveryRule(
	node *dao.Goods,
) (deliveryRule []*pbGoodsDeliveryRule.DescribeReply, err jErr.Error) {
	if len(node.Edges.GoodsDeliveryRuleGroup) == 0 {
		return
	}
	deliveryRuleMap := make(map[string]*pbGoodsDeliveryRule.DescribeReply)
	// 交付规则组处理：同一种类型的规则,最多存在一条
	for _, drg := range node.Edges.GoodsDeliveryRuleGroup {
		for _, dr := range drg.Edges.GoodsDeliveryRule {
			if _, ok := deliveryRuleMap[dr.Rule.String()]; !ok {
				deliveryRuleMap[dr.Rule.String()] = jDto.UseDTO(uc.DTO, dr, (*pbGoodsDeliveryRule.DescribeReply)(nil))
				continue
			}
			// 同一种规则,按权重进行优先覆盖
			switch {
			case deliveryRuleMap[dr.Rule.String()].Weight < pointy.Int32Value(dr.Weight, -1):
				deliveryRuleMap[dr.Rule.String()] = jDto.UseDTO(uc.DTO, dr, (*pbGoodsDeliveryRule.DescribeReply)(nil))
			case deliveryRuleMap[dr.Rule.String()].Weight == pointy.Int32Value(dr.Weight, -1):
				if deliveryRuleMap[dr.Rule.String()].Id == dr.ID {
					continue
				}
				// 存在同一种类型的规则，且权重一致,则报错
				return nil, jErr.NewMessagef(Self, "标品<%s>关联了相同类型、相同权重的交付规则<规则ID:[%d,%d]>,请确认数据是否正确!",
					node.ID, deliveryRuleMap[dr.Rule.String()].Id, dr.ID)
			default: // ignore
			}
		}
	}
	// 交付规则组处理：根据权重进行互斥规则的覆盖
	for mk1, _ := range deliveryRuleMap {
		mk2 := CheckMutexDeliveryRule(mk1)
		if _, isExist := deliveryRuleMap[mk2]; !isExist {
			continue
		}
		switch {
		case deliveryRuleMap[mk1].Weight == deliveryRuleMap[mk2].Weight:
			// 存在互斥规则且权重一致,则报错
			return nil, jErr.NewMessagef(Self, "标品[%s]关联了互斥且权重相同的交付规则<规则ID:[%d,%d]>,请确认数据是否正确!",
				node.ID, deliveryRuleMap[mk1].Id, deliveryRuleMap[mk2].Id)
		case deliveryRuleMap[mk1].Weight < deliveryRuleMap[mk2].Weight:
			delete(deliveryRuleMap, mk1)
		case deliveryRuleMap[mk1].Weight > deliveryRuleMap[mk2].Weight:
			delete(deliveryRuleMap, mk2)
		default: // ignore
		}
	}

	deliveryRule = make([]*pbGoodsDeliveryRule.DescribeReply, 0, len(deliveryRuleMap))
	for _, dr := range deliveryRuleMap {
		deliveryRule = append(deliveryRule, dr)
	}

	return
}
