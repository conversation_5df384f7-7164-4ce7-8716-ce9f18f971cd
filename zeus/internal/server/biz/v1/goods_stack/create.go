package bizGoodsStack

import (
	"context"
	"fmt"
	"strings"

	"github.com/openlyinc/pointy"

	jPolicy "coding.jd.com/pcd-application/win-go/biz_policy"
	jErr "coding.jd.com/pcd-application/win-go/error"
	"coding.jd.com/pcd-application/win-go/third_party/winapi"

	pbGoodsStack "coding.jd.com/fabric/zeusV2/zeus/api/server/pb/goods_stack/v1"
	"coding.jd.com/fabric/zeusV2/zeus/internal/server/data/db_master/dao"
	"coding.jd.com/fabric/zeusV2/zeus/internal/server/data/db_master/dao/goods"
	"coding.jd.com/fabric/zeusV2/zeus/internal/server/data/db_master/dao/goodsconfig"
	"coding.jd.com/fabric/zeusV2/zeus/internal/server/data/db_master/dao/metadataapplication"
	"coding.jd.com/fabric/zeusV2/zeus/internal/server/data/db_master/dao/metadatacategory"
	"coding.jd.com/fabric/zeusV2/zeus/internal/server/data/db_master/dao/metadataresource"
	"coding.jd.com/fabric/zeusV2/zeus/internal/server/data/db_master/dao/metadataservice"
)

var (
	// 允许前端调用 createGoods 策略淘汰关联的下级ID请按照名称顺序填写，然后修改goodsRepo.deleteEdgesTx
	goodsFKDMap = map[string]string{
		"FKD:GoodsDelivery":               goods.GoodsDeliveryTable,
		"FKD:GoodsDomain":                 goods.GoodsDomainTable,
		"FKD:GoodsFilecenter":             goods.GoodsFilecenterTable,
		"FKD:GoodsFlavor":                 goods.GoodsFlavorTable,
		"FKD:GoodsIac":                    goods.GoodsIacTable,
		"FKD:GoodsPerformanceMeasurement": goods.GoodsPerformanceMeasurementTable,
		"FKD:GoodsQPSGrowth":              goods.GoodsQPSGrowthTable,
		"FKD:GoodsDeliveryRuleGroup":      goods.GoodsDeliveryRuleGroupTable,
		"FKD:GoodsMiddleware":             goods.GoodsMiddlewareTable,
		"FKD:HardwareRole":                goods.HardwareRoleTable,
		"FKD:HardwareRoleLabel":           goods.HardwareRoleLabelTable,
	}
)

func (uc *goodsStackUsecase) Create(ctx context.Context, req *pbGoodsStack.CreateReq) (err jErr.Error) {
	if req.CloudType == "" {
		req.CloudType = goods.DefaultCloudType.String()
	}

	if req.Metadata != nil {
		if req.Metadata.Category != nil {
			req.Metadata.Category.CloudType = req.CloudType
		}
		if req.Metadata.Service != nil {
			req.Metadata.Service.CloudType = req.CloudType
		}
		if req.Metadata.Resource != nil {
			req.Metadata.Resource.CloudType = req.CloudType
		}
		if req.Metadata.Application != nil {
			req.Metadata.Application.CloudType = req.CloudType
		}

		mRes, err := uc.MetadataCase.Create(ctx, req.Metadata)
		if err != nil {
			return err
		}
		if mRes.Category != nil {
			req.Category = mRes.Category.NameCn
		}
		if mRes.Service != nil {
			req.Service = mRes.Service.Name
		}
		if mRes.Resource != nil {
			req.Resource = mRes.Resource.Name
		}
		if mRes.Application != nil {
			req.Application = mRes.Application.Name
		}
	}

	var node *dao.Goods
	policy := jPolicy.NewPolicyStatus(req.Policy, jPolicy.FKDMap(goodsFKDMap))
	if req.Application != "" {
		if req.Metadata != nil && req.Metadata.Application.DeployMode != "" {
			if req.Config == nil {
				req.Config = []*pbGoodsStack.CreateConfigReq{
					&pbGoodsStack.CreateConfigReq{
						Kind: goodsconfig.KindDeployType.String(),
						Data: req.Metadata.Application.DeployMode,
					},
				}
			} else {
				req.Config = append(req.Config, &pbGoodsStack.CreateConfigReq{
					Kind: goodsconfig.KindDeployType.String(),
					Data: req.Metadata.Application.DeployMode,
				})
			}

		}
		node, err = uc.fetchOrCreateGoodsApplication(ctx, req, policy)
	} else if req.Resource != "" {
		node, err = uc.fetchOrCreateGoodsResource(ctx, req, policy)
	} else if req.Service != "" {
		node, err = uc.fetchOrCreateGoodsService(ctx, req, policy)
	} else if req.Category != "" {
		node, err = uc.fetchOrCreateGoodsCategory(ctx, req, policy)
	} else {
		return jErr.NewMessage(Self, "未指定分类、产品、服务、应用")
	}

	if err != nil {
		return err
	}

	err = jErr.NewEmpty(Self, "创建失败")
	if node != nil {
		if req.Category != "" && (node.Edges.MetadataCategory == nil || req.Category != *node.Edges.MetadataCategory.NameCn) {
			if node.Edges.MetadataCategory == nil {
				err = err.AddReasonf("unknown category name '%s'", req.Category)
			} else {
				err = err.AddReasonf("category name '%s' differs from associated name '%s'", req.Category, *node.Edges.MetadataCategory.NameCn)
			}
		}
		if req.Service != "" && (node.Edges.MetadataService == nil || req.Service != *node.Edges.MetadataService.Name) {
			if node.Edges.MetadataService == nil {
				err = err.AddReasonf("unknown service name '%s'", req.Service)
			} else {
				err = err.AddReasonf("service name '%s' differs from associated name '%s'", req.Service, *node.Edges.MetadataService.Name)
			}
		}
		if req.Resource != "" && (node.Edges.MetadataResource == nil || req.Resource != *node.Edges.MetadataResource.Name) {
			if node.Edges.MetadataResource == nil {
				err = err.AddReasonf("unknown resource name '%s'", req.Resource)
			} else {
				err = err.AddReasonf("resource name '%s' differs from associated name '%s'", req.Resource, *node.Edges.MetadataResource.Name)
			}
		}
		if req.Application != "" && (node.Edges.MetadataApplication == nil || req.Application != *node.Edges.MetadataApplication.Name) {
			if node.Edges.MetadataApplication == nil {
				err = err.AddReasonf("unknown application name '%s'", req.Application)
			} else {
				err = err.AddReasonf("application name '%s' differs from associated name '%s'", req.Application, *node.Edges.MetadataApplication.Name)
			}
		}
	}
	return err.Get()
}

func (uc *goodsStackUsecase) fetchOrCreateGoodsCategory(ctx context.Context, req *pbGoodsStack.CreateReq, policy *jPolicy.PolicyStatus) (node *dao.Goods, err jErr.Error) {
	if req.Category == "" {
		return nil, jErr.NewMessage(Self, "category name must exists")
	}

	filters := winapi.Filters{
		&winapi.Filter{Name: "version", Values: []string{req.Version}},
		&winapi.Filter{Name: winapi.EmptyValue, Values: []string{
			goods.FilterWithJdstackRelease,
			goods.FilterWithMetadataCategory,
		}},
		&winapi.Filter{Name: "category", Values: []string{req.Category}},
		&winapi.Filter{Name: winapi.MakeFilterName(goods.FilterHasMetadataCategoryWith, metadatacategory.FieldCloudType), Values: []string{req.CloudType}},
		&winapi.Filter{Name: goods.FieldServiceID, Values: []string{"0"}},
		// &winapi.Filter{Name: goods.FieldResourceID, Values: []string{"0"}},
		// &winapi.Filter{Name: goods.FieldApplicationID, Values: []string{"0"}},
		&winapi.Filter{Name: goods.FieldCloudType, Values: []string{req.CloudType}},
	}
	node, _ = uc.GoodsRepo.Get(ctx, filters...)
	if node != nil && (policy == nil || !policy.OnOverrideAction) {
		return
	}

	var newData *dao.Goods
	if node != nil {
		newData = &dao.Goods{ID: node.ID}
	} else {
		newData = &dao.Goods{}
		if newData.Edges.JdstackRelease, err = uc.JdstackReleaseRepo.GetName(ctx, req.Version); err != nil {
			return
		}
		if newData.Edges.MetadataCategory, err = uc.MetadataCategoryRepo.GetName(ctx, req.Category, &winapi.Filter{
			Name:   metadatacategory.FieldCloudType,
			Values: []string{req.CloudType},
		}); err != nil {
			return
		}
	}

	if newData.ID == "" || (policy != nil && policy.OnOverrideAction) {
		var goodsData *dao.Goods
		goodsData, err = uc.CreateReq2Goods(ctx, req, policy, newData)
		if err != nil {
			return nil, err
		}
		if _, err = uc.GoodsRepo.PolicyOperate(ctx, goodsData, policy); err != nil {
			return nil, err
		}
	}
	return uc.fetchOrCreateGoodsCategory(ctx, req, nil)
}

func (uc *goodsStackUsecase) fetchOrCreateGoodsService(ctx context.Context, req *pbGoodsStack.CreateReq, policy *jPolicy.PolicyStatus) (node *dao.Goods, err jErr.Error) {
	if req.Service == "" {
		return nil, jErr.NewMessage(Self, "service name must exists")
	}

	filters := winapi.Filters{
		&winapi.Filter{Name: "version", Values: []string{req.Version}},
		&winapi.Filter{Name: winapi.EmptyValue, Values: []string{
			goods.FilterWithJdstackRelease,
			goods.FilterWithMetadataCategory,
			goods.FilterWithMetadataService,
		}},
		&winapi.Filter{Name: "service", Values: []string{req.Service}},
		// FIXME: 添加之后在申请封版之后无法过滤应用信息，先去掉，待排查
		&winapi.Filter{Name: "category", Values: []string{req.Category}}, // 不要过滤category，是为了支持策略覆盖
		&winapi.Filter{Name: winapi.MakeFilterName(goods.FilterHasMetadataServiceWith, metadataservice.FieldCloudType), Values: []string{req.CloudType}},
		&winapi.Filter{Name: goods.FieldResourceID, Values: []string{"0"}},
		// &winapi.Filter{Name: goods.FieldApplicationID, Values: []string{"0"}},
		&winapi.Filter{Name: goods.FieldCloudType, Values: []string{req.CloudType}},
	}
	node, _ = uc.GoodsRepo.Get(ctx, filters...)
	if node != nil && (policy == nil || !policy.OnOverrideAction) {
		return
	}

	var newData *dao.Goods
	if node != nil && node.Edges.MetadataCategory != nil &&
		(req.Category == "" || *node.Edges.MetadataCategory.NameCn == req.Category) {
		newData = &dao.Goods{ID: node.ID}
	} else {
		node, err = uc.fetchOrCreateGoodsCategory(ctx, &pbGoodsStack.CreateReq{
			Version:      req.Version,
			Policy:       req.Policy,
			Category:     req.Category,
			Action:       req.Action,
			VirtualGoods: req.VirtualGoods,
			CloudType:    req.CloudType,
		}, nil)
		if err != nil {
			return
		}

		err = jErr.NewEmpty(Self, "查询或创建失败")
		if node.Edges.MetadataCategory == nil {
			err = err.AddReasonf("unknown category name '%s'", req.Category)
		} else if req.Category != pointy.StringValue(node.Edges.MetadataCategory.NameCn, "") {
			err = err.AddReasonf("category name '%s' differs from associated name '%s'", req.Category, pointy.StringValue(node.Edges.MetadataCategory.NameCn, ""))
		}
		if err.Exists() {
			return node, err.Get()
		}

		newData = &dao.Goods{
			Edges: dao.GoodsEdges{
				JdstackRelease:   node.Edges.JdstackRelease,
				MetadataCategory: node.Edges.MetadataCategory,
			},
		}
		if newData.Edges.MetadataService, err = uc.MetadataServiceRepo.GetName(ctx, req.Service, &winapi.Filter{
			Name:   metadataservice.FieldCloudType,
			Values: []string{req.CloudType},
		}); err != nil {
			return
		}
	}

	if newData.ID == "" || (policy != nil && policy.OnOverrideAction) {
		var goodsData *dao.Goods
		goodsData, err = uc.CreateReq2Goods(ctx, req, policy, newData)
		if err != nil {
			return nil, err
		}
		if _, err = uc.GoodsRepo.PolicyOperate(ctx, goodsData, policy); err != nil {
			return nil, err
		}
	}
	return uc.fetchOrCreateGoodsService(ctx, req, nil)
}

func (uc *goodsStackUsecase) fetchOrCreateGoodsResource(ctx context.Context, req *pbGoodsStack.CreateReq, policy *jPolicy.PolicyStatus) (node *dao.Goods, err jErr.Error) {
	if req.Resource == "" {
		return nil, jErr.NewMessage(Self, "resource name must exists")
	}

	filters := winapi.Filters{
		&winapi.Filter{Name: "version", Values: []string{req.Version}},
		&winapi.Filter{Name: winapi.EmptyValue, Values: []string{
			goods.FilterWithJdstackRelease,
			goods.FilterWithMetadataCategory,
			goods.FilterWithMetadataService,
			goods.FilterWithMetadataResource,
			goods.FilterWithGoodsChainFrom,
		}},
		&winapi.Filter{Name: "resource", Values: []string{req.Resource}},
		// FIXME: 添加之后在申请封版之后无法过滤应用信息，先去掉，待排查
		&winapi.Filter{Name: "service", Values: []string{req.Service}},
		&winapi.Filter{Name: "category", Values: []string{req.Category}}, // 不要过滤category、sc，是为了支持策略覆盖
		&winapi.Filter{Name: winapi.MakeFilterName(goods.FilterHasMetadataResourceWith, metadataresource.FieldCloudType), Values: []string{req.CloudType}},
		&winapi.Filter{Name: goods.FieldApplicationID, Values: []string{"0"}},
		&winapi.Filter{Name: goods.FieldCloudType, Values: []string{req.CloudType}},
	}
	node, _ = uc.GoodsRepo.Get(ctx, filters...)
	if node != nil && (policy == nil || !policy.OnOverrideAction) {
		return
	}
	// 仅执行下线操作时生效
	if req.Action == goods.ActionOffline.String() && node != nil && len(node.Edges.GoodsChainFrom) > 0 {
		goodsIds := make([]string, 0)
		for _, v := range node.Edges.GoodsChainFrom {
			if v.Edges.Goods != nil && v.Edges.Goods.Action != nil && *v.Edges.Goods.Action == goods.ActionOnline {
				if v.ToID != nil {
					goodsIds = append(goodsIds, *v.ToID)
				}
			}
		}
		if len(goodsIds) > 0 {
			return node, jErr.NewMessage(Self, fmt.Sprintf("当前resource被其他online的标品依赖，请确认是否删除依赖关系，再执行操作: %s", strings.Join(goodsIds, ",")))
		}
	}

	var newData *dao.Goods
	if node != nil && node.Edges.MetadataService != nil && node.Edges.MetadataCategory != nil &&
		(req.Service == "" || *node.Edges.MetadataService.Name == req.Service) &&
		(req.Category == "" || *node.Edges.MetadataCategory.NameCn == req.Category) {
		newData = &dao.Goods{ID: node.ID}
	} else {
		node, err = uc.fetchOrCreateGoodsService(ctx, &pbGoodsStack.CreateReq{
			Version:      req.Version,
			Policy:       req.Policy,
			Category:     req.Category,
			Service:      req.Service,
			Action:       req.Action,
			VirtualGoods: req.VirtualGoods,
			CloudType:    req.CloudType,
		}, nil)
		if err != nil {
			return
		}

		err = jErr.NewEmpty(Self, "查询或创建失败")
		if node.Edges.MetadataCategory == nil {
			err = err.AddReasonf("unknown category name '%s'", req.Category)
		} else if req.Category != pointy.StringValue(node.Edges.MetadataCategory.NameCn, "") {
			err = err.AddReasonf("category name '%s' differs from associated name '%s'", req.Category, pointy.StringValue(node.Edges.MetadataCategory.NameCn, ""))
		}

		if node.Edges.MetadataService == nil {
			err = err.AddReasonf("unknown service name '%s'", req.Service)
		} else if req.Service != pointy.StringValue(node.Edges.MetadataService.Name, "") {
			err = err.AddReasonf("service name '%s' differs from associated name '%s'", req.Service, pointy.StringValue(node.Edges.MetadataService.Name, ""))
		}
		if err.Exists() {
			return node, err.Get()
		}

		newData = &dao.Goods{
			Edges: dao.GoodsEdges{
				JdstackRelease:   node.Edges.JdstackRelease,
				MetadataCategory: node.Edges.MetadataCategory,
				MetadataService:  node.Edges.MetadataService,
			},
		}
		if newData.Edges.MetadataResource, err = uc.MetadataResourceRepo.GetName(ctx, req.Resource, &winapi.Filter{
			Name:   metadataresource.FieldCloudType,
			Values: []string{req.CloudType},
		}); err != nil {
			return
		}
	}

	if newData.ID == "" || (policy != nil && policy.OnOverrideAction) {
		var goodsData *dao.Goods
		goodsData, err = uc.CreateReq2Goods(ctx, req, policy, newData)
		if err != nil {
			return nil, err
		}
		if _, err = uc.GoodsRepo.PolicyOperate(ctx, goodsData, policy); err != nil {
			return nil, err
		}
	}
	return uc.fetchOrCreateGoodsResource(ctx, req, nil)
}

func (uc *goodsStackUsecase) fetchOrCreateGoodsApplication(ctx context.Context, req *pbGoodsStack.CreateReq, policy *jPolicy.PolicyStatus) (node *dao.Goods, err jErr.Error) {
	if req.Application == "" {
		return nil, jErr.NewMessage(Self, "application name must exists")
	}

	filters := winapi.Filters{
		&winapi.Filter{Name: "version", Values: []string{req.Version}},
		&winapi.Filter{Name: winapi.EmptyValue, Values: []string{
			goods.FilterWithJdstackRelease,
			goods.FilterWithMetadataCategory,
			goods.FilterWithMetadataService,
			goods.FilterWithMetadataResource,
			goods.FilterWithMetadataApplication,
		}},
		&winapi.Filter{Name: "application", Values: []string{req.Application}}, // 不要过滤category、sc、rc，是为了支持策略覆盖
		&winapi.Filter{Name: "resource", Values: []string{req.Resource}},
		// FIXME: 添加之后在申请封版之后无法过滤应用信息，先去掉，待排查
		&winapi.Filter{Name: "service", Values: []string{req.Service}},
		&winapi.Filter{Name: "category", Values: []string{req.Category}}, // 过滤产品，因为一个应用可能对应多个产品
		&winapi.Filter{Name: winapi.MakeFilterName(goods.FilterHasMetadataApplicationWith, metadataapplication.FieldCloudType), Values: []string{req.CloudType}},
		&winapi.Filter{Name: goods.FieldCloudType, Values: []string{req.CloudType}},
	}
	node, _ = uc.GoodsRepo.Get(ctx, filters...)
	if node != nil && (policy == nil || !policy.OnOverrideAction) {
		return
	}

	var newData *dao.Goods
	if node != nil && node.Edges.MetadataResource != nil && node.Edges.MetadataService != nil && node.Edges.MetadataCategory != nil &&
		(req.Resource == "" || *node.Edges.MetadataResource.Name == req.Resource) &&
		(req.Service == "" || *node.Edges.MetadataService.Name == req.Service) &&
		(req.Category == "" || *node.Edges.MetadataCategory.NameCn == req.Category) {
		newData = &dao.Goods{ID: node.ID}
	} else {
		node, err = uc.fetchOrCreateGoodsResource(ctx, &pbGoodsStack.CreateReq{
			Version:      req.Version,
			Policy:       req.Policy,
			Category:     req.Category,
			Service:      req.Service,
			Resource:     req.Resource,
			Action:       req.Action,
			VirtualGoods: req.VirtualGoods,
			CloudType:    req.CloudType,
		}, nil)
		if err != nil {
			return
		}

		err = jErr.NewEmpty(Self, "查询或创建失败")
		if node.Edges.MetadataCategory == nil {
			err = err.AddReasonf("unknown category name '%s'", req.Category)
		} else if req.Category != pointy.StringValue(node.Edges.MetadataCategory.NameCn, "") {
			err = err.AddReasonf("category name '%s' differs from associated name '%s'", req.Category, pointy.StringValue(node.Edges.MetadataCategory.NameCn, ""))
		}

		if node.Edges.MetadataService == nil {
			err = err.AddReasonf("unknown service name '%s'", req.Service)
		} else if req.Service != pointy.StringValue(node.Edges.MetadataService.Name, "") {
			err = err.AddReasonf("service name '%s' differs from associated name '%s'", req.Service, pointy.StringValue(node.Edges.MetadataService.Name, ""))
		}

		if node.Edges.MetadataResource == nil {
			err = err.AddReasonf("unknown resource name '%s'", req.Resource)
		} else if req.Resource != pointy.StringValue(node.Edges.MetadataResource.Name, "") {
			err = err.AddReasonf("resource name '%s' differs from associated name '%s'", req.Resource, pointy.StringValue(node.Edges.MetadataResource.Name, ""))
		}
		if err.Exists() {
			return node, err.Get()
		}

		newData = &dao.Goods{
			Edges: dao.GoodsEdges{
				JdstackRelease:   node.Edges.JdstackRelease,
				MetadataCategory: node.Edges.MetadataCategory,
				MetadataService:  node.Edges.MetadataService,
				MetadataResource: node.Edges.MetadataResource,
			},
		}
		if newData.Edges.MetadataApplication, err = uc.MetadataApplicationRepo.GetName(ctx, req.Application, &winapi.Filter{
			Name:   metadataapplication.FieldCloudType,
			Values: []string{req.CloudType},
		}); err != nil {
			return
		}
	}

	if newData.ID == "" || (policy != nil && policy.OnOverrideAction) {
		// 执行以下两种场景：
		// 1. 已经存在的标品，需要策略覆盖
		// 2. 不存在的标品，需要创建（这里支持创应用时同时创建服务、产品、分类，但后者policy=nil）
		// 因为策略不会上浮：
		// 1. 改了产品、服务路径
		//   1-1. 不会顺带影响上层，因为id不一样了，应该将action改为discard，然后把其关联的表挪到新的id上（todo）
		//   1-2. 会影响上层，表示所有与此关联的应用都要一起改名（这种情况直接修改metadata_xxx.name即可，不用走goods接口）
		// 2. 上线状态、虚拟商品不跟着一起改，这也是对的
		var goodsData *dao.Goods
		goodsData, err = uc.CreateReq2Goods(ctx, req, policy, newData)
		if err != nil {
			return nil, err
		}
		if _, err = uc.GoodsRepo.PolicyOperate(ctx, goodsData, policy); err != nil {
			return nil, err
		}
	}

	return uc.fetchOrCreateGoodsApplication(ctx, req, nil)
}
