package bizGoodsStack

import (
	"context"
	"fmt"

	"github.com/openlyinc/pointy"

	jErr "coding.jd.com/pcd-application/win-go/error"
	"coding.jd.com/pcd-application/win-go/third_party/winapi"

	pbGoodsStack "coding.jd.com/fabric/zeusV2/zeus/api/server/pb/goods_stack/v1"
	"coding.jd.com/fabric/zeusV2/zeus/internal/server/data/db_master/dao"
	"coding.jd.com/fabric/zeusV2/zeus/internal/server/data/db_master/dao/goods"
)

// ModifySyncGoodsAction 同步标品状态，将不在列表中的标品状态设置为offline
func (uc *goodsStackUsecase) ModifySyncGoodsAction(ctx context.Context, req *pbGoodsStack.SyncGoodsActionReq) (int32, jErr.Error) {
	// 记录日志
	uc.log.Infof("SyncGoodsAction called with version: %s, goodsList count: %d", req.Version, len(req.GoodsList))

	// 构建在列表中的标品的过滤条件
	goodsInList := make(map[string]map[string]map[string]map[string]bool)

	// 新增：记录所有出现过的分类
	categoriesInList := make(map[string]bool)

	// 新增：记录所有出现过的分类+SC组合
	servicesInList := make(map[string]map[string]bool)

	// 新增：记录所有出现过的分类+SC+产品组合
	resourcesInList := make(map[string]map[string]map[string]bool)

	for _, goodsInfo := range req.GoodsList {
		// 记录分类
		categoriesInList[goodsInfo.Category] = true

		// 记录分类+SC组合
		if _, ok := servicesInList[goodsInfo.Category]; !ok {
			servicesInList[goodsInfo.Category] = make(map[string]bool)
		}
		servicesInList[goodsInfo.Category][goodsInfo.Service] = true

		// 记录分类+SC+产品组合
		if _, ok := resourcesInList[goodsInfo.Category]; !ok {
			resourcesInList[goodsInfo.Category] = make(map[string]map[string]bool)
		}
		if _, ok := resourcesInList[goodsInfo.Category][goodsInfo.Service]; !ok {
			resourcesInList[goodsInfo.Category][goodsInfo.Service] = make(map[string]bool)
		}
		resourcesInList[goodsInfo.Category][goodsInfo.Service][goodsInfo.Resource] = true

		// 记录完整的四元组
		if _, ok := goodsInList[goodsInfo.Category]; !ok {
			goodsInList[goodsInfo.Category] = make(map[string]map[string]map[string]bool)
		}
		if _, ok := goodsInList[goodsInfo.Category][goodsInfo.Service]; !ok {
			goodsInList[goodsInfo.Category][goodsInfo.Service] = make(map[string]map[string]bool)
		}
		if _, ok := goodsInList[goodsInfo.Category][goodsInfo.Service][goodsInfo.Resource]; !ok {
			goodsInList[goodsInfo.Category][goodsInfo.Service][goodsInfo.Resource] = make(map[string]bool)
		}
		goodsInList[goodsInfo.Category][goodsInfo.Service][goodsInfo.Resource][goodsInfo.Application] = true
	}

	// 查询当前版本下所有online状态的标品
	filters := winapi.Filters{
		&winapi.Filter{Name: "version", Values: []string{req.Version}},
		&winapi.Filter{Name: "action", Values: []string{goods.ActionOnline.String()}},
		&winapi.Filter{Name: winapi.EmptyValue, Values: []string{
			goods.FilterWithJdstackRelease,
			goods.FilterWithMetadataCategory,
			goods.FilterWithMetadataService,
			goods.FilterWithMetadataResource,
			goods.FilterWithMetadataApplication,
		}},
	}

	// 获取所有在线的标品
	onlineGoods, err := uc.GoodsRepo.List(ctx, -1, 0, filters, nil)
	if err != nil {
		return 0, jErr.NewMessage(Self, fmt.Sprintf("查询在线标品失败: %v", err))
	}

	// 找出不在列表中的标品，将其状态设置为offline
	var offlineCount int32
	for _, g := range onlineGoods {
		// 检查标品是否在列表中
		inList := false

		// 获取标品的分类、产品、服务、应用信息
		category := ""
		if g.Edges.MetadataCategory != nil && g.Edges.MetadataCategory.NameCn != nil {
			category = *g.Edges.MetadataCategory.NameCn
		}

		service := ""
		if g.Edges.MetadataService != nil && g.Edges.MetadataService.Name != nil {
			service = *g.Edges.MetadataService.Name
		}

		resource := ""
		if g.Edges.MetadataResource != nil && g.Edges.MetadataResource.Name != nil {
			resource = *g.Edges.MetadataResource.Name
		}

		application := ""
		if g.Edges.MetadataApplication != nil && g.Edges.MetadataApplication.Name != nil {
			application = *g.Edges.MetadataApplication.Name
		}

		// 判断标品的层级并进行相应的检查
		isApplication := application != ""
		isResource := resource != "" && !isApplication
		isService := service != "" && !isResource && !isApplication
		isCategory := category != "" && !isService && !isResource && !isApplication

		// 检查是否在列表中
		if isCategory {
			// 如果是分类级别的标品，检查分类是否在列表中
			if _, ok := categoriesInList[category]; ok {
				inList = true
				uc.log.Infof("Category level goods found in list: ID=%s, Category=%s", g.ID, category)
			}
		} else if isService {
			// 如果是SC级别的标品，检查分类+SC组合是否在列表中
			if serviceMap, ok := servicesInList[category]; ok {
				if _, ok := serviceMap[service]; ok {
					inList = true
					uc.log.Infof("Service level goods found in list: ID=%s, Category=%s, Service=%s",
						g.ID, category, service)
				}
			}
		} else if isResource {
			// 如果是产品级别的标品，检查分类+SC+产品组合是否在列表中
			if serviceMap, ok := resourcesInList[category]; ok {
				if resourceMap, ok := serviceMap[service]; ok {
					if _, ok := resourceMap[resource]; ok {
						inList = true
						uc.log.Infof("Resource level goods found in list: ID=%s, Category=%s, Service=%s, Resource=%s",
							g.ID, category, service, resource)
					}
				}
			}
		} else {
			// 如果是应用级别的标品，检查完整的四元组是否在列表中
			if categoryMap, ok := goodsInList[category]; ok {
				if serviceMap, ok := categoryMap[service]; ok {
					if resourceMap, ok := serviceMap[resource]; ok {
						if _, ok := resourceMap[application]; ok {
							inList = true
							uc.log.Infof("Application level goods found in list: ID=%s, Category=%s, Service=%s, Resource=%s, Application=%s",
								g.ID, category, service, resource, application)
						}
					}
				}
			}
		}

		// 如果不在列表中，将其状态设置为offline
		if !inList && g.ID != "" {
			uc.log.Infof("Setting goods offline: ID=%s, Category=%s, Service=%s, Resource=%s, Application=%s",
				g.ID, category, service, resource, application)

			// 更新标品状态为offline
			updateGoods := &dao.Goods{
				ID:     g.ID,
				Action: (*goods.Action)(pointy.String(goods.ActionOffline.String())),
			}

			updateErr := uc.GoodsRepo.Update(ctx, updateGoods)
			if updateErr != nil {
				uc.log.Errorf("Failed to set goods offline: ID=%s, error=%v", g.ID, updateErr)
				continue
			}

			offlineCount++
		}
	}

	uc.log.Infof("SyncGoodsAction completed, set %d goods to offline", offlineCount)
	return offlineCount, nil
}
