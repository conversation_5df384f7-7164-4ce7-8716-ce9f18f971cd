package bizJDStackFilecenter

import (
	"context"

	"github.com/go-kratos/kratos/v2/log"

	pbJdstackFilecenter "coding.jd.com/fabric/zeusV2/zeus/api/server/pb/jdstack_filecenter/v1"
	doJDStackFilecenter "coding.jd.com/fabric/zeusV2/zeus/internal/server/domain/jdstack_filecenter"
	jErr "coding.jd.com/pcd-application/win-go/error"
)

func (uc *jdstackFilecenterUsecase) CreateArtifactPackageTask(ctx context.Context, req *pbJdstackFilecenter.CreateArtifactPackageReq) (string, jErr.Error) {
	// todo-查看是否有运行中和待运行的任务，如果有，则拒绝创建
	// addTask
	params := &doJDStackFilecenter.ModelFilecenterArtifactPackageTaskParams{
		Params:   req,
		IsFailed: false,
	}
	if uc.FilecenterConf.GetRsyncBwLimit() != 0 {
		params.RsyncBwLimit = uc.FilecenterConf.GetRsyncBwLimit()
	}
	taskId, err := uc.filecenterArtifactPackageTaskAdd(ctx, params)
	if err != nil {
		return "", jErr.NewMessage(Self, "创建任务失败").WithData(err)
	}
	uc.log.Debugw(log.DefaultMessageKey, "CreateArtifactPackageTask success", "taskid", taskId)
	if taskId == "" {
		return "", jErr.NewMessage(Self, "创建任务失败, taskId为空，可能是创建出包任务重复")
	}
	return taskId, nil
}
