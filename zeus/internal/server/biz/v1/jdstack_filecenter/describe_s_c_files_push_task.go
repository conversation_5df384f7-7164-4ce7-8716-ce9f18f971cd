package bizJDStackFilecenter

import (
	"context"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/gogf/gf/v2/util/gconv"

	jErr "coding.jd.com/pcd-application/win-go/error"
	moTaskState "coding.jd.com/pcd-application/win-go/project/data/task/model/task_state"

	pbJdstackFilecenter "coding.jd.com/fabric/zeusV2/zeus/api/server/pb/jdstack_filecenter/v1"
	doJDStackFilecenter "coding.jd.com/fabric/zeusV2/zeus/internal/server/domain/jdstack_filecenter"
	"coding.jd.com/fabric/zeusV2/zeus/internal/server/server"
)

func (uc *jdstackFilecenterUsecase) DescribeSCFilesPushTask(ctx context.Context, req *pbJdstackFilecenter.DescribeTaskReq) (*moTaskState.Kind, *doJDStackFilecenter.ModelFilecenterSCFilesPushTaskParams, jErr.Error) {
	res, err := uc.TaskRepo.GetOneID(ctx, gconv.Int64(req.TaskID))
	if err != nil {
		return nil, nil, jErr.NewMessage(Self, "查询任务失败").WithData(err)
	}
	uc.log.Debugw(log.DefaultMessageKey, "DescribeFileSCFilesPushTask success", "taskid", req.TaskID, "isLeader", server.IsLeader())
	if res != nil {
		data, _ := uc.filecenterSCFilesPushTaskScan(res.TaskParams)
		if err != nil {
			return nil, nil, jErr.NewMessage(Self, "查询任务失败").WithData(err)
		}
		return res.Task.State, data, nil
	}
	return nil, nil, jErr.NewMessage(Self, "查询任务失败")
}
