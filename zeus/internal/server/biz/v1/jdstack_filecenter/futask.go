package bizJDStackFilecenter

import (
	"context"
	"errors"
	"fmt"
	"runtime/debug"
	"time"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/gogf/gf/v2/util/gconv"

	jDMutex "coding.jd.com/pcd-application/win-go/distributed_mutex"
	jErr "coding.jd.com/pcd-application/win-go/error"
	moTaskContext "coding.jd.com/pcd-application/win-go/project/data/task/model/task_context"
	moTaskParams "coding.jd.com/pcd-application/win-go/project/data/task/model/task_params"
	moTaskState "coding.jd.com/pcd-application/win-go/project/data/task/model/task_state"
	moTaskType "coding.jd.com/pcd-application/win-go/project/data/task/model/task_type"
	"coding.jd.com/pcd-application/win-go/third_party/winapi"

	doJDStackFilecenter "coding.jd.com/fabric/zeusV2/zeus/internal/server/domain/jdstack_filecenter"
	"coding.jd.com/fabric/zeusV2/zeus/internal/server/server"
)

func (uc *jdstackFilecenterUsecase) filecenterUploadTaskAdd(ctx context.Context, data *doJDStackFilecenter.ModelFilecenterUploadTaskParams) (string, jErr.Error) {
	return uc.TaskCase.CreateDelayTask(ctx, nil, moTaskType.New("FilecenterUpload"), data.GoodsId, &moTaskParams.TaskParams{
		Data: data,
	}, 0)
}

func (uc *jdstackFilecenterUsecase) filecenterUploadTaskScan(taskParams *moTaskParams.TaskParams) (*doJDStackFilecenter.ModelFilecenterUploadTaskParams, error) {
	var data *doJDStackFilecenter.ModelFilecenterUploadTaskParams
	if _ = taskParams.DataScan(&data); data == nil || data.GoodsId == "" {
		return &doJDStackFilecenter.ModelFilecenterUploadTaskParams{}, errors.New("parse data failed")
	}
	return data, nil
}

func (uc *jdstackFilecenterUsecase) filecenterDownloadTaskAdd(ctx context.Context, data *doJDStackFilecenter.ModelFilecenterDownloadTaskParams) (string, jErr.Error) {
	return uc.TaskCase.CreateDelayTask(ctx, nil, moTaskType.New("FilecenterDownload"), data.AppVersionID+":"+data.Params.FilePath, &moTaskParams.TaskParams{
		Data: data,
	}, 0)
}

func (uc *jdstackFilecenterUsecase) filecenterDownloadTaskScan(taskParams *moTaskParams.TaskParams) (*doJDStackFilecenter.ModelFilecenterDownloadTaskParams, error) {
	var data *doJDStackFilecenter.ModelFilecenterDownloadTaskParams
	if _ = taskParams.DataScan(&data); data == nil {
		return &doJDStackFilecenter.ModelFilecenterDownloadTaskParams{}, errors.New("parse data failed")
	}
	return data, nil
}

// zzc-gen generate ↓
func (uc *jdstackFilecenterUsecase) filecenterSCFilesPushTaskAdd(ctx context.Context, data *doJDStackFilecenter.ModelFilecenterSCFilesPushTaskParams) (string, jErr.Error) {
	// 构造唯一标识符
	refId := data.Params.Version + ":" + data.Params.ServiceCode + ":" + data.Params.Architecture + ":" + data.Params.HostOsType + ":" + data.Params.Region + ":" + data.Params.AppName

	// 检查是否存在重复任务
	filters := winapi.Filters{
		{
			Name:     "kind",
			Operator: winapi.OpEQ,
			Values:   []string{moTaskType.New("FilecenterSCFilesPush").Code()},
		},
		{
			Name:     "rel_id",
			Operator: winapi.OpEQ,
			Values:   []string{refId},
		},
		{
			Name:     "state",
			Operator: winapi.OpIN,
			Values: []string{
				moTaskState.CreatedKind.Code(),
				moTaskState.DispatchedKind.Code(),
				moTaskState.RunningKind.Code(),
			},
		},
	}

	total := uc.TaskRepo.TotalCount(ctx, nil, filters)
	if total > 0 {
		// 任务已存在，获取已存在的任务
		existingTasks, err := uc.TaskRepo.List(ctx, nil, 0, 1, filters, nil)
		if err != nil {
			return "", jErr.NewError(Self, err)
		}
		if len(existingTasks) > 0 {
			existingTaskId := gconv.String(existingTasks[0].ID)
			uc.log.Infow(log.DefaultMessageKey, "task already exists", "kind", "FilecenterSCFilesPush", "refId", refId, "taskId", existingTaskId)
			return existingTaskId, nil
		}
	}

	// 创建新任务
	taskId, err := uc.TaskCase.CreateDelayTask(ctx, nil, moTaskType.New("FilecenterSCFilesPush"), refId, &moTaskParams.TaskParams{
		Data: data,
	}, 0)

	if err != nil {
		return "", err
	}

	uc.log.Infow(log.DefaultMessageKey, "new task created", "kind", "FilecenterSCFilesPush", "refId", refId, "taskId", taskId)
	return taskId, nil
}
func (uc *jdstackFilecenterUsecase) filecenterSCFilesPushTaskScan(taskParams *moTaskParams.TaskParams) (*doJDStackFilecenter.ModelFilecenterSCFilesPushTaskParams, error) {
	var data *doJDStackFilecenter.ModelFilecenterSCFilesPushTaskParams
	if _ = taskParams.DataScan(&data); data == nil {
		return &doJDStackFilecenter.ModelFilecenterSCFilesPushTaskParams{}, errors.New("parse data failed")
	}
	return data, nil
}
func (uc *jdstackFilecenterUsecase) filecenterSCFilesPushTaskHandler(ctx context.Context, tCtx *moTaskContext.TaskContext) (rCtx *moTaskContext.TaskContext, isRemove bool) {
	var (
		data *doJDStackFilecenter.ModelFilecenterSCFilesPushTaskParams
		err  error
	)
	defer func() {
		// 必须拦截崩溃信息并回写
		if r := recover(); r != nil {
			err = errors.New("[" + gconv.String(r) + "]" + string(debug.Stack()))
		}

		// 任务收尾工作放在 recover 后面
		if err != nil {
			isRemove = true

			// TaskParams 状态改为失败
			data.IsFailed = true
			data.ErrorMessage = err.Error()

			// Task 状态改为失败
			tCtx.Task.State = moTaskState.FailedKind.GetValue()
		} else if isRemove {
			// Task 状态改为成功
			tCtx.Task.State = moTaskState.SucceedKind.GetValue()
		} else if data != nil && data.Delay > 0 {
			// 推迟下次执行时间
			tCtx.Task.ScheduleAt = time.Now().Add(time.Duration(data.Delay) * time.Second)
		}

		tCtx.TaskParams.Data = data

		// 返回任务的最终状态
		rCtx = tCtx
	}()
	if tCtx.TaskParams == nil {
		tCtx.Task.State = moTaskState.CanceledKind.GetValue()
		return
	}
	if !server.IsLeader() {
		return
	}
	data, err = uc.filecenterSCFilesPushTaskScan(tCtx.TaskParams)
	if err != nil {
		return
	}
	tCtx.Task.State = moTaskState.RunningKind.GetValue()
	if isRemove, err = uc.filecenterTaskPush4Out(context.Background(), data); err != nil {
		return
	}
	return
}

func (uc *jdstackFilecenterUsecase) filecenterArtifactPackageTaskAdd(ctx context.Context, data *doJDStackFilecenter.ModelFilecenterArtifactPackageTaskParams) (string, jErr.Error) {
	refId := fmt.Sprintf("%s:%s:%s", data.Params.PackageVersion, data.Params.TaskType, data.Params.Architecture)
	return uc.TaskCase.CreateDelayTask(ctx, nil, moTaskType.New("FilecenterArtifactPackage"), refId, &moTaskParams.TaskParams{
		Data: data,
	}, 0)
}
func (uc *jdstackFilecenterUsecase) filecenterArtifactPackageTaskScan(taskParams *moTaskParams.TaskParams) (*doJDStackFilecenter.ModelFilecenterArtifactPackageTaskParams, error) {
	var data *doJDStackFilecenter.ModelFilecenterArtifactPackageTaskParams
	if _ = taskParams.DataScan(&data); data == nil {
		return &doJDStackFilecenter.ModelFilecenterArtifactPackageTaskParams{}, errors.New("parse data failed")
	}
	return data, nil
}
func (uc *jdstackFilecenterUsecase) filecenterArtifactPackageTaskHandler(ctx context.Context, tCtx *moTaskContext.TaskContext) (rCtx *moTaskContext.TaskContext, isRemove bool) {
	var (
		data *doJDStackFilecenter.ModelFilecenterArtifactPackageTaskParams
		err  error
	)
	defer func() {
		// 必须拦截崩溃信息并回写
		if r := recover(); r != nil {
			err = errors.New("[" + gconv.String(r) + "]" + string(debug.Stack()))
		}

		// 任务收尾工作放在 recover 后面
		if err != nil {
			isRemove = true

			// TaskParams 状态改为失败
			data.IsFailed = true
			data.ErrorMessage = err.Error()

			// Task 状态改为失败
			tCtx.Task.State = moTaskState.FailedKind.GetValue()
		} else if isRemove {
			// Task 状态改为成功
			tCtx.Task.State = moTaskState.SucceedKind.GetValue()
		} else if data != nil && data.Delay > 0 {
			// 推迟下次执行时间
			tCtx.Task.ScheduleAt = time.Now().Add(time.Duration(data.Delay) * time.Second)
		}

		tCtx.TaskParams.Data = data

		// 返回任务的最终状态
		rCtx = tCtx
	}()
	if tCtx.TaskParams == nil {
		tCtx.Task.State = moTaskState.CanceledKind.GetValue()
		return
	}
	data, err = uc.filecenterArtifactPackageTaskScan(tCtx.TaskParams)
	if err != nil {
		return
	}
	tCtx.Task.State = moTaskState.RunningKind.GetValue()
	if isRemove, err = uc.filecenterTaskPackage(context.Background(), data); err != nil {
		return
	}
	return
}

func (uc *jdstackFilecenterUsecase) filecenterAppFilesPushTaskAdd(ctx context.Context, data *doJDStackFilecenter.ModelFilecenterAppFilesPushTaskParams) (string, jErr.Error) {
	return uc.TaskCase.CreateDelayTask(ctx, nil, moTaskType.New("FilecenterAppFilesPush"), data.AppVersionID+":"+data.Params.EnvName, &moTaskParams.TaskParams{
		Data: data,
	}, 0)
}
func (uc *jdstackFilecenterUsecase) filecenterAppFilesPushTaskScan(taskParams *moTaskParams.TaskParams) (*doJDStackFilecenter.ModelFilecenterAppFilesPushTaskParams, error) {
	var data *doJDStackFilecenter.ModelFilecenterAppFilesPushTaskParams
	if _ = taskParams.DataScan(&data); data == nil {
		return &doJDStackFilecenter.ModelFilecenterAppFilesPushTaskParams{}, errors.New("parse data failed")
	}
	return data, nil
}
func (uc *jdstackFilecenterUsecase) filecenterAppFilesPushTaskHandler(ctx context.Context, tCtx *moTaskContext.TaskContext) (rCtx *moTaskContext.TaskContext, isRemove bool) {
	var (
		data *doJDStackFilecenter.ModelFilecenterAppFilesPushTaskParams
		err  error
	)
	defer func() {
		// 必须拦截崩溃信息并回写
		if r := recover(); r != nil {
			err = errors.New("[" + gconv.String(r) + "]" + string(debug.Stack()))
		}

		// 任务收尾工作放在 recover 后面
		if err != nil {
			isRemove = true

			// TaskParams 状态改为失败
			data.IsFailed = true
			data.ErrorMessage = err.Error()

			// Task 状态改为失败
			tCtx.Task.State = moTaskState.FailedKind.GetValue()
		} else if isRemove {
			// Task 状态改为成功
			tCtx.Task.State = moTaskState.SucceedKind.GetValue()
		} else if data != nil && data.Delay > 0 {
			// 推迟下次执行时间
			tCtx.Task.ScheduleAt = time.Now().Add(time.Duration(data.Delay) * time.Second)
		}

		tCtx.TaskParams.Data = data

		// 返回任务的最终状态
		rCtx = tCtx
	}()
	if tCtx.TaskParams == nil {
		tCtx.Task.State = moTaskState.CanceledKind.GetValue()
		return
	}
	data, err = uc.filecenterAppFilesPushTaskScan(tCtx.TaskParams)
	if err != nil {
		return
	}
	tCtx.Task.State = moTaskState.RunningKind.GetValue()
	if isRemove, err = uc.filecenterTaskPush(context.Background(), data); err != nil {
		return
	}
	return
}

// zzc-gen generate ↑

// 任务处理器
func (uc *jdstackFilecenterUsecase) filecenterUploadTaskHandler(ctx context.Context, tCtx *moTaskContext.TaskContext) (rCtx *moTaskContext.TaskContext, isRemove bool) {
	var (
		deadline = time.Now().Add(15 * time.Minute)
		data     *doJDStackFilecenter.ModelFilecenterUploadTaskParams
		pass     = true
		err      error
	)
	defer func() {
		// 必须拦截崩溃信息并回写
		if r := recover(); r != nil {
			err = errors.New("[" + gconv.String(r) + "]" + string(debug.Stack()))
		}

		// 任务收尾工作放在 recover 后面
		if err != nil {
			isRemove = true

			// TaskParams 状态改为失败
			data.IsFailed = true
			data.ErrorMessage = err.Error()

			// Task 状态改为失败
			tCtx.Task.State = moTaskState.FailedKind.GetValue()
		} else if isRemove {
			// Task 状态改为成功
			tCtx.Task.State = moTaskState.SucceedKind.GetValue()
		} else if data != nil && data.Delay > 0 {
			// 推迟下次执行时间
			tCtx.Task.ScheduleAt = time.Now().Add(time.Duration(data.Delay) * time.Second)
		}

		tCtx.TaskParams.Data = data

		// 返回任务的最终状态
		rCtx = tCtx
	}()

	if tCtx.TaskParams == nil {
		tCtx.Task.State = moTaskState.CanceledKind.GetValue()
		return
	}
	data, err = uc.filecenterUploadTaskScan(tCtx.TaskParams)
	if err != nil {
		return
	}
	if data.Step == 0 {
		tCtx.Task.State = moTaskState.RunningKind.GetValue()
	}

	lockKey := doJDStackFilecenter.LockCacheFilecenterUpload.Parse(data.GoodsId, data.Params.Arch, data.Params.Os)
	if lockErr := uc.lock.LockFast(lockKey, func() error {
		for pass && err == nil && !isRemove && deadline.After(time.Now()) {
			data.Count++
			if isRemove, err = uc.filecenterUploadTask[data.Step](context.Background(), data); err != nil {
				return err
			}
			data.Step++
			if data.Step == len(uc.filecenterUploadTask) {
				isRemove = true
			}
		}
		return nil
	}); lockErr != nil {
		uc.handleLockError(lockErr, data, lockKey)
	}

	return
}

func (uc *jdstackFilecenterUsecase) filecenterDownloadTaskHandler(ctx context.Context, tCtx *moTaskContext.TaskContext) (rCtx *moTaskContext.TaskContext, isRemove bool) {
	var (
		data *doJDStackFilecenter.ModelFilecenterDownloadTaskParams
		err  error
	)
	defer func() {
		// 必须拦截崩溃信息并回写
		if r := recover(); r != nil {
			err = errors.New("[" + gconv.String(r) + "]" + string(debug.Stack()))
		}

		// 任务收尾工作放在 recover 后面
		if err != nil {
			isRemove = true

			// TaskParams 状态改为失败
			data.IsFailed = true
			data.ErrorMessage = err.Error()

			// Task 状态改为失败
			tCtx.Task.State = moTaskState.FailedKind.GetValue()
		} else if isRemove {
			// Task 状态改为成功
			tCtx.Task.State = moTaskState.SucceedKind.GetValue()
		} else if data != nil && data.Delay > 0 {
			// 推迟下次执行时间
			tCtx.Task.ScheduleAt = time.Now().Add(time.Duration(data.Delay) * time.Second)
		}

		tCtx.TaskParams.Data = data

		// 返回任务的最终状态
		rCtx = tCtx
	}()
	if tCtx.TaskParams == nil {
		tCtx.Task.State = moTaskState.CanceledKind.GetValue()
		return
	}
	data, err = uc.filecenterDownloadTaskScan(tCtx.TaskParams)
	if err != nil {
		return
	}
	tCtx.Task.State = moTaskState.RunningKind.GetValue()
	if isRemove, err = uc.filecenterTaskDownload2(context.Background(), data); err != nil {
		return
	}
	return
}

func (uc *jdstackFilecenterUsecase) handleTaskCompletion(tCtx *moTaskContext.TaskContext, data interface{}, isRemove *bool, err *error) {
	if r := recover(); r != nil {
		*err = errors.New("[" + gconv.String(r) + "]" + string(debug.Stack()))
	}

	if *err != nil {
		*isRemove = true
		uc.updateTaskStateOnError(tCtx, data, *err)
	} else if *isRemove {
		tCtx.Task.State = moTaskState.SucceedKind.GetValue()
	} else if data != nil {
		tCtx.Task.ScheduleAt = time.Now().Add(time.Duration(2) * time.Second)
	}

	tCtx.TaskParams.Data = data
}

func (uc *jdstackFilecenterUsecase) updateTaskStateOnError(tCtx *moTaskContext.TaskContext, data interface{}, err error) {
	if uploadData, ok := data.(*doJDStackFilecenter.ModelFilecenterUploadTaskParams); ok {
		uploadData.IsFailed = true
		uploadData.ErrorMessage = err.Error()
	} else if downloadData, ok := data.(*doJDStackFilecenter.ModelFilecenterDownloadTaskParams); ok {
		downloadData.IsFailed = true
		downloadData.ErrorMessage = err.Error()
	}
	tCtx.Task.State = moTaskState.FailedKind.GetValue()
}

func (uc *jdstackFilecenterUsecase) handleLockError(lockErr error, data *doJDStackFilecenter.ModelFilecenterUploadTaskParams, lockKey string) {
	if jDMutex.IsLockFailed(lockErr) {
		data.Delay = 60
		uc.log.Debugw(log.DefaultMessageKey, "upload task", "lockKey", lockKey, "err", lockErr)
	} else {
		uc.log.Errorw(log.DefaultMessageKey, "upload task", "lockKey", lockKey, "err", lockErr)
	}
}
