package bizJDStackFilecenter

import (
	"context"

	"github.com/go-kratos/kratos/v2/log"

	jErr "coding.jd.com/pcd-application/win-go/error"

	pbJdstackFilecenter "coding.jd.com/fabric/zeusV2/zeus/api/server/pb/jdstack_filecenter/v1"
	doJDStackFilecenter "coding.jd.com/fabric/zeusV2/zeus/internal/server/domain/jdstack_filecenter"
)

func (uc *jdstackFilecenterUsecase) CreateSCFilesPushTask(ctx context.Context, req *pbJdstackFilecenter.CreateSCFilesTaskReq) (string, jErr.Error) {
	uc.log.Debugw(log.DefaultMessageKey, "start CreateSCFilesPushTask2")
	// addTask
	taskId, err := uc.filecenterSCFilesPushTaskAdd(ctx, &doJDStackFilecenter.ModelFilecenterSCFilesPushTaskParams{
		Params:   req,
		IsFailed: false,
	})
	if err != nil {
		return "", jErr.NewMessage(Self, "创建任务失败").WithData(err)
	}
	uc.log.Debugw(log.DefaultMessageKey, "CreateSCFilesPushTask success", "taskid", taskId)
	return taskId, nil
}
