package bizJDStackFilecenter

import (
	"bytes"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"os"
	"path/filepath"
	"regexp"
	"strings"
	"time"

	"os/exec"

	"github.com/gogf/gf/v2/util/gconv"

	"coding.jd.com/fabric/zeusV2/zeus/internal/server/data/db_master/dao/goodsfilecenter"
	doJDStackFilecenter "coding.jd.com/fabric/zeusV2/zeus/internal/server/domain/jdstack_filecenter"
	jErr "coding.jd.com/pcd-application/win-go/error"
)

func (uc *jdstackFilecenterUsecase) filecenterTaskDownload2(ctx context.Context, data *doJDStackFilecenter.ModelFilecenterDownloadTaskParams) (isRemove bool, err error) {
	eErr := jErr.NewEmptyf(Self, "下载任务失败")
	downloadFile := &doJDStackFilecenter.ModelDownload2PackageFilePathReply{}
	newData := &doJDStackFilecenter.ModelDownload2PackageFilePathReq{
		PackagePath: uc.FilecenterConf.Cli["package_path"],
		PackageFilePath: &doJDStackFilecenter.ModelPackageFilePathReq{
			ServiceCode:   data.Params.ServiceCode,
			FileType:      data.Params.FileType, // todo
			Arch:          doJDStackFilecenter.Jdock2Arch(data.Params.Arch),
			Filename:      doJDStackFilecenter.Address2Filename(data.Params.FilePath),
			DirectorySpec: goodsfilecenter.DirectorySpecArchFileTypeServiceCode.String(),
		},
		Address: data.Params.FilePath,
	}
	newData.Stage = doJDStackFilecenter.GetDownloadStage(doJDStackFilecenter.Jdock2FileType(data.Params.FileType))
	if downloadFile, err = uc.DoDownload2PackageFilePath(ctx, newData); err != nil {
		return false, err
	}
	if !downloadFile.Success {
		eErr = eErr.AddReason(gconv.String(downloadFile))
	}
	if eErr.Exists() {
		return false, eErr.Get()
	}
	if doJDStackFilecenter.IsHarborURL(data.Params.FilePath) {
		data.FileName = doJDStackFilecenter.Address2Filename(data.Params.FilePath)
	} else {
		data.FileName = doJDStackFilecenter.Address2Filename(data.Params.FilePath) + "-" + downloadFile.Md5
	}
	data.MD5 = downloadFile.Md5
	var md5Tag string
	if len(data.MD5) >= 12 {
   		md5Tag = data.MD5[:12]
	} else {
   		md5Tag = data.MD5
	}
	fedhub := uc.FilecenterConf.Cli["fedhub"]
	switch doJDStackFilecenter.Jdock2FileType(data.Params.FileType) {
	case doJDStackFilecenter.JdockFileTypeImage, doJDStackFilecenter.JdockFileTypeOutImage:
		if err := PushImageWithURL(data.Params.FilePath, fedhub, uc.log.Debugf); err != nil {
			uc.log.Debugw("fedhub", "push image & out_image", "err", err)
			return false, err
		} else {
			uc.log.Debugw("fedhub", "push image & out_image", "success", data.Params.FilePath)
		}
	case doJDStackFilecenter.JdockFileTypeHelm:
		// NOTE: 未使用HELM

	case doJDStackFilecenter.JdockFileTypeRpm:
		buildRPMRepoTAG := fmt.Sprintf("%s/%s:%s", data.Params.ServiceCode, data.FileName, md5Tag)
		err = PushRPM(data.Params.FilePath, buildRPMRepoTAG, uc.log.Debugf)
		if err != nil {
			uc.log.Debugw("fedhub", "push rpm", "err", err)
			return false, err
		} else {
			uc.log.Debugw("fedhub", "push rpm", "success", buildRPMRepoTAG)
		}
	case doJDStackFilecenter.JdockFileTypeOsFile, doJDStackFilecenter.JdockFileTypePackage:
		at := ArtifactTarget{
			Repository: data.Params.ServiceCode,
			Path: data.FileName,
			Version:    md5Tag,
		}
		err = PushRaw(data.Params.FilePath, at, uc.log.Debugf)
		if err != nil {
			uc.log.Debugw("fedhub", "push raw", "err", err)
			return false, err
		} else {
			uc.log.Debugw("fedhub", "push raw", "success", at)
		}
	}

	return true, nil
}

func PushImageWithURL(sourceImageOrURL, targetRegistry string, logf func(format string, a ...interface{})) error {
    var sourceImage string
    var err error
    
    if isURL(sourceImageOrURL) {
        logf("检测到 URL，开始下载并加载镜像: %s", sourceImageOrURL)
        sourceImage, err = downloadAndLoadImage(sourceImageOrURL, logf)
        if err != nil {
            return fmt.Errorf("下载并加载镜像失败: %v", err)
        }
        logf("成功加载镜像: %s", sourceImage)
	return PushImageClosePull(sourceImage, targetRegistry, logf)
    } else {
        sourceImage = sourceImageOrURL
    }
    
    return PushImage(sourceImage, targetRegistry, logf)
}

func PushImage(sourceImage, targetRegistry string, logf func(format string, a ...interface{})) error {
	return pushImage(sourceImage, targetRegistry, false, logf)
}

func PushImageClosePull(sourceImage, targetRegistry string, logf func(format string, a ...interface{})) error {
	return pushImage(sourceImage, targetRegistry, true, logf)
}

func pushImage(sourceImage, targetRegistry string, closePull bool, logf func(format string, a ...interface{})) error {
    repoAndTag, err := getRepoAndTag(sourceImage)
    if err != nil {
        return err
    }
    targetImage := fmt.Sprintf("%s/%s", targetRegistry, repoAndTag)

	var output []byte
	if !closePull {
    	pullCmd := exec.Command("docker", "pull", sourceImage)
    	output, err := pullCmd.CombinedOutput()
    	if err != nil {
        	return fmt.Errorf("docker pull failed: %v\n%s", err, string(output))
    	}
		logf("docker pull %s, output: %s", pullCmd, string(output))
	}

    tagCmd := exec.Command("docker", "image", "tag", sourceImage, targetImage)
    output, err = tagCmd.CombinedOutput()
    if err != nil {
        return fmt.Errorf("docker tag failed: %v\n%s", err, string(output))
    }
    logf("docker tag %s, output: %s", tagCmd, string(output))

    pushCmd := exec.Command("docker", "push", targetImage)
    output, err = pushCmd.CombinedOutput()
    if err != nil {
        return fmt.Errorf("docker push failed: %v\n%s", err, string(output))
    }
    logf("docker push %s, output: %s", pushCmd, string(output))

    return nil
}

func isURL(str string) bool {
    u, err := url.Parse(str)
    return err == nil && (u.Scheme == "http" || u.Scheme == "https")
}

func downloadAndLoadImage(imageURL string, logf func(format string, a ...interface{})) (string, error) {
    tmpFile, err := os.CreateTemp("", "docker-image-*.tar")
    if err != nil {
        return "", fmt.Errorf("创建临时文件失败: %v", err)
    }
    defer os.Remove(tmpFile.Name()) // 清理临时文件
    defer tmpFile.Close()

    logf("开始下载镜像文件: %s", imageURL)
    resp, err := http.Get(imageURL)
    if err != nil {
        return "", fmt.Errorf("下载失败: %v", err)
    }
    defer resp.Body.Close()

    if resp.StatusCode != http.StatusOK {
        return "", fmt.Errorf("下载失败，HTTP状态码: %d", resp.StatusCode)
    }

    _, err = io.Copy(tmpFile, resp.Body)
    if err != nil {
        return "", fmt.Errorf("写入文件失败: %v", err)
    }
    tmpFile.Close() // 确保文件关闭后再使用

    logf("下载完成，文件保存至: %s", tmpFile.Name())

    loadCmd := exec.Command("docker", "load", "-i", tmpFile.Name())
    output, err := loadCmd.CombinedOutput()
    if err != nil {
        return "", fmt.Errorf("docker load 失败: %v\n%s", err, string(output))
    }

    logf("docker load 输出: %s", string(output))

    sourceImage, err := extractImageNameFromLoadOutput(string(output))
    if err != nil {
        return "", fmt.Errorf("无法从 docker load 输出中提取镜像名称: %v", err)
    }

    return sourceImage, nil
}

func extractImageNameFromLoadOutput(output string) (string, error) {
    lines := strings.Split(strings.TrimSpace(output), "\n")
    for _, line := range lines {
        if strings.HasPrefix(line, "Loaded image: ") {
            return strings.TrimPrefix(line, "Loaded image: "), nil
        }
        if strings.HasPrefix(line, "Loaded image ID: ") {
            imageID := strings.TrimPrefix(line, "Loaded image ID: ")
            return getImageNameByID(imageID)
        }
    }
    
    return "", fmt.Errorf("无法从输出中找到镜像信息: %s", output)
}

func getImageNameByID(imageID string) (string, error) {
    imageID = strings.TrimPrefix(imageID, "sha256:")
    
    cmd := exec.Command("docker", "images", "--format", "{{.Repository}}:{{.Tag}}", "--filter", fmt.Sprintf("reference=%s", imageID[:12]))
    output, err := cmd.CombinedOutput()
    if err != nil {
        return "", fmt.Errorf("获取镜像名称失败: %v\n%s", err, string(output))
    }
    
    lines := strings.Split(strings.TrimSpace(string(output)), "\n")
    if len(lines) > 0 && lines[0] != "" {
        return lines[0], nil
    }
    
    return imageID[:12], nil
}

func getRepoAndTag(image string) (string, error) {
    parts := strings.Split(image, "/")
    if len(parts) > 0 {
        return parts[len(parts)-1], nil
    }
    return image, nil
}

func PushChart(sourceChart, targetRegistry string, logf func(format string, a ...interface{})) error {
	// NOTE: jdstack ADL 无chart的推送
	return nil
}

func PushRPM(downloadURL, repoTag string, logf func(format string, a ...interface{})) error {
    return PushRPMWithOptions(downloadURL, repoTag, nil, logf)
}

func PushRPMWithOptions(downloadURL, repoTag string, options []string, logf func(format string, a ...interface{})) error {
    if logf == nil {
        logf = func(format string, a ...interface{}) {
            fmt.Printf("[LOG] "+format+"\n", a...)
        }
    }

    if downloadURL == "" {
        return fmt.Errorf("download URL cannot be empty")
    }
    if repoTag == "" {
        return fmt.Errorf("repository:tag cannot be empty")
    }

    if !isValidURL(downloadURL) {
        return fmt.Errorf("invalid URL format: %s", downloadURL)
    }

    if !strings.Contains(repoTag, ":") {
        return fmt.Errorf("repository:tag format should be 'repo:tag', got: %s", repoTag)
    }

    logf("开始处理 RPM 推送任务")
    logf("下载地址: %s", downloadURL)
    logf("目标仓库: %s", repoTag)

    rpmFile, err := downloadRPM(downloadURL, logf)
    if err != nil {
        return fmt.Errorf("下载 RPM 文件失败: %v", err)
    }
    defer func() {
        if err := os.Remove(rpmFile); err != nil {
            logf("清理临时文件失败: %v", err)
        } else {
            logf("已清理临时文件: %s", rpmFile)
        }
    }()

    err = pushRPMWithHaven(rpmFile, repoTag, options, logf)
    if err != nil {
        return fmt.Errorf("推送 RPM 到仓库失败: %v", err)
    }

    logf("RPM 推送完成: %s -> %s", downloadURL, repoTag)
    return nil
}

func isValidURL(str string) bool {
    u, err := url.Parse(str)
    return err == nil && (u.Scheme == "http" || u.Scheme == "https") && u.Host != ""
}

func downloadRPM(downloadURL string, logf func(format string, a ...interface{})) (string, error) {
    logf("开始下载 RPM 文件: %s", downloadURL)

    client := &http.Client{
        Timeout: 10 * time.Minute, // 10分钟超时
    }

    resp, err := client.Get(downloadURL)
    if err != nil {
        return "", fmt.Errorf("HTTP 请求失败: %v", err)
    }
    defer resp.Body.Close()

    if resp.StatusCode != http.StatusOK {
        return "", fmt.Errorf("HTTP 请求失败，状态码: %d", resp.StatusCode)
    }

    fileName := extractFileNameFromURL(downloadURL)
    if fileName == "" {
        fileName = fmt.Sprintf("rpm_file_%d.rpm", time.Now().Unix())
    }

    if !strings.HasSuffix(strings.ToLower(fileName), ".rpm") {
        fileName += ".rpm"
    }

    tmpDir := os.TempDir()
    tmpFile := filepath.Join(tmpDir, fileName)

    logf("保存文件到: %s", tmpFile)

    file, err := os.Create(tmpFile)
    if err != nil {
        return "", fmt.Errorf("创建本地文件失败: %v", err)
    }
    defer file.Close()

    written, err := io.Copy(file, resp.Body)
    if err != nil {
        os.Remove(tmpFile) // 清理失败的文件
        return "", fmt.Errorf("下载文件失败: %v", err)
    }

    logf("下载完成，文件大小: %d bytes", written)
    return tmpFile, nil
}

func extractFileNameFromURL(downloadURL string) string {
    u, err := url.Parse(downloadURL)
    if err != nil {
        return ""
    }
    
    fileName := filepath.Base(u.Path)
    if fileName == "." || fileName == "/" {
        return ""
    }
    
    return fileName
}

func pushRPMWithHaven(rpmFile, repoTag string, options []string, logf func(format string, a ...interface{})) error {
    logf("开始使用 haven 推送 RPM: %s -> %s", rpmFile, repoTag)

    args := []string{"rpm", "push"}
    
    if len(options) > 0 {
        args = append(args, options...)
    }
    
    args = append(args, rpmFile, repoTag)

    logf("执行命令: haven %s", strings.Join(args, " "))

    cmd := exec.Command("haven", args...)
    
    output, err := cmd.CombinedOutput()
    if err != nil {
        return fmt.Errorf("haven 命令执行失败: %v\n输出: %s", err, string(output))
    }

    logf("haven 命令执行成功")
    logf("输出: %s", string(output))
    
    return nil
}

type TransferMethod int

const (
    MethodSCP TransferMethod = iota
    MethodHTTP
    MethodUnsupported
)

func PushRaw(downloadURL string, target ArtifactTarget, logf func(format string, a ...interface{})) error {
	return map[TransferMethod]func(downloadURL string, target ArtifactTarget, logf func(format string, a ...interface{})) error {
		MethodHTTP: pushRawLocal,
		MethodSCP: pushRawRemote,
		MethodUnsupported: pushRawUnkonwn,
	}[identifyTransferMethod(downloadURL)](downloadURL, target, logf)
}

func identifyTransferMethod(address string) TransferMethod {
    if isHTTPURL(address) {
        return MethodHTTP
    }
    
    if isHTTPLikeURL(address) {
        return MethodHTTP
    }
    
    if isSCPAddress(address) {
        return MethodSCP
    }
    
    return MethodUnsupported
}

type SCPInfo struct {
    BaseURL string // HTTP BaseURL
    Path    string // SCP路径
}

func extractSCPInfo(scpAddr string) (*SCPInfo, error) {
    scpAddr = strings.TrimSpace(scpAddr)
    
    var host, path string
    
    if strings.HasPrefix(scpAddr, "scp://") {
        u, err := url.Parse(scpAddr)
        if err != nil {
            return nil, fmt.Errorf("解析SCP URL失败: %v", err)
        }
        host = u.Hostname()
        path = u.Path
    } else {
        re := regexp.MustCompile(`^(?:[^@]+@)?([^:]+):(.*)$`)
        matches := re.FindStringSubmatch(scpAddr)
        
        if len(matches) < 3 {
            return nil, fmt.Errorf("无效的SCP地址格式: %s", scpAddr)
        }
        
        host = matches[1]
        path = matches[2]
    }
    
    if host == "" {
        return nil, fmt.Errorf("无法从SCP地址中提取主机信息: %s", scpAddr)
    }
    
    baseURL := fmt.Sprintf("http://%s:8093", host)
    
    return &SCPInfo{
        BaseURL: baseURL,
        Path:    path,
    }, nil
}

func extractBaseURL(scpAddr string) (string, error) {
    info, err := extractSCPInfo(scpAddr)
    if err != nil {
        return "", err
    }
    return info.BaseURL, nil
}

func extractPath(scpAddr string) (string, error) {
    info, err := extractSCPInfo(scpAddr)
    if err != nil {
        return "", err
    }
    return info.Path, nil
}

func isHTTPURL(address string) bool {
    u, err := url.Parse(address)
    if err != nil {
        return false
    }
    
    scheme := strings.ToLower(u.Scheme)
    return scheme == "http" || scheme == "https"
}

func isHTTPLikeURL(address string) bool {
    // 检查是否包含HTTP URL的特征
    patterns := []string{
        // 包含查询参数的URL
        `^[a-zA-Z0-9.-]+[a-zA-Z0-9._/-]*\?.*=.*`,
        // 包含常见文件扩展名和路径的URL
        `^[a-zA-Z0-9.-]+/.*\.(tar\.gz|zip|tar|gz|bz2|xz|deb|rpm|exe|msi|dmg|pkg|apk|jar|war|ear)(\?.*)?$`,
        // 包含版本号路径的URL
        `^[a-zA-Z0-9.-]+/.*/(v\d+|version|release)/.*`,
        // 包含常见云服务域名模式
        `^[a-zA-Z0-9.-]*\.(amazonaws\.com|aliyuncs\.com|qcloud\.com|jcloud\.com|proxy\.)/.*`,
    }
    
    for _, pattern := range patterns {
        if matched, _ := regexp.MatchString(pattern, address); matched {
            return true
        }
    }
    
    // 额外检查：如果地址包含域名和路径，且不符合SCP格式
    if strings.Contains(address, "/") && !isSCPAddress(address) {
        // 检查是否为域名格式
        parts := strings.Split(address, "/")
        if len(parts) > 1 {
            domain := parts[0]
            // 简单的域名格式检查
            if matched, _ := regexp.MatchString(`^[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$`, domain); matched {
                return true
            }
        }
    }
    
    return false
}

func isSCPAddress(address string) bool {
    // SCP格式: user@host:path 或 host:path
    // 但需要排除HTTP URL格式
    
    // 如果包含查询参数，很可能是HTTP URL
    if strings.Contains(address, "?") && strings.Contains(address, "=") {
        return false
    }
    
    // 如果包含多个斜杠，很可能是HTTP URL路径
    if strings.Count(address, "/") > 2 {
        return false
    }
    
    patterns := []string{
        // user@host:path (单个路径)
        `^[a-zA-Z0-9._-]+@[a-zA-Z0-9.-]+:[^:/]+(/[^/]*)?$`,
        // user@IPv4:path
        `^[a-zA-Z0-9._-]+@\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}:[^:/]+(/[^/]*)?$`,
        // user@[IPv6]:path
        `^[a-zA-Z0-9._-]+@\[[0-9a-fA-F:]+\]:[^:/]+(/[^/]*)?$`,
        // host:path (without user, 简单路径)
        `^[a-zA-Z0-9.-]+:[^:/]+(/[^/]*)?$`,
        // IPv4:path
        `^\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}:[^:/]+(/[^/]*)?$`,
        // [IPv6]:path
        `^\[[0-9a-fA-F:]+\]:[^:/]+(/[^/]*)?$`,
    }
    
    for _, pattern := range patterns {
        matched, _ := regexp.MatchString(pattern, address)
        if matched {
            // 排除Windows路径 (C:\ 等)
            if matched, _ := regexp.MatchString(`^[a-zA-Z]:[\\\/]`, address); matched {
                return false
            }
            return true
        }
    }
    return false
}

func pushRawLocal(downloadURL string, target ArtifactTarget, logf func(format string, a ...interface{})) error {
	repoTag := fmt.Sprintf("%s/%s:%s", target.Repository, target.Path, target.Version)
	return pushRawWithOptions(downloadURL, repoTag, nil, logf)
}

func pushRawRemote(downloadURL string, target ArtifactTarget, logf func(format string, a ...interface{})) error {
	info, err := extractSCPInfo(downloadURL)
	if err != nil {
		return err
	}
	havenClient := NewHavenClient(ClientConfig{
		BaseURL: info.BaseURL,
		Timeout: 600*time.Second,
	})
	_, err = havenClient.PushArtifacts(&ArtifactItem{
		Type: ArtifactTypeRawObject,
		Name: target.Path,
		Source: ArtifactSource{
			Path: info.Path,
		},
		Target: target,
		Labels: map[string]string{
			"fnname": "pushRawWithHaven",
			"fn": pushRawWithHavenFn,
		},
	})
	return err
}

type ClientConfig struct {
	BaseURL string
	Timeout time.Duration
}

type HavenClient struct {
	baseURL    string
	httpClient *http.Client
	timeout    time.Duration
}

func NewHavenClient(config ClientConfig) *HavenClient {
	if config.Timeout == 0 {
		config.Timeout = 30 * time.Second
	}

	return &HavenClient{
		baseURL: config.BaseURL,
		httpClient: &http.Client{
			Timeout: config.Timeout,
		},
		timeout: config.Timeout,
	}
}

type ArtifactItem struct {
	// 制品类型
	Type ArtifactType `json:"type"`
	// 制品名称
	Name string `json:"name"`
	// 源信息
	Source ArtifactSource `json:"source"`
	// 目标信息
	Target ArtifactTarget `json:"target,omitempty"`
	// 标签
	Labels map[string]string `json:"labels,omitempty"`
}

type ArtifactType string

const (
	ArtifactTypeContainerImage ArtifactType = "container-image"
	ArtifactTypeHelmChart      ArtifactType = "helm-chart"
	ArtifactTypeRawObject      ArtifactType = "raw-object"
	ArtifactTypeRPMPackage     ArtifactType = "rpm-package"
	ArtifactTypeGeneric        ArtifactType = "generic"
)

type ArtifactSource struct {
	// 仓库URL
	Repository string `json:"repository"`
	// 路径或名称
	Path string `json:"path"`
	// 版本或标签
	Version string `json:"version,omitempty"`
	// 认证信息引用
	CredentialsRef string `json:"credentialsRef,omitempty"`
}

type ArtifactTarget struct {
	// 目标仓库
	Repository string `json:"repository,omitempty"`
	// 目标路径
	Path string `json:"path,omitempty"`
	// 目标版本
	Version string `json:"version,omitempty"`
}

func (at ArtifactTarget) String() string {
	return fmt.Sprintf("%s/%s:%s", at.Repository, at.Path, at.Version)
}

func (c *HavenClient) PushArtifacts(artifact *ArtifactItem) (*ArtifactItem, error) {
	if artifact == nil {
		return nil, fmt.Errorf("artifact cannot be nil")
	}

	// 序列化请求体
	jsonData, err := json.Marshal(artifact)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal artifact: %w", err)
	}

	// 构建请求URL
	requestURL := c.baseURL + "/remote/v1/artifacts"

	// 创建HTTP请求
	req, err := http.NewRequest("POST", requestURL, bytes.NewBuffer(jsonData))
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	// 设置请求头
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Accept", "application/json")

	// 发送请求
	resp, err := c.httpClient.Do(req)
	if err != nil {
		return nil, fmt.Errorf("failed to send request: %w", err)
	}
	defer resp.Body.Close()

	// 读取响应体
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read response body: %w", err)
	}

	// 检查HTTP状态码
	if resp.StatusCode != http.StatusAccepted {
		return nil, c.handleErrorResponse(resp.StatusCode, body)
	}

	// 解析响应
	result := ArtifactItem{}
	if err := json.Unmarshal(body, &result); err != nil {
		return nil, fmt.Errorf("failed to unmarshal response: %w", err)
	}

	return &result, nil
}

func (c *HavenClient) handleErrorResponse(statusCode int, body []byte) error {
	var errorMsg string
	
	// 尝试解析错误响应
	var errorResp map[string]interface{}
	if err := json.Unmarshal(body, &errorResp); err == nil {
		if msg, ok := errorResp["error"].(string); ok {
			errorMsg = msg
		} else if msg, ok := errorResp["message"].(string); ok {
			errorMsg = msg
		}
	}
	
	if errorMsg == "" {
		errorMsg = string(body)
	}

	return &APIError{
		StatusCode: statusCode,
		Message:    errorMsg,
	}
}

type APIError struct {
	StatusCode int    `json:"status_code"`
	Message    string `json:"message"`
}

func (e *APIError) Error() string {
	return fmt.Sprintf("API error (status %d): %s", e.StatusCode, e.Message)
}


func pushRawUnkonwn(downloadURL string, target ArtifactTarget, logf func(format string, a ...interface{})) error {
	return errors.New("unknown transfer method")
}

func pushRawWithOptions(downloadURL, repoTag string, options []string, logf func(format string, a ...interface{})) error {
    if logf == nil {
        logf = func(format string, a ...interface{}) {
            fmt.Printf("[LOG] "+format+"\n", a...)
        }
    }

    if downloadURL == "" {
        return fmt.Errorf("download URL cannot be empty")
    }
    if repoTag == "" {
        return fmt.Errorf("repository:tag cannot be empty")
    }

    normalizedURL := normalizeURL(downloadURL)
    if !isValidURL(normalizedURL) {
        return fmt.Errorf("invalid URL format: %s", downloadURL)
    }

    if !strings.Contains(repoTag, ":") {
        return fmt.Errorf("repository:tag format should be 'repo:tag', got: %s", repoTag)
    }

    logf("开始处理 Raw 文件推送任务")
    logf("下载地址: %s", normalizedURL)
    logf("目标仓库: %s", repoTag)

    downloadedFile, err := downloadRawFile(normalizedURL, logf)
    if err != nil {
        return fmt.Errorf("下载文件失败: %v", err)
    }
    defer func() {
        if err := os.Remove(downloadedFile); err != nil {
            logf("清理临时文件失败: %v", err)
        } else {
            logf("已清理临时文件: %s", downloadedFile)
        }
    }()

    err = pushRawWithHaven(downloadedFile, repoTag, options, logf)
    if err != nil {
        return fmt.Errorf("推送文件到仓库失败: %v", err)
    }

    logf("Raw 文件推送完成: %s -> %s", downloadURL, repoTag)
    return nil
}

func normalizeURL(address string) string {
    if strings.HasPrefix(address, "http://") || strings.HasPrefix(address, "https://") {
        return address
    }
    
    return "https://" + address
}

func downloadRawFile(downloadURL string, logf func(format string, a ...interface{})) (string, error) {
    logf("开始下载文件: %s", downloadURL)

    client := &http.Client{
        Timeout: 30 * time.Minute, // 30分钟超时，适应大文件下载
    }

    resp, err := tryHTTPRequest(client, downloadURL, logf)
    if err != nil {
        return "", fmt.Errorf("HTTP 请求失败: %v", err)
    }
    defer resp.Body.Close()

    if resp.StatusCode != http.StatusOK {
        return "", fmt.Errorf("HTTP 请求失败，状态码: %d", resp.StatusCode)
    }

    fileName := extractFileNameFromURL(downloadURL)
    if fileName == "" {
        fileName = fmt.Sprintf("raw_file_%d", time.Now().Unix())
        if cd := resp.Header.Get("Content-Disposition"); cd != "" {
            if name := extractFileNameFromContentDisposition(cd); name != "" {
                fileName = name
            }
        }
    }

    tmpDir := os.TempDir()
    tmpFile := filepath.Join(tmpDir, fileName)

    logf("保存文件到: %s", tmpFile)

    file, err := os.Create(tmpFile)
    if err != nil {
        return "", fmt.Errorf("创建本地文件失败: %v", err)
    }
    defer file.Close()

    written, err := copyWithProgress(file, resp.Body, resp.ContentLength, logf)
    if err != nil {
        os.Remove(tmpFile)
        return "", fmt.Errorf("下载文件失败: %v", err)
    }

    logf("下载完成，文件大小: %d bytes", written)
    return tmpFile, nil
}

func tryHTTPRequest(client *http.Client, downloadURL string, logf func(format string, a ...interface{})) (*http.Response, error) {
    req, err := http.NewRequest("GET", downloadURL, nil)
    if err != nil {
        return nil, err
    }
    
    req.Header.Set("User-Agent", "GoFileTransfer/1.0")
    
    resp, err := client.Do(req)
    if err != nil {
        if strings.HasPrefix(downloadURL, "https://") {
            httpURL := strings.Replace(downloadURL, "https://", "http://", 1)
            logf("HTTPS请求失败，尝试HTTP: %s", httpURL)
            
            req, err = http.NewRequest("GET", httpURL, nil)
            if err != nil {
                return nil, err
            }
            req.Header.Set("User-Agent", "GoFileTransfer/1.0")
            
            return client.Do(req)
        }
        return nil, err
    }
    
    return resp, nil
}

func extractFileNameFromContentDisposition(cd string) string {
    parts := strings.Split(cd, ";")
    for _, part := range parts {
        part = strings.TrimSpace(part)
        if strings.HasPrefix(part, "filename=") {
            filename := strings.TrimPrefix(part, "filename=")
            filename = strings.Trim(filename, `"`)
            return filename
        }
    }
    return ""
}

func copyWithProgress(dst io.Writer, src io.Reader, totalSize int64, logf func(format string, a ...interface{})) (int64, error) {
    var written int64
    buf := make([]byte, 32*1024) // 32KB buffer
    lastLogTime := time.Now()
    
    for {
        nr, er := src.Read(buf)
        if nr > 0 {
            nw, ew := dst.Write(buf[0:nr])
            if nw > 0 {
                written += int64(nw)
            }
            if ew != nil {
                return written, ew
            }
            if nr != nw {
                return written, io.ErrShortWrite
            }
            
            if time.Since(lastLogTime) > 5*time.Second {
                if totalSize > 0 {
                    progress := float64(written) / float64(totalSize) * 100
                    logf("下载进度: %.1f%% (%d/%d bytes)", progress, written, totalSize)
                } else {
                    logf("已下载: %d bytes", written)
                }
                lastLogTime = time.Now()
            }
        }
        if er != nil {
            if er != io.EOF {
                return written, er
            }
            break
        }
    }
    return written, nil
}

var pushRawWithHavenFn = `
function pushRawWithHaven(filePath, repoTag, options, logf)
    logf("开始使用 haven 推送文件: %s -> %s", filePath, repoTag)

    -- 构建 haven 命令: haven raw push myapp:latest --path /path/to/files
    local args = {"raw", "push", repoTag, "--path", filePath}
    
    -- 添加额外选项参数
    if options and #options > 0 then
        for i = 1, #options do
            table.insert(args, options[i])
        end
    end

    logf("执行命令: haven %s", table.concat(args, " "))

    -- 构建完整命令字符串
    local cmd = "haven " .. table.concat(args, " ")
    
    -- 获取文件所在目录
    local dir = filePath:match("(.*/)")
    if dir then
        cmd = "cd " .. dir .. " && " .. cmd
    end
    
    -- 执行命令并捕获输出
    local handle = io.popen(cmd .. " 2>&1")
    if not handle then
        return string.format("无法执行命令: %s", cmd)
    end
    
    local output = handle:read("*a")
    local success, exit_type, exit_code = handle:close()
    
    if not success or (exit_code and exit_code ~= 0) then
        return string.format("haven 命令执行失败: 退出码 %s\n输出: %s", 
                           tostring(exit_code), output)
    end

    logf("haven 命令执行成功")
    if output and #output > 0 then
        logf("输出: %s", output)
    end
    
    return nil
end
`

func pushRawWithHaven(filePath, repoTag string, options []string, logf func(format string, a ...interface{})) error {
    logf("开始使用 haven 推送文件: %s -> %s", filePath, repoTag)

    args := []string{"raw", "push", repoTag, "--path", filePath}
    
    if len(options) > 0 {
        args = append(args, options...)
    }

    logf("执行命令: haven %s", strings.Join(args, " "))

    cmd := exec.Command("haven", args...)
    
    cmd.Dir = filepath.Dir(filePath)
    
    output, err := cmd.CombinedOutput()
    if err != nil {
        return fmt.Errorf("haven 命令执行失败: %v\n输出: %s", err, string(output))
    }

    logf("haven 命令执行成功")
    if len(output) > 0 {
        logf("输出: %s", string(output))
    }
    
    return nil
}