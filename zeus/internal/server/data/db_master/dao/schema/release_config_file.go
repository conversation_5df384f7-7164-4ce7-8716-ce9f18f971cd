package schema

import (
	"entgo.io/ent"
	"entgo.io/ent/dialect"
	"entgo.io/ent/dialect/entsql"
	"entgo.io/ent/schema"
	"entgo.io/ent/schema/edge"
	"entgo.io/ent/schema/field"
	"entgo.io/ent/schema/index"
	"github.com/openlyinc/pointy"

	jMixin "coding.jd.com/pcd-application/win-go/ent/mixin"
)

type ReleaseConfigFile struct {
	ent.Schema
}

func (ReleaseConfigFile) Mixin() []ent.Mixin {
	return []ent.Mixin{
		jMixin.UpdatedAt{},
		jMixin.CreatedAt{},
	}
}

func (ReleaseConfigFile) Fields() []ent.Field {
	return []ent.Field{
		field.Int32("id").
			Comment("主键").
			SchemaType(map[string]string{
				dialect.MySQL: "int unsigned",
			}).
			Immutable(),
		field.Enum("cloud_type").
			Comment("云底座类别").
			Values("jdstack", "cvessel").
			Default("jdstack").
			Nillable().
			Optional().
			Immutable(),
		field.Int32("release_id").
			Comment("封版版本ID").
			SchemaType(map[string]string{
				dialect.MySQL: "int unsigned",
			}).
			Nillable().
			Default(0).
			Immutable(),
		field.String("name").
			Comment("文件名称").
			SchemaType(map[string]string{
				dialect.MySQL: "varchar(255)",
			}).
			Nillable().
			Optional().
			MinLen(1).
			MaxLen(50).
			Default(""),
		field.String("relative_path").
			Comment("文件相对路径").
			SchemaType(map[string]string{
				dialect.MySQL: "varchar(255)",
			}).
			Nillable().
			Optional().
			MinLen(1).
			MaxLen(50).
			Default(""),
		field.String("description").
			Comment("描述").
			SchemaType(map[string]string{
				dialect.MySQL: "varchar(255)",
			}).
			Nillable().
			Optional().
			MinLen(0).
			MaxLen(255).
			Default(""),
		field.String("content").
			Comment("文件内容").
			SchemaType(map[string]string{
				dialect.MySQL: "longtext",
			}).
			Nillable().
			Optional().
			Default(""),
	}
}

func (ReleaseConfigFile) Indexes() []ent.Index {
	return []ent.Index{
		index.Fields("name", "relative_path", "release_id").Unique(),
		index.Fields("release_id"),
	}
}

func (ReleaseConfigFile) Annotations() []schema.Annotation {
	table := "release_config_file"
	return []schema.Annotation{
		schema.Comment("云舰/JDStack版本内配置文件管理"),
		entsql.Annotation{
			Table:        table,
			WithComments: pointy.Bool(true),
			Incremental:  pointy.Bool(true),
			Charset:      "utf8mb4",
			Collation:    "utf8mb4_general_ci",
			Checks:       map[string]string{},
		},
	}
}

func (ReleaseConfigFile) Hooks() []ent.Hook {
	// 禁止删除在这里配置
	// return []ent.Hook{
	// 	hook.Reject(ent.OpDelete | ent.OpDeleteOne),
	// }
	return []ent.Hook{}
}

func (ReleaseConfigFile) Edges() []ent.Edge {
	return []ent.Edge{
		edge.From("jdstack_release", JdstackRelease.Type).
			Comment("关联的封版版本主键Id").
			Ref("release_config_file").
			Field("release_id").
			Immutable().
			Required().
			Unique(),
	}
}
