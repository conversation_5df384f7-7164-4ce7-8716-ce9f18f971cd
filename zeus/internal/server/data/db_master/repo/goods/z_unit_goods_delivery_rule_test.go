package repoGoods

import (
	"context"
	"testing"

	"coding.jd.com/fabric/zeusV2/zeus/internal/server/data/db_master/dao/goods"
	"coding.jd.com/fabric/zeusV2/zeus/internal/server/data/db_master/dao/metadataresource"
	"coding.jd.com/pcd-application/win-go/third_party/winapi"
)

func Test_CreateGoodsDeliveryRuleGroup(t *testing.T) {

	var (
		ctx = context.Background()
	)

	// node2, err2 := instanceDeliveryRule.List(ctx, -1, 0, winapi.Filters{
	// 	{
	// 		Name: goodsdeliveryrule.FilterWithGoodsDeliveryRuleGroup,
	// 	},
	// }, nil)
	// if err2 != nil {
	// 	panic("error")
	// }
	// println(node2)

	node1, err1 := instance.List(ctx, -1, 0, winapi.Filters{
		{Name: winapi.MakeFilterName(goods.FilterHasMetadataResourceWith, metadataresource.FieldName), Values: []string{"vm_dataplane_kvm", "vm_dataplane_alb"}},
		{Name: goods.FilterWithHardwareRole},
		{Name: "version", Values: []string{"5.3.0"}},
		{Name: goods.FilterWithHardwareRoleLabel},
	}, nil)
	if err1 != nil {
		panic("error")
	}
	println(node1)
}

func Test_CreateGoodsGoodsDeliveryRuleGroup(t *testing.T) {

	var (
		ctx = context.Background()
	)

	err := instance.CreateGoodsDeliveryRuleGroupID(ctx, "goods-0i130a70r51p1", 1, 2)
	if err != nil {
		println("error")
	}

}
