package servMetadata

import (
	"context"

	pbMetadata "coding.jd.com/fabric/zeusV2/zeus/api/server/pb/metadata/v1"
	jContext "coding.jd.com/pcd-application/win-go/context"
	jDto "coding.jd.com/pcd-application/win-go/dto"
)

func (uc *metadataServer) DescribesResource(ctx context.Context, uCtx jContext.Context, req *pbMetadata.DescribesReq) (*pbMetadata.DescribesResourceReply, error) {

	nodes, err := uc.MetadataResourceRepo.List(ctx, req.PageNumber, req.PageSize, req.Filters, req.Sorts)
	if err != nil {
		return nil, err
	}

	return &pbMetadata.DescribesResourceReply{
		List:       jDto.UseDTOs(uc.DTO, nodes, (*pbMetadata.DescribeResourceReply)(nil)),
		TotalCount: uc.MetadataResourceRepo.TotalCount(ctx, req.Filters),
	}, nil
}
