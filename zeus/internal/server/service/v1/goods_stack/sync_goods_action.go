package servGoodsStack

import (
	"context"

	pbGoodsStack "coding.jd.com/fabric/zeusV2/zeus/api/server/pb/goods_stack/v1"
	jContext "coding.jd.com/pcd-application/win-go/context"
)

// ModifySyncGoodsAction 同步标品状态，将不在列表中的标品状态设置为offline
func (uc *goodsStackServer) ModifySyncGoodsAction(ctx context.Context, uCtx jContext.Context, req *pbGoodsStack.SyncGoodsActionReq) (*pbGoodsStack.SyncGoodsActionReply, error) {
	// 调用业务层方法
	offlineCount, err := uc.GoodsStackCase.ModifySyncGoodsAction(ctx, req)
	if err != nil {
		return nil, err
	}

	// 返回结果
	return &pbGoodsStack.SyncGoodsActionReply{
		OfflineCount: offlineCount,
	}, nil
}
