package servJDStackFilecenter

import (
	"context"

	jContext "coding.jd.com/pcd-application/win-go/context"
	"github.com/go-kratos/kratos/v2/log"

	pbJdstackFilecenter "coding.jd.com/fabric/zeusV2/zeus/api/server/pb/jdstack_filecenter/v1"
)

// CreateSCFilesPushTask is a generated function.
func (uc *jdstackFilecenterServer) CreateSCFilesPushTask(ctx context.Context, c jContext.Context, req *pbJdstackFilecenter.CreateSCFilesTaskReq) (*pbJdstackFilecenter.DescribeTaskReply, error) {
	uc.log.Debugw(log.DefaultMessageKey, "start CreateSCFilesPushTask1")
	taskID, err := uc.JDStackFilecenterCase.CreateSCFilesPushTask(ctx, req)
	if err != nil {
		return nil, err
	}
	return &pbJdstackFilecenter.DescribeTaskReply{
		Code: 0,
		Data: &pbJdstackFilecenter.TaskInfo{
			TaskID: taskID,
		},
		Message: "任务创建成功",
	}, nil
}
