package servJDStackFilecenter

import (
	doVersion "coding.jd.com/fabric/zeusV2/zeus/internal/server/domain/version"
	"context"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/golang/protobuf/ptypes/empty"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"

	jContext "coding.jd.com/pcd-application/win-go/context"
	jDto "coding.jd.com/pcd-application/win-go/dto"

	pbJdstackFilecenter "coding.jd.com/fabric/zeusV2/zeus/api/server/pb/jdstack_filecenter/v1"
	"coding.jd.com/fabric/zeusV2/zeus/internal/server/conf"
	doGoodsStack "coding.jd.com/fabric/zeusV2/zeus/internal/server/domain/goods_stack"
	doJDStackFilecenter "coding.jd.com/fabric/zeusV2/zeus/internal/server/domain/jdstack_filecenter"
)

const Self = conf.DomainCode_JDStackFilecenter

type jdstackFilecenterServer struct {
	log                   *log.Helper
	DTO                   jDto.IDTO
	GoodsStackCase        doGoodsStack.GoodsStackCase
	GoodsRepo             doGoodsStack.GoodsRepo
	JdstackReleaseRepo    doVersion.JdstackReleaseRepo
	JDStackFilecenterCase doJDStackFilecenter.JDStackFilecenterCase
	FilecenterConf        *conf.Other_Filecenter
}

func (uc *jdstackFilecenterServer) CreateArtifactPackage(ctx context.Context, c jContext.Context, req *pbJdstackFilecenter.CreateArtifactPackageReq) (*pbJdstackFilecenter.DescribeCommonReply, error) {
	// TODO implement me
	panic("implement me")
}

func (uc *jdstackFilecenterServer) CreateDockerImagePushTask(ctx context.Context, c jContext.Context, req *pbJdstackFilecenter.DockerImageTaskReq) (*pbJdstackFilecenter.DescribeCommonReply, error) {
	// TODO implement me
	panic("implement me")
}

func (uc *jdstackFilecenterServer) CreateMetadataPackage(ctx context.Context, c jContext.Context, req *pbJdstackFilecenter.CreateMetadataPackageReq) (*pbJdstackFilecenter.DescribeCommonReply, error) {
	// TODO implement me
	panic("implement me")
}

func (uc *jdstackFilecenterServer) CreateRegularFilePushTask(ctx context.Context, c jContext.Context, req *pbJdstackFilecenter.RegularFileTaskReq) (*pbJdstackFilecenter.DescribeCommonReply, error) {
	// TODO implement me
	panic("implement me")
}

func (uc *jdstackFilecenterServer) CreateRpmPushTask(ctx context.Context, c jContext.Context, req *pbJdstackFilecenter.RpmTaskReq) (*pbJdstackFilecenter.DescribeCommonReply, error) {
	// TODO implement me
	panic("implement me")
}

func (uc *jdstackFilecenterServer) CreateSkywingPackagePushTask(ctx context.Context, c jContext.Context, req *pbJdstackFilecenter.SkywingPackageTaskReq) (*pbJdstackFilecenter.DescribeCommonReply, error) {
	// TODO implement me
	panic("implement me")
}

func (uc *jdstackFilecenterServer) DescribeArtifactPackage(ctx context.Context, c jContext.Context, req *pbJdstackFilecenter.DescribeTaskReq) (*pbJdstackFilecenter.DescribeArtifactPackageReply, error) {
	// TODO implement me
	panic("implement me")
}

func (uc *jdstackFilecenterServer) DescribeDockerImagePushTask(ctx context.Context, c jContext.Context, req *pbJdstackFilecenter.DescribeTaskReq) (*pbJdstackFilecenter.DescribeCommonFileTaskReply, error) {
	// TODO implement me
	panic("implement me")
}

func (uc *jdstackFilecenterServer) DescribeMetadataPackage(ctx context.Context, c jContext.Context, req *pbJdstackFilecenter.DescribeTaskReq) (*pbJdstackFilecenter.DescribeArtifactPackageReply, error) {
	// TODO implement me
	panic("implement me")
}

func (uc *jdstackFilecenterServer) DescribeRegularFilePushTask(ctx context.Context, c jContext.Context, req *pbJdstackFilecenter.DescribeTaskReq) (*pbJdstackFilecenter.DescribeCommonFileTaskReply, error) {
	// TODO implement me
	panic("implement me")
}

func (uc *jdstackFilecenterServer) DescribeRpmPushTask(ctx context.Context, c jContext.Context, req *pbJdstackFilecenter.DescribeTaskReq) (*pbJdstackFilecenter.DescribeCommonFileTaskReply, error) {
	// TODO implement me
	panic("implement me")
}

func (uc *jdstackFilecenterServer) DescribeSkywingPackagePushTask(ctx context.Context, c jContext.Context, req *pbJdstackFilecenter.DescribeTaskReq) (*pbJdstackFilecenter.DescribeCommonFileTaskReply, error) {
	// TODO implement me
	panic("implement me")
}

func (uc *jdstackFilecenterServer) DescribeDeploymentServerInfo(ctx context.Context, c jContext.Context, _ *empty.Empty) (*pbJdstackFilecenter.DeploymentServerInfoReply, error) {
	if uc.FilecenterConf == nil || uc.FilecenterConf.Ssh.Server == "" {
		return nil, status.Error(codes.Internal, "Deployment server IP is not configured")
	}

	return &pbJdstackFilecenter.DeploymentServerInfoReply{
		Ip: uc.FilecenterConf.Ssh.Server,
		// Path: uc.FilecenterConf.DeploymentServerPath, // This might be empty if not configured
	}, nil
}

func NewJDStackFilecenterServer(
	logger log.Logger,
	DTO jDto.IDTO,
	GoodsStackCase doGoodsStack.GoodsStackCase,
	GoodsRepo doGoodsStack.GoodsRepo,
	JdstackReleaseRepo doVersion.JdstackReleaseRepo,
	JDStackFilecenterCase doJDStackFilecenter.JDStackFilecenterCase,
	FilecenterConf *conf.Other_Filecenter,
) pbJdstackFilecenter.JdstackFilecenterHTTPServer {
	return &jdstackFilecenterServer{
		log:                   log.NewHelper(log.With(logger, "module", "serv/jdstackFilecenter")),
		DTO:                   DTO,
		JDStackFilecenterCase: JDStackFilecenterCase,
		GoodsStackCase:        GoodsStackCase,
		JdstackReleaseRepo:    JdstackReleaseRepo,
		GoodsRepo:             GoodsRepo,
		FilecenterConf:        FilecenterConf,
	}
}
