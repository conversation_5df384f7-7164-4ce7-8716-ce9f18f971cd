// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: conf.proto

package conf

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on Bootstrap with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *Bootstrap) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Bootstrap with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in BootstrapMultiError, or nil
// if none found.
func (m *Bootstrap) ValidateAll() error {
	return m.validate(true)
}

func (m *Bootstrap) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(m.GetTitle()) < 1 {
		err := BootstrapValidationError{
			field:  "Title",
			reason: "value length must be at least 1 bytes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetEnv() == nil {
		err := BootstrapValidationError{
			field:  "Env",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetEnv()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, BootstrapValidationError{
					field:  "Env",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, BootstrapValidationError{
					field:  "Env",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetEnv()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return BootstrapValidationError{
				field:  "Env",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	{
		sorted_keys := make([]int32, len(m.GetTask()))
		i := 0
		for key := range m.GetTask() {
			sorted_keys[i] = key
			i++
		}
		sort.Slice(sorted_keys, func(i, j int) bool { return sorted_keys[i] < sorted_keys[j] })
		for _, key := range sorted_keys {
			val := m.GetTask()[key]
			_ = val

			// no validation rules for Task[key]

			if all {
				switch v := interface{}(val).(type) {
				case interface{ ValidateAll() error }:
					if err := v.ValidateAll(); err != nil {
						errors = append(errors, BootstrapValidationError{
							field:  fmt.Sprintf("Task[%v]", key),
							reason: "embedded message failed validation",
							cause:  err,
						})
					}
				case interface{ Validate() error }:
					if err := v.Validate(); err != nil {
						errors = append(errors, BootstrapValidationError{
							field:  fmt.Sprintf("Task[%v]", key),
							reason: "embedded message failed validation",
							cause:  err,
						})
					}
				}
			} else if v, ok := interface{}(val).(interface{ Validate() error }); ok {
				if err := v.Validate(); err != nil {
					return BootstrapValidationError{
						field:  fmt.Sprintf("Task[%v]", key),
						reason: "embedded message failed validation",
						cause:  err,
					}
				}
			}

		}
	}

	if m.GetWeb() == nil {
		err := BootstrapValidationError{
			field:  "Web",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetWeb()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, BootstrapValidationError{
					field:  "Web",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, BootstrapValidationError{
					field:  "Web",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetWeb()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return BootstrapValidationError{
				field:  "Web",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if m.GetLog() == nil {
		err := BootstrapValidationError{
			field:  "Log",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetLog()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, BootstrapValidationError{
					field:  "Log",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, BootstrapValidationError{
					field:  "Log",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetLog()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return BootstrapValidationError{
				field:  "Log",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if m.GetHttpClient() == nil {
		err := BootstrapValidationError{
			field:  "HttpClient",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetHttpClient()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, BootstrapValidationError{
					field:  "HttpClient",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, BootstrapValidationError{
					field:  "HttpClient",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHttpClient()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return BootstrapValidationError{
				field:  "HttpClient",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if m.GetMc() == nil {
		err := BootstrapValidationError{
			field:  "Mc",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetMc()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, BootstrapValidationError{
					field:  "Mc",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, BootstrapValidationError{
					field:  "Mc",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetMc()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return BootstrapValidationError{
				field:  "Mc",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	{
		sorted_keys := make([]string, len(m.GetSdk()))
		i := 0
		for key := range m.GetSdk() {
			sorted_keys[i] = key
			i++
		}
		sort.Slice(sorted_keys, func(i, j int) bool { return sorted_keys[i] < sorted_keys[j] })
		for _, key := range sorted_keys {
			val := m.GetSdk()[key]
			_ = val

			// no validation rules for Sdk[key]

			if all {
				switch v := interface{}(val).(type) {
				case interface{ ValidateAll() error }:
					if err := v.ValidateAll(); err != nil {
						errors = append(errors, BootstrapValidationError{
							field:  fmt.Sprintf("Sdk[%v]", key),
							reason: "embedded message failed validation",
							cause:  err,
						})
					}
				case interface{ Validate() error }:
					if err := v.Validate(); err != nil {
						errors = append(errors, BootstrapValidationError{
							field:  fmt.Sprintf("Sdk[%v]", key),
							reason: "embedded message failed validation",
							cause:  err,
						})
					}
				}
			} else if v, ok := interface{}(val).(interface{ Validate() error }); ok {
				if err := v.Validate(); err != nil {
					return BootstrapValidationError{
						field:  fmt.Sprintf("Sdk[%v]", key),
						reason: "embedded message failed validation",
						cause:  err,
					}
				}
			}

		}
	}

	if all {
		switch v := interface{}(m.GetJob()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, BootstrapValidationError{
					field:  "Job",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, BootstrapValidationError{
					field:  "Job",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetJob()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return BootstrapValidationError{
				field:  "Job",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if m.GetOther() == nil {
		err := BootstrapValidationError{
			field:  "Other",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetOther()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, BootstrapValidationError{
					field:  "Other",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, BootstrapValidationError{
					field:  "Other",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetOther()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return BootstrapValidationError{
				field:  "Other",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return BootstrapMultiError(errors)
	}

	return nil
}

// BootstrapMultiError is an error wrapping multiple validation errors returned
// by Bootstrap.ValidateAll() if the designated constraints aren't met.
type BootstrapMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m BootstrapMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m BootstrapMultiError) AllErrors() []error { return m }

// BootstrapValidationError is the validation error returned by
// Bootstrap.Validate if the designated constraints aren't met.
type BootstrapValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e BootstrapValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e BootstrapValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e BootstrapValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e BootstrapValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e BootstrapValidationError) ErrorName() string { return "BootstrapValidationError" }

// Error satisfies the builtin error interface
func (e BootstrapValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sBootstrap.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = BootstrapValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = BootstrapValidationError{}

// Validate checks the field values on Other with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *Other) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Other with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in OtherMultiError, or nil if none found.
func (m *Other) ValidateAll() error {
	return m.validate(true)
}

func (m *Other) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetFilecenter() == nil {
		err := OtherValidationError{
			field:  "Filecenter",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetFilecenter()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, OtherValidationError{
					field:  "Filecenter",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, OtherValidationError{
					field:  "Filecenter",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetFilecenter()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return OtherValidationError{
				field:  "Filecenter",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if m.GetTaskConfig() == nil {
		err := OtherValidationError{
			field:  "TaskConfig",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetTaskConfig()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, OtherValidationError{
					field:  "TaskConfig",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, OtherValidationError{
					field:  "TaskConfig",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTaskConfig()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return OtherValidationError{
				field:  "TaskConfig",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return OtherMultiError(errors)
	}

	return nil
}

// OtherMultiError is an error wrapping multiple validation errors returned by
// Other.ValidateAll() if the designated constraints aren't met.
type OtherMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m OtherMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m OtherMultiError) AllErrors() []error { return m }

// OtherValidationError is the validation error returned by Other.Validate if
// the designated constraints aren't met.
type OtherValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e OtherValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e OtherValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e OtherValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e OtherValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e OtherValidationError) ErrorName() string { return "OtherValidationError" }

// Error satisfies the builtin error interface
func (e OtherValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sOther.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = OtherValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = OtherValidationError{}

// Validate checks the field values on Other_Filecenter with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *Other_Filecenter) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Other_Filecenter with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// Other_FilecenterMultiError, or nil if none found.
func (m *Other_Filecenter) ValidateAll() error {
	return m.validate(true)
}

func (m *Other_Filecenter) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetSsh() == nil {
		err := Other_FilecenterValidationError{
			field:  "Ssh",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetSsh()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, Other_FilecenterValidationError{
					field:  "Ssh",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, Other_FilecenterValidationError{
					field:  "Ssh",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetSsh()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return Other_FilecenterValidationError{
				field:  "Ssh",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	{
		sorted_keys := make([]string, len(m.GetCli()))
		i := 0
		for key := range m.GetCli() {
			sorted_keys[i] = key
			i++
		}
		sort.Slice(sorted_keys, func(i, j int) bool { return sorted_keys[i] < sorted_keys[j] })
		for _, key := range sorted_keys {
			val := m.GetCli()[key]
			_ = val

			if _, ok := _Other_Filecenter_Cli_InLookup[key]; !ok {
				err := Other_FilecenterValidationError{
					field:  fmt.Sprintf("Cli[%v]", key),
					reason: "value must be in list [package_path handler_path handler_rc_path is_test export_sql_shell_path export_sql_user export_sql_password jdstack_yum_path fedhub]",
				}
				if !all {
					return err
				}
				errors = append(errors, err)
			}

			// no validation rules for Cli[key]
		}
	}

	if m.RsyncBwLimit != nil {

		if val := m.GetRsyncBwLimit(); val < 100 || val > 300 {
			err := Other_FilecenterValidationError{
				field:  "RsyncBwLimit",
				reason: "value must be inside range [100, 300]",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

	}

	if len(errors) > 0 {
		return Other_FilecenterMultiError(errors)
	}

	return nil
}

// Other_FilecenterMultiError is an error wrapping multiple validation errors
// returned by Other_Filecenter.ValidateAll() if the designated constraints
// aren't met.
type Other_FilecenterMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m Other_FilecenterMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m Other_FilecenterMultiError) AllErrors() []error { return m }

// Other_FilecenterValidationError is the validation error returned by
// Other_Filecenter.Validate if the designated constraints aren't met.
type Other_FilecenterValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e Other_FilecenterValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e Other_FilecenterValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e Other_FilecenterValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e Other_FilecenterValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e Other_FilecenterValidationError) ErrorName() string { return "Other_FilecenterValidationError" }

// Error satisfies the builtin error interface
func (e Other_FilecenterValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sOther_Filecenter.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = Other_FilecenterValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = Other_FilecenterValidationError{}

var _Other_Filecenter_Cli_InLookup = map[string]struct{}{
	"package_path":          {},
	"handler_path":          {},
	"handler_rc_path":       {},
	"is_test":               {},
	"export_sql_shell_path": {},
	"export_sql_user":       {},
	"export_sql_password":   {},
	"jdstack_yum_path":      {},
	"fedhub":                {},
}

// Validate checks the field values on Other_TaskConfig with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *Other_TaskConfig) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Other_TaskConfig with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// Other_TaskConfigMultiError, or nil if none found.
func (m *Other_TaskConfig) ValidateAll() error {
	return m.validate(true)
}

func (m *Other_TaskConfig) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetConcurrency() < 1 {
		err := Other_TaskConfigValidationError{
			field:  "Concurrency",
			reason: "value must be greater than or equal to 1",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return Other_TaskConfigMultiError(errors)
	}

	return nil
}

// Other_TaskConfigMultiError is an error wrapping multiple validation errors
// returned by Other_TaskConfig.ValidateAll() if the designated constraints
// aren't met.
type Other_TaskConfigMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m Other_TaskConfigMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m Other_TaskConfigMultiError) AllErrors() []error { return m }

// Other_TaskConfigValidationError is the validation error returned by
// Other_TaskConfig.Validate if the designated constraints aren't met.
type Other_TaskConfigValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e Other_TaskConfigValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e Other_TaskConfigValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e Other_TaskConfigValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e Other_TaskConfigValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e Other_TaskConfigValidationError) ErrorName() string { return "Other_TaskConfigValidationError" }

// Error satisfies the builtin error interface
func (e Other_TaskConfigValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sOther_TaskConfig.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = Other_TaskConfigValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = Other_TaskConfigValidationError{}
