syntax = "proto3";

package jdstackintegration.conf;

option go_package = "internal/server/conf;conf";

import "validate/validate.proto";
import "winconf/winconf.proto";

message Bootstrap {
  string title = 1 [(validate.rules).string.min_bytes = 1];

  wingo.conf.Env env = 2 [(validate.rules).message.required = true];
  map<int32, wingo.conf.Task> task = 3;
  wingo.conf.Web web = 4 [(validate.rules).message.required = true];
  wingo.conf.Log log = 7 [(validate.rules).message.required = true];
  wingo.conf.HttpClient http_client = 8 [(validate.rules).message.required = true];

  wingo.conf.Middleware mc = 10 [(validate.rules).message.required = true];
  map<string, wingo.conf.SDK> sdk = 11;
  wingo.conf.Job job = 12;
  Other other = 13 [(validate.rules).message.required = true];
}

message Other {
  message Filecenter {
    // 目标机器及ssh登录信息
    wingo.conf.Ssh ssh = 1 [(validate.rules).message = {required: true}];
    // 公共路径配置
    map<string, string> cli = 2 [
      (validate.rules).map.keys.string = {in: "package_path", in: "handler_path", in: "handler_rc_path", in: "is_test", in: "export_sql_shell_path", in: "export_sql_user", in: "export_sql_password", in: "jdstack_yum_path", in: "fedhub"}
    ];
    // rsync传输速度限制 MB/s
    optional int64 rsyncBwLimit = 3 [
      (validate.rules).int64 = {lte: 300, gte: 100}
    ];
  }
  message TaskConfig {
    int32 concurrency = 1 [(validate.rules).int32 = {gte: 1}];
  }
  // Filecenter相关配置
  Filecenter filecenter = 1 [(validate.rules).message = {required: true}];
  // 任务系统参数配置,非必填,并发度默认为1
  TaskConfig task_config = 2 [(validate.rules).message = {required: true}];
}
