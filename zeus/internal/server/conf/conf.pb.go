// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.29.3
// source: conf.proto

package conf

import (
	winconf "coding.jd.com/pcd-application/win-go/third_party/winconf"
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type Bootstrap struct {
	state         protoimpl.MessageState  `protogen:"open.v1"`
	Title         string                  `protobuf:"bytes,1,opt,name=title,proto3" json:"title,omitempty"`
	Env           *winconf.Env            `protobuf:"bytes,2,opt,name=env,proto3" json:"env,omitempty"`
	Task          map[int32]*winconf.Task `protobuf:"bytes,3,rep,name=task,proto3" json:"task,omitempty" protobuf_key:"varint,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	Web           *winconf.Web            `protobuf:"bytes,4,opt,name=web,proto3" json:"web,omitempty"`
	Log           *winconf.Log            `protobuf:"bytes,7,opt,name=log,proto3" json:"log,omitempty"`
	HttpClient    *winconf.HttpClient     `protobuf:"bytes,8,opt,name=http_client,json=httpClient,proto3" json:"http_client,omitempty"`
	Mc            *winconf.Middleware     `protobuf:"bytes,10,opt,name=mc,proto3" json:"mc,omitempty"`
	Sdk           map[string]*winconf.SDK `protobuf:"bytes,11,rep,name=sdk,proto3" json:"sdk,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	Job           *winconf.Job            `protobuf:"bytes,12,opt,name=job,proto3" json:"job,omitempty"`
	Other         *Other                  `protobuf:"bytes,13,opt,name=other,proto3" json:"other,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Bootstrap) Reset() {
	*x = Bootstrap{}
	mi := &file_conf_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Bootstrap) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Bootstrap) ProtoMessage() {}

func (x *Bootstrap) ProtoReflect() protoreflect.Message {
	mi := &file_conf_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Bootstrap.ProtoReflect.Descriptor instead.
func (*Bootstrap) Descriptor() ([]byte, []int) {
	return file_conf_proto_rawDescGZIP(), []int{0}
}

func (x *Bootstrap) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *Bootstrap) GetEnv() *winconf.Env {
	if x != nil {
		return x.Env
	}
	return nil
}

func (x *Bootstrap) GetTask() map[int32]*winconf.Task {
	if x != nil {
		return x.Task
	}
	return nil
}

func (x *Bootstrap) GetWeb() *winconf.Web {
	if x != nil {
		return x.Web
	}
	return nil
}

func (x *Bootstrap) GetLog() *winconf.Log {
	if x != nil {
		return x.Log
	}
	return nil
}

func (x *Bootstrap) GetHttpClient() *winconf.HttpClient {
	if x != nil {
		return x.HttpClient
	}
	return nil
}

func (x *Bootstrap) GetMc() *winconf.Middleware {
	if x != nil {
		return x.Mc
	}
	return nil
}

func (x *Bootstrap) GetSdk() map[string]*winconf.SDK {
	if x != nil {
		return x.Sdk
	}
	return nil
}

func (x *Bootstrap) GetJob() *winconf.Job {
	if x != nil {
		return x.Job
	}
	return nil
}

func (x *Bootstrap) GetOther() *Other {
	if x != nil {
		return x.Other
	}
	return nil
}

type Other struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Filecenter相关配置
	Filecenter *Other_Filecenter `protobuf:"bytes,1,opt,name=filecenter,proto3" json:"filecenter,omitempty"`
	// 任务系统参数配置,非必填,并发度默认为1
	TaskConfig    *Other_TaskConfig `protobuf:"bytes,2,opt,name=task_config,json=taskConfig,proto3" json:"task_config,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Other) Reset() {
	*x = Other{}
	mi := &file_conf_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Other) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Other) ProtoMessage() {}

func (x *Other) ProtoReflect() protoreflect.Message {
	mi := &file_conf_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Other.ProtoReflect.Descriptor instead.
func (*Other) Descriptor() ([]byte, []int) {
	return file_conf_proto_rawDescGZIP(), []int{1}
}

func (x *Other) GetFilecenter() *Other_Filecenter {
	if x != nil {
		return x.Filecenter
	}
	return nil
}

func (x *Other) GetTaskConfig() *Other_TaskConfig {
	if x != nil {
		return x.TaskConfig
	}
	return nil
}

type Other_Filecenter struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 目标机器及ssh登录信息
	Ssh *winconf.Ssh `protobuf:"bytes,1,opt,name=ssh,proto3" json:"ssh,omitempty"`
	// 公共路径配置
	Cli map[string]string `protobuf:"bytes,2,rep,name=cli,proto3" json:"cli,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	// rsync传输速度限制 MB/s
	RsyncBwLimit  *int64 `protobuf:"varint,3,opt,name=rsyncBwLimit,proto3,oneof" json:"rsyncBwLimit,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Other_Filecenter) Reset() {
	*x = Other_Filecenter{}
	mi := &file_conf_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Other_Filecenter) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Other_Filecenter) ProtoMessage() {}

func (x *Other_Filecenter) ProtoReflect() protoreflect.Message {
	mi := &file_conf_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Other_Filecenter.ProtoReflect.Descriptor instead.
func (*Other_Filecenter) Descriptor() ([]byte, []int) {
	return file_conf_proto_rawDescGZIP(), []int{1, 0}
}

func (x *Other_Filecenter) GetSsh() *winconf.Ssh {
	if x != nil {
		return x.Ssh
	}
	return nil
}

func (x *Other_Filecenter) GetCli() map[string]string {
	if x != nil {
		return x.Cli
	}
	return nil
}

func (x *Other_Filecenter) GetRsyncBwLimit() int64 {
	if x != nil && x.RsyncBwLimit != nil {
		return *x.RsyncBwLimit
	}
	return 0
}

type Other_TaskConfig struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Concurrency   int32                  `protobuf:"varint,1,opt,name=concurrency,proto3" json:"concurrency,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Other_TaskConfig) Reset() {
	*x = Other_TaskConfig{}
	mi := &file_conf_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Other_TaskConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Other_TaskConfig) ProtoMessage() {}

func (x *Other_TaskConfig) ProtoReflect() protoreflect.Message {
	mi := &file_conf_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Other_TaskConfig.ProtoReflect.Descriptor instead.
func (*Other_TaskConfig) Descriptor() ([]byte, []int) {
	return file_conf_proto_rawDescGZIP(), []int{1, 1}
}

func (x *Other_TaskConfig) GetConcurrency() int32 {
	if x != nil {
		return x.Concurrency
	}
	return 0
}

var File_conf_proto protoreflect.FileDescriptor

const file_conf_proto_rawDesc = "" +
	"\n" +
	"\n" +
	"conf.proto\x12\x17jdstackintegration.conf\x1a\x17validate/validate.proto\x1a\x15winconf/winconf.proto\"\x9e\x05\n" +
	"\tBootstrap\x12\x1d\n" +
	"\x05title\x18\x01 \x01(\tB\a\xfaB\x04r\x02 \x01R\x05title\x12+\n" +
	"\x03env\x18\x02 \x01(\v2\x0f.wingo.conf.EnvB\b\xfaB\x05\x8a\x01\x02\x10\x01R\x03env\x12@\n" +
	"\x04task\x18\x03 \x03(\v2,.jdstackintegration.conf.Bootstrap.TaskEntryR\x04task\x12+\n" +
	"\x03web\x18\x04 \x01(\v2\x0f.wingo.conf.WebB\b\xfaB\x05\x8a\x01\x02\x10\x01R\x03web\x12+\n" +
	"\x03log\x18\a \x01(\v2\x0f.wingo.conf.LogB\b\xfaB\x05\x8a\x01\x02\x10\x01R\x03log\x12A\n" +
	"\vhttp_client\x18\b \x01(\v2\x16.wingo.conf.HttpClientB\b\xfaB\x05\x8a\x01\x02\x10\x01R\n" +
	"httpClient\x120\n" +
	"\x02mc\x18\n" +
	" \x01(\v2\x16.wingo.conf.MiddlewareB\b\xfaB\x05\x8a\x01\x02\x10\x01R\x02mc\x12=\n" +
	"\x03sdk\x18\v \x03(\v2+.jdstackintegration.conf.Bootstrap.SdkEntryR\x03sdk\x12!\n" +
	"\x03job\x18\f \x01(\v2\x0f.wingo.conf.JobR\x03job\x12>\n" +
	"\x05other\x18\r \x01(\v2\x1e.jdstackintegration.conf.OtherB\b\xfaB\x05\x8a\x01\x02\x10\x01R\x05other\x1aI\n" +
	"\tTaskEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\x05R\x03key\x12&\n" +
	"\x05value\x18\x02 \x01(\v2\x10.wingo.conf.TaskR\x05value:\x028\x01\x1aG\n" +
	"\bSdkEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\x12%\n" +
	"\x05value\x18\x02 \x01(\v2\x0f.wingo.conf.SDKR\x05value:\x028\x01\"\x8a\x05\n" +
	"\x05Other\x12S\n" +
	"\n" +
	"filecenter\x18\x01 \x01(\v2).jdstackintegration.conf.Other.FilecenterB\b\xfaB\x05\x8a\x01\x02\x10\x01R\n" +
	"filecenter\x12T\n" +
	"\vtask_config\x18\x02 \x01(\v2).jdstackintegration.conf.Other.TaskConfigB\b\xfaB\x05\x8a\x01\x02\x10\x01R\n" +
	"taskConfig\x1a\x9c\x03\n" +
	"\n" +
	"Filecenter\x12+\n" +
	"\x03ssh\x18\x01 \x01(\v2\x0f.wingo.conf.SshB\b\xfaB\x05\x8a\x01\x02\x10\x01R\x03ssh\x12\xe2\x01\n" +
	"\x03cli\x18\x02 \x03(\v22.jdstackintegration.conf.Other.Filecenter.CliEntryB\x9b\x01\xfaB\x97\x01\x9a\x01\x93\x01\"\x90\x01r\x8d\x01R\fpackage_pathR\fhandler_pathR\x0fhandler_rc_pathR\ais_testR\x15export_sql_shell_pathR\x0fexport_sql_userR\x13export_sql_passwordR\x10jdstack_yum_pathR\x06fedhubR\x03cli\x123\n" +
	"\frsyncBwLimit\x18\x03 \x01(\x03B\n" +
	"\xfaB\a\"\x05\x18\xac\x02(dH\x00R\frsyncBwLimit\x88\x01\x01\x1a6\n" +
	"\bCliEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\x12\x14\n" +
	"\x05value\x18\x02 \x01(\tR\x05value:\x028\x01B\x0f\n" +
	"\r_rsyncBwLimit\x1a7\n" +
	"\n" +
	"TaskConfig\x12)\n" +
	"\vconcurrency\x18\x01 \x01(\x05B\a\xfaB\x04\x1a\x02(\x01R\vconcurrencyB\x1bZ\x19internal/server/conf;confb\x06proto3"

var (
	file_conf_proto_rawDescOnce sync.Once
	file_conf_proto_rawDescData []byte
)

func file_conf_proto_rawDescGZIP() []byte {
	file_conf_proto_rawDescOnce.Do(func() {
		file_conf_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_conf_proto_rawDesc), len(file_conf_proto_rawDesc)))
	})
	return file_conf_proto_rawDescData
}

var file_conf_proto_msgTypes = make([]protoimpl.MessageInfo, 7)
var file_conf_proto_goTypes = []any{
	(*Bootstrap)(nil),          // 0: jdstackintegration.conf.Bootstrap
	(*Other)(nil),              // 1: jdstackintegration.conf.Other
	nil,                        // 2: jdstackintegration.conf.Bootstrap.TaskEntry
	nil,                        // 3: jdstackintegration.conf.Bootstrap.SdkEntry
	(*Other_Filecenter)(nil),   // 4: jdstackintegration.conf.Other.Filecenter
	(*Other_TaskConfig)(nil),   // 5: jdstackintegration.conf.Other.TaskConfig
	nil,                        // 6: jdstackintegration.conf.Other.Filecenter.CliEntry
	(*winconf.Env)(nil),        // 7: wingo.conf.Env
	(*winconf.Web)(nil),        // 8: wingo.conf.Web
	(*winconf.Log)(nil),        // 9: wingo.conf.Log
	(*winconf.HttpClient)(nil), // 10: wingo.conf.HttpClient
	(*winconf.Middleware)(nil), // 11: wingo.conf.Middleware
	(*winconf.Job)(nil),        // 12: wingo.conf.Job
	(*winconf.Task)(nil),       // 13: wingo.conf.Task
	(*winconf.SDK)(nil),        // 14: wingo.conf.SDK
	(*winconf.Ssh)(nil),        // 15: wingo.conf.Ssh
}
var file_conf_proto_depIdxs = []int32{
	7,  // 0: jdstackintegration.conf.Bootstrap.env:type_name -> wingo.conf.Env
	2,  // 1: jdstackintegration.conf.Bootstrap.task:type_name -> jdstackintegration.conf.Bootstrap.TaskEntry
	8,  // 2: jdstackintegration.conf.Bootstrap.web:type_name -> wingo.conf.Web
	9,  // 3: jdstackintegration.conf.Bootstrap.log:type_name -> wingo.conf.Log
	10, // 4: jdstackintegration.conf.Bootstrap.http_client:type_name -> wingo.conf.HttpClient
	11, // 5: jdstackintegration.conf.Bootstrap.mc:type_name -> wingo.conf.Middleware
	3,  // 6: jdstackintegration.conf.Bootstrap.sdk:type_name -> jdstackintegration.conf.Bootstrap.SdkEntry
	12, // 7: jdstackintegration.conf.Bootstrap.job:type_name -> wingo.conf.Job
	1,  // 8: jdstackintegration.conf.Bootstrap.other:type_name -> jdstackintegration.conf.Other
	4,  // 9: jdstackintegration.conf.Other.filecenter:type_name -> jdstackintegration.conf.Other.Filecenter
	5,  // 10: jdstackintegration.conf.Other.task_config:type_name -> jdstackintegration.conf.Other.TaskConfig
	13, // 11: jdstackintegration.conf.Bootstrap.TaskEntry.value:type_name -> wingo.conf.Task
	14, // 12: jdstackintegration.conf.Bootstrap.SdkEntry.value:type_name -> wingo.conf.SDK
	15, // 13: jdstackintegration.conf.Other.Filecenter.ssh:type_name -> wingo.conf.Ssh
	6,  // 14: jdstackintegration.conf.Other.Filecenter.cli:type_name -> jdstackintegration.conf.Other.Filecenter.CliEntry
	15, // [15:15] is the sub-list for method output_type
	15, // [15:15] is the sub-list for method input_type
	15, // [15:15] is the sub-list for extension type_name
	15, // [15:15] is the sub-list for extension extendee
	0,  // [0:15] is the sub-list for field type_name
}

func init() { file_conf_proto_init() }
func file_conf_proto_init() {
	if File_conf_proto != nil {
		return
	}
	file_conf_proto_msgTypes[4].OneofWrappers = []any{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_conf_proto_rawDesc), len(file_conf_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   7,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_conf_proto_goTypes,
		DependencyIndexes: file_conf_proto_depIdxs,
		MessageInfos:      file_conf_proto_msgTypes,
	}.Build()
	File_conf_proto = out.File
	file_conf_proto_goTypes = nil
	file_conf_proto_depIdxs = nil
}
